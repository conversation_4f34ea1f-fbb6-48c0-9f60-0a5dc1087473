<?php

/*
 * 医师快速调查项目结构表[dudong]
 */
class Qk_dr_struct_model extends Common_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = TABLE_QK_DR_STRUCT;
        $this->column_ary = [
            'id', //'ID',
            'source_id', //'平台ID 1、drsay',
            'type_id', //类型ID',
            'project_id', //项目编号',
            'question_id', //问题ID',
            'variable_id', //变量ID',
            'type', //类型',
            'start', //站位起点',
            'finish', //站位完成',
            'min_num',//题目最少选择，字符串最少，数字最小',
            'max_num',//题目最多选择，字符串最长，数字最大',
            'answer_code', //答案code',
            'qt_size',//配额量',
            'question_label', //问题标签',
            'answer_label', //答案标签',
            'question_sort', //问题排序',
            'question_must', //问题必答 1 必答  0 非必答',
            'question_hide', //问题隐藏  0 显示  1 隐藏',
            'answer_must', //答案必须 1 必选  0 非必选',
            'answer_sort', //答案排序',
            'answer_hide', //答案隐藏 0 显示  1 隐藏',
            'answer_result', //题目答案最后结果',
            'answer_jump_touch', //选中答案触发 是否跳转题目显示题目 0 显示  1不显示',
            'answer_jump_question_id', //答案显示 问题编号 问题编号',
        ];
        $this->all_column = '`id`,`source_id`,`type_id`,`project_id`,`question_id`,`variable_id`,`type`,`start`,`finish`,`min_num`,`max_num`,`answer_code`,`qt_size`,`question_label`,`answer_label`,`question_sort`,`question_must`,`question_hide`,`answer_must`,`answer_sort`,`answer_hide`,`answer_result`,`answer_jump_touch`,`answer_jump_question_id`';
    }

    public function get_all_choose($project_id)
    {
        $res = $this->db->select('MIN(t.id) as min')->from($this->table.' t')
            // ->join($this->table.' s','s.id=t.id','left')
            ->where(['t.project_id' => $project_id])->group_by('t.question_id')->get();
        $res = $res ? $res->result_array() : [];
        $ids = [];
        foreach ($res as $key => $value) {
            $ids[]=$value['min'];
        }
        if($ids){
            $list = $this->db->select('id,project_id,question_id,variable_id,type,start,finish,min_num,max_num,question_label,question_sort,question_must,question_hide,question_view_time')->from($this->table)->where_in('type',['single','multi','selectinput'])->where_in('id',$ids)->where_not_in('question_id',['responseid','respid','status','interview_start','interview_end','last_touched','lastcomplete','outsource','uid','username','ip','browsertype','browserversion','Timestamp1','Timediff','SurveyChannel','DeviceType','RenderingMode','surveyStatus','warnflg','cookie_error','cookie_log','logo','button'])->order_by('question_sort asc')->get();
            return $list ? $list->result_array() : [];
        }else{
            return [];
        }
    }

    // 所有描述题
    public function get_all_describe($project_id)
    {
        $res = $this->db->select('MIN(t.id) as min')->from($this->table.' t')
            // ->join($this->table.' s','s.id=t.id','left')
            ->where(['t.project_id' => $project_id])->group_by('t.question_id')->get();
        $res = $res ? $res->result_array() : [];
        $ids = [];
        foreach ($res as $key => $value) {
            $ids[]=$value['min'];
        }
        if($ids){
            $list = $this->db->select('id,project_id,question_id,variable_id,type,start,finish,min_num,max_num,question_label,question_sort,question_must,question_hide,question_view_time,answer_label')->from($this->table)->where(['type'=>'describe'])->where_in('id',$ids)->where_not_in('question_id',['responseid','respid','status','interview_start','interview_end','last_touched','lastcomplete','outsource','uid','username','ip','browsertype','browserversion','Timestamp1','Timediff','SurveyChannel','DeviceType','RenderingMode','surveyStatus','warnflg','cookie_error','cookie_log','logo','button'])->order_by('question_sort asc')->get();
            return $list ? $list->result_array() : [];
        }else{
            return [];
        }
    }
}
