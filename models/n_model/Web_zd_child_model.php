<?php

/**
 * 网站-字典管理-子类
 * User: bryant
 * Date: 2020-04-10
 */
class Web_zd_child_model extends Common_Model {

    protected $table = TABLE_WEB_ZD_CHILD;
    protected $all_column = 'id,parent_id,big_class_id,child_code,label_cn,label_en,notice_cn,notice_en,'
            . 'sort,is_must_select,status,is_delete,add_uid,up_date';

    public function __construct() {
        parent::__construct();
    }
    
    //获取子类下所有子级[bryant]
    private function get_parent($array,$child_code=0){
        static $list = [];
        foreach ($array as $key => $value){
            //第一次遍历,找到父节点为根节点的节点 也就是pid=0的节点
            if ($value['child_code'] == $child_code){
                //把数组放到list中
                $list[] = $value;
                //把这个节点从数组中移除,减少后续递归消耗
                unset($array[$key]);
                //开始递归,查找父ID为该节点ID的节点
                $this->get_parent($array, $value['parent_code']);
            }
        }
        return $list;
    }
    
    /**
     * 获取向上所有父级的信息
     */
    public function getParent($id, $class_id=""){
        if(is_array($class_id)){
            $in = explode(",", $class_id);
        } else {
            $in = $class_id;
        }
        
        // 取得属于id的所有父级
        $sql = "
        SELECT DICT.id, DICT.parent_id, DICT.label_cn
        FROM ( 
            SELECT 
                @id AS _id, 
                (SELECT @id := parent_id FROM sys_child_dic WHERE id = _id) AS parent_id
            FROM 
                (SELECT @id := {$id}) V,
                sys_child_dic
            WHERE @id <> 0) T
        JOIN sys_child_dic DICT
        ON T._id = DICT.id ";

        if($class_id != "") {
            $sql .= " WHERE DICT.big_class_id in ({$in}) ";
        }
        $sql .= " ORDER BY DICT.parent_id";

        $res = $this->db->query($sql)->result_array() ? : array();
        return $res;
    }
    
    /**
     * 获取字典子分类列表
     */
    public function getDict($offset, $numpage, $class_id, $dic_id, $search, $include,$status=''){
        $sql_where = "";
        $sql_cnt = "SELECT COUNT(*) AS cnt FROM sys_child_dic A WHERE ";
        // 获取子分类信息
        if($search != ""){
            $sql_where = " AND (A.id LIKE '%{$search}%' OR A.val LIKE '%{$search}%' OR A.mark LIKE '%{search}%' OR A.remark like '%{search}%')";
        }
        if($status!=''){
            $sql_where .= ' and A.status='. $status;
        }
        switch (TRUE) {
            // 根节点 并且 没有跨类(正常情况)
            case !$include:
                $sql = "SELECT A.* FROM sys_child_dic A WHERE A.big_class_id={$class_id} AND A.pid={$dic_id} ";
                $sql .= $sql_where . " ORDER BY A.sort_number,A.id DESC ";
                // 翻页用
                $sql_cnt .= "A.big_class_id={$class_id} AND A.pid={$dic_id}" . $sql_where;
                break;
            // 根节点 并且 有跨类(这时需要取父类和大类名称)
            case $dic_id == 0 && $include:
                $sql = "SELECT A.*, B.val AS pval, B.big_class_id AS cid,
                                (SELECT val FROM app_sys_big_class C WHERE C.id=B.big_class_id) AS class_name
                        FROM sys_child_dic A 
                            JOIN sys_child_dic B ON(A.pid=B.id)
                        WHERE A.big_class_id={$class_id} ";
                $sql .= $sql_where . " ORDER BY A.sort_number,A.pid ASC, A.id DESC";
                // 翻页用
                $sql_cnt .= "A.big_class_id={$class_id}" . $sql_where;
                break;
            // 不是根节点 并且 有跨类(这时需要取父类和大类名称  只能用pid)
            case $dic_id != 0 && $include:
                $sql = "SELECT A.*, B.val AS pval, A.big_class_id AS cid,
                                (SELECT val FROM app_sys_big_class C WHERE C.id=A.big_class_id) AS class_name
                        FROM sys_child_dic A 
                            JOIN sys_child_dic B ON(A.pid=B.id) 
                        WHERE A.pid={$dic_id}";
                $sql .= $sql_where . " ORDER BY A.sort_number,A.pid ASC, A.id DESC";
                // 翻页用
                $sql_cnt .= "A.pid={$dic_id}" . $sql_where;
                break;
        }
        $sql .= " LIMIT {$offset},{$numpage}";

        $total = $this->db->query($sql_cnt)->row()->cnt;
        $list =  $this->db->query($sql)->result_array();

        $res = array('list'=>$list,'total'=>$total);
        return $res;
    }
}