<?php

/*
 * 医师快速调查项目返回设置表[dudong]
 */
class Qk_dr_reback_model extends Common_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = TABLE_QK_DR_REBACK;
        $this->column_ary = [
            'id', //'ID',
            'qk_id', //快速调查项目ID',
            'type', //返回状态 s 被甄别 c 有效完成  q 配额已满  o 调查已经结束 f 已经参与过',
            'type_title', //类标题',
            'cn', //提示中文',
            'en', //提示英文',
            'jump_url', //跳转路径',
            'vb', //链接中的接收值变量名',
            'status_label', //返回结果状态标code标识',
            'cost', //奖励金额',
            'add_uid', //添加人',
            'add_time', //添加时间',
        ];
        $this->all_column = '`id`,`qk_id`,`type`,`type_title`,`cn`,`en`,`jump_url`,`vb`,`status_label`,`cost`,`add_uid`,`add_time`';
    }

    public function get_list($project_id)
    {
        $list = [];
        $result = $this->db->select('*')->from($this->table)->where(['qk_id' => $project_id])->get();
        $result = $result ? $result->result_array() : [];

        foreach ($result as $key => $val) {
            $list[$val['type']] = $val;
        }
        return $list;
    }

}
