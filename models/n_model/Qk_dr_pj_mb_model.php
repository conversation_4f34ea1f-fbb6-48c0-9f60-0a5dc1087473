<?php

/*
 * 企业员工快速调查项目[dudong]
 */
class Qk_dr_pj_mb_model extends Common_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = TABLE_QK_DR_PJ_MB;
        $this->column_ary = [
            'id', //'ID',
            'short_url', //短连接',
            'dr_id', //医生ID',
            'link', //学习链接',
            'province', //省份',
            'city_level', //城市等级',
            'city', //省份',
            'district', //区',
            'dr_nice', //医生昵称',
            'mobile', //手机号码',
            'email', //email',
            'unit', //医院名字',
            'unit_level', //医院等级',
            'department', //科室',
            'title', //职称',
            'qk_id', //快速调研编号',
            'msg_st', //短信邀请次数',
            'email_st', //email邀请次数',
            'st', //点击状态  1为进入',
            'point', //奖励积分 100积分=1元',
            'finished_st', //项目完成状态',
            'pay_st', //支付状态',
        ];
        $this->all_column = '`id`,`short_url`,`dr_id`,`link`,`province`,`city_level`,`city`,`district`,`dr_nice`,`mobile`,`email`,`unit`,`unit_level`,`department`,`title`,`qk_id`,`msg_st`,`email_st`,`st`,`point`,`finished_st`,`pay_st`';
    }
}
