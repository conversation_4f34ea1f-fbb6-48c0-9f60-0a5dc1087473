<?php

/*
 * 企业员工快速调查项目[dudong]
 */
class Qk_dr_pj_model extends Common_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = TABLE_QK_DR_PJ;
        $this->column_ary = [
            'id', //'ID',
            'title', //问卷标题',
            'brief', //简介',
            'home_info', //首页详情',
            'top_img',
            'type_id', //'1、满意度  2、创新 3、培训  4 薪酬调研',
            'kh', //'客户编号',
            'kp', //'开票公司编号',
            'template', // 短信模板
            'report_st',//报告分享状态  0为分享  1为不分享',
            'invite_st',//限制被邀请才可答题：0不限制，1限制',
            'timmer_st',//是否显示计时器：0不显示，1显示',
            'subject',//定考培训科目分类（对应字典code87）',
            'course',//定考培训科目二级分类（对应字典code87）',
            'sales_id',
            'reward', //'礼金',
            'reward_st', // 是否显示兑换提现按钮：0显示，1不显示
            'sample', //数量',
            'start_time', //'开始时间',
            'close_time', //'关闭时间',
            'add_uid', //'添加人',
            'add_time', //'添加时间',
            'status', //'状态 1、暂停 2、进行 3关闭',
            'sort', //排序
            'price', //单个样本奖励
        ];
        $this->all_column = '`id`,`title`,`brief`,`home_info`,`top_img`,`type_id`,`kh`,`kp`,`template`,`report_st`,`invite_st`,`timmer_st`,`subject`,`course`,`sales_id`,`reward`,`reward_st`,`sample`,`start_time`,`close_time`,`add_uid`,`add_time`,`status`,`sort`,`price`';
    }

    public function all_count($type_id='', $search_key = '')
    {
        $where = [];
        if ($type_id) {
            $where['gc.type_id'] = $type_id;
        }
        if ($search_key) {
            $this->db->or_group_start()->or_like('gc.title', $search_key)->or_like('gc.brief', $search_key)->or_like('m.name', $search_key)->group_end();
        }
        // 总条数
        $total = $this->db->where($where)
            ->join('app_admin m', 'gc.add_uid=m.id', 'left')
            ->count_all_results($this->table . ' gc');
        return $total;
    }

    public function all_get($type_id='', $search_key = '', $offset = 0)
    {
        $where = [];
        if ($type_id) {
            $where['gc.type_id'] = $type_id;
        }
        if ($search_key) {
            $this->db->or_group_start()->or_like('gc.title', $search_key)->or_like('gc.brief', $search_key)->or_like('m.name', $search_key)->group_end();
        }
        $list = $this->db->select('gc.*,m.name,d.label_cn as type_name,w.short_name as kp_name,c.client_name as kh_name')->from($this->table . ' gc')
            ->join('app_admin m', 'gc.add_uid=m.id', 'left')
            ->join('sys_child_dic d', 'gc.type_id=d.id', 'left')
            ->join('work_check_company w', 'gc.kp=w.id', 'left')
            ->join('app_client c', 'gc.kh=c.id', 'left')
            ->where($where)->order_by('gc.id desc')->limit(PAGE_NUM, $offset)->get();
        $list = $list ? $list->result_array() : [];
        return $list;
    }
}
