<?php

/*
 * 企业员工快速调查项目[dudong]
 */
class Qk_dr_val_model extends Common_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = TABLE_QK_DR_VAL;
        $this->column_ary = [
            'id', //'ID',
            'pid',//'项目ID',
            'survey_link',//'问卷链接',
            'r',//问卷链接r',
            's',//问卷链接s',
            'survey_structure',//问卷结构JSON',
            'survey_val',//问卷答案JSON',
            'last_question_id',//最后提价的题目号',
            'status',//参与状态 1 是途中  2 是结束',
            'finish_status',//'c s q 等',
            'payment',
            'payment_type',
            'point',
            'update_time',//'更新时间',
            'uid',//会员ID',
            'province',//省份',
            'city',//城市',
            'city_level',//城市级别',
            'district',//地区',
            'name',//参与者名字',
            'nice',
            'gender',//性别',
            'mobile',//手机号码',
            'unit',//机构名字',
            'unit_area_type',//城乡分类',
            'unit_level',//医院等级',
            'unit_type',//医院类型',
            'department',//科室',
            'dr_standard_department_big',//标准科室大类',
            'dr_standard_department_small',//标准科室小类',
            'title',//职称',
            'dr_sfz',//身份证
            'dr_zyz',//执业证',
            'dr_profession',//专业,
            'dr_scope_of_practice',//执业范围
            'start_time',//答卷开始时间',
            'end_time',//答卷结束时间',
            'answer_differ',//答卷时长',
            'type',//数据收集方式：0问卷，1医师库',
            'push',
        ];
        $this->all_column = '`id`,`pid`,`survey_link`,`r`,`s`,`survey_structure`,`survey_val`,`last_question_id`,`status`,`finish_status`,`payment`,`payment_type`,`point`,`update_time`,`uid`,`province`,`city`,`city_level`,`district`,`name`,`nice`,`gender`,`mobile`,`unit`,`unit_area_type`,`unit_level`,`unit_type`,`department`,`dr_standard_department_big`,`dr_standard_department_small`,`title`,`dr_sfz`,`dr_zyz`,`dr_scope_of_practice`,`dr_profession`,`start_time`,`end_time`,`answer_differ`,`type`,`push`';
    }
}
