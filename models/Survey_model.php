<?php
/**
 * Created by PhpStorm.
 * User: amy
 * Date: 2017/11/15
 * Time: 下午15:56
 */

class Survey_model extends CI_Model{

    //问卷完成，对跳转到公共提示页前，链接加密操作处理
    public function get_redirect_info($url, $param, $uid = "", $pid = "", $other_param = "", $is_redirect = true)
    {
        $str = $param. "DRSAY" . $uid. "DRSAY" . $pid;
        $encode_str = substr(md5($str. "DRSAY" .PROJECT_ENCODE_KEY), 8, 16);
        $code = $str."DRSAY".$encode_str;
        $other_param = $other_param ? "&{$other_param}" : "";
        //本地跳转才需要加密code，否则直接跳转
        $pos = strpos($url, "/bk/rs");
        $is_param = $this->redirect_is_param($url);
        if ($is_param) {//存在问号
            $linker = "&";
        } else {//不存在问号
            $linker = "?";
        }
        if ($pos === false) {//不是上医说返回链接]
            $redirect_url = $url."{$linker}st={$param}{$other_param}";
//            redirect($url."?st={$param}&{$other_param}");
        } else {
            $redirect_url = $url."{$linker}code={$code}{$other_param}";
//            redirect($url."?code={$code}{$other_param}");
        }
        if ($is_redirect) {
            if ($param == "p_s") {
                file_put_contents('./tmp/get_redirect_info_s.txt', $redirect_url.PHP_EOL.PHP_EOL,FILE_APPEND | LOCK_EX);
            }
            redirect($redirect_url);
        } else {
            return $redirect_url;
        }
    }

    //重定向链接是否已经存在参数
    public function redirect_is_param($back_url){
        if ($back_url) {
            if (strpos($back_url, "?") !== false) {//存在问号
                return true;
            } else {//不存在问号
                return false;
            }
        } else {
            return false;
        }
    }


    //分配问卷链接
    public function get_survey_link($pid, $groupno, $partner_uid, $partner_id, $lang)
    {
        $survey_link = $this->distribution_link($pid, $groupno, $partner_uid);//分配的链接
        //判断是否有未用链接，如果有，取一根链接做跳转
        $jkt_thanks_url = "/bk/rs";
        if(!$survey_link){
            //获取项目某组号下最后一根链接
            $pro_s_link = $this->db->query("SELECT max(id),link_type FROM app_project_s_link WHERE pid='{$pid}' AND link_group='{$groupno}' LIMIT 1")->row_array();
            if (!$pro_s_link) {
                return false;
            }
            //判断是否是单链接
            if ($pro_s_link['link_type'] == 1) {//单链接，系统自动添加100根链接，再分配问卷链接给用户
                //生成问卷链接
                $add_pro_survey_link = $this->project_survey_link($pid, 100);
                if ($add_pro_survey_link) {//链接生成成功
                    if ($partner_uid != 'test=1') {
                        $survey_link = $this->distribution_link($pid, $groupno, $partner_uid);//分配链接
                    }
                } else {//创建问卷链接不成功，跳转到结束页
                    set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang[LABEL_SURVEY_SINGLE_LINK_INSUFFICIENT]);
                    $this->get_redirect_info($jkt_thanks_url, "ne");
//                    redirect("/go/thanks?st=ne");
                }
            } else {//多链接，直接提示信息
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang[LABEL_MULTIPLE_LINK_INSUFFICIENT]);
                $this->get_redirect_info($jkt_thanks_url, "ne");
//                redirect("/go/thanks?st=ne");
            }
        }
        return $survey_link ? $survey_link : false;
    }

    //通过quta_setting与用户的原始信息查询匹配是否符合配额条件
    /*
     * quota_setting  配额属性
     * member_info    用户信息
     * new_attribute  自定义参数
     */
    public function get_by_quota_cond_and_member_info_control($pid, $quota_setting, $member_info, $new_attribute, $res_survey_decrypt, $is_ajax = false, $lang)
    {
        if (!$pid || !$quota_setting || !$member_info) {return false;}
        $arr_quota_setting = json_decode($quota_setting, true);
        $res_new_attr = array();
        if ($new_attribute) {//有自定义参数
            $arr_new_attribute = trim($new_attribute);
            if ($arr_new_attribute) {
                $arr_new_attr = explode("\n", $new_attribute);
                foreach ($arr_new_attr as $v_attr) {
                    $v_attr = trim($v_attr);
                    $res_new_attr[$v_attr] = $v_attr;
                }
            }
        }
        $rs_quota_cond = array();//直接需要查询配额记录的信息
        $rs_not_quota_cond = array();//查询配额的自定义信息
        foreach ($arr_quota_setting as $v_setting) {
            if (!in_array($v_setting, $this->quta_setting_filter_param)) {//不在过滤的数组里
                if ($res_new_attr) {//有自定义参数
                    if (isset($member_info[$v_setting]) && $member_info[$v_setting]) {
                        if (!in_array($v_setting, $res_new_attr)) {//不在自定义参数里
                            if ($v_setting == "department") {
                                $rs_quota_cond[] = $v_setting." LIKE '%".$member_info[$v_setting]."%'";
                            } else {
                                $rs_quota_cond[] = $v_setting."='".$member_info[$v_setting]."'";
                            }
                        } else {//存在自定义参数字段里
                            $rs_not_quota_cond[$v_setting] = $member_info[$v_setting];
                        }
                    }
                } else {//没有自定义参数，都是固定参数
                    if (isset($member_info[$v_setting]) && $member_info[$v_setting]) {
                        if ($v_setting == "department") {
                            $rs_quota_cond[] = $v_setting . " LIKE '%".$member_info[$v_setting]."%'";
                        } else {
                            $rs_quota_cond[] = $v_setting . "='" . $member_info[$v_setting] . "'";
                        }
                    }
                }
            }
        }
        if ($rs_quota_cond) {
//            $partner_id = $res_survey_decrypt['msg']['go_partner_id'];
//            $groupno = $res_survey_decrypt['msg']['go_groupno'];
            $res_quota_info = $this->get_accord_quota_info_control($pid, $rs_quota_cond, $rs_not_quota_cond);
            if (!$res_quota_info) {//说明没有符合条件的配额
                $session_answer = isset($this->session->userdata['pid_'.$pid]['session_answer']) ? $this->session->userdata['pid_'.$pid]['session_answer'] : array();
                $this->update_implement_s_link_info($pid, $session_answer, $res_survey_decrypt, "", $is_ajax, $lang);
            } else {
                return $res_quota_info;//把配额编号返回
            }
        }
    }

    //查找符合条件的配额
    //$is_quota_id是否是最终的匹配结果，返回最终符合条件的配额编号
    public function get_accord_quota_info_control($pid, $rs_quota_cond, $rs_not_quota_cond)
    {
        if (!$pid) {return false;}
        $quta_cond = " AND ".implode(" AND ",$rs_quota_cond);
        $res_project_quta = $this->db->query("SELECT * FROM app_project_quta_control WHERE pid='{$pid}' AND mount > 0 ".$quta_cond)->result_array();
        file_put_contents('./tmp/get_accord_quota_info.txt',"SELECT * FROM app_project_quta_control WHERE pid='{$pid}' AND mount > 0 ".$quta_cond);

        $res_quota_id = false;//没有符合条件的配额
        if ($res_project_quta) {//没有符合条件的配额
            if ($rs_not_quota_cond) {//答案里存在有附加配额信息
                foreach ($res_project_quta as $v) {//有符合条件的配额，接着检测附加配额设置
                    if ($v['new_attribute']) {
                        $arr_new_attribute = json_decode($v['new_attribute'], true);//附加配额设置
                        //匹配两个数组是否一致
                        $check_quota_info_member = array_diff($rs_not_quota_cond, $arr_new_attribute);
                        if ($check_quota_info_member) {//存在不一致的，continue，继续匹配，直到结束
                            continue;
                        } else {//最终匹配结果一致
                            if ($v['now_c_mount'] < $v['mount']) {//c的完成量未满
                                $res_quota_id = $v['id'];//配额编号
                            } else {
                                $res_quota_id = false;
                            }
                            break;
                        }
                    }
                }
            } else {//答案不存在自定义配额，直接返回配额编号
                $res_quota_id = $res_project_quta[0]['id'];
            }
        }
        return $res_quota_id;
    }

    //通过quta_setting与用户的原始信息查询匹配是否符合配额条件
    /*
     * quota_setting  配额属性
     * member_info    用户信息
     * new_attribute  自定义参数
     */
    public function get_by_quota_cond_and_member_info($pid, $quota_setting, $member_info, $new_attribute, $res_survey_decrypt, $is_ajax = false, $is_quota_id = false, $lang)
    {
        if (!$pid || !$quota_setting || !$member_info) {return false;}
        $arr_quota_setting = json_decode($quota_setting, true);
        $res_new_attr = array();
        if ($new_attribute) {//有自定义参数
            $arr_new_attribute = trim($new_attribute);
            if ($arr_new_attribute) {
                $arr_new_attr = explode("\n", $new_attribute);
                foreach ($arr_new_attr as $v_attr) {
                    $v_attr = trim($v_attr);
                    $res_new_attr[$v_attr] = $v_attr;
                }
            }
        }
        $rs_quota_cond = array();//直接需要查询配额记录的信息
        $rs_not_quota_cond = array();//查询配额的自定义信息
        foreach ($arr_quota_setting as $v_setting) {
            if (!in_array($v_setting, $this->quta_setting_filter_param)) {//不在过滤的数组里
                if ($res_new_attr) {//有自定义参数
                    if (isset($member_info[$v_setting]) && $member_info[$v_setting]) {
                        if (!in_array($v_setting, $res_new_attr)) {//不在自定义参数里
                            if ($v_setting == "department") {
                                $rs_quota_cond[] = $v_setting." LIKE '%".$member_info[$v_setting]."%'";
                            } else {
                                $rs_quota_cond[] = $v_setting."='".$member_info[$v_setting]."'";
                            }
                        } else {//存在自定义参数字段里
                            $rs_not_quota_cond[$v_setting] = $member_info[$v_setting];
                        }
                    }
                } else {//没有自定义参数，都是固定参数
                    if (isset($member_info[$v_setting]) && $member_info[$v_setting]) {
                        if ($v_setting == "department") {
                            $rs_quota_cond[] = $v_setting . " LIKE '%".$member_info[$v_setting]."%'";
                        } else {
                            $rs_quota_cond[] = $v_setting . "='" . $member_info[$v_setting] . "'";
                        }
                    }
                }
            }
        }
        if ($rs_quota_cond) {
            $partner_id = $res_survey_decrypt['msg']['go_partner_id'];
            $groupno = $res_survey_decrypt['msg']['go_groupno'];
            $res_quota_info = $this->get_accord_quota_info($pid, $partner_id, $groupno, $rs_quota_cond, $rs_not_quota_cond, $is_quota_id);
            if (!$res_quota_info) {//说明没有符合条件的配额
                $session_answer = isset($this->session->userdata['pid_'.$pid]['session_answer']) ? $this->session->userdata['pid_'.$pid]['session_answer'] : array();
                $this->update_implement_s_link_info($pid, $session_answer, $res_survey_decrypt, "", $is_ajax, $lang);
            } else {
                return $res_quota_info;//把配额编号返回
            }
        }
    }

    //查找符合条件的配额
    //$is_quota_id是否是最终的匹配结果，返回最终符合条件的配额编号
    public function get_accord_quota_info($pid, $partner_id, $groupno, $rs_quota_cond, $rs_not_quota_cond, $is_quota_id = false)
    {
        if (!$pid) {return false;}
        $quta_cond = " AND ".implode(" AND ",$rs_quota_cond);
        $res_project_quta = $this->db->query("SELECT * FROM app_project_quta WHERE pid='{$pid}' AND mount > 0 AND quta_status='1' AND groupno='{$groupno}' ".$quta_cond)->result_array();
        file_put_contents('./tmp/get_accord_quota_info.txt',"SELECT * FROM app_project_quta WHERE pid='{$pid}' AND mount > 0 AND groupno='{$groupno}' ".$quta_cond);

        $res_quota_id = false;//没有符合条件的配额
        if ($res_project_quta) {//没有符合条件的配额
            if ($rs_not_quota_cond) {//答案里存在有附加配额信息
                foreach ($res_project_quta as $v) {//有符合条件的配额，接着检测附加配额设置
                    if ($v['new_attribute']) {
                        $arr_new_attribute = json_decode($v['new_attribute'], true);//附加配额设置
                        //匹配两个数组是否一致
                        $check_quota_info_member = array_diff($rs_not_quota_cond, $arr_new_attribute);
                        if ($check_quota_info_member) {//存在不一致的，continue，继续匹配，直到结束
                            continue;
                        } else {//最终匹配结果一致
                            if ($v['now_c_mount'] < $v['mount']) {//c的完成量未满
//                                if ($is_quota_id) {
//                                    $res_quota_id = $v['id'];//配额编号
//                                } else {
//                                    $res_quota_id = true;//有符合条件的配额
//                                }
                                $res_quota_id = $v['id'];//配额编号
                            } else {
                                $res_quota_id = false;
                            }
                            break;
                        }
                    }
                }
            } else {//答案不存在自定义配额，直接返回配额编号
//                $res_quota_id = true;
                $res_quota_id = $res_project_quta[0]['id'];
            }
        }
        return $res_quota_id;
    }

    //没有匹配的配额信息，项目结束
    public function update_implement_s_link_info($pid, $session_info_project, $res_survey_decrypt, $post_data, $is_ajax, $lang)
    {

        $partner_uid = $res_survey_decrypt['msg']['partner_uid'];
        $partner_id = $res_survey_decrypt['msg']['go_partner_id'];
        $groupno = $res_survey_decrypt['msg']['go_groupno'];
        if (!$pid || !$partner_uid) {return false;}
        $member_uid = 0;
        if (strpos($partner_uid, "uid={$pid}_") !== false) {//存在会员编号
            $member_uid = str_replace("uid=".$pid."_", "", $partner_uid);
            $member_uid = (int)trim($member_uid);
        }

        ############  检测是否是测试链接，如果是测试链接，不记录信息  ############
        if ($partner_uid != 'test=1') {
            ##############  AMY 2018-05-20，rainnie要求，被自己甄别的也分配链接    ##############
            //随机分配一根链接
            $get_survey_link = $this->get_survey_link($pid, $groupno, $partner_uid, $partner_id, $lang);
            ##############  AMY 2018-05-20，rainnie要求，被自己甄别的也分配链接    ##############
            //甄别问卷答案
//            $quta_survey_answer = $session_info_project  ? array_merge($session_info_project, $post_data) : $post_data;
            $quta_survey_answer = $session_info_project;
            $st = "s";
            //记录点击信息
            //从我们系统进入，经过甄别或者条件查询，不符合条件，被系统甄别的，给状态，给对应状态积分奖励
            $data_implement['finish_status'] = $st;
            $data_implement['original_finish_status'] = $st;
            $data_implement['survey_uid'] = $get_survey_link['survey_uid'];
//            $data_implement['inconsistent'] = "2";//不符合甄别条件
            if ($quta_survey_answer) {
                $data_implement['quta_survey_answer'] = json_encode($quta_survey_answer, JSON_UNESCAPED_UNICODE);
            }
            $res = $this->update_implement_a_s_link_table(5, $pid, $data_implement, $res_survey_decrypt, true, $get_survey_link['id']);
            if ($res) {//被甄别，奖励的积分,直接到账

                //通过pid与partner_id获取外包信息，获取外包奖励信息
                $res_project_partner = $this->db->query("SELECT a.*,b.pro_type FROM app_project_partner a left join app_project b on a.project_id=b.id WHERE a.project_id='{$pid}' AND a.partner_id='{$partner_id}' AND a.groupno='{$groupno}' LIMIT 1")->row_array();
                //查询是否是内部资源的外包
                if ($res_project_partner && $res_project_partner['property'] == 1) {
                    $point_info = $res_project_partner['point_'.$st];
                    if ($member_uid) {
                        add_member_point_info($member_uid, $point_info, POINT_CHANGE_CODE_SURVEY, $pid, $res_project_partner['pro_type'], array("finish_status" => $st), POINT_AUDITING_END, "survey_point");
                        //条件不符合的结束
                        $con_partner_uid = $res_survey_decrypt['msg']['partner_uid'];
                        $p_table = "app_project_implement_" . $pid;
                        $where_implement = array('partner_uid'=>$con_partner_uid);
                        $screening_status = array(
                            'screening_status'=>6,
                        );
                        $this->update_surface($p_table,$screening_status , $where_implement); // 更新相关内容到项目明细表
                    }
                }
            }
        }
        ############  检测是否是测试链接，如果是测试链接，不记录信息  ############
        $res_session_pid = isset($this->session->userdata["pid_".$pid]) ? $this->session->userdata("pid_".$pid) : "";
        if ($res_session_pid) {
            //清session
            $this->session->unset_userdata("pid_".$pid);
        }
        set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang['LABEL_SURVEY_QUOTA_ERROR_OR_QUOTA_FULL']);
        if ($is_ajax) {
            _back_msg("survey_finish", 's');
        } else {
            $this->get_redirect_info("/bk/rs", "s", $member_uid, $pid);
//            redirect("/go/thanks?st=s");
        }
    }

    //更新项目执行表与问卷链接表信息
    /*
     * is_finish_quta:当甄别问卷到最后一步操作成功时，需要更新问卷链接表信息
     * survey_link_id:is_finish_quta为true时，此项必需有值
     */
    public function update_implement_a_s_link_table($source,$pid, $res, $res_survey_decrypt, $is_finish_quta = false, $survey_link_id = "")
    {
        if (!$pid || !$res_survey_decrypt) {return false;}
        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);
        $data_implement = array(
            'ip' => $ip,
            'ip_addr' => $ip_addr,
            'click_time' => time(),
            'screening_status' => 8,
        );
        if ($res) {
            $data_implement = array_merge($data_implement, $res);
        }
        $p_table = "app_project_implement_" . $pid;
        $partner_uid = $res_survey_decrypt['msg']['partner_uid'];
        $partner_id = $res_survey_decrypt['msg']['go_partner_id'];
        $group_no = $res_survey_decrypt['msg']['go_groupno'];
        $sys_id = $res_survey_decrypt['msg']['sys_id'];//问卷来源
        $where_implement = array('partner_uid'=>$partner_uid);

        if (strpos($partner_uid, "uid=".$pid."_") !== false) {//是上医说会员
            $uid = str_replace("uid=".$pid."_","",$partner_uid);
            $uid = (int)$uid;
            //通过pid与uid查询此链接是否是被集团账号点击过
            if ($uid > 0) {
                $i_survey_info = $this->db->query("SELECT * FROM i_survey_project_bespeak WHERE pid=? AND member_uid=?", [$pid, $uid])->row_array();
                if ($i_survey_info) {//记录存在，说明此条记录是被集团账号过来，更新到项目执行表中
                    $data_implement = array_merge($data_implement, ["my" => 1]);
                }
            }
        }

        $this->update_surface($p_table, $data_implement, $where_implement); // 更新相关内容到项目明细表

        if ($is_finish_quta) {
            if ($survey_link_id) {
                $update_cond = '';
                $link_source = $this->db->where(array('id'=>$survey_link_id))->get('app_project_s_link')->row_array();
                if($link_source['source']){
                    $update_cond = "second_source={$source},";
                } else{
                    $update_cond = "source={$source},";
                }
                $update_cond .= "entry_time='".time()."',";
                $this->db->query("UPDATE app_project_s_link SET {$update_cond} is_used='2',partner_id='{$partner_id}',partner_uid='{$partner_uid}' WHERE id='{$survey_link_id}' LIMIT 1");
            }
        }

        //更新项目起始信息
        $get_project_info = $this->db->query("SELECT * FROM app_project WHERE id='{$pid}' LIMIT 1")->row_array();
        $get_project_status_info = $this->db->query("SELECT * FROM app_project_bespeak WHERE pid='{$pid}' AND partner_uid='{$partner_uid}' LIMIT 1")->row_array();
        if (!$get_project_status_info) {
            $project_type = $get_project_info['pro_type'];
            if (strpos($partner_uid, "uid=".$pid."_") === false) {//不是上医说会员
                $uid = 0;
            } else {//是上医说会员
                $uid = str_replace("uid=".$pid."_","",$partner_uid);
                $uid = (int)$uid;
            }
            $insert_project_status = array(
                "project_type" => $project_type,
                "pid" => $pid,
                "add_time" => time(),
                "partner_uid" => $partner_uid,
                "uid" => $uid,
                "status" => PROJECT_STATUS_BESPEAK_SUCCESS,
                "finish_status" => isset($data_implement['finish_status']) ? $data_implement['finish_status'] : "",
                "partner_id" => $partner_id,
                "group_no" => $group_no,
                "sys_id" => $sys_id,
            );
            $this->db->insert("app_project_bespeak", $insert_project_status);
        } else {
            if (isset($data_implement['finish_status'])) {
                $update_project_status = array(
                    "finish_status" => isset($data_implement['finish_status']) ? $data_implement['finish_status'] : "",
                );
                $this->db->where("pid", $pid);
                $this->db->where("partner_uid", $partner_uid);
                $this->db->where("finish_status", "");
                $this->db->update("app_project_bespeak", $update_project_status);
            } else {//更新来源地址
                $update_project_status = ["sys_id" => $sys_id];
                $this->db->where("pid", $pid);
                $this->db->where("partner_uid", $partner_uid);
                $this->db->update("app_project_bespeak", $update_project_status);
            }
        }

        $res_session_pid = isset($this->session->userdata["pid_".$pid]) ? $this->session->userdata("pid_".$pid) : "";
        if ($res_session_pid) {
            //清session
            $this->session->unset_userdata("pid_".$pid);
        }
        return true;
    }

    /**
     * @param $pid
     * @param $link_amount
     * @return bool
     * 生成链接地址
     */
    public function project_survey_link($pid, $link_amount){
        if(!$pid || !$link_amount){return false;}
        $ci = &get_instance();
        $app_project_s_link = $ci->db->where(array('pid'=>$pid))->order_by('id','desc')->get('app_project_s_link')->row_array(); //查询链接表
        $link_str = $app_project_s_link['unique_variable'].'='.$app_project_s_link['survey_uid']; // 链接变量、参数
        $link_str_variable = $app_project_s_link['unique_variable'].'={'.$app_project_s_link['unique_variable'].'}'; // 替换链接
        $survey_link = $app_project_s_link['survey_link']; //链接路径

        $res_survey_link = str_replace($link_str,$link_str_variable,$survey_link); //替换链接参数为大括号
        $app_project_link_data = array(
            'pid' => $app_project_s_link['pid'], // 项目id pid
            'link_type' => $app_project_s_link['link_type'], // 链接类型 link_type
            'is_link_unique_variable' => $app_project_s_link['is_link_unique_variable'], // 进入问卷连接是否携带参数？ is_link_unique_variable
            'link_group' => $app_project_s_link['link_group'], // 问卷组号设置 link_group
            'link_group_info' => $app_project_s_link['link_group_info'], // 问卷组号说明 link_group_info
            'unique_variable' => $app_project_s_link['unique_variable'], // 问卷链接参数 unique_variable
            'survey_link' => $res_survey_link, // 问卷链接 survey_link
        );

        //单链接上传
        if ($app_project_s_link['link_type'] == 1) {
            ####    新问卷链接生成规则 AMY 2023-03-20   ####
            $s_num = 0;
            $link_info = $this->db->query("SELECT * FROM app_project_s_link WHERE pid=? AND link_type=1 AND survey_uid LIKE '{$pid}l%' ORDER BY id DESC LIMIT 1", [$pid])->row_array();
            if ($link_info) {
                $arr_survey_uid = explode("l", $link_info['survey_uid']);
                $num_survey_uid = $arr_survey_uid[1];
                if (is_numeric($num_survey_uid)) {
                    $s_num = $num_survey_uid;
                }
            }
            ####    新问卷链接生成规则 AMY 2023-03-20  ####

            $survey_link = $app_project_link_data['survey_link'];
            $unique_variable = $app_project_link_data['unique_variable'];
            $str = '{' . $unique_variable . '}';

            $time = time();
            $insert_data = [];
            for ($i = 1; $i <= $link_amount; $i++) {
//                $surver_uid = substr(md5($time . $i), 8, 16);
                ####    新问卷链接生成规则   ####
                $s_num += 1;
                $surver_uid = $pid."l".$s_num;
                ####    新问卷链接生成规则   ####

                $link = str_replace($str, $surver_uid, $survey_link); // 问卷链接 survey_link
                $app_project_link_data['survey_link'] = $link;
                $app_project_link_data['survey_uid'] = $surver_uid;
                $insert_data[] = $app_project_link_data;
            }
            if ($insert_data) {
                insert_ignore_bath('app_project_s_link', $insert_data);
            }
            return true;
        }
        return false;
    }

    //分配链接
    public function distribution_link($pid, $link_group, $partner_uid = "")
    {
        if (!$pid || !$link_group) {return false;}
        //通过项目编号获取项目信息
        $pro_info = $this->db->query("SELECT * FROM app_project WHERE id=?", [$pid])->row_array();

        $from_where = 0;
        $get_survey_link = array();
        $survey_uid = "";
        if ($partner_uid) {//已经存在点击记录也分配了问卷链接的，直接进入问卷
//            $get_survey_link = $this->ci->db->query("SELECT * FROM app_project_s_link WHERE pid='{$pid}' AND `link_group`='{$link_group}' AND partner_uid='{$partner_uid}' LIMIT 1")->row_array();
            $get_survey_link = $this->db->query("SELECT * FROM app_project_s_link WHERE pid='{$pid}' AND partner_uid='{$partner_uid}' LIMIT 1")->row_array();
            $survey_uid = $get_survey_link['survey_uid'];
            $from_where = 1;
        }
        if (!$get_survey_link) {//不存在已点击的链接时，随机分配一条链接
            //检查是否是内部资源的外包才可以用此项配置
            $is_property = false;
            if ($partner_uid) {
                $pro_imp_info = $this->db->query("SELECT a.*,b.property FROM app_project_implement_{$pid} a left join app_vendor b on a.partner_id=b.id WHERE partner_uid=?", [$partner_uid])->row_array();
                if ($pro_imp_info && $pro_imp_info['property'] == VENDOR_INTERNAL) {//内部资源的外包
                    $is_property = true;
                }
            }
            if ($pro_info['is_finish'] == 1 && $is_property) {//问卷直接进入状态提交页，主要针对cati，没有问卷链接的项目，系统自动生成链接,内部资源的外包
                ####    新问卷链接生成规则 AMY 2023-03-20   ####
                $s_num = 0;
                $link_info = $this->db->query("SELECT * FROM app_project_s_link WHERE pid=? AND link_type=1 AND survey_uid LIKE '{$pid}l%' ORDER BY id DESC LIMIT 1", [$pid])->row_array();
                if ($link_info) {
                    $arr_survey_uid = explode("l", $link_info['survey_uid']);
                    $num_survey_uid = $arr_survey_uid[1];
                    if (is_numeric($num_survey_uid)) {
                        $s_num = $num_survey_uid;
                    }
                }
                $s_num += 1;
                $survey_uid = $pid."l".$s_num;
                ####    新问卷链接生成规则 AMY 2023-03-20  ####

//                $survey_uid = substr(md5(time()), 8, 16);
                $survey_code = $pid."_".substr(md5($pid."_".$survey_uid."_".PROJECT_ENCODE_KEY), 8, 16);
                $survey_link = DRSAY_WEB."bk/comm_sv/".$survey_uid."/".$survey_code;
                $insert_link_data = [
                    "pid" => $pid,
                    "link_group" => 1,
                    "link_group_info" => "LIVE",
                    "unique_variable" => "uid",
                    "partner_id" => 1,
                    "link_type" => 1,
                    "is_used" => 2,
                    "survey_link" => $survey_link,
                    "survey_uid" => $survey_uid,
                    "partner_uid" => $partner_uid,
                    "entry_time" => time(),
                ];
                $res = $this->db->insert("app_project_s_link", $insert_link_data);
                if (!$res) {
                    $get_survey_link = null;
                    $survey_uid = '';
                } else {
                    $get_survey_link = $insert_link_data;
                }
                $from_where = 2;
            } else {
//            $get_survey_link = $this->db->query("SELECT * FROM app_project_s_link WHERE pid='{$pid}' AND `link_group`='{$link_group}' AND is_used='1' ORDER BY RAND() LIMIT 1")->row_array();
//            $survey_uid = $get_survey_link['survey_uid'];
//            $from_where = 2;
                //随机获取一根未使用的问卷链接
                $sort_list = ['asc', 'desc'];
                $sort = $sort_list[array_rand($sort_list)];
                $get_survey_link_ids = $this->db->query("SELECT id FROM app_project_s_link WHERE pid='{$pid}' AND `link_group`='{$link_group}' AND is_used='1'  and partner_uid='' ORDER BY id {$sort}  LIMIT 1000")->result_array();
                if(count($get_survey_link_ids) > 0) {
                    $link_ids = [];
                    foreach($get_survey_link_ids as $get_survey_link_id) {
                        $link_ids[] = $get_survey_link_id['id'];
                    }
                    $link_id = intval($link_ids[array_rand($link_ids)]);
                    $get_survey_link = $this->db->query("SELECT * FROM app_project_s_link WHERE id={$link_id}")->row_array();
                    $survey_uid = $get_survey_link['survey_uid'];
                    $from_where = 2;
                } else {
                    $get_survey_link = null;
                    $survey_uid = '';
                    $from_where = 2;
                }
            }
        }
        if ($survey_uid && $partner_uid) {
            ####    查询partner_uid是否有点击记录，有的时候，不更新项目扩展表的点击状态 amy 2020-03-04
            $get_imp_info = $this->db->query("SELECT id,click_time FROM app_project_implement_{$pid} WHERE partner_uid='{$partner_uid}' LIMIT 1")->row_array();
            if ($get_imp_info['click_time'] == 0) {//说明是第一次点击，需要记录响应时间
                $lct_now_time = time();
                $get_click_num = $this->db->query("SELECT * FROM app_project_response_lct WHERE project_id='{$pid}' LIMIT 1")->row_array();
                if (!$get_click_num) {//不存在
                    $this->db->query("INSERT INTO app_project_response_lct(`project_id`,`response_num`,update_t)VALUES('{$pid}',1,'{$lct_now_time}')");
                } else {
                    $this->db->query("UPDATE app_project_response_lct SET response_num=`response_num`+1,update_t='{$lct_now_time}' WHERE project_id='{$pid}' LIMIT 1");
                }
            }
            ####    查询partner_uid是否有点击记录，有的时候，不更新项目扩展表的点击状态 amy 2020-03-04

            //更新项目执行表记录，防止后期数据丢失
            $local_ip = getip();
            $ip = ip2long($local_ip);
            $ip_addr = ip2location($ip);
            $data_implement = array(
                'ip' => $ip,
                'ip_addr' => $ip_addr,
                'click_time' => time(),
                'screening_status' => 8,
                'survey_answer_status' => SURVEY_ANSWER_START, // 受访者进入正式问卷的状态：1、答卷中 2、答卷结束
                'survey_uid' => $survey_uid,
            );
            if (strpos($partner_uid, "uid=".$pid."_") !== false) {//是上医说会员
                $uid = str_replace("uid=".$pid."_","",$partner_uid);
                $uid = (int)$uid;
                //通过pid与uid查询此链接是否是被集团账号点击过
                if ($uid > 0) {
                    $i_survey_info = $this->db->query("SELECT * FROM i_survey_project_bespeak WHERE pid=? AND member_uid=?", [$pid, $uid])->row_array();
                    if ($i_survey_info) {//记录存在，说明此条记录是被集团账号过来，更新到项目执行表中
                        $data_implement = array_merge($data_implement, ["my" => 1]);
                    }
                }
            }

            ### 获取到的survey_uid，再次去检测项目执行表，是否已经存在，存在时需要再分配 AMY 2020-06-18
            $check_pro_imp_info = $this->db->query("SELECT * FROM app_project_implement_{$pid} WHERE survey_uid=? AND partner_uid!=?", [$survey_uid, $partner_uid])->row_array();
            if ($check_pro_imp_info) {
                //此survey_uid已经被占用，则把占用的名单检测一下
                $this->db->query("UPDATE app_project_s_link SET partner_uid=? WHERE pid=? AND survey_uid=? AND partner_uid=''", [$check_pro_imp_info['partner_uid'], $pid, $survey_uid]);
                //记录警告信息
                set_sys_warning($pid, "", $link_group, $partner_uid, "survey_uid[{$survey_uid}]已经被{$check_pro_imp_info['partner_uid']}占用,partner_uid[{$partner_uid}]需要重新分配");
                //重新分配问卷链接
                //return $this->distribution_link($pid, $link_group, $partner_uid);
                //停止重新分配-bryant-0827
                //$this->get_redirect_info("/bk/rs", "ne");
                return false;
            }
            ### 获取到的survey_uid，再次去检测项目执行表，是否已经存在，存在时需要再分配 AMY 2020-06-18

            $where_implement = array('partner_uid'=>$partner_uid);
            $res = $this->update_surface("app_project_implement_{$pid}", $data_implement, $where_implement); // 更新相关内容到项目明细表
            if (!$res) {//更新失败
                //记录警告信息
                set_sys_warning($pid, "", $link_group, $partner_uid, "分配链接成功，但是更新项目执行表失败!".$this->db->last_query());
                return false;
            }
        }

        //更新问卷来源地址
        $this->db->query("insert into app_project_s_link_log(pid,link_group,survey_uid,partner_uid,add_time,from_where)VALUES('{$pid}','{$link_group}','{$survey_uid}','{$partner_uid}','".time()."','{$from_where}')");
        return $get_survey_link ? $get_survey_link : false;
    }

    //url解密
    /*
     * go_code 外包链接的加密串
     * query_string_uid 跳转到甄别问卷前生成的加密串
     * is_quta_survey 是否是甄别问卷下的验证，如果是，才做上一步的加密验证判断，否则不做判断
     */
    public function survey_decrypt($go_code, $query_string_uid, $is_quta_survey = true, $is_ajax = true){
        if ($is_quta_survey) {
            if(!$go_code || !$query_string_uid){
                if ($is_ajax){
                    _back_msg("error", "bl");
                } else {
                    $this->get_redirect_info("/bk/rs", "bl");
//                    redirect("/go/thanks?st=bl");
                }
            }
        } else {
            if(!$go_code){
                if ($is_ajax){
                    _back_msg("error", "bl");
                } else {
                    $this->get_redirect_info("/bk/rs", "bl");
//                    redirect("/go/thanks?st=bl");
                }
            }
        }

        $partner_paran = explode('_',$go_code);
        $pid = $partner_paran[0];
        if(!$pid) {
            if ($is_ajax){
                _back_msg("error", "bl");
            } else {
                $this->get_redirect_info("/bk/rs", "bl");
//                redirect("/go/thanks?st=bl");
            }
        }

        $go_partner_id = $partner_paran[1];
        $go_groupno = $partner_paran[2];
        //加密的代码
        $go_encryption = $partner_paran[3];
        $sys_id = isset($partner_paran[4]) ? $partner_paran[4] : 0;

        //当前组装的加密代码
        $str = $pid . '_' . $go_partner_id . '_' . $go_groupno;
        //来源与加密方式验证
        $from_where = $this->survey_from_where($str, $go_encryption);
        if (!$from_where) {
            if ($is_ajax){
                _back_msg("error", "bl");
            } else {
                $this->get_redirect_info("/bk/rs", "bl");
//                redirect("/go/thanks?st=bl");
            }
        }

//        $go_local_encryption = str_md5_code($str.PROJECT_ENCODE_KEY);
        //1、验证参数是否存在
        if(!$pid || !$go_partner_id || !$go_groupno){
            if ($is_ajax){
                _back_msg("error", "bl");
            } else {
                $this->get_redirect_info("/bk/rs", "bl");
//                redirect("/go/thanks?st=bl");
            }
        }

        if ($is_quta_survey) {
            $query_string_uid_arr = explode('_',$query_string_uid);
            $partner_uid_code = $query_string_uid_arr[0];
            $partner_uid_encryption = $query_string_uid_arr[1];
            $partner_uid = base64_decode($partner_uid_code); // partner_uid
            $current_partner_uid_encryption = md5($partner_uid_code.PROJECT_ENCODE_KEY.$pid);


            if($partner_uid_encryption != $current_partner_uid_encryption){
                if ($is_ajax){
                    _back_msg("error", "bl");
                } else {
                    $this->get_redirect_info("/bk/rs", "bl");
//                    redirect("/go/thanks?st=bl");
                }
            }
            $res = array(
                'pid' => $pid,
                'partner_uid' => $partner_uid,
                'go_partner_id' => $go_partner_id,
                'go_groupno' => $go_groupno,
                'from_where' => $from_where,
                'sys_id' => $sys_id,
            );
        } else {
            $res = array(
                'pid' => $pid,
                'go_partner_id' => $go_partner_id,
                'go_groupno' => $go_groupno,
                'from_where' => $from_where,
                'sys_id' => $sys_id,
            );
        }
        return json_encode(array('code'=>'success','msg'=>$res));
    }

    //问卷来源
    public function survey_from_where($str, $bk_info){
        $pc_info = $str ."_".SURVEY_FROM_WHERE_PC;
        $app_info = $str ."_".SURVEY_FROM_WHERE_APP;
        $sms_info = $str ."_".SURVEY_FROM_WHERE_SMS;
        $email_info = $str ."_".SURVEY_FROM_WHERE_EMAIL;
        $res_pc_info = str_md5_code($pc_info.PROJECT_ENCODE_KEY);
        $res_app_info = str_md5_code($app_info.PROJECT_ENCODE_KEY);
        $res_sms_info = str_md5_code($sms_info.PROJECT_ENCODE_KEY);
        $res_email_info = str_md5_code($email_info.PROJECT_ENCODE_KEY);
        $from_where = "";
        switch ($bk_info){
            case $res_pc_info:
                $from_where = SURVEY_FROM_WHERE_PC;
                break;
            case $res_app_info:
                $from_where = SURVEY_FROM_WHERE_APP;
                break;
            case $res_sms_info:
                $from_where = SURVEY_FROM_WHERE_SMS;
                break;
            case $res_email_info:
                $from_where = SURVEY_FROM_WHERE_EMAIL;
                break;
        }
        return $from_where;
    }

    // 更新项目执行表
    public function update_surface($p_table, $data_implement, $where_implement){
        if(!$p_table || !$where_implement){ return false;}
        ####    AMY 2023-05-04 孙负一需求调整：只记录第一次点击时间（click_time）
        if (strpos($p_table, "app_project_implement_") !== false) {//是项目执行表
            if (isset($data_implement['click_time'])) {//存在点击时间记录
                $imp_info = $this->db->where($where_implement)->get($p_table)->row_array();
                if ($imp_info && $imp_info['click_time'] > 0) {
                    unset($data_implement['click_time']);
                }
            }
        }
        ####    AMY 2023-05-04 孙负一需求调整：只记录第一次点击时间（click_time）
        $res_info = $this->db->where($where_implement)->update($p_table,$data_implement);
        if(!$res_info){
            return false;
        }
        return true;
    }

    // 获取外包国家相关信息，通过国家获取默认语言
    public function get_country_default_lang($pid, $partner_id, $group_no)
    {
        $lang = 140;
        $res = array(
            "country" => 0,
            "lang" => $lang,
        );
        if (!$pid || !$partner_id || !$group_no) {return $res;}
        $check_project_partner_data = array(
            'project_id'=>$pid,
            'partner_id'=>$partner_id,
            'groupno'=>$group_no
        );
        $res_project_partner = $this->db->where($check_project_partner_data)->get('app_project_partner')->row_array();
        //通过国家获取国家默认语言
        $country_pro_partner = $res_project_partner['sample_country'];
        if ($country_pro_partner) {
            $country_default_lang = $this->db->query("SELECT * FROM app_sys_setting WHERE `key`='lang_setting' AND country='{$country_pro_partner}'")->row_array();
            $lang = $country_default_lang['lang'] ? $country_default_lang['lang'] : 140;
        }
        $res = array(
            "country" => $country_pro_partner,
            "lang" => $lang,
        );
        return $res;
    }

    //获取项目信息
    public function getProjectInfoById($pid)
    {
        if (!$pid) {return false;}
        $res = $this->db->query("SELECT * FROM app_project WHERE id='{$pid}' LIMIT 1")->row_array();
        return $res ? $res : false;
    }

    //通过配额编号查询配额是否收集满，收集满后，关闭配额
    public function close_project_quota($quota_id,$sign =''){
        if (!$quota_id) {return false;}
        $res_quta_info = $this->db->query("SELECT * FROM app_project_quta WHERE id='{$quota_id}'  LIMIT 1")->row_array();
        if(intval($sign) !== intval(VERIFICATION_QUOTA)){
            if($res_quta_info['quta_status'] == QUOTA_STATUS_CLOSE){//配额已关闭
                $this->get_redirect_info("/bk/rs", "suspend");
//                redirect("/go/thanks?st=suspend");
            }
        }
        if ($res_quta_info['mount'] <= $res_quta_info['now_c_mount']) {//已收集满，关闭配额
            $this->db->query("UPDATE app_project_quta SET quta_status='".QUOTA_STATUS_CLOSE."' WHERE id='{$quota_id}' LIMIT 1");
            return true;
        }
        return false;
    }


    //准备跳转到健康通官网的数据
    public function get_gooddr_log_info($uid, $pid)
    {
        $code = "";
        if ($uid && $pid) {
            $code = $uid."_".$pid."_".substr(md5($uid. "_" . $pid . PROJECT_ENCODE_KEY), 8, 16);
        }
        return $code;
    }


}
