<?php
class Twoauth_elements_api_log_model extends Common_Model {
    protected $table = TABLE_TWOAUTH_API_LOG;
    protected $column_ary = [
        'id', // 编号
        'appid', // 密钥ID
        'request', // 请求参数
        'result', // 返回结果
        'status', //认证状态
        'msg', //'认证提示
        'ins_time', //添加时间
        'clientip', //客户端IP
        'source', //操作来源(操作来源(3-客户API-创蓝),4-客户API-贵州数据宝))
        'isfee', //是否付费(1-付费,2-未付费)
    ]; 
    protected $all_column = '`id`, `appid`, `request`, `result`, `status`, `msg`, `ins_time`, `clientip`, `source` , `isfee`';

    public function __construct()
    {
        parent::__construct();
    }

    public function get_twoauth_elements_api_logs($prarm = array() ,$offset)
    { 
        $this->db->select('*');
        // if(isset($prarm['name'])) {
        //     $this->db->like('name', $prarm['name']);
        //     unset($prarm['name']);
        // }
        $this->db->where($prarm);

        $this->db->order_by('id', 'DESC');
        $this->db->limit(PAGE_NUM, $offset);
        $this->db->from($this->table);
        $list = $this->db->get();
        return $list ? $list->result_array() : [];

    }


    public function get_count_Twoauth_elements_api_logs($prarm)
    {
        // if(isset($prarm['name'])) {
        //     $this->db->like('name', $prarm['name']);
        //     unset($prarm['name']);
        // }
        $this->db->where($prarm);
        $total = $this->db->count_all_results($this->table);
        return isset($total) ? $total : 0;
    }


    public function insert($data) {
        if (!$data) {
            return false;
        }
        $insert_data = [
            "appid" => $data['appid'] ?? "",
            "request" => $data['request'] ?? "",
            "result" => $data['result'] ?? "",
            "status" => $data['status'] ? intval($data['status']) : 1,
            "msg" => $data['msg'] ?? "",
            "clientip" => $data['clientip'] ?? "",
            "source" => $data['source'] ? intval($data['source']) : 0,
            "isfee" => $data['isfee'] ? intval($data['isfee']) : 0,
            "ins_time" =>  time(), 
        ];
        $insert_id = $this->add($insert_data);
        return $insert_id ? $insert_id : false;
    }

   
    public function update($data = [], $edit_id = 0) {
        if (!$data || !$edit_id) {
            return false;
        }
        $up_data = [
            "appid" => $data['appid'] ?? "",
            "request" => $data['request'] ?? "",
            "result" => $data['result'] ?? "",
            "status" => $data['status'] ? intval($data['status']) : 1,
            "msg" => $data['msg'] ?? "",
            "clientip" => $data['clientip'] ?? "",
            "source" => $data['source'] ? intval($data['source']) : 0,
            "isfee" => $data['isfee'] ? intval($data['isfee']) : 0,
        ];
        $upres = $this->db->where(['id' => $edit_id])->update($this->table, $up_data);
        return $upres ? $upres : false;
    }

}