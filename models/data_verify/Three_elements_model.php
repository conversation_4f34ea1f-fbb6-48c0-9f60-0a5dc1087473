<?php
class Three_elements_model extends Common_Model {
    protected $table = TABLE_THREE_ELEMENTS;
    protected $column_ary = [
        'id', // 编号
        'name', // 名称
        'id_num', // 身份证号
        'mobile', // 手机号码
        'status', //认证状态(01-认证一致(收费) 02-认证不一致(收费) 03-认证不确定（收费）   04-认证失败(不收费))
        'error_msg', //认证失败信息
        'add_time', //添加时间
        'source', //操作来源(1-赛小欧自动认证,2-赛小欧人工认证,3-项目--进入--邀请管理,4-批量执行数据‘idcode_three_211115’,6-认证管理)
    ];
    protected $all_column = '`id`, `name`, `id_num`, `mobile`, `status`, `error_msg`, `add_time`, `source`';

    public function __construct()
    {
        parent::__construct();
    }

    public function get_three_elements($prarm = array() ,$offset)
    { 
        $this->db->select('*');

        // if(isset($prarm['name'])) {
        //     $this->db->like('name', $prarm['name']);
        //     unset($prarm['name']);
        // }
        $this->db->where($prarm);
        $this->db->order_by('id', 'DESC');
        $this->db->limit(PAGE_NUM, $offset);
        $this->db->from($this->table);
        $list = $this->db->get();
        return $list ? $list->result_array() : [];

    }


    public function get_count_three_elements($prarm)
    {  
        // if(isset($prarm['name'])) {
        //     $this->db->like('name', $prarm['name']);
        //     unset($prarm['name']);
        // }
        
        $this->db->where($prarm);
        $total = $this->db->count_all_results($this->table);
        return isset($total) ? $total : 0;
    }

    public function row($id)
    {
        if(!$id) {
            return false;
        }
        
        $query = $this->db->get_where($this->table, array('id' => $id));
        $res = $query->row_array();
        return isset($res) ? $res : [];
    }

    public function insert($data) {
        if (!$data) {
            return false;
        }
        $insert_data = [
            "name" => $data['name'] ?? "",
            "id_num" => $data['id_num'] ? trim($data['id_num']) : "",
            "mobile" => $data['mobile'] ? trim($data['mobile']) : "",
            "status" => $data['status'] ?? "",
            "error_msg" => $data['error_msg'] ?? "",
            "status" => $data['status'] ? intval($data['status']) : 1,
            "source" => $data['source'] ? intval($data['source']) : 0,
            "add_time" => time(), 
        ];
        $insert_id = $this->add($insert_data);
        return $insert_id ? $insert_id : false;
    }

   
    public function update($data = [], $id = 0) {
        if (!$data || !$id) {
            return false;
        }
        $updata = $this->row($id);

        $up_data = [
            "name" => $data['name'] ? trim($data['name']) : $updata['name'],
            "id_num" => $data['id_num'] ? trim($data['id_num']) : $updata['id_num'],
            "mobile" => $data['mobile'] ? trim($data['mobile']) : $updata['mobile'],
            "error_msg" => $data['error_msg'] ? trim($data['error_msg']) :  $updata['error_msg'],
            "status" => $data['status'] ? intval($data['status']) : $updata['status'],
            "source" => $data['source'] ? intval($data['source']) : $updata['source'],     
        ];
        $upres = $this->db->where(['id' => $id])->update($this->table, $up_data);
        return $upres ? $upres : false;
    }

}