<?php
class Twoauth_elements_log_model extends Common_Model {
    protected $table = TABLE_TWOAUTH_ELEMENTS_LOG;
    protected $column_ary = [
        'id', // 编号
        'return_time', // 请求时间
        'result', // 请求结果
        'source', //操作来源(1-客户数据,2-认证管理,3-客户API-创蓝),4-客户API-贵州数据宝)
    ];
    protected $all_column = '`id`, `return_time`, `result`, `source`';

    public function __construct()
    {
        parent::__construct();
    }

    public function get_twoauth_elements_log($prarm = array() , $offset = 0, $pageSize = -1)
    { 
        
        $sql ='select * from '.$this->table . ' where 1';

        if(isset($prarm['name'])) {
            $sql .= " and json_extract(result, '$.request.name') = '" . $prarm['name'] . "'";
            unset($prarm['name']);
        }
        if(isset($prarm['mobile'])) {
            $sql .= " and json_extract(result, '$.request.mobile') = '" . $prarm['mobile']."'";
            unset($prarm['mobile']);
        }
        $sql .= ' order by id desc ';
        if($pageSize > 0) {
            $sql .= ' limit ' . $offset .' , '.$pageSize;
        }
        
        //print_r($sql);exit;
        $res = $this->db->query($sql);
        $result = $res->result_array();
        foreach($result  as &$val) {
            $val['result'] = json_decode($val['result'], true);
        }
        return $result ? $result : [];
    }


    public function get_count_twoauth_elements_log($prarm = array())
    {
         
        $sql ='select count(*) as count from '.$this->table . ' where 1';

        if(isset($prarm['name'])) {
            $sql .= " and json_extract(result, '$.request.name') = '" . $prarm['name'] . "'";
            unset($prarm['name']);
        }
        if(isset($prarm['mobile'])) {
            $sql .= " and json_extract(result, '$.request.mobile') = '" . $prarm['mobile']."'";
            unset($prarm['mobile']);
        }
        $list = $this->db->query($sql);
        $total = $list->row_array();
        return isset($total) ? $total : 0;
    }



    public function insert($data) {
        if (!$data) {
            return false;
        }
        $insert_data = [
            "return_time" => $data['return_time'] ?? "",
            "result" => $data['result'] ?? "",
            "source" => $data['source'] ? intval($data['source']) : 0,
        ];
        $insert_id = $this->add($insert_data);
        return $insert_id ? $insert_id : false;
    }

   
    public function update($data = [], $edit_id = 0) {
        if (!$data || !$edit_id) {
            return false;
        }
        $up_data = [
            "return_time" => $data['return_time'] ?? "",
            "result" => $data['result'] ?? "",
            "source" => $data['source'] ? intval($data['source']) : 0,
        ];
        $upres = $this->db->where(['id' => $edit_id])->update($this->table, $up_data);
        return $upres ? $upres : false;
    }

}