<?php
//二要素数据复核表
class Twoauth_elements_recheck_model extends Common_Model
{
    protected $table = TABLE_TWOAUTH_ELEMENTS_RECHECK;
    protected $column_ary = [
        'id', // 编号
        'name', // 名称
        'mobile', // 手机号码
        'img', // 凭证图片
        'sys_twoauth_elements_log_id', // 二要素请求日志表ID
        'appid', // appid
        'check_status', //认证状态(01-认证一致(收费) 02-认证不一致(收费) 03-认证不确定（收费）   04-认证失败(不收费))
        'old_check_status', //认证失败信息
        'st',
        'add_time',
        'up_time',


    ];
    protected $all_column = '`id`, `name`, `mobile`, `img`, `sys_twoauth_elements_log_id`, `appid`, `check_status` , `old_check_status` , `st` , `add_time` , `up_time`';

    public function __construct()
    {
        parent::__construct();
    }

    public function get_twoauth_elements_recheck($prarm = array(), $offset = -1)
    {
        $this->db->select('*');
        //print_r($prarms);exit;
        if (isset($prarm['data_start_time'])) {
            $this->db->where('add_time > ', $prarm['data_start_time']);
            unset($prarm['data_start_time']);
        }
        if (isset($prarm['data_end_time'])) {
            $this->db->where('add_time  < ', $prarm['data_end_time']);
            unset($prarm['data_end_time']);
        }

        $this->db->where($prarm);
        $this->db->order_by('id', 'DESC');
        if ($offset >= 0) {
            $this->db->limit(PAGE_NUM, $offset);
        }

        $this->db->from($this->table);
        $list = $this->db->get();
        return $list ? $list->result_array() : [];
    }

    public function get_recheck_group($appid)
    {
        $res = $this->db->query('select FROM_UNIXTIME(add_time,"%Y-%m-%d") as days, count(1) as num from sys_twoauth_elements_recheck where  appid = "' . $appid . '" group by days order by add_time desc limit 20')->result_array();
        return $res ? $res : [];
    }



    public function check_exit($mobiles, $appid)
    {
        $res = $this->db->query('select mobile, name from sys_twoauth_elements_recheck where appid = "' . $appid . '" and mobile in ("' . implode('","', $mobiles) . '")')->result_array();
        return $res ? $res : [];
    }

    public function get_count_twoauth_elements_recheck($prarm = array())
    {

        if (isset($prarm['data_start_time'])) {
            $this->db->where('add_time > ', $prarm['data_start_time']);
            unset($prarm['data_start_time']);
        }
        if (isset($prarm['data_end_time'])) {
            $this->db->where('add_time  < ', $prarm['data_end_time']);
            unset($prarm['data_end_time']);
        }

        $this->db->where($prarm);
        $total = $this->db->count_all_results($this->table);
        return isset($total) ? $total : 0;
    }

    public function row($id)
    {
        if (!$id) {
            return false;
        }

        $query = $this->db->get_where($this->table, array('id' => $id));
        $res = $query->row_array();
        return isset($res) ? $res : [];
    }


    public function insert($data)
    {
        if (!$data) {
            return false;
        }
        $insert_data = [
            "name" => $data['name'] ?? "",
            "mobile" => $data['mobile'] ?? "",
            "img" => $data['img'] ?? "",
            "sys_twoauth_elements_log_id" => $data['sys_twoauth_elements_log_id'] ?? "",
            "appid" => $data['appid'] ?? "",
            "check_status" => isset($data['check_status']) ? intval($data['check_status']) : 0,
            "old_check_status" => isset($data['old_check_status']) ? intval($data['old_check_status']) : 0,
            "st" => $data['st'] ? intval($data['st']) : 0,
            "add_time" => time(),
        ];
        $insert_id = $this->add($insert_data);
        return $insert_id ? $insert_id : false;
    }


    public function update($data = [], $id = 0)
    {
        if (!$data || !$id) {
            return false;
        }

        $upres = $this->db->update($this->table, $data, ["id" => $id]);
        return $upres ? $upres : false;
    }


    public function insert_batch($add_data)
    {
        if (empty($add_data)) {
            return false;
        }

        $res_add = $this->db->insert_batch($this->table, $add_data);
        if (!$res_add) {
            return false;
        }
        return $res_add ? $res_add : false;
    }

    public function update_batch($res_edit_data)
    {
        if (empty($res_edit_data)) {
            return false;
        }

        $res_edit = $this->db->update_batch($this->table, $res_edit_data, "id");
        if (!$res_edit) {
            return false;
        }
        return $res_edit ? $res_edit : false;
    }
}
