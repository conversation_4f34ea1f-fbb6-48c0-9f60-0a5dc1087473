<?php
//数据验证业务登记表
class Data_vf_model extends Common_Model {
    protected $table = TABLE_DATA_VF;
    protected $column_ary = [
        'id', // 编号
        'po', // 名称
        'enterprise_id', 
        'client_sales_id', 
        'client_it_id', 
        'jkt_sales_id', 
        'jkt_it_id', 
        'service_third_party',
        'summary',
        'api_url',
        'app_id',
        'app_key',
        'ip_white_list',
        'start_time',
        'end_time',
        'free_test_num',
        'add_uid',
        'add_time',
        'up_time',
        'status',
        'is_delete',
        'rec_act',
        'rec_pwd',

    ];
    //protected $all_column = '`id`, `po`, `enterprise_id`, `client_sales_id`, `client_it_id`, `jkt_sales_id`, `jkt_it_id` , `service_third_party` , `summary` , `api_url` , `app_id`' ;

    public function __construct()
    {
        parent::__construct();
    }


    public function get_data_vf($prarm = array() ,$offset = -1)
    { 
        $this->db->select('*');

        $this->db->where($prarm);

        $this->db->order_by('id', 'DESC');
        if($offset > 0) {
            $this->db->limit(PAGE_NUM, $offset);
        }
        
        $this->db->from($this->table);
        $list = $this->db->get();
        return $list ? $list->result_array() : [];

    }


    public function get_count_data_vf($prarm = array())
    {
        $this->db->where($prarm);
        $total = $this->db->count_all_results($this->table);
        return isset($total) ? $total : 0;
    }
     
    public function row($id)
    {
        if(!$id) {
            return false;
        }
        
        $query = $this->db->get_where($this->table, array('id' => $id));
        $res = $query->row_array();
        return isset($res) ? $res : [];
    }


     public function insert($data) {
        if (!$data) {
            return false;
        }
        $insert_data = [
            "po" => $data['po'] ?? "",
            "enterprise_id" => $data['enterprise_id'] ?? 0,
            "client_sales_id" => $data['client_sales_id'] ?? 0,
            "client_it_id" => $data['client_it_id'] ?? "",
            "jkt_sales_id" => $data['jkt_sales_id'] ?? "",
            "jkt_it_id" => $data['jkt_it_id'] ?? "",
            "service_type" => $data['service_type'] ?? "",
            "service_third_party" => $data['service_third_party'] ?? "",
            "summary" => $data['summary'] ?? "",
            "api_url" => $data['api_url'] ?? "",
            "app_id" => $data['app_id'] ?? "",
            "app_key" => $data['app_key'] ?? "",
            "ip_white_list" => $data['ip_white_list'] ?? "",
            "start_time" => time(),
            "free_test_num" => $data['free_test_num'] ?? "",
            "add_uid" => $data['add_uid'] ?? "",
            "add_time" => $data['add_time'] ?? "",
            "up_time" => $data['up_time'] ?? "",
            "status" => $data['status'] ?? "",
            "is_delete" => $data['is_delete'] ?? "",


            "rec_act" => isset($data['rec_act']) ? trim($data['rec_act']) : 0,
            "rec_pwd" => isset($data['rec_pwd']) ? intval($data['rec_pwd']) : 0,
        ];
        $insert_id = $this->add($insert_data);
        return $insert_id ? $insert_id : false;
    }

   
    public function update($data = [], $id = 0) {
        if (!$data || !$id) {
            return false;
        }

        $upres = $this->db->update($this->table, $data, ["id" => $id]);
        return $upres ? $upres : false;
    }


    public function insert_batch($add_data) {
        if(empty($add_data)) {
            return false;
        }

        $res_add = $this->db->insert_batch($this->table, $add_data);
        if (!$res_add) {
            return false;
        }
        return $res_add ? $res_add : false;
    }

    public function update_batch($res_edit_data) {
        if(empty($res_edit_data)) {
            return false;
        }

        $res_edit = $this->db->update_batch($this->table, $res_edit_data, "id");
        if (!$res_edit) {
            return false;
        }
        return $res_edit ? $res_edit : false;
    }

}