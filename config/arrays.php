<?php  if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
| -------------------------------------------------------------------
| ARRAYS
| -------------------------------------------------------------------
|	全局数组函数
|
| Please see user guide for more info:
| http://codeigniter.org.cn/user_guide/helpers/array_helper.html
|
*/

$config = array(
    'point_change_code' => array(
        '101' => _('付费调查奖励积分'),
        '103' => _('付费调查调整积分'),
        '104' => _('付费调查提成积分'),
        '201' => _('推荐会员奖励积分'),
        '301' => _('注册奖励积分'),
        '401' => _('兑换抵扣积分'),
        '402' => _('兑换拒绝'),
        '403' => _('删除兑换记录返还积分'),
        '404' => _('拒绝兑换后重置为通过'),
        '405' => _('拒绝兑换后重置为暂缓'),
        '406' => _('支付成功'),
        '407' => _('支付失败'),
        '501' => _('会员发表观点奖励积分'),
        '502' => _('幸运之星奖励积分'),
        '503' => _('日登录奖励积分'),
        '601' => _('快速调查奖励积分'),
        '701' => _('额外奖励积分'),
        '702' => _('额外处罚积分'),
        '801' => _('分享奖励积分'),
        '901' => _('月度推荐的会员参与过调查人数较多者奖励积分'),
        '99999' => _('老系统剩余积分'),
    ),

    'arr_pro_type' => array(
        1=>'在线问卷',
        2=>'电话预约',
        3=>'面访预约',
        4=>'座谈预约',
        5=>'短信推广',
        6=>'邮件推广',
        7=>'APP推广',
        8=>'Re-contact',
        9=>'患者招募',
        10=>'专家招募',
    ),

    "project_status" => array(
        "3"		=>	"进行",
        "2"		=>	"暂停",
        "1"		=>	"准备",
        "4"		=>	"结束",
    ),


);



/* End of file arrays.php */
/* Location: ./application/testapp/config/arrays.php */
