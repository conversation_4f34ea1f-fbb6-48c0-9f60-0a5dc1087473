<?php  if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
| -------------------------------------------------------------------
| DEFINES
| -------------------------------------------------------------------
|	全局常量
|
| Please see user guide for more info:
|
*/
$config = array();


define('TEMPLATE_DIR', 'management');
define('TEMPLATE_DIR_NEW', 'new_manage');
//define('PAGE_NUM', 10);                 //每页数量
//define('SMS_RESEND_TIME', 60);          //注册每次提交的最小时间间隔
//define('SMS_VCODE_ACTIVE_TIME', 60000);   //验证码的有效时间
define('PIC_BLOGS_LEAST_NUM', 1);       //朋友圈最少图片数量
define('PIC_BLOGS_MOST_NUM', 10);        //朋友圈最多图片数量

//define('DRSAY_WEB', 'http://www.drsay.cn');
define('ADMIN_URL', 'http://adminlocal.drsay.cn');
define('SITE_URL', 'https://api.drsay.cn');
define('USER_IMG_URL', 'https://api.drsay.cn');
//define('USER_IMG_URL_NOTS', 'http://api.drsay.cn');

//define('SC_SMS_USER', 'cati_sms');
//define('SC_SMS_KEY', '44UPCR8NazPrmMXRH7TjZ3nwCMNJAQjC');
//define('SC_API_SINGLE', 'http://www.sendcloud.net/smsapi/send');
//define('SC_API_BATCH', 'http://www.sendcloud.net/smsapi/sendx');
//define('SMS_INVITE_TEMPLETE', '6595');
//
//define('SMS_ACTIVE_TIME',5); // 短信有效时间 （分钟）
//define('SMS_REG_TEMPLATE',6652); // 注册 验证码%VCODE% 有效时间%VTIME%
//define('SMS_UP_PWD_TEMPLATE',6715); // 修改密码 验证码%VCODE% 有效时间%VTIME%
//define('SMS_GET_PWD_TEMPLATE',6714); // 找回密码 验证码%VCODE% 有效时间%VTIME%
//define('SMS_UP_MOB_TEMPLATE',6716); // 修改手机 验证码%VCODE% 有效时间%VTIME%
//define('SMS_BIND_MOB_TEMPLATE',6717); // 绑定手机 验证码%VCODE% 有效时间%VTIME%
//define('SMS_COMM_MOB_TEMPLATE',7540); // 常规验证码 验证码%VCODE% 有效时间%VTIME%


////手机话费充值接口地址
//define('RECHARGE_URL', 'http://op.juhe.cn/ofpay/mobile/');
////手机话费充值的openid
//define('OPENID', 'JHf42ce173e4346033677ba463cc5a73f5');
//define('RECHARGE_KEY', 'd6d3146d0672fb8812edecfd5c9e9956');

////腾讯云的app_id
//define('WX_APP_ID', 1400038476);

//// 极光推送
//define('JPUSH_KEY', 'b942b8eaf02761572127c5cf');
//define('JPUSH_MS', '3a2bfc8943bf7e92b5a97109');
//define('JPUSH_URL', 'https://api.jpush.cn/v3/push');

/**-------REDIS START-------*/
define('REDIS_SERVER', '127.0.0.1');
define('REDIS_PORT', 6379);
define('REDIS_TIME_OUT', 86400);
define('REDIS_PCONNECT', true);
define('REDIS_LOCK_NAMESPACE', 'API_REDIS_LOCK_');
/**-------REDIS END-------*/



//某些限制开发初期要去掉，比如验证码
define('IS_DEBUG', true);

//define('PROJECT_ENCODE_KEY', 'ritadrsay.cn');

#### 短信发送配置，对接公司的统一短信发送平台（负责人：miles）：给www.drsay.cn平台开设的发送账号 BEGIN ####
define('IDR_SMS_API', 'https://sms.idr.cn/sendsmsv2'); // 短信发送接口
define('IDR_SMS_APP_ID', 'RPzROamT'); // 短信发送APP_ID
define('IDR_SMS_APP_SECRET', 'UKAbYqiXKNAjwf1mBFDoZf0V3jj1ONqv'); // 短信发送APP_SECRET
#### 短信发送配置，对接公司的统一短信发送平台（负责人：miles）：给www.drsay.cn平台开设的发送账号 END ####

/* End of file defines.php */
/* Location: ./application/config/defines.php */
