<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

/**
 * 后台公共控制器（网站配置及权限验证）
 * <AUTHOR>
 */
class MY_Controller extends CI_Controller {
    public $quta_setting_filter_param;
    public $ci;
    public $lang;
    public $setting_cond_field_doc;
    public $lang_version;
    public function __construct() {
        parent::__construct();

        //配额抽样中，需要过滤的抽样条件
        $this->quta_setting_filter_param = array(
            "name",
            "mobile",
            "email",
            "home_addr",
            "pass_card",
            "birthday",
        );

        $this->ci = & get_instance();
        //获取当前最新的语言版本
        $this->lang_version = get_lang_version();

        //配额抽样字段定义
        $this->setting_cond_field_doc = array(
            'country',
            'province',
            'city',
            'district',
            'unit_name',
            'name',
            'mobile',
            'email',
            'address as home_addr',
            'gender',
            'office_phone',
            'top_education',
            'top_degree',
            'doctor_profession', // 医生职业类型
            'practice_sort',
            'unit_level',
            'grade',
            'job_title',
            'position',
            'b.department',
            'a.id as member_uid',
            'unit_id',
        );
    }

    /*
     * [check_country 检查语言与国家]
     * @param  Int $lang [语言ID]
     * @param  Int $country [国家ID]
     * @return Null
     */
    public function check_lang_country($lang, $country)
    {
        if (empty($lang) || !is_numeric($lang) || empty($country) || !is_numeric($country)) {
            return false;
        }
        $this->ci->load->model('dictionary_model');
        $l = $this->ci->dictionary_model->checkLang($lang);
        $c = $this->ci->dictionary_model->checkCountry($country);
        if ($l&&$c) {
            $this->ci->load->model('setting_model');
            $cl_set = $this->ci->setting_model->getSettingByCondition(' AND `key`="lang_setting" and country='.$country);
            if ($cl_set == false) {
                return false;
            }
            $ls_ary = explode(',', $cl_set[0]['val']);
            return in_array($lang, $ls_ary);
        } else {
            return false;
        }
    }

    /**
     * [check_login_token 检查登录令牌]
     * @param  Int $uid                  [用户ID]
     * @param  String $login_token       [登录令牌]
     * @return Null
     */
    function check_login_token($uid, $login_token)
    {
        if (empty($uid) || !is_numeric($uid) || empty($login_token)) {
            return false;
        }
        $this->CI = & get_instance();
        $q = $this->CI->db->query("SELECT count(`id`) AS num FROM app_member_login_token WHERE id='{$uid}' and login_token='{$login_token}'");
        $res = $q->result_array();
        return (int)$res[0]['num'] > 0 ? true : false;
    }

    /**
     * [get_filter_word 获取过滤词列表]
     * @return [array||bollean]
     */
    function get_filter_word()
    {
        $filter_list = $this->ci->db->query("SELECT val,rep FROM app_filtrate")->result_array();
        if (empty($filter_list)) {
            return false;
        }
        $filter_ary = array();
        foreach ($filter_list as $row) {
            $filter_ary['var'][] = '/'.$row['val'].'/'; // 准备替换的字符数组
            $filter_ary['rep'][] = $row['rep']; // 替换后的字符
        }
        return $filter_ary;
    }
}
