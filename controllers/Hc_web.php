<?php
/**
 * User: annie
 * Date: 2020-02-17
 */
class Hc_web extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        // error_reporting(-1);
        // ini_set('display_errors', 1);
    }

    public function hos_url(){
        $get_info = $this->input->get();
        $where = "";
        $search_info = "";
        if ($get_info) {
            $search_info = isset($get_info['search_info']) ? $get_info['search_info'] : "";
            if ($search_info) {
                $where = " and (class like '%".$search_info."%' or url_name like '%".$search_info."%')";
            }
        }
        $info = $this->db->query("SELECT class, url_name, url FROM hc_web WHERE status = 1" . $where)->result_array();
        $hos = [];
        if ($info) {
            foreach ($info as $key => $v) {
                $hos[$v['class']][] = [
                    'url_name' => $v['url_name'],
                    'url' => $v['url']
                ];
            }
        }
        $data = array(
            'hos' => $hos,
            'search_info' => $search_info,
        );
        $this->load->view('/hos/hos_url', $data);
    }
}
