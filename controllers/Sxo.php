<?php
/**
 * 赛小欧
 * Created by PhpStorm.
 * User: AMY
 * Date: 2020-10-12
 * Time: 10:36
 */

use EasyWeChat\Factory;

class Sxo extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('survey_model');
    }
    
    /**
     * 活动说明
     */
    public function activity(){
        $sid = $this->input->get('code',true);
        if(sxo_decrypt($sid)==false){
            redirect('/');
        }
        $data = ['sid'=>$sid];
        
        // 流程案例
        if($sid=='cac4bfe844486f111188831945638429'){
            $this->load->view("/sxo/activity_1",$data);
        }else{
            $this->load->view("/sxo/activity",$data);
        }
    }
    
    /**
     * 基础信息确认
     */
    public function affirm(){
        $sid = $this->input->get('code',true);
        $sid_decrypt = sxo_decrypt($sid);
        if($sid_decrypt==false){
            redirect('/');
        }
        //查询基础信息
        $mb_sxo = $this->db->select('id,unit_name,dr_name,dr_department,dr_mobile')
                ->where(['sxo_status !='=>6,'has_sent'=>3,'id'=>$sid_decrypt])->get('mb_sxo')->row_array();
        if(!empty($mb_sxo)){
            $invite_info = $this->db->select('id,is_dep_patient,internet_platform')
                ->where(['is_del'=>1,'dr_mobile'=>$mb_sxo['dr_mobile']])->get('mb_sxo_invite_info')->row_array();
            if(!empty($invite_info)){
                $mb_sxo['is_dep_patient'] = isset($invite_info['is_dep_patient'])?$invite_info['is_dep_patient']:0;
                $mb_sxo['internet_platform'] = isset($invite_info['internet_platform'])?$invite_info['internet_platform']:'';
                $mb_sxo['invite_info_id'] = isset($invite_info['id'])?$invite_info['id']:'';
            }
        }
        
        $data = ['sid'=>$sid,'info'=>$mb_sxo];
        $this->load->view("/sxo/affirm",$data);
    }
    
    public function affirmsave(){
        $data = $this->input->post(['isDepPatient','internetPlatform','id','code'],true);
        $sid_decrypt = sxo_decrypt($data['code']);
        if($sid_decrypt==false){
            _back_msg("errors", '未通过认证！', '/');
        }
        
        if(empty($data['isDepPatient'])){
            _back_msg("error", '请选择Q1！');
        }
        if(empty($data['internetPlatform'])){
            _back_msg("error", '请输入Q2！');
        }
        
        //查询基础信息
        $mb_sxo = $this->db->select('id,dr_mobile')
                ->where(['sxo_status !='=>6,'has_sent'=>3,'id'=>$sid_decrypt])->get('mb_sxo')->row_array();
        $dr_mobile = $mb_sxo['dr_mobile']?$mb_sxo['dr_mobile']:'';
        
        if(!empty($data['id'])){
            $exist = $this->db->select('id')->where(['is_del'=>1,'id'=>$data['id']])->get('mb_sxo_invite_info',1)->row_array();
            if(!empty($exist)){
                $updata = ['is_dep_patient'=>$data['isDepPatient'],'internet_platform'=>$data['internetPlatform'],'up_time'=> time()];
                $upres = $this->db->where(['id'=>$data['id']])->update('mb_sxo_invite_info',$updata);
                if(!$upres){
                    _back_msg("error", '请求异常，请稍后重试！');
                }
            }else{
                $adddata = [
                    'status'=>4,'dr_mobile'=>$dr_mobile,'add_time'=> time(),
                    'is_dep_patient'=>$data['isDepPatient'],'internet_platform'=>$data['internetPlatform']
                ];
                $addres = $this->db->insert('mb_sxo_invite_info',$adddata);
                if(!$addres){
                    _back_msg("error", '请求异常，请稍后重试！');
                }
            }
        }else{
            $adddata = [
                'status'=>4,'dr_mobile'=>$dr_mobile,'add_time'=> time(),
                'is_dep_patient'=>$data['isDepPatient'],'internet_platform'=>$data['internetPlatform']
            ];
            $addres = $this->db->insert('mb_sxo_invite_info',$adddata);
            if(!$addres){
                _back_msg("error", '请求异常，请稍后重试！');
            }
        }
        _back_msg("success", "请求成功");
    }
    
    public function first()
    {
        $sid = $this->input->get('code',true);
        $sid_decrypt = sxo_decrypt($sid);
        if($sid_decrypt==false){
            redirect('/');
        }
        //查询基础信息
        $mb_sxo = $this->db->select('id,distribution_uid,dr_mobile')
                ->where(['sxo_status !='=>6,'has_sent'=>3,'id'=>$sid_decrypt])->get('mb_sxo')->row_array();
        $distribution_uid = isset($mb_sxo['distribution_uid'])?$mb_sxo['distribution_uid']:'';
        $dr_mobile = isset($mb_sxo['dr_mobile'])?$mb_sxo['dr_mobile']:'';
        
        if(!empty($dr_mobile)){
            //记录访问状态
            $invite_info = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$dr_mobile])->get('mb_sxo_invite_info',1)->row_array();
            $info_id = isset($invite_info['id'])?$invite_info['id']:0;
            //查询报告结果
            $exist_res = $this->db->select('id')->where(['invite_info_id'=>$info_id])->get('mb_sxo_invite_info_result',1)->row_array();
            //访问状态
            if(!empty($exist_res)){
                $upres = $this->db->where(['id'=>$exist_res['id'],'status'=>0])->update('mb_sxo_invite_info_result',['status'=>1]);
                if(!$upres){
                    redirect('/');
                }
            }else{
                //记录查询报名成功记录
                $addres = $this->db->insert('mb_sxo_invite_info_result',['invite_info_id'=>$info_id,'status'=>1,'add_time'=> time()]);
                if(!$addres){
                    redirect('/');
                }
            }
        }
        
        //是否存在访问员二维码
        $exist_code = $this->db->select('code')->where(['visitor_id'=>$distribution_uid])
                ->get('mb_sxo_visitor_code')->row_array();
        if(empty($exist_code)){
            redirect('/');
        }
        $data = ['sid'=>$sid,'code_img'=>(isset($exist_code['code'])?$exist_code['code']:'')];
        $this->load->view("/sxo/first",$data);
    }

    public function second()
    {
        $sid = $this->input->get('code',true);
        $sid_decrypt = sxo_decrypt($sid);
        if($sid_decrypt==false){
            redirect('/');
        }
        $sxo_task1 = $this->db->select('a.id,a.dr_mobile,b.local_qr_code')->from('mb_sxo a')->join('mb_sxo_q_code b', 'a.id=b.sxo_id', 'left')
                ->where(['a.sxo_status !='=>6,'a.has_sent'=>3,'a.id'=>$sid_decrypt])->get()->row_array();
        if(empty($sxo_task1)){
            redirect('/');
        }
        $task1_img = isset($sxo_task1['local_qr_code'])?$sxo_task1['local_qr_code']:'';
        
        $dr_mobile = isset($sxo_task1['dr_mobile'])?$sxo_task1['dr_mobile']:'';
        if(!empty($dr_mobile)){
            //记录访问状态
            $invite_info = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$dr_mobile])->get('mb_sxo_invite_info',1)->row_array();
            $info_id = isset($invite_info['id'])?$invite_info['id']:0;
            //查询报告结果
            $exist_res = $this->db->select('id')->where(['invite_info_id'=>$info_id])->get('mb_sxo_invite_info_result',1)->row_array();
            //访问状态
            if(!empty($exist_res)){
                $upres = $this->db->where(['id'=>$exist_res['id'],'status'=>1])->update('mb_sxo_invite_info_result',['status'=>2]);
                if(!$upres){
                    redirect('/');
                }
            }
        }
        
        $data = ['sid'=>$sid,'code_img'=>$task1_img];
        $this->load->view("/sxo/second",$data);
    }
    
    public function qfads(){
        $data = [];
        $authen = $this->input->get('authen',true);
        if($authen){
            $arr_code = explode("DRSAY", $authen);
            if (count($arr_code) != 3) {
                redirect("/");
            }
            $sid = $arr_code[0];    //mb_sxo-id
            $vid = $arr_code[1];    //访问员ID
            $authen_code = $arr_code[2];    //认证code
            $authen_scene = $sid . '_' . $vid;
            $authen_code_new = substr(md5($authen_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($authen_code_new !== $authen_code) {//解密值不等
                redirect("/");
            }
            //查询记录是否存在-是否确认参与
            $exist_sxo = $this->db->select('id,participation')->where('id',$sid)->get('mb_sxo',1)->row_array();
            if(empty($exist_sxo)){
                redirect("/");
            }
            //如果初次打开-设置为participation-2-参与
            if($exist_sxo['participation']==3){
                $upres = $this->db->where(['id'=>$sid])->update('mb_sxo',['participation'=>2]);
                if(!$upres){
                    redirect("/");
                }
            }
            //是否存在访问员二维码
            $exist_code = $this->db->select('id,code')->where('visitor_id',$vid)->get('mb_sxo_visitor_code',1)->row_array();
            if(empty($exist_code)){
                redirect("/");
            }
            $data['authen'] = $authen;
            $data['participation'] = $exist_sxo['participation'];
            $data['code'] = $exist_code['code'];
        }
        $this->load->view("/sxo/to_wx",$data);
    }
    
    /**
     * 群发-健康通微信专员
     */
    public function qfad(){
        $data = [];
        $authen = $this->input->get('authen',true);
        if($authen){
            $arr_code = explode("DRSAY", $authen);
            if (count($arr_code) != 3) {
                redirect("/");
            }
            $sid = $arr_code[0];    //mb_sxo-id
            $vid = $arr_code[1];    //访问员ID
            $authen_code = $arr_code[2];    //认证code
            $authen_scene = $sid . '_' . $vid;
            $authen_code_new = substr(md5($authen_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($authen_code_new !== $authen_code) {//解密值不等
                redirect("/");
            }
            $data['authen'] = $authen;
            $this->load->view("/sxo/to_wxs",$data);
        }else{
            $vid = $this->uri->segment(3);
            if($vid){
                $vcode = $this->db->select('code')->where('id',$vid)->get('mb_sxo_visitor_code')->row_array();
                if(empty($vcode)){
                    redirect("/");
                }
                $data['code'] = $vcode['code'];
            }else{
                redirect("/");
            }
            $this->load->view("/sxo/to_wx",$data);
        }
    }
    
    /**
     * 临时活动页
     */
    public function test_ad(){
        $this->load->view("/sxo/to_wx");
    }
    
    public function participation(){
        $authen = $this->input->post('authen',true);
        if($authen){
            $arr_code = explode("DRSAY", $authen);
            if (count($arr_code) != 3) {
                _back_msg("error", "认证参数错误！");
            }
            $sid = $arr_code[0];    //mb_sxo-id
            $vid = $arr_code[1];    //访问员ID
            $authen_code = $arr_code[2];    //认证code
            $authen_scene = $sid . '_' . $vid;
            $authen_code_new = substr(md5($authen_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($authen_code_new !== $authen_code) {//解密值不等
                _back_msg("error", "认证参数解密值错误！");
            }
            //查询记录是否存在-是否确认参与
            $exist_sxo = $this->db->select('id,participation')->where('id',$sid)->get('mb_sxo',1)->row();
            if(empty($exist_sxo)){
                _back_msg("error", "未存在参与数据！");
            }
            //是否存在访问员二维码
            $exist_code = $this->db->select('id,code')->where('visitor_id',$vid)->get('mb_sxo_visitor_code',1)->row();
            if(empty($exist_code)){
                _back_msg("error", "未存在专员微信！");
            }
            $upres = $this->db->where(['id'=>$sid])->update('mb_sxo',['participation'=>2]);
            if($upres){
                _back_msg("success", "参与成功");
            }
            _back_msg("error", "未知错误！");
        }
        _back_msg("error", "未存在认证参数！");
    }

    public function third()
    {
        $sid = $this->input->get('code',true);
        $sid_decrypt = sxo_decrypt($sid);
        if($sid_decrypt==false){
            redirect('/');
        }
        
        $sxo_task2 = $this->db->select('id,dr_mobile,task_two_qrcode')->where(['sxo_status !='=>6,'has_sent'=>3,'id'=>$sid_decrypt])
                ->get('mb_sxo',1)->row_array();
        if(empty($sxo_task2)){
            redirect('/');
        }
        
        $dr_mobile = isset($sxo_task2['dr_mobile'])?$sxo_task2['dr_mobile']:'';
        if(!empty($dr_mobile)){
            //记录访问状态
            $invite_info = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$dr_mobile])->get('mb_sxo_invite_info',1)->row_array();
            $info_id = isset($invite_info['id'])?$invite_info['id']:0;
            //查询报告结果
            $exist_res = $this->db->select('id')->where(['invite_info_id'=>$info_id])->get('mb_sxo_invite_info_result',1)->row_array();
            //访问状态
            if(!empty($exist_res)){
                $upres = $this->db->where(['id'=>$exist_res['id'],'status'=>2])->update('mb_sxo_invite_info_result',['status'=>3]);
                if(!$upres){
                    redirect('/');
                }
            }
        }
        
        $sxo_task2 = isset($sxo_task2['task_two_qrcode'])?json_decode($sxo_task2['task_two_qrcode'],true):'';
        $task2_img = isset($sxo_task2[0])?$sxo_task2[0]:'';
        $data = ['sid'=>$sid,'code_img'=>$task2_img];
        
        $this->load->view("/sxo/third_two",$data);
    }
    
    public function four(){
        $sid = $this->input->get('code',true);
        $sid_decrypt = sxo_decrypt($sid);
        if($sid_decrypt==false){
            redirect('/');
        }
        $sxo_task3 = $this->db->select('id,dr_mobile,task_three_qrcode')->where(['sxo_status !='=>6,'has_sent'=>3,'id'=>$sid_decrypt])
                ->get('mb_sxo',1)->row_array();
        if(empty($sxo_task3)){
            redirect('/');
        }
        
        $dr_mobile = isset($sxo_task3['dr_mobile'])?$sxo_task3['dr_mobile']:'';
        if(!empty($dr_mobile)){
            //记录访问状态
            $invite_info = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$dr_mobile])->get('mb_sxo_invite_info',1)->row_array();
            $info_id = isset($invite_info['id'])?$invite_info['id']:0;
            //查询报告结果
            $exist_res = $this->db->select('id')->where(['invite_info_id'=>$info_id])->get('mb_sxo_invite_info_result',1)->row_array();
            //访问状态
            if(!empty($exist_res)){
                $upres = $this->db->where(['id'=>$exist_res['id'],'status'=>3])->update('mb_sxo_invite_info_result',['status'=>4]);
                if(!$upres){
                    redirect('/');
                }
            }
        }
        
        $sxo_task3 = isset($sxo_task3['task_three_qrcode'])?json_decode($sxo_task3['task_three_qrcode'],true):'';
        $task3_img = isset($sxo_task3[0][0])?$sxo_task3[0][0]:'';
        $data = ['sid'=>$sid,'code_img'=>$task3_img];
        
        $this->load->view("/sxo/four",$data);
    }
    
    /**
     * 赛小欧-支付页
     */
    public function paysxo(){
        $code = $this->input->get("st");
        $is_submit_account = $is_phone_type = $is_show_payment = false;
        if ($code) {
            $arr_code = explode("DRSAY", $code);
            if (count($arr_code) != 2) {
                redirect("/");
            }
            $encode_str = $arr_code[1];
            $st = $arr_code[0];
            
            //验证账号是否有误
            $arr_scene = explode("_", $encode_str);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                redirect("/");
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                redirect("/");
            }
            $st_msg = '您已选择奖励领取方式，7个工作日内支付！';
            $point = $filename = $scene = '';
            $payment_type = [];
        }else{
        
            $bk_code = $this->uri->segment(3);
            //验证账号是否有误
            $get_code_param = $this->sxoacc_validity($bk_code);
            if (!$get_code_param) {
                $this->survey_model->get_redirect_info("/sxo/paysxo", "l_errDRSAY".$bk_code);
            }

            //支付前是否登录认证成功
            if($this->session->has_userdata('sxo_payment_authentication')===false){
                $redirect_url = DRSAY_WEB . 'pa/s_auth/' . $bk_code;
                redirect($redirect_url);
            }

            $member_id = $get_code_param['member_id'];
            $sid = $get_code_param['sid'];
            $point = $get_code_param['point'];//获取积分
            $pid = $get_code_param['pid'];
            $config = [
    //            上医说
                'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
                'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
                'response_type' => 'array',
            ];

            $app = Factory::miniProgram($config);
            //临时二维码，由于小程序二维码参数最多只能传32位，因此要控制参数长度
            $scene = "yp_".$member_id.'_'.$sid;
            $scene = $scene."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
            $filename = "";
            if ($point >= 100) {
                $response = $app->app_code->getUnlimit($scene, [
                    //医map页面
    //                'page'  => 'pages/queryScene/queryScene',
                    //上医说页面
                    'page'  => 'pages/drsayExchange/drsayExchange',
                    'width' => 50,
                ]);
                // 或
                if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                    if (!is_dir("./uploads/wechat/ylpj/{$pid}/")) {
                        mk_dir("./uploads/wechat/ylpj/{$pid}/", 0777, true);
                    }
    //                    $filename = $response->saveAs("./uploads/wechat/{$pid}/", $pid."_".$member_uid.'.png');
                    $filename = $response->saveAs("./uploads/wechat/ylpj/{$pid}/", $scene.'.png');
                } else {
    //                $error_info = isset($error_code_info[$response['errcode']]) ? $error_code_info[$response['errcode']] : "";
                    echo "error_code:{$response['errcode']}";
                    die;
                }
            }
            $payment_type = [];//[206,8779];
            //通过用户编号，获取用户的，手机充值爱，支付宝，微信自动打款账号
            $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type in('".EXCHANGE_MOBILE."','".EXCHANGE_ALIPAY."','".EXCHANGE_WEBCHAT_AUTO."') ORDER BY id ASC", [$member_id])->result_array();
            $payment_type = [];
            if ($account_info) {
                foreach($account_info as $v_account){
                    $payment_type[$v_account['payment_type']] = $v_account['payment_type'];
                }
            }
            $st = "c";
            $is_submit_account = true;
            $is_show_payment = true;
            $lang_ary = get_lang(140);
            $st_msg = '感谢您参与本次互联网项目，选择收款账号领取奖励！';
        }
        $data = array(
            "st_msg" => $st_msg,
            "scene" => $scene,
            "filename" => $filename,
            "point" => $point,
            "payment_type" => $payment_type,
            "st" => $st,"pid" => $pid,
            "is_submit_account" => $is_submit_account,
            "is_phone_type" => $is_phone_type,
            "is_phone_val" => '',"gooddr_code" => '',"project_info" => '',
            "is_show_payment" => $is_show_payment,
        );
        $this->load->view("/sxo/to_wx_sxo.php",$data);
    }

    /**
     * 赛小欧参数认证
     */
    private function sxoacc_validity($bk_code){
        if(!$bk_code){
            return false;
        }
        $partner_paran = explode('_',$bk_code);
        $pid = $partner_paran[0];
        $mem_id = $partner_paran[1];
        $sid = $partner_paran[2];
        $old_certification = $partner_paran[3];
        $new_certification = substr(md5('3_'.$mem_id.'_'.$sid . PROJECT_ENCODE_KEY), 8, 16);
        if($pid!=3 || !$mem_id || !$sid || $old_certification!==$new_certification){
            return false;
        }
        //检测-(pay_status-2 已申请)
        $check_sxo = $this->db->select('id,cash,tj_cach')->where(['id'=>$sid,'pay_status'=>2])->get(TABLE_MB_SXO,1)->row_array();
        //不存在记录
        if(empty($check_sxo)){
            return false;
        }
        $point = $check_sxo['cash']+$check_sxo['tj_cach'];
        //实际金额小于1
        if($point<1){
            return false;
        }
        return ['point'=>$point*100,'pid'=>$pid,'member_id'=>$mem_id,'sid'=>$sid];
    }

    /**
     * 检测是否绑定微信收款码
     * @throws Exception
     */
    public function check_sxo_order_info(){
        $scene = $this->input->post("scene", true);
        if (!$scene) {
            _back_msg("error", "无效");
        }
        $arr_scene = explode("_", $scene);
        $pid = 3;
        $member_uid = $arr_scene[1];
        $sxo_id = $arr_scene[2];
        $encrypted_data = $arr_scene[3];
        if ($pid!=3 || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
            _back_msg("error", "参数有误");
        }
        $decrypt_scene = "yp_".$member_uid.'_'.$sxo_id;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            _back_msg("error", "参数有误");
        }

        //通过member_uid查询账号表里记录信息,查询微信唯一码是否存在
        $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type=? LIMIT 1", [$member_uid, EXCHANGE_WEBCHAT_AUTO])->row_array();
        if ($account_info) {
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }
    
    /**
     * 项目-赛小欧-支付提交
     * @apiDescription 兑换提交
     * @apiParam {string} scene （必填）提交验证加密串
     * @apiParam {int} payment_type （必填）提现方式
     * @apiParam {string} payment_name （必填）支付宝姓名 payment_type=206必填
     * @apiParam {string} payment_account （必填）支付宝账号 payment_type=206必填
     * @apiParam {string} mobile_payment_account （必填）手机号码 payment_type=209必填
     *
     * @apiParamExample {jsonp} Request Example
     *  POST /api_exchange_wx/sxo_payment_sub
     *  // 请求成功
     *  {
     *      "re_st": "success",
     *      "re_info": "info content" // 对应内容String类型
     *  }
     *
     *  // 无数据
     *  {
     *     "re_st": "empty",
     *     "re_info": ""
     *  }
     *
     *  // 请求记录错误
     *  {
     *     "re_st": "error",
     *     "re_info": "请求无效，请联系客服！"
     *  }
     */
    public function sxo_payment_sub(){
        try {
            //支付前是否登录认证成功
            if($this->session->has_userdata('sxo_payment_authentication')===false){
                throw new Exception("支付超时，请刷新重试！");
            }
        
            $post_data = $this->input->post();
            $post_data = format_post_data($post_data);
            $payment_name = $post_data['payment_name'];
            $payment_account = $post_data['payment_account'];
            $scene = $post_data['scene'];
            $payment_type = $post_data['payment_type'];
            $payment_type = $payment_type ? (int)$payment_type : "";
            //验证数据有效性
//            $verify_mobile = $this->input->post("verify_mobile", true);
//            $verify_code = $this->input->post("verify_code", true);
//            if (!$verify_mobile || !$verify_code) {
//                throw new Exception("请输入验证码！");
//            }
            if (!in_array($payment_type, [EXCHANGE_MOBILE, EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO, EXCHANGE_SURPLUS, DON_BE_POLITE])) {
                throw new Exception("支付方式选择有误！");
            }
            if ($payment_account) {
                if (!check_mobile($payment_account) && !check_email($payment_account)) {//支付宝账号只有手机号码或者邮箱格式
                    throw new Exception("支付宝账号格式错误,请重新输入");
                }
            }
            if ($payment_name && !isAllChinese($payment_name)) {//支付宝账号只支持真实姓名
                throw new Exception("请输入真实姓名!");
            }
//            if ($payment_type == EXCHANGE_MOBILE) {//如果支付方式是手机号码，则payment_account为mobile_payment_account
//                $payment_account = $mobile_payment_account;
//            }
            if (!is_dir("./tmp/exchange/")) {
                mk_dir("./tmp/exchange/", 0777, true);
            }

            //兑换日志
            //设备信息
            $this->load->library('user_agent');
            $platform = $this->agent->platform();
            $browser = $this->agent->browser();
            $browser_version = $this->agent->version();
            $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
            $device_info = [
                "device_type" => $platform,
                "http_user_agent" => $http_user_agent,//设备信息
                "browser" => $browser,
                "browser_version" => $browser_version,
            ];
            $device_info = array_merge($device_info, $post_data);
            file_put_contents("./tmp/exchange/api_exchange_new_".date("Y")."_".date("m").".txt", "start:".date("Y-m-d H:i:s")."**".json_encode($device_info, JSON_UNESCAPED_UNICODE).PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);

            $arr_scene = explode("_", $scene);
            $pid = 3;
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if ($pid!=3 || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                throw new Exception("参数有误！");
            }
            $decrypt_scene = "yp_".$member_uid.'_'.$sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                throw new Exception("参数有误！");
            }
            
            //根据sxo-id查询是否存在记录信息
            $sxo_info = $this->db->select('id,dr_id,dr_name,dr_mobile,cash,tj_cach,unit_name,department,dr_job_title,province,city')
                    ->where(['id'=>$sxo_id,'pay_status'=>2])->get('mb_sxo',1)->row_array();
            if(empty($sxo_info)){
                throw new Exception("未存在正确的调研记录！");
            }
            $department = $sxo_info['department'];
            $job_title = $sxo_info['dr_job_title'];
            $unit_name = $sxo_info['unit_name'];
            $province = $sxo_info['province'];
            $city = $sxo_info['city'];
            $name = $sxo_info['dr_name'];
            $member_mobile = $sxo_info['dr_mobile'];
            $point = ($sxo_info['cash']+$sxo_info['tj_cach'])*100;  //礼金金额+推荐费
            if ($point < 100) {
                throw new Exception("兑换失败，兑换分值({$point}分)太低，无法进行兑换，100积分起兑！");
            }
                
            //事务开始
            $this->db->trans_start();
            //加锁处理
            if(Redis_lock::getInstance()->lockNoWait("payment_sub_sxo_{$member_uid}", 120) !== true) {
                //错误提示
                throw new Exception("请不要重复提交！");
            }
            //赛小欧-只能微信自动打款+支付宝方式
            if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
                throw new Exception("提现方式选择有误，请重新选择！");
            }

            //获取用户信息
            $member_info = getDataByConditionCi("app_member", " AND id=?", "id,country,indentity_code", true, [$member_uid]);
            if (!$member_info) {
                throw new Exception("用户不存在，记录有误！");
            }
            $country = $member_info['country'];//会员所在国家
            $certif_id = $member_info['indentity_code'];//身份证号
            $ip = getip();
            
            //验证码认证
//            $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_EXCHANGE);
//            if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
//                throw new Exception($this->lang_ary[LABEL_EXCHANGE_VERIFICATION_CODE_ERROR]);
//            }

            //检测pid与member_uid信息是否有误，是否已经做过兑换
            //查询订单表，查询是否已经有过兑换记录
            $check_order = getDataByConditionCi(SURVEY_TABLE_APP_PAYMENT_ORDER, " AND pid=? AND uid=? AND param=? AND order_status=1","id", true, [2092, $member_uid, $sxo_id]);
            if ($check_order) {
                throw new Exception("您已申请过兑换，不能重复操作！");
            }

            //检测提现账号是否正确
            $check_account = getDataByConditionCi("app_ex_payment_account", " AND uid=? AND payment_type=?","id,payment_name,payment_account", true, [$member_uid, $payment_type]);
            if (!$check_account) {//账号不存在，入库
                if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                    if (!$payment_name || !$payment_account) {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                    if ($payment_name=='undefined' || $payment_account=='undefined') {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                    //检测收款账号与领取名称是否一致
                    if($payment_name!=$name){
                        throw new Exception("提交支付宝名称与领取人名称不一致，请重试输入！");
                    }
                }
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信自动打款
                    throw new Exception("微信零钱收款，请先扫码！！");
                }
                $insert_account_data = [
                    "uid" => $member_uid,
                    "payment_type" => $payment_type,
                    "payment_name" => $payment_name ? $payment_name : ($name ? $name : ""),//支付账号表不存在支付名称时，用明细表的姓名填充
                    "payment_account" => $payment_account,
                    "add_time" => time(),
                ];
                $payment_name = $payment_name ? $payment_name : ($name ? $name : "");
                $this->db->insert("app_ex_payment_account", $insert_account_data);
                $payment_account_id = $this->db->insert_id();
                if (!$payment_account_id) {
                    file_put_contents("./tmp/fail_bk_exchange.txt", "支付账号创建失败：".$this->db->last_query()."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                    throw new Exception("支付账号存储失败！");
                }
            } else {//账号已存在
                $payment_name = $check_account['payment_name'];
                $payment_account = $check_account['payment_account'];
                $payment_account_id = $check_account['id'];
            }

            //先添加提现积分日志，把用户想提现的积分记录下来
            $insert_flow_log = [
                "log_code" => ORDER_SOURCE_STATUS_PROSXO,
                "param_id" => $sxo_id,  //mb_sxo-id
                "point" => $point,//总积分,
                "member_uid" => $member_uid,
                "add_time" => time(),
            ];
            $this->db->insert("app_project_flow_log", $insert_flow_log);
            $flow_log_id = $this->db->insert_id();//流水编号
            if (!$flow_log_id) {
                throw new Exception("积分日志异常，请重试！");
            }

            ##############  所有积分都转换成人民币支付出去 start ##############
            $get_survey_point = getDataByConditionCi("app_survey_point", " AND country_id=?","*", true, [$country]);
            if (!$get_survey_point) {
                throw new Exception("积分与兑换比例未配置，请联系管理员！");
            }
            //积分转换成对应国家币种金额 round(5.055, 2)
            $exchange_amount = round(($point * $get_survey_point['china_money']) / $get_survey_point['country_point'], 2);
            //对应币种金额转换成提现金额
            $pay_exchange_amount = $exchange_amount * $get_survey_point['exchange_rate'];
            //对应币种金额转换成人民币金额
            $rmb_exchange_amount = $exchange_amount * $get_survey_point['to_rmb_exchange_rate'];
            if ($pay_exchange_amount < 1) {//小于1块钱，不能提现
                throw new Exception("兑换失败，您的积分不足1元，暂不能进行兑换！");
            }
            
            //公共订单表
            $insert_order = [
                'log_code'=>ORDER_SOURCE_STATUS_PROSXO,
                "exchange_point" => $point,//积分
                'exchange_amount'=>$rmb_exchange_amount,//项目站点金额
                "pay_exchange_amount" => $pay_exchange_amount,//提现金额
                "pay_rmb_exchange_amount" => $rmb_exchange_amount,//对应人民币金额
                "examine_type" => 2,//默认财务已审核待支付
                'param'=>$sxo_id,
                "uid" => $member_uid,
                "payment_type" => $payment_type,
                "payment_account" => $payment_account ? $payment_account : "",
                "payment_name" => $payment_name ? $payment_name : "",
                "add_time" => time(),
                "adder_ip" => ip2long($ip),
                "adder_address" => ip2location($ip),
                "pid" => 2092,
                "pid_id" => $sxo_id,
                "currency" => $get_survey_point['currency'],
                "pay_currency" => $get_survey_point['pay_currency'],
                "pay_exchange_rate" => $get_survey_point['exchange_rate'],
                "pay_rmb_exchange_rate" => $get_survey_point['to_rmb_exchange_rate'],
                "id_card" => $certif_id,//从会员表获取
                "name" => $name,//从项目明细表获取
                "mobile" => $member_mobile,//从项目明细表获取
                "department" => $department,//从项目明细表获取
                "job_title" => $job_title,//从项目明细表获取
                "unit_name" => $unit_name,//从项目明细表获取
//                "unit_level" => $unit_level,//从项目明细表获取
                "province" => $province,//从项目明细表获取
                "city" => $city,//从项目明细表获取
            ];
            $this->db->insert("app_payment_order_new", $insert_order);
            $order_insid = $this->db->insert_id();
            if(!$order_insid){
                throw new Exception("提交失败，请重新操作【支付订单异常】!");
            }
            //更新赛小欧支付状态
            $up_res = $this->db->where(['id'=>$sxo_id])->update(TABLE_MB_SXO,['pay_status'=>1]);
            if(!$up_res){
                throw new Exception("提交失败，请重新操作【更新支付状态异常】!");
            }
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE){
                file_put_contents("./tmp/fail_bk_exchange.txt", $member_uid."**".$pid."**".$payment_type."**".$payment_name."**".$payment_account."**".$sxo_id."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                throw new Exception("兑换失败，请联系管理员！");
            }
            if (file_exists("./uploads/wechat/ylpj/{$pid}/{$scene}.png")) {
                unlink("./uploads/wechat/ylpj/{$pid}/{$scene}.png");
            }
            
            //更新验证码为已使用状态
//            if ($user_verification_code) {
//                update_verification_code($user_verification_code['id']);
//            }
            //支付成功删除支付认证标示
            $this->session->unset_userdata('sxo_payment_authentication');
            _back_msg("success", "");
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
        ##############  所有积分都转换成人民币支付出去 end ##############
    }
    
    
    /**
     * 赛小欧-推荐支付页
     */
    public function paysxoref(){
        $code = $this->input->get("st");
        $is_submit_account = $is_phone_type = $is_show_payment = false;
        if ($code) {
            $arr_code = explode("DRSAY", $code);
            if (count($arr_code) != 2) {
                redirect("/");
            }
            $encode_str = $arr_code[1];
            $st = $arr_code[0];
            
            //验证账号是否有误
            $arr_scene = explode("_", $encode_str);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $ref_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$ref_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                redirect("/");
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $ref_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                redirect("/");
            }
            $st_msg = '您进入的链接无效或已领取推荐奖励，请耐心等待！';
            $point = $filename = $scene = '';
            $payment_type = [];
        }else{
            $bk_code = $this->uri->segment(3);
            //验证账号是否有误
            $get_code_param = $this->sxoref_validity($bk_code);
            if (!$get_code_param) {
                $this->survey_model->get_redirect_info("/sxo/paysxoref", "l_errDRSAY".$bk_code);
            }

            //支付前是否登录认证成功
            if($this->session->has_userdata('sxo_payment_referee_authentication')===false){
                $redirect_url = DRSAY_WEB . 'pa/s_ref_auth/' . $bk_code;
                redirect($redirect_url);
            }

            $member_id = $get_code_param['member_id'];
            $ref_id = $get_code_param['ref_id'];
            $point = $get_code_param['point'];//获取积分
            $pid = $get_code_param['pid'];
            $config = [
    //            上医说
                'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
                'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
                'response_type' => 'array',
            ];

            $app = Factory::miniProgram($config);
            //临时二维码，由于小程序二维码参数最多只能传32位，因此要控制参数长度
            $scene = "ypref_".$member_id.'_'.$ref_id;
            $scene = $scene."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
            $filename = "";
            if ($point >= 100) {
                $response = $app->app_code->getUnlimit($scene, [
                    //医map页面
    //                'page'  => 'pages/queryScene/queryScene',
                    //上医说页面
                    'page'  => 'pages/drsayExchange/drsayExchange',
                    'width' => 50,
                ]);
                // 或
                if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                    if (!is_dir("./uploads/wechat/ylpjref/{$pid}/")) {
                        mk_dir("./uploads/wechat/ylpjref/{$pid}/", 0777, true);
                    }
    //                    $filename = $response->saveAs("./uploads/wechat/{$pid}/", $pid."_".$member_uid.'.png');
                    $filename = $response->saveAs("./uploads/wechat/ylpjref/{$pid}/", $scene.'.png');
                } else {
    //                $error_info = isset($error_code_info[$response['errcode']]) ? $error_code_info[$response['errcode']] : "";
                    echo "error_code:{$response['errcode']}";
                    die;
                }
            }
            $payment_type = [];//[206,8779];
            //通过用户编号，获取用户的，手机充值爱，支付宝，微信自动打款账号
            $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type in('".EXCHANGE_MOBILE."','".EXCHANGE_ALIPAY."','".EXCHANGE_WEBCHAT_AUTO."') ORDER BY id ASC", [$member_id])->result_array();
            $payment_type = [];
            if ($account_info) {
                foreach($account_info as $v_account){
                    $payment_type[$v_account['payment_type']] = $v_account['payment_type'];
                }
            }
            $st = "c";
            $is_submit_account = true;
            $is_show_payment = true;
            $lang_ary = get_lang(140);
            $st_msg = '感谢您参与本次互联网项目，选择收款账号领取推荐奖励！';
        }
        $data = array(
            "st_msg" => $st_msg,"scene" => $scene,"filename" => $filename,"point" => $point,
            "payment_type" => $payment_type,"st" => $st,"pid" => $pid,
            "is_submit_account" => $is_submit_account,"is_phone_type" => $is_phone_type,
            "is_phone_val" => '',"gooddr_code" => '',"project_info" => '',
            "is_show_payment" => $is_show_payment,
        );
        $this->load->view("/sxo/to_sxo_ref.php",$data);
    }
    
    /**
     * 赛小欧推荐支付参数认证
     */
    private function sxoref_validity($bk_code){
        if(!$bk_code){
            return false;
        }
        $partner_paran = explode('_',$bk_code);
        $pid = $partner_paran[0];
        $mem_id = $partner_paran[1];
        $ref_id = $partner_paran[2];
        $old_certification = $partner_paran[3];
        $new_certification = substr(md5('4_'.$mem_id.'_'.$ref_id . PROJECT_ENCODE_KEY), 8, 16);
        if($pid!=4 || !$mem_id || !$ref_id || $old_certification!==$new_certification){
            return false;
        }
        //检测-(send_sms_status-2 已发送支付短信，pay_status=1-已申请)
        $check_sxo = $this->db->select('id,payment_amount')
                ->where(['id'=>$ref_id,'dr_id'=>$mem_id,'pay_status'=>1,'send_sms_status'=>2])
                ->get('mb_sxo_payment_referee',1)->row_array();
        //不存在记录
        if(empty($check_sxo)){
            return false;
        }
        $point = $check_sxo['payment_amount'];
        //实际金额小于1
        if($point<1){
            return false;
        }
        return ['point'=>$point*100,'pid'=>$pid,'member_id'=>$mem_id,'ref_id'=>$ref_id];
    }
    
    /**
     * 检测是否绑定微信收款码
     * @throws Exception
     */
    public function check_sxoref_order_info(){
        $scene = $this->input->post("scene", true);
        if (!$scene) {
            _back_msg("error", "无效");
        }
        $arr_scene = explode("_", $scene);
        $pid = 4;
        $member_uid = $arr_scene[1];
        $ref_id = $arr_scene[2];
        $encrypted_data = $arr_scene[3];
        if ($pid!=4 || !$ref_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
            _back_msg("error", "参数有误");
        }
        $decrypt_scene = "ypref_".$member_uid.'_'.$ref_id;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            _back_msg("error", "参数有误");
        }

        //通过member_uid查询账号表里记录信息,查询微信唯一码是否存在
        $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type=? LIMIT 1", [$member_uid, EXCHANGE_WEBCHAT_AUTO])->row_array();
        if ($account_info) {
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }
    
    /**
     * 项目-赛小欧-支付提交
     * @apiDescription 兑换提交
     * @apiParam {string} scene （必填）提交验证加密串
     * @apiParam {int} payment_type （必填）提现方式
     * @apiParam {string} payment_name （必填）支付宝姓名 payment_type=206必填
     * @apiParam {string} payment_account （必填）支付宝账号 payment_type=206必填
     * @apiParam {string} mobile_payment_account （必填）手机号码 payment_type=209必填
     *
     * @apiParamExample {jsonp} Request Example
     *  POST /api_exchange_wx/sxoref_payment_sub
     *  // 请求成功
     *  {
     *      "re_st": "success",
     *      "re_info": "info content" // 对应内容String类型
     *  }
     *
     *  // 无数据
     *  {
     *     "re_st": "empty",
     *     "re_info": ""
     *  }
     *
     *  // 请求记录错误
     *  {
     *     "re_st": "error",
     *     "re_info": "请求无效，请联系客服！"
     *  }
     */
    public function sxoref_payment_sub(){
        try {
            //支付前是否登录认证成功
            if($this->session->has_userdata('sxo_payment_referee_authentication')===false){
                throw new Exception("支付超时，请刷新重试！");
            }
        
            $post_data = $this->input->post();
            $post_data = format_post_data($post_data);
            $payment_name = $post_data['payment_name'];
            $payment_account = $post_data['payment_account'];
            $scene = $post_data['scene'];
            $payment_type = $post_data['payment_type'];
            $payment_type = $payment_type ? (int)$payment_type : "";
            if (!in_array($payment_type, [EXCHANGE_MOBILE, EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO, EXCHANGE_SURPLUS, DON_BE_POLITE])) {
                throw new Exception("支付方式选择有误！");
            }
            if ($payment_account) {
                if (!check_mobile($payment_account) && !check_email($payment_account)) {//支付宝账号只有手机号码或者邮箱格式
                    throw new Exception("支付宝账号格式错误,请重新输入");
                }
            }
            if ($payment_name && !isAllChinese($payment_name)) {//支付宝账号只支持真实姓名
                throw new Exception("请输入真实姓名!");
            }
            if (!is_dir("./tmp/exchange/")) {
                mk_dir("./tmp/exchange/", 0777, true);
            }

            //兑换日志
            //设备信息
            $this->load->library('user_agent');
            $platform = $this->agent->platform();
            $browser = $this->agent->browser();
            $browser_version = $this->agent->version();
            $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
            $device_info = [
                "device_type" => $platform,
                "http_user_agent" => $http_user_agent,//设备信息
                "browser" => $browser,
                "browser_version" => $browser_version,
            ];
            $device_info = array_merge($device_info, $post_data);
            file_put_contents("./tmp/exchange/api_exchange_new_".date("Y")."_".date("m").".txt", "start:".date("Y-m-d H:i:s")."**".json_encode($device_info, JSON_UNESCAPED_UNICODE).PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);

            $arr_scene = explode("_", $scene);
            $pid = 4;
            $member_uid = $arr_scene[1];
            $ref_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if ($pid!=4 || !$ref_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                throw new Exception("参数有误！");
            }
            $decrypt_scene = "ypref_".$member_uid.'_'.$ref_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                throw new Exception("参数有误！");
            }
            
            //根据$ref_id获取支付记录
            $ref_info = $this->db->select('id,ref_id,sxo_id,payment_amount')
                    ->where(['id' => $ref_id, 'dr_id'=>$member_uid, 'pay_status' => 1,'send_sms_status'=>2])
                    ->get('mb_sxo_payment_referee', 1)->row_array();
            if(empty($ref_info)){
                throw new Exception("未存在正确的支付记录！");
            }
            $ref_ary_id = explode(',',$ref_info['ref_id']);
            
            //根据sxo-id查询是否存在记录信息
            $sxo_info = $this->db->select('id,dr_id,dr_name,dr_mobile,unit_name,department,dr_job_title,province,city')
                ->where(['id'=>$ref_info['sxo_id'],'has_sent'=>3,'distribution_status'=>2])->get('mb_sxo',1)->row_array();
            if(empty($sxo_info)){
                throw new Exception("未存在正确的调研记录！");
            }
            $department = $sxo_info['department'];
            $job_title = $sxo_info['dr_job_title'];
            $unit_name = $sxo_info['unit_name'];
            $province = $sxo_info['province'];
            $city = $sxo_info['city'];
            $name = $sxo_info['dr_name'];
            $member_mobile = $sxo_info['dr_mobile'];
            $point = $ref_info['payment_amount']*100;  //推荐费
            if ($point < 100) {
                throw new Exception("兑换失败，兑换分值({$point}分)太低，无法进行兑换，100积分起兑！");
            }
                
            //事务开始
            $this->db->trans_start();
            //加锁处理
            if(Redis_lock::getInstance()->lockNoWait("payment_referee_sub_{$member_uid}", 120) !== true) {
                //错误提示
                throw new Exception("请不要重复提交！");
            }
            //赛小欧-只能微信自动打款+支付宝方式
            if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
                throw new Exception("提现方式选择有误，请重新选择！");
            }

            //获取用户信息
            $member_info = getDataByConditionCi("app_member", " AND id=?", "id,country,indentity_code", true, [$member_uid]);
            if (!$member_info) {
                throw new Exception("用户不存在，记录有误！");
            }
            $country = $member_info['country'];//会员所在国家
            $certif_id = $member_info['indentity_code'];//身份证号
            $ip = getip();
            
            //检测pid与member_uid信息是否有误，是否已经做过兑换
            //查询订单表，查询是否已经有过兑换记录
            $check_order = getDataByConditionCi(SURVEY_TABLE_APP_PAYMENT_ORDER, " AND pid=? AND uid=? AND param=? AND order_status=1","id", true, [2092, $member_uid, $ref_id]);
            if ($check_order) {
                throw new Exception("您已申请过兑换，不能重复操作！");
            }

            //检测提现账号是否正确
            $check_account = getDataByConditionCi("app_ex_payment_account", " AND uid=? AND payment_type=?","id,payment_name,payment_account", true, [$member_uid, $payment_type]);
            if (!$check_account) {//账号不存在，入库
                if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                    if (!$payment_name || !$payment_account) {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                    if ($payment_name=='undefined' || $payment_account=='undefined') {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                    //检测收款账号与领取名称是否一致
                    if($payment_name!=$name){
                        throw new Exception("提交支付宝名称与领取人名称不一致，请重试输入！");
                    }
                }
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信自动打款
                    throw new Exception("微信零钱收款，请先扫码！！");
                }
                $insert_account_data = [
                    "uid" => $member_uid,
                    "payment_type" => $payment_type,
                    "payment_name" => $payment_name ? $payment_name : ($name ? $name : ""),//支付账号表不存在支付名称时，用明细表的姓名填充
                    "payment_account" => $payment_account,
                    "add_time" => time(),
                ];
                $payment_name = $payment_name ? $payment_name : ($name ? $name : "");
                $this->db->insert("app_ex_payment_account", $insert_account_data);
                $payment_account_id = $this->db->insert_id();
                if (!$payment_account_id) {
                    file_put_contents("./tmp/fail_bk_exchange.txt", "支付账号创建失败：".$this->db->last_query()."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                    throw new Exception("支付账号存储失败！");
                }
            } else {//账号已存在
                $payment_name = $check_account['payment_name'];
                $payment_account = $check_account['payment_account'];
                $payment_account_id = $check_account['id'];
            }

            //先添加提现积分日志，把用户想提现的积分记录下来
            $insert_flow_log = [
                "log_code" => ORDER_SOURCE_STATUS_PROSXO_REFEREE,
                "param_id" => $ref_id,  //mb_sxo_referee-id
                "point" => $point,//总积分,
                "member_uid" => $member_uid,
                "add_time" => time(),
            ];
            $this->db->insert("app_project_flow_log", $insert_flow_log);
            $flow_log_id = $this->db->insert_id();//流水编号
            if (!$flow_log_id) {
                throw new Exception("积分日志异常，请重试！");
            }

            ##############  所有积分都转换成人民币支付出去 start ##############
            $get_survey_point = getDataByConditionCi("app_survey_point", " AND country_id=?","*", true, [$country]);
            if (!$get_survey_point) {
                throw new Exception("积分与兑换比例未配置，请联系管理员！");
            }
            //积分转换成对应国家币种金额 round(5.055, 2)
            $exchange_amount = round(($point * $get_survey_point['china_money']) / $get_survey_point['country_point'], 2);
            //对应币种金额转换成提现金额
            $pay_exchange_amount = $exchange_amount * $get_survey_point['exchange_rate'];
            //对应币种金额转换成人民币金额
            $rmb_exchange_amount = $exchange_amount * $get_survey_point['to_rmb_exchange_rate'];
            if ($pay_exchange_amount < 1) {//小于1块钱，不能提现
                throw new Exception("兑换失败，您的积分不足1元，暂不能进行兑换！");
            }
            
            //公共订单表
            $insert_order = [
                'log_code'=>ORDER_SOURCE_STATUS_PROSXO_REFEREE,
                "exchange_point" => $point,//积分
                'exchange_amount'=>$rmb_exchange_amount,//项目站点金额
                "pay_exchange_amount" => $pay_exchange_amount,//提现金额
                "pay_rmb_exchange_amount" => $rmb_exchange_amount,//对应人民币金额
                "examine_type" => 2,//默认财务已审核待支付
                'param'=>$ref_id,
                "uid" => $member_uid,
                "payment_type" => $payment_type,
                "payment_account" => $payment_account ? $payment_account : "",
                "payment_name" => $payment_name ? $payment_name : "",
                "add_time" => time(),
                "adder_ip" => ip2long($ip),
                "adder_address" => ip2location($ip),
                "pid" => 2092,
                "pid_id" => $ref_id,
                "currency" => $get_survey_point['currency'],
                "pay_currency" => $get_survey_point['pay_currency'],
                "pay_exchange_rate" => $get_survey_point['exchange_rate'],
                "pay_rmb_exchange_rate" => $get_survey_point['to_rmb_exchange_rate'],
                "id_card" => $certif_id,//从会员表获取
                "name" => $name,//从项目明细表获取
                "mobile" => $member_mobile,//从项目明细表获取
                "department" => $department,//从项目明细表获取
                "job_title" => $job_title,//从项目明细表获取
                "unit_name" => $unit_name,//从项目明细表获取
//                "unit_level" => $unit_level,//从项目明细表获取
                "province" => $province,//从项目明细表获取
                "city" => $city,//从项目明细表获取
            ];
            $this->db->insert("app_payment_order_new", $insert_order);
            $order_insid = $this->db->insert_id();
            if(!$order_insid){
                throw new Exception("提交失败，请重新操作【支付订单异常】!");
            }
            //更新赛小欧推荐人支付信息状态
            $up_res = $this->db->where(['id'=>$ref_id])->update('mb_sxo_payment_referee',['pay_status'=>2]);
            if(!$up_res){
                throw new Exception("提交失败，请重新操作【更新推荐支付状态异常】!");
            }
            //更新推荐记录支付状态
            if(!empty($ref_ary_id)){
                $up_referee_res = $this->db->where_in('id',$ref_ary_id)->update(TABLE_MB_SXO_REFEREE,['status'=>3,'status_time'=> time()]);
                if(!$up_referee_res){
                    throw new Exception("提交失败，请重新操作【更新推荐记录支付状态异常】!");
                }
            }
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE){
                file_put_contents("./tmp/fail_bk_exchange.txt", $member_uid."**".$pid."**".$payment_type."**".$payment_name."**".$payment_account."**".$sxo_id."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                throw new Exception("兑换失败，请联系管理员！");
            }
            if (file_exists("./uploads/wechat/ylpjref/{$pid}/{$scene}.png")) {
                unlink("./uploads/wechat/ylpjref/{$pid}/{$scene}.png");
            }
            
            //更新验证码为已使用状态
//            if ($user_verification_code) {
//                update_verification_code($user_verification_code['id']);
//            }
            //支付成功删除支付认证标示
            $this->session->unset_userdata('sxo_payment_referee_authentication');
            _back_msg("success", "");
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
        ##############  所有积分都转换成人民币支付出去 end ##############
    }
    
    /**
     * 健康通-微信专员
     */
    public function jkt_commissioner(){
        $info = $this->db->select('url,file_name')->where(['id >='=>91,'id<='=>131])->order_by('id desc')->get('sv_img')->result_array();
        $info_res = [];
        foreach($info as $val){
            $explode = explode('-', $val['file_name']);
            $info_res[$explode[0]] = $val['url'];
        }
        $data['info'] = $info_res;
        $this->load->view("/sxo/jkt_commissioner",$data);
    }
    
    /**
     * 赛小欧-邀请-介绍
     * @throws Exception
     */
    public function invite(){
        $code = $this->uri->segment(3);
        $data['code'] = $code;
        // 流程案例
        if($code=='358112DRSAY1607241910479466DRSAY996e18'){
            $this->load->view("/sxo/registration_inquiry_1",$data);
        }else{
            $this->load->view("/sxo/registration_inquiry",$data);
        }
    }
    
    /**
     * 赛小欧-邀请-我要报名-项目介绍
     * @throws Exception
     */
    public function prointroduce(){
        $code = $this->uri->segment(3);
        $data['code'] = $code;
        $this->load->view("/sxo/introduce",$data);
    }
    
    /**
     * 报名进度查询
     */
    public function inquiry(){
        $error_msg = '';
        try {
            $code = $this->uri->segment(3);
            //解析参数
            $check_code = $this->check_invite_code($code);
            if(!$check_code){
                throw new Exception("邀请链接有误!");
            }
        } catch (Exception $e) {
            $error_msg = $e->getMessage();
            redirect("/sxo/commissioner?code=error_link");
        }
        $data = ['code'=>$code,'error_msg'=>$error_msg];
        $this->load->view("/sxo/inquiry",$data);
    }
    
    /**
     * 获取手机动态码
     * 报名进度查询
     */
    public function inquiry_vercode(){
        //加密参数认证
        $code = $this->input->post('code',true);
        if(!empty($code)){
            //解析参数
            $check_code = $this->check_invite_code($code);
            if($check_code){
                $post_data = $this->input->post(['verify_mobile'], true);
                $post_data = format_post_data($post_data);
                $verify_mobile = $post_data['verify_mobile'];
                if(empty($verify_mobile)){
                    _back_msg("error", "请输入手机号码！");
                }
                if(!check_mobile($verify_mobile)){
                    _back_msg("error", "请输入正确手机号码格式！");
                }
                //检测是否存在邀请中
                $exist = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$verify_mobile])
                        ->get('mb_sxo_invite_info')->row_array();
                if(empty($exist)){
                    _back_msg("error_vercode", "未存在邀请中，请先点击‘我要报名‘！");
                }
                
                //检测是否在当前时间30分钟内，查询报告结果
                $max_res_num = 10;
                $exist_res = $this->db->select('id,res_num,add_time,update_time')
                        ->where(['invite_info_id'=>$exist['id']])
                        ->get('mb_sxo_invite_info_result',1)->row_array();
                if(!empty($exist_res)){
                    $code_str = 'errorDRSAY'.$exist['id'];
                    $code_res = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                    $code_str.='DRSAY'.$code_res;
                    
                    //检测距离上次查询日期时间，是否在3分钟之内
                    $authen_time_3 = !empty($exist_res['update_time'])?$exist_res['update_time']:$exist_res['add_time'];
                    $time = time();
                    if(($time-$authen_time_3)<=180){
                        _back_msg("error_max_res", "请耐心等待，每隔3分钟可以查询一次！", $code_str);
                    }
                    
                    if($exist_res['res_num']>$max_res_num){
                        _back_msg("error_max_res", "已超过最大查询报名次数！", $code_str);
                    }
                }
                
                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_SXO_INVITE_REG;
                //检测验证码是否重复获取
                $exist_ver = $this->db->select('id,mobile,create_time')
                        ->where(['status'=>1,'type'=>"$type",'mobile'=>$verify_mobile])
                        ->order_by('id desc')->get('app_member_verify',1)->row_array();
                if(!empty($exist_ver)){
                    //检测重新获取是否大于60秒
                    $time_diff = time()-$exist_ver['create_time'];
                    if($time_diff<SMS_RESEND_TIME){
                        _back_msg("error", "操作频繁，请等待".(SMS_RESEND_TIME-$time_diff)."秒后重新获取！");
                    }
                }
                
                //发送短信验证码
                $vcode = rand(10000, 99999);
                $sms_log_code = SMS_LOG_CODE_SXO_INVITE_REG;
                //创蓝
                if ($_SERVER['HTTP_HOST'] == "dev.drsay.cn") {//本地
                    $st = true;
                } else {
                    $sms_content = "互联网医院活动报名结果查询，您的验证码为:{$vcode}，" . SMS_ACTIVE_TIME . "分钟内有效，请勿泄漏！";
                    $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                }
                
                if ($st) {//短信发送成功,记录入库
                    $verify_data = [
                        'mobile' => $verify_mobile,'vcode' => $vcode,
                        'create_time' => time(),'type' => (string) $type
                    ];
                    $add_res = $this->db->insert('app_member_verify', $verify_data);
                    if ($add_res) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        _back_msg("error", "请输入正确手机号码格式！");
                    }
                }
                _back_msg("error", "短信发送失败，请重试！");
            }
            _back_msg("error", "无效操作，认证参数失败！");
        }
        _back_msg("error", "请求参数错误，请重试！");
    }
    
    public function inquiry_res(){
        //加密参数认证
        $code = $this->input->post('code',true);
        if(empty($code)){
            _back_msg("error", "请求参数错误！",'ver_code_err');
        }
        //解析参数
        $check_code = $this->check_invite_code($code);
        if(!$check_code){
            _back_msg("error", "无效操作，认证失败！",'ver_code_err');
        }
        
        //根据code获取邀请链接ID
        $invite = $this->db->select('id,distribution_uid')->where(['is_del'=>1,'code'=>$code])->get('mb_sxo_invite',1)->row_array();
        if(empty($invite)){
            _back_msg("error", "无效操作，邀请链接错误！",'ver_code_err');
        }
        
        $fields = ['dr_mobile','ver_code'];
        $param = $this->input->post($fields,true);
        $data = $this->filter_data($param);
        if(empty($data['dr_mobile'])){
            _back_msg("error", "请输入手机号码！", 'dr_mobile');
        }
        if(!check_mobile($data['dr_mobile'])){
            _back_msg("error", "请输入正确手机号码格式！", 'dr_mobile');
        }
        if(empty($data['ver_code'])){
            _back_msg("error", "请输入验证码！",'ver_code_err');
        }
        $verify_code = $data['ver_code'];
        unset($data['ver_code']);
        //验证码认证
        $user_verification_code = get_verification_code($data['dr_mobile'], VERIFICATION_CODE_SXO_INVITE_REG);
        if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
            _back_msg("error", "验证码输入错误，请重新输入！",'ver_code_err');
        }
        //更新验证码为已使用状态
        if ($user_verification_code) {
            update_verification_code($user_verification_code['id']);
        }
        //检测是否审核通过
        $where = ['dr_mobile'=>$data['dr_mobile'],'is_del'=>1];
        $exist = $this->db->select('id,status,err_msg')->where($where)->get('mb_sxo_invite_info')->row_array();
        //是否已存在查询报名的记录
        $exist_res = $this->db->select('id')->where(['invite_info_id'=>$exist['id']])
                ->get('mb_sxo_invite_info_result')->row_array();
        if(!empty($exist_res)){
            $this->db->where(['id'=>$exist_res['id']])->set('res_num', 'res_num+1' ,false)->set('update_time', time())->update('mb_sxo_invite_info_result');
        }else{
            //记录查询报名成功记录
            $this->db->insert('mb_sxo_invite_info_result',['code'=>$code,'invite_info_id'=>$exist['id'],'res_num'=>1,'add_time'=> time()]);
        }
        if($exist['status']!=1){
            _back_msg("errors", "审核失败！",'/sxo/commissioner?code=examine_code&codes='.$code.'&infoid='.$exist['id']);
        }
        //查询二维码是否下载完成
        $sxo_task1 = $this->db->select('a.id,b.local_qr_code')->from('mb_sxo a')->join('mb_sxo_q_code b', 'a.id=b.sxo_id', 'left')
                ->where(['a.sxo_status'=>5,'a.has_sent'=>3,'a.dr_mobile'=>$data['dr_mobile'],'b.local_qr_code !='=>''])
                ->get()->row_array();
        if(empty($sxo_task1)){
            _back_msg("errors", "任务素材正在下载，请稍等！",'/sxo/commissioner?code=examine_code&codes='.$code.'&infoid='.$exist['id']);
        }
        _back_msg("success", "审核成功！",'/sxo/registration_results?code='.$code.'&infoid='.$exist['id']);
    }
    
    /**
     * 报名结果查询
     */
    public function registration_results(){
        $code = $this->input->get('code',true);
        //解析参数
        $check_code = $this->check_invite_code($code);
        if(!$check_code){
            redirect("/sxo/commissioner?code=error_link");
        }
        $invite_info = $this->db->select('id')->where(['is_del'=>1,'code'=>$code])->get('mb_sxo_invite')->row_array();
        $invite_id = !empty($invite_info)?$invite_info['id']:'';
        
        $infoid = $this->input->get('infoid',true);
        //是否存在邀请记录
        $exist = $this->db->select('id,internet_platform,is_dep_patient')
            ->where(['id'=>$infoid,'is_del'=>1,'invite_id'=>$invite_id])->get('mb_sxo_invite_info',1)->row_array();
        if(empty($exist)){
            redirect("/sxo/invite/".$code);
        }
        //检测是否在当前时间30分钟内，查询报告结果
        $exist_res = $this->db->select('id,add_time,update_time')->where(['invite_info_id'=>$infoid])
                ->get('mb_sxo_invite_info_result',1)->row_array();
        if(!empty($exist_res)){
            $authen_time = !empty($exist_res['update_time'])?$exist_res['update_time']:$exist_res['add_time'];
            $time = time();
            if(($time-$authen_time)>1800){
                redirect("/sxo/inquiry/".$code);
            }
        }else{
            redirect('/');
        }
        
        $data = ['code'=>$code,'infoid'=>$infoid,'info'=>$exist];
        $this->load->view("/sxo/registration_results",$data);
    }
    
    /**
     * 赛小欧-提示
     */
    public function sxo_tips(){
        $code = $this->input->get('code',true);
        //解析参数
        $check_code = $this->check_invite_code($code);
        if(!$check_code){
            redirect("/sxo/commissioner?code=error_link");
        }
        //获取当前对应健康通专员微信二维码
        $invite_info = $this->db->select('id')->where(['is_del'=>1,'code'=>$code])->get('mb_sxo_invite')->row_array();
        $invite_id = !empty($invite_info)?$invite_info['id']:'';

        $info_id = $this->input->get('infoid',true);
        //推荐管理(推荐人信息设置)-链接邀请人详情
        $exist_info = $this->db->select('id,distribution_uid')->where(['is_del'=>1,'id'=>$info_id,'invite_id'=>$invite_id])
                ->get('mb_sxo_invite_info',1)->row_array();
        if(empty($exist_info)){
            redirect('/');
        }
        
        //检测是否在当前时间30分钟内，查询报告结果
        $exist_res = $this->db->select('id,add_time,update_time')->where(['invite_info_id'=>$info_id])
                ->get('mb_sxo_invite_info_result',1)->row_array();
        if(!empty($exist_res)){
            $authen_time = !empty($exist_res['update_time'])?$exist_res['update_time']:$exist_res['add_time'];
            $time = time();
            if(($time-$authen_time)>1800){
                redirect("/sxo/inquiry/".$code);
            }
            //访问状态
            $this->db->where(['id'=>$exist_res['id'],'status'=>0])->update('mb_sxo_invite_info_result',['status'=>1]);
        }else{
            redirect('/');
        }
        
        //是否存在访问员二维码
        $exist_code = $this->db->select('code')->where(['visitor_id'=>$exist_info['distribution_uid']])
                ->get('mb_sxo_visitor_code')->row_array();
        if(empty($exist_code)){
            redirect('/');
        }
        
        $data = ['code'=>$code,'infoid'=>$info_id,'code_img'=>$exist_code['code']];
        $this->load->view("/sxo/sxo_tips",$data);
    }
    /**
     * 赛小欧-提示 任务一
     */
    public function sxo_task1(){
        $code = $this->input->get('code',true);
        //解析参数
        $check_code = $this->check_invite_code($code);
        if(!$check_code){
            redirect("/sxo/commissioner?code=error_link");
        }
        
        $invite_info = $this->db->select('id')->where(['is_del'=>1,'code'=>$code])->get('mb_sxo_invite')->row_array();
        $invite_id = !empty($invite_info)?$invite_info['id']:'';

        $info_id = $this->input->get('infoid',true);
        //推荐管理(推荐人信息设置)-链接邀请人详情
        $exist_info = $this->db->select('id,dr_mobile')->where(['is_del'=>1,'id'=>$info_id,'invite_id'=>$invite_id])
                ->get('mb_sxo_invite_info',1)->row_array();
        if(empty($exist_info)){
            redirect('/');
        }
        
        //检测是否在当前时间30分钟内，查询报告结果
        $exist_res = $this->db->select('id,add_time,update_time')->where(['invite_info_id'=>$info_id])
                ->get('mb_sxo_invite_info_result',1)->row_array();
        if(!empty($exist_res)){
            $authen_time = !empty($exist_res['update_time'])?$exist_res['update_time']:$exist_res['add_time'];
            $time = time();
            if(($time-$authen_time)>1800){
                redirect("/sxo/inquiry/".$code);
            }
            //访问状态
            $this->db->where(['id'=>$exist_res['id'],'status'=>1])->update('mb_sxo_invite_info_result',['status'=>2]);
        }else{
            redirect('/');
        }
        
        $sxo_task1 = $this->db->select('a.id,b.local_qr_code')->from('mb_sxo a')->join('mb_sxo_q_code b', 'a.id=b.sxo_id', 'left')
                ->where(['a.sxo_status'=>5,'a.has_sent'=>3,'a.dr_mobile'=>$exist_info['dr_mobile']])->get()->row_array();
        if(empty($sxo_task1)){
            redirect('/');
        }
        $task1_img = isset($sxo_task1['local_qr_code'])?$sxo_task1['local_qr_code']:'';
        
        $data = ['code'=>$code,'infoid'=>$info_id,'code_img'=>$task1_img];
        $this->load->view("/sxo/sxo_task1",$data);
    }
    /**
     * 赛小欧-提示 任务二
     */
    public function sxo_task2(){
        $code = $this->input->get('code',true);
        //解析参数
        $check_code = $this->check_invite_code($code);
        if(!$check_code){
            redirect("/sxo/commissioner?code=error_link");
        }
        
        $invite_info = $this->db->select('id')->where(['is_del'=>1,'code'=>$code])->get('mb_sxo_invite')->row_array();
        $invite_id = !empty($invite_info)?$invite_info['id']:'';

        $info_id = $this->input->get('infoid',true);
        //推荐管理(推荐人信息设置)-链接邀请人详情
        $exist_info = $this->db->select('id,dr_mobile')->where(['is_del'=>1,'id'=>$info_id,'invite_id'=>$invite_id])
                ->get('mb_sxo_invite_info',1)->row_array();
        if(empty($exist_info)){
            redirect('/');
        }
        
        //检测是否在当前时间30分钟内，查询报告结果
        $exist_res = $this->db->select('id,add_time,update_time')->where(['invite_info_id'=>$info_id])
                ->get('mb_sxo_invite_info_result',1)->row_array();
        if(!empty($exist_res)){
            $authen_time = !empty($exist_res['update_time'])?$exist_res['update_time']:$exist_res['add_time'];
            $time = time();
            if(($time-$authen_time)>1800){
                redirect("/sxo/inquiry/".$code);
            }
            //访问状态
            $this->db->where(['id'=>$exist_res['id'],'status'=>2])->update('mb_sxo_invite_info_result',['status'=>3]);
        }else{
            redirect('/');
        }
        
        $sxo_task2 = $this->db->select('id,task_two_qrcode')
                ->where(['sxo_status'=>5,'has_sent'=>3,'dr_mobile'=>$exist_info['dr_mobile']])
                ->get('mb_sxo',1)->row_array();
        if(empty($sxo_task2)){
            redirect('/');
        }
        $sxo_task2 = isset($sxo_task2['task_two_qrcode'])?json_decode($sxo_task2['task_two_qrcode'],true):'';
        $task2_img = isset($sxo_task2[0])?$sxo_task2[0]:'';
        
        $data = ['code'=>$code,'infoid'=>$info_id,'code_img'=>$task2_img];
        $this->load->view("/sxo/sxo_task2",$data);
    }
    /**
     * 赛小欧-提示 任务三
     */
    public function sxo_task3(){
        $code = $this->input->get('code',true);
        //解析参数
        $check_code = $this->check_invite_code($code);
        if(!$check_code){
            redirect("/sxo/commissioner?code=error_link");
        }
        
        $invite_info = $this->db->select('id')->where(['is_del'=>1,'code'=>$code])->get('mb_sxo_invite')->row_array();
        $invite_id = !empty($invite_info)?$invite_info['id']:'';
        
        $info_id = $this->input->get('infoid',true);
        //推荐管理(推荐人信息设置)-链接邀请人详情
        $exist_info = $this->db->select('id,dr_mobile')->where(['is_del'=>1,'id'=>$info_id,'invite_id'=>$invite_id])
                ->get('mb_sxo_invite_info',1)->row_array();
        if(empty($exist_info)){
            redirect('/');
        }
        
        //检测是否在当前时间30分钟内，查询报告结果
        $exist_res = $this->db->select('id,add_time,update_time')->where(['invite_info_id'=>$info_id])
                ->get('mb_sxo_invite_info_result',1)->row_array();
        if(!empty($exist_res)){
            $authen_time = !empty($exist_res['update_time'])?$exist_res['update_time']:$exist_res['add_time'];
            $time = time();
            if(($time-$authen_time)>1800){
                redirect("/sxo/inquiry/".$code);
            }
            //访问状态
            $this->db->where(['id'=>$exist_res['id'],'status'=>3])->update('mb_sxo_invite_info_result',['status'=>4]);
        }else{
            redirect('/');
        }
        
        $sxo_task3 = $this->db->select('id,task_three_qrcode')
                ->where(['sxo_status'=>5,'has_sent'=>3,'dr_mobile'=>$exist_info['dr_mobile']])
                ->get('mb_sxo',1)->row_array();
        if(empty($sxo_task3)){
            redirect('/');
        }
        $sxo_task3 = isset($sxo_task3['task_three_qrcode'])?json_decode($sxo_task3['task_three_qrcode'],true):'';
        $task3_img = isset($sxo_task3[0][0])?$sxo_task3[0][0]:'';
        
        $data = ['code_img'=>$task3_img];
        $this->load->view("/sxo/sxo_task3",$data);
    }
    
    /**
     * 赛小欧-邀请
     * @throws Exception
     */
    public function invites(){
        $error_msg = '';
        $province_list = $practice = [];
        try {
            $code = $this->uri->segment(3);
            //解析参数
            $check_code = $this->check_invite_code($code);
            if(!$check_code){
                throw new Exception("邀请链接有误!");
            }
            
            //省份
            $province = $this->db->select('code,name')->where(['level' => 1])->get(TABLE_BASE_AREA)->result_array();
            $province_list = !empty($province) ? array_column($province, 'name', 'code') : [];
            
            //职务
            $dic_list = $this->db->select('id,val')->where(['status'=>1,'big_class_id'=>APP_SYS_BIG_CLASS_PRACTICE_SORT,'pid'=>RELATION_TYPE_DOCTOR])
                    ->get('app_sys_dictionary')->result_array();
            $practice = !empty($dic_list)?array_column($dic_list, 'val','id'):[];
        } catch (Exception $e) {
            $error_msg = $e->getMessage();
            redirect("/sxo/commissioner?code=error_link");
        }
        
        $data = [
            'code'=>$code,'error_msg'=>$error_msg,'province'=>$province_list,'practice'=>$practice
        ];
        
        $this->load->view("/sxo/invite",$data);
    }
    
    /**
     * 过滤请求数据
     * 去除左右空格
     * @param type $data
     */
    private function filter_data($data) {
        if (!is_array($data))
            return trim($data);

        array_walk($data, function (&$value) {
            $value = trim($value);
        });
        return $data;
    }
    
    /**
     * 邀请保存
     */
    public function save_invite(){
        //加密参数认证
        $code = $this->input->post('code',true);
        if(empty($code)){
            _back_msg("error", "请求参数错误！",'ver_code_err');
        }
        //解析参数
        $check_code = $this->check_invite_code($code);
        if(!$check_code){
            _back_msg("error", "无效操作，认证失败！",'ver_code_err');
        }
        //根据code获取邀请链接ID
        $invite = $this->db->select('id,distribution_uid')->where(['is_del'=>1,'code'=>$code])->get('mb_sxo_invite',1)->row_array();
        if(empty($invite)){
            _back_msg("error", "无效操作，邀请链接错误！",'ver_code_err');
        }
        $fields = ['province_code','province','city_code','city','unit_name','dr_department','dr_name','dr_mobile',
            'is_dep_patient','license_no','credentials_no','dr_practice','dr_job_title','dr_position','ver_code',
            'is_internet_make','internet_platform','agreement_val','certificate_no'];
        $param = $this->input->post($fields,true);
        $data = $this->filter_data($param);
        $or_where = [];
//        if(empty($data['license_no']) && empty($data['credentials_no'])){
//            _back_msg("error", "请输入医师执业证号或医师资格证号！",'license_credentials');
//        }
        if(empty($data['license_no']) && empty($data['certificate_no'])){
            _back_msg("error", "请输入医师执业证、乡村医师证其中一项！",'license_no');
        }
//        if(empty($data['license_no'])){
//            _back_msg("error", "请输入医师执业证号！",'license_no');
//        }
        //医师执业证号
        if(!empty($data['license_no'])){
            if(!check_license_no($data['license_no'])){
                _back_msg("error_license_no", "执业证号格式错误,请重新输入！",'license_no');
            }
            $or_where['license_no'] = $data['license_no'];
        }
        //医师证件号
        if(!empty($data['certificate_no'])){
            if(strlen($data['certificate_no'])<5){
                _back_msg("error", "请输入不少于5位乡村医师证号！",'certificate_no');
            }
            if(!preg_match('/^[a-zA-Z0-9+]+$/u',$data['certificate_no'])){
                _back_msg("error", "请输入正确的乡村医师证号（只允许输入：字母、数字、+）！",'certificate_no');
            }
            $or_where['certificate_no'] = $data['certificate_no'];
        }
//        if(empty($data['credentials_no'])){
//            _back_msg("error", "请输入医师资格证号！",'credentials_no');
//        }
        //医师资格证号
        if(!empty($data['credentials_no'])){
            if(!check_credentials_no($data['credentials_no']) && !check_rural_credentials_no($data['credentials_no'])){
                _back_msg("error_credentials_no", "资格证号格式错误,请重新输入！",'credentials_no');
            }
            $or_where['credentials_no'] = $data['credentials_no'];
        }
        //是否有互联网医院诊疗经验
        if($data['is_internet_make']==1 && empty($data['internet_platform'])){
            _back_msg("error", "请输入互联网平台名称！",'internet_platform');
        }
        if($data['is_internet_make']==1 && !preg_match("/^[\x{4e00}-\x{9fa5}A-Za-z0-9]{2,50}$/u", $data['internet_platform'])){
            _back_msg("error", "请输入2-50位(包含：中英文数字)的互联网平台！",'internet_platform');
        }elseif($data['is_internet_make']==2){
            $data['internet_platform'] = '无';
        }
        unset($data['is_internet_make']);
        
        if(empty($data['province_code'])){
            _back_msg("error", "请选择省份！", 'province_code');
        }
        if(empty($data['city_code'])){
            _back_msg("error", "请选择城市！", 'city_code');
        }
        if(empty($data['unit_name'])){
            _back_msg("error", "请输入医院名称！", 'unit_name');
        }
        if(!preg_match("/^[\x{4e00}-\x{9fa5}A-Za-z0-9]{2,50}$/u", $data['unit_name'])){
            _back_msg("error", "请输入2-50位(包含：中英文数字)的医院名称！", 'unit_name');
        }
        if(empty($data['dr_department'])){
            _back_msg("error", "请输入所在科室！", 'dr_department');
        }
        if(!preg_match("/^[\x{4e00}-\x{9fa5}A-Za-z0-9]{2,20}$/u", $data['dr_department'])){
            _back_msg("error", "请输入2-20位(包含：中英文数字)的科室名称！", 'dr_department');
        }
        if(empty($data['dr_name'])){
            _back_msg("error", "请输入姓名！", 'dr_name');
        }
        if(!preg_match("/^[\x{4e00}-\x{9fa5}]{2,10}$/u", $data['dr_name'])){
            _back_msg("error", "请输入2-10位的中文名称！", 'dr_name');
        }
        if(empty($data['dr_mobile'])){
            _back_msg("error", "请输入手机号码！", 'dr_mobile');
        }
        if(!check_mobile($data['dr_mobile'])){
            _back_msg("error", "请输入正确手机号码格式！", 'dr_mobile');
        }
        //检测是否已邀请
        $or_where['dr_mobile'] = $data['dr_mobile'];
        
        $exist = $this->db->select('id')->where(['is_del'=>1])->group_start()->or_where($or_where)->group_end()
                ->get('mb_sxo_invite_info')->row_array();
        if($exist!=0){
            $code_str = 'errorDRSAY'.$exist['id'];
            $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
            $code_str.='DRSAY'.$code;
            _back_msg("error_vercode", "您已报名，请等待审核！", $code_str);
        }
        
        if(empty($data['ver_code'])){
            _back_msg("error", "请输入验证码！",'ver_code_err');
        }
        //检测是否选中协议
        if($data['agreement_val']=='false'){
            _back_msg("error", "请勾选免费成为健康通会员！",'ver_code_err');
        }
        unset($data['agreement_val']);
        
        $verify_code = $data['ver_code'];
        unset($data['ver_code']);
        //验证码认证
        $user_verification_code = get_verification_code($data['dr_mobile'], VERIFICATION_CODE_SXO_INVITE_REG);
        if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
            _back_msg("error", "验证码输入错误，请重新输入！",'ver_code_err');
        }
        
        //操作信息
        $data['invite_id'] = $invite['id'];
        //访问员-随机选择一位访问员
        $dis_ary = isset($invite['distribution_uid'])?explode(',', rtrim($invite['distribution_uid'],',')):[];
        $dis_key = array_rand($dis_ary,1);
        $data['distribution_uid'] = isset($dis_ary[$dis_key])?$dis_ary[$dis_key]:0;
        $data['add_time'] = time();
        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);
        $data['adder_ip'] = $local_ip;
        $data['adder_address'] = $ip_addr;
        
        $enclosure = $_FILES['enclosure'];
        //附件
        if(!empty($enclosure) && !empty($enclosure['name'])){
            $extArr = ['jpg','jpeg','png','bmp'];
            
            $extend = pathinfo($enclosure['name']);
            $suffix = strtolower($extend["extension"]);
            if (!in_array($suffix, $extArr)) {
                _back_msg('error', '附件格式错误，请上传.jpg,.jpeg,.png,.bmp附件', 'enclosure');
            }
            //上传路径
            $save_path = '/uploads/' . WORK_JOB . '/sxo/' . date('Y-m-d');
            if (!is_dir('.'.$save_path)) {
                @mk_dir('.'.$save_path);
            }
            $image_name = 'enclosure_'.time() .'_'. rand(111111, 999999) . "." . $suffix;
            $tmp_name = $enclosure['tmp_name'];
            $move_to_file = $save_path . "/" . $image_name;
            if (move_uploaded_file($tmp_name, iconv("utf-8", "gb2312", '.'.$move_to_file))) {
                $data['enclosure'] = $move_to_file;
            }
        }

        //开启事物
        $this->db->trans_begin();
        $this->db->insert('mb_sxo_invite_info',$data);
        $insert_id = $this->db->insert_id();
        //更新验证码为已使用状态
        if ($user_verification_code) {
            update_verification_code($user_verification_code['id']);
        }
        if ($this->db->trans_status() === FALSE){
            $this->db->trans_rollback();
            _back_msg("error", "操作失败，请稍后重试！",'error_vercode');
        }else{
            $this->db->trans_commit();
            $special_ary = [];//['752136DRSAY1607584441923385DRSAYe00cb0'];
            if(in_array($code, $special_ary)){
                _back_msg("success_special", "操作成功！");
            }else{
                _back_msg("success", "操作成功！",'/sxo/commissioner?code='.$insert_id.'DRSAY'.$code);
            }
        }
    }
    
    /**
     * 邀请完成展示对应访问员微信二维码
     * 健康通-微信专员
     */
    public function commissioner(){
        //加密参数认证
        $code = $this->input->get('code',true);
        $data = [];
        if($code==='error_link'){
            $visitor_id = '98,52,135,91,137,133,25,13,18,20,22,314,77,93,108,321,136,19,81,92,280,87,330,345,346,337,374,376,389,393,409';
            $sql = "select visitor_id,code from mb_sxo_visitor_code where visitor_id in ({$visitor_id}) ORDER BY RAND() LIMIT 1";
            $visitor_code = $this->db->query($sql)->row_array();
            $data['msg'] = '因邀请链接错误无法继续，请添加健康通专员为您解决！';
            $data['code'] = $visitor_code['code'];
        }else if($code==='error'){
            $code_str = $this->input->get('codes',true);
            //解析参数
            $check_code = $this->check_invite_code($code_str);
            if($check_code){
                $invite_info = $this->db->select('distribution_uid')->where(['is_del'=>1,'code'=>$code_str])->get('mb_sxo_invite')->row_array();
                $visitor_id = !empty($invite_info)?$invite_info['distribution_uid']:[];
            }
            if(empty($visitor_id)){
                $visitor_id = '98,52,135,91,137,133,25,13,18,20,22,314,77,93,108,321,136,19,81,92,280,87,330,345,346,337,374,376,389,393,409';
            }
            $sql = "select visitor_id,code from mb_sxo_visitor_code where visitor_id in ({$visitor_id}) ORDER BY RAND() LIMIT 1";
            $visitor_code = $this->db->query($sql)->row_array();
            $data['msg'] = '因医师执业证号、资格证号格式错误无法继续，请添加健康通专员为您解决！';
            $data['code'] = $visitor_code['code'];
        }elseif($code=='examine_code'){
            $code_str = $this->input->get('codes',true);
            //解析参数
            $check_code = $this->check_invite_code($code_str);
            if(!$check_code){
                redirect('/');
            }
            $invite_info = $this->db->select('id')->where(['is_del'=>1,'code'=>$code_str])->get('mb_sxo_invite')->row_array();
            $invite_id = !empty($invite_info)?$invite_info['id']:'';
            
            $info_id = $this->input->get('infoid',true);
            //推荐管理(推荐人信息设置)-链接邀请人详情
            $exist_info = $this->db->select('id,distribution_uid')->where(['is_del'=>1,'id'=>$info_id,'invite_id'=>$invite_id])
                    ->get('mb_sxo_invite_info',1)->row_array();
            if(empty($exist_info)){
                redirect('/');
            }
            //是否存在访问员二维码
            $exist_code = $this->db->select('code')->where(['visitor_id'=>$exist_info['distribution_uid']])
                    ->get('mb_sxo_visitor_code')->row_array();
            if(empty($exist_code)){
                redirect('/');
            }
            $data['msg'] = '审核失败无法继续，请添加健康通专员为您解决！';
            $data['code'] = $exist_code['code'];
        }elseif(!empty($code)){
            $new_code = '';
            $explode_param = explode('DRSAY',$code);
            if($explode_param[0]=='error'){
                //解析认证参数
                $info_id = $explode_param[1];
                $old_encryption = $explode_param[2];
                //根据参数组建加密字符
                $decrypt_scene = 'errorDRSAY'.$info_id;
                $new_encryption = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
                if(count($explode_param)!==3 || !$info_id || $old_encryption!==$new_encryption){
                    redirect('/');
                }
            }else{
                $info_id = $explode_param[0];
                unset($explode_param[0]);
                $new_code = implode('DRSAY', $explode_param);
                //解析参数
                $check_code = $this->check_invite_code($new_code);
                if(!$check_code){
                    redirect('/');
                }
            }
            
            //指定链接特殊操作
            $special_ary = ['752136DRSAY1607584441923385DRSAYe00cb0'];
            if(!empty($new_code) && in_array($new_code, $special_ary)){
                $data['code'] = 'https://www.drsay.cn/theme/sv/img/2020-12-14/150e2f5c1141f2c1dde873b52906cff4.jpg';
            }else{
                //推荐管理(推荐人信息设置)-链接邀请人详情
                $exist_info = $this->db->select('id,distribution_uid')->where(['is_del'=>1,'id'=>$info_id])
                        ->get('mb_sxo_invite_info',1)->row_array();
                if(empty($exist_info)){
                    redirect('/');
                }

                //是否存在访问员二维码
                $exist_code = $this->db->select('code')->where(['visitor_id'=>$exist_info['distribution_uid']])
                        ->get('mb_sxo_visitor_code')->row_array();
                if(empty($exist_code)){
                    redirect('/');
                }
                $data['code'] = $exist_code['code'];
            }
        }
        $this->load->view("/sxo/commissioner",$data);
    }
    
    //异步城市（base_area 地区统计用区划代码表）
    public function sel_city(){
        //加密参数认证
        $code = $this->input->post('code',true);
        $city_info = [];
        if(!empty($code)){
            //解析参数
            $check_code = $this->check_invite_code($code);
            if($check_code){
                //省份代码
                $province_code = $this->input->post('province_id',true);
                if($province_code):
                    $base_area = $this->db->select('province')->where(['code'=>$province_code,'level'=>1])->get(TABLE_BASE_AREA)->row_array();
                    $where = ['level'=>2,'province'=>$base_area['province']];
                    $city_info = $this->db->select('name as val_translate,code as sys_dictionary_id')
                        ->where($where)->get(TABLE_BASE_AREA)->result_array();
                endif;
            }
        }
        _back_msg("success", $city_info);
    }
    
    /**
     * 校验加密字符
     */
    private function check_invite_code($code){
        if($code){
            //解析参数
            $explode_param = explode('DRSAY',$code);
            $invid = $explode_param[0]; //
            $mic = $explode_param[1];
            $old_encryption = $explode_param[2];
            //根据参数组建加密字符
            $decrypt_scene = $invid.'DRSAY'.$mic;
            $new_encryption = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
            if(count($explode_param)===3 && $invid && $mic && $old_encryption===$new_encryption){
                //检测当前邀请链接是否有效
                $check_invite = $this->db->where(['is_del'=>1,'sxo_id'=>$invid,'microtime'=>$mic])->count_all_results('mb_sxo_invite');
                if($check_invite==1){
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 职称、职务
     * @return boolean
     */
    public function sel_job_title(){
        //加密参数认证
        $code = $this->input->post('code',true);
        $position_job_title = [];
        if(!empty($code)){
            //解析参数
            $check_code = $this->check_invite_code($code);
            if($check_code){
                $data_post = $this->input->post(['practice_sort'],true);
                if(empty($data_post['practice_sort'])){
                    return false;
                }
                $position_job_title = getDataByCondition("app_sys_dictionary","and status=1 and pid ={$data_post['practice_sort']}","id,local_id,pid,big_class_id,val");
            }
        }
        _back_msg("success",$position_job_title);
    }
    
    /**
     * 获取手机动态码
     */
    public function obtain_vercode(){
        //加密参数认证
        $code = $this->input->post('code',true);
        if(!empty($code)){
            //解析参数
            $check_code = $this->check_invite_code($code);
            if($check_code){
                $post_data = $this->input->post(['verify_mobile'], true);
                $post_data = format_post_data($post_data);
                $verify_mobile = $post_data['verify_mobile'];
                if(empty($verify_mobile)){
                    _back_msg("error", "请输入手机号码！");
                }
                if(!check_mobile($verify_mobile)){
                    _back_msg("error", "请输入正确手机号码格式！");
                }
                //检测是否存在邀请中
                $exist = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$verify_mobile])
                        ->get('mb_sxo_invite_info')->row_array();
                if(!empty($exist)){
                    $code_str = 'errorDRSAY'.$exist['id'];
                    $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                    $code_str.='DRSAY'.$code;
                    _back_msg("error_vercode", "您已报名，请等待审核！",$code_str);
                }
                
                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_SXO_INVITE_REG;
                //检测验证码是否重复获取
                $exist_ver = $this->db->select('id,mobile,create_time')
                        ->where(['status'=>1,'type'=>"$type",'mobile'=>$verify_mobile])
                        ->order_by('id desc')->get('app_member_verify',1)->row_array();
                if(!empty($exist_ver)){
                    //检测重新获取是否大于60秒
                    $time_diff = time()-$exist_ver['create_time'];
                    if($time_diff<SMS_RESEND_TIME){
                        _back_msg("error", "操作频繁，请等待".(SMS_RESEND_TIME-$time_diff)."秒后重新获取！");
                    }
                }
                
                //发送短信验证码
                $vcode = rand(10000, 99999);
                $sms_log_code = SMS_LOG_CODE_SXO_INVITE_REG;
                //创蓝
                if ($_SERVER['HTTP_HOST'] == "dev.drsay.cn") {//本地
                    $st = true;
                } else {
                    $sms_content = "邀您报名互联网医院活动，您的验证码为:{$vcode}，" . SMS_ACTIVE_TIME . "分钟内有效，请勿泄漏！";
                    $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                }
                
                if ($st) {//短信发送成功,记录入库
                    $verify_data = [
                        'mobile' => $verify_mobile,'vcode' => $vcode,
                        'create_time' => time(),'type' => (string) $type
                    ];
                    $add_res = $this->db->insert('app_member_verify', $verify_data);
                    if ($add_res) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        _back_msg("error", "请输入正确手机号码格式！");
                    }
                }
                _back_msg("error", "短信发送失败，请重试！");
            }
            _back_msg("error", "无效操作，认证参数失败！");
        }
        _back_msg("error", "请求参数错误，请重试！");
    }
    
    public function check_certificate_no(){
        //加密参数认证
        $code = $this->input->post('code',true);
        if(!empty($code)){
            //解析参数
            $check_code = $this->check_invite_code($code);
            if($check_code){
                $post_data = $this->input->post(['certificate_no'], true);
                $data = format_post_data($post_data);
                
                if(empty($data['certificate_no'])){
                    _back_msg("error", "请输入乡村医师证号！",'certificate_no');
                }
                
                if(strlen($data['certificate_no'])<5){
                    _back_msg("error", "请输入不少于5位乡村医师证号！",'certificate_no');
                }
                if(!preg_match('/^[a-zA-Z0-9+]+$/u',$data['certificate_no'])){
                    _back_msg("error", "请输入正确的乡村医师证号（只允许输入：字母、数字、+）！",'certificate_no');
                }
                
                //是否已报名
                $exist = $this->db->select('id')->where(['is_del'=>1,'certificate_no'=>$data['certificate_no']])
                        ->get('mb_sxo_invite_info')->row_array();
                if(!empty($exist)){
                    //加密已存在信息
                    $code_str = 'errorDRSAY'.$exist['id'];
                    $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                    $code_str.='DRSAY'.$code;
                    _back_msg("error_exist_certificate", htmlentities("您已报名，请等待审核！"),$code_str);
                }
                _back_msg("success", "验证成功！");
            }
            _back_msg("error", "无效操作，认证参数失败！",'certificate_no');
        }
        _back_msg("error", "请求参数错误，请重试！",'certificate_no');
    }
    
    /**
     * 异步验证参数
     * 医师执业证号、医师资格证号
     */
    public function check_license_credentials(){
        //加密参数认证
        $code = $this->input->post('code',true);
        if(!empty($code)){
            //解析参数
            $check_code = $this->check_invite_code($code);
            if($check_code){
                $post_data = $this->input->post(['license_no','credentials_no'], true);
                $data = format_post_data($post_data);
                
                if(empty($data['license_no']) && empty($data['credentials_no'])){
                    _back_msg("error", "请输入医师执业证号或医师资格证号！",'license_credentials');
                }
                //医师执业证号
                if(!empty($data['license_no'])){
                    if(!check_license_no($data['license_no'])){
                        _back_msg("error", "执业证号格式错误,请重新输入！",'license_no');
                    }
                    $or_where['license_no'] = $data['license_no'];
                    //是否已报名
                    $exist = $this->db->select('id')->where(['is_del'=>1,'license_no'=>$data['license_no']])
                            ->get('mb_sxo_invite_info')->row_array();
                    if(!empty($exist)){
                        $code_str = 'errorDRSAY'.$exist['id'];
                        $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                        $code_str.='DRSAY'.$code;
                        _back_msg("error_exist_license", "您已报名，请等待审核！",$code_str);
                    }
                }
                
                //医师资格证号
                if(!empty($data['credentials_no'])){
                    if(!check_credentials_no($data['credentials_no']) && !check_rural_credentials_no($data['credentials_no'])){
                        _back_msg("error", "资格证号格式错误,请重新输入！",'credentials_no');
                    }
                    $or_where['credentials_no'] = $data['credentials_no'];
                    //是否已报名
                    $exist = $this->db->select('id')->where(['is_del'=>1,'credentials_no'=>$data['credentials_no']])
                            ->get('mb_sxo_invite_info')->row_array();
                    if(!empty($exist)){
                        //加密已存在信息
                        $code_str = 'errorDRSAY'.$exist['id'];
                        $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                        $code_str.='DRSAY'.$code;
                        _back_msg("error_exist_credentials", htmlentities("您已报名，请等待审核！"),$code_str);
                    }
                }
                _back_msg("success", "验证成功！");
            }
            _back_msg("error", "无效操作，认证参数失败！",'license_credentials');
        }
        _back_msg("error", "请求参数错误，请重试！",'license_credentials');
    }
    
    /**
     * 手机号三要素实名验证
     * 姓名、身份证号码、手机号码，验证此三种信息是否一致
     */
//    private function three_elements_auth($idcard,$phone,$realname){
//        if(!$idcard || !$phone || !$realname){
//            return false;
//        }
//        $host = "https://phone3.market.alicloudapi.com";
//        $path = "/phonethree";
//        $method = "GET";
//        $appcode = "7032413c1a224a0391e6a9b9b4d94f1c";
//        $headers = array();
//        array_push($headers, "Authorization:APPCODE " . $appcode);
//        $querys = "idcard={$idcard}&phone={$phone}&realname=".urlencode($realname);
//        $bodys = "";
//        $url = $host . $path . "?" . $querys;
//
//        $curl = curl_init();
//        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
//        curl_setopt($curl, CURLOPT_URL, $url);
//        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
//        curl_setopt($curl, CURLOPT_FAILONERROR, false);
//        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
//        curl_setopt($curl, CURLOPT_HEADER, false);
//        if (1 == strpos("$".$host, "https://")){
//            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
//            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
//        }
//        $content = curl_exec($curl);
//        curl_close($curl);
//        return $content?json_decode($content,true):[];
//    }
    
    
    /**
     * 赛小欧-核验支付页
     */
    public function verifpay(){
        $code = $this->input->get("st");
        $is_submit_account = $is_phone_type = $is_show_payment = false;
        if ($code) {
            $arr_code = explode("DRSAY", $code);
            if (count($arr_code) != 2) {
                redirect("/");
            }
            $encode_str = $arr_code[1];
            $st = $arr_code[0];
            
            //验证账号是否有误
            $arr_scene = explode("_", $encode_str);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                redirect("/");
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                redirect("/");
            }
            $st_msg = '您已选择奖励领取方式，7个工作日内支付！';
            $point = $filename = $scene = '';
            $payment_type = [];
        }else{
        
            $bk_code = $this->uri->segment(3);
            //验证账号是否有误
            $get_code_param = $this->sxoverification_validity($bk_code);
            if (!$get_code_param) {
                $this->survey_model->get_redirect_info("/sxo/verifpay", "l_errDRSAY".$bk_code);
            }

            //支付前是否登录认证成功
            if($this->session->has_userdata('sxo_verifpay_authentication')===false){
                $redirect_url = DRSAY_WEB . 'pa/verif_auth/' . $bk_code;
                redirect($redirect_url);
            }

            $member_id = $get_code_param['member_id'];
            $sid = $get_code_param['sid'];
            $point = $get_code_param['point'];//获取积分
            $pid = $get_code_param['pid'];
            $config = [
    //            上医说
                'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
                'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
                'response_type' => 'array',
            ];

            $app = Factory::miniProgram($config);
            //临时二维码，由于小程序二维码参数最多只能传32位，因此要控制参数长度
            $scene = "ypsxovf_".$member_id.'_'.$sid;
            $scene = $scene."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
            $filename = "";
            if ($point >= 100) {
                $response = $app->app_code->getUnlimit($scene, [
                    //医map页面
    //                'page'  => 'pages/queryScene/queryScene',
                    //上医说页面
                    'page'  => 'pages/drsayExchange/drsayExchange',
                    'width' => 50,
                ]);
                // 或
                if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                    if (!is_dir("./uploads/wechat/ylpj/{$pid}/")) {
                        mk_dir("./uploads/wechat/ylpj/{$pid}/", 0777, true);
                    }
    //                    $filename = $response->saveAs("./uploads/wechat/{$pid}/", $pid."_".$member_uid.'.png');
                    $filename = $response->saveAs("./uploads/wechat/ylpj/{$pid}/", $scene.'.png');
                } else {
    //                $error_info = isset($error_code_info[$response['errcode']]) ? $error_code_info[$response['errcode']] : "";
                    echo "error_code:{$response['errcode']}";
                    die;
                }
            }
            $payment_type = [];//[206,8779];
            //通过用户编号，获取用户的，手机充值爱，支付宝，微信自动打款账号
            $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type in('".EXCHANGE_MOBILE."','".EXCHANGE_ALIPAY."','".EXCHANGE_WEBCHAT_AUTO."') ORDER BY id ASC", [$member_id])->result_array();
            $payment_type = [];
            if ($account_info) {
                foreach($account_info as $v_account){
                    $payment_type[$v_account['payment_type']] = $v_account['payment_type'];
                }
            }
            $st = "c";
            $is_submit_account = true;
            $is_show_payment = true;
            $lang_ary = get_lang(140);
            $st_msg = '感谢您参与本次互联网项目，选择收款账号领取奖励！';
        }
        $data = array(
            "st_msg" => $st_msg,
            "scene" => $scene,
            "filename" => $filename,
            "point" => $point,
            "payment_type" => $payment_type,
            "st" => $st,"pid" => $pid,
            "is_submit_account" => $is_submit_account,
            "is_phone_type" => $is_phone_type,
            "is_phone_val" => '',"gooddr_code" => '',"project_info" => '',
            "is_show_payment" => $is_show_payment,
        );
        $this->load->view("/sxo/to_verification_sxo.php",$data);
    }
    
    private function sxoverification_validity($bk_code){
        if(!$bk_code){
            return false;
        }
        $partner_paran = explode('_',$bk_code);
        $pid = $partner_paran[0];
        $mem_id = $partner_paran[1];
        $sid = $partner_paran[2];
        $old_certification = $partner_paran[3];
        $new_certification = substr(md5('2092_'.$mem_id.'_'.$sid . PROJECT_ENCODE_KEY), 8, 16);
        if($pid!=2092 || !$mem_id || !$sid || $old_certification!==$new_certification){
            return false;
        }
        //检测-(status-1未支付,2已支付)
        $check_sxo = $this->db->select('id,payment_amount')->where(['sxo_id'=>$sid,'status'=>1])
                ->get('mb_sxo_verification_payment',1)->row_array();
        //不存在记录
        if(empty($check_sxo)){
            return false;
        }
        $point = $check_sxo['payment_amount'];
        //实际金额小于1
        if($point<1){
            return false;
        }
        return ['point'=>$point*100,'pid'=>$pid,'member_id'=>$mem_id,'sid'=>$sid];
    }
    
    /**
     * 检测是否绑定微信收款码
     * @throws Exception
     */
    public function check_sxoverif_order_info(){
        $scene = $this->input->post("scene", true);
        if (!$scene) {
            _back_msg("error", "无效");
        }
        $arr_scene = explode("_", $scene);
        $pid = 2092;
        $member_uid = $arr_scene[1];
        $sxo_id = $arr_scene[2];
        $encrypted_data = $arr_scene[3];
        if ($pid!=2092 || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
            _back_msg("error", "参数有误");
        }
        $decrypt_scene = "ypsxovf_".$member_uid.'_'.$sxo_id;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            _back_msg("error", "参数有误");
        }

        //通过member_uid查询账号表里记录信息,查询微信唯一码是否存在
        $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type=? LIMIT 1", [$member_uid, EXCHANGE_WEBCHAT_AUTO])->row_array();
        if ($account_info) {
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }
    
    /**
     * 项目-赛小欧-核验支付提交
     * @apiDescription 兑换提交
     * @apiParam {string} scene （必填）提交验证加密串
     * @apiParam {int} payment_type （必填）提现方式
     * @apiParam {string} payment_name （必填）支付宝姓名 payment_type=206必填
     * @apiParam {string} payment_account （必填）支付宝账号 payment_type=206必填
     * @apiParam {string} mobile_payment_account （必填）手机号码 payment_type=209必填
     *
     * @apiParamExample {jsonp} Request Example
     *  POST /sxo/sxoverif_payment_sub
     *  // 请求成功
     *  {
     *      "re_st": "success",
     *      "re_info": "info content" // 对应内容String类型
     *  }
     *
     *  // 无数据
     *  {
     *     "re_st": "empty",
     *     "re_info": ""
     *  }
     *
     *  // 请求记录错误
     *  {
     *     "re_st": "error",
     *     "re_info": "请求无效，请联系客服！"
     *  }
     */
    public function sxoverif_payment_sub(){
        try {
            //支付前是否登录认证成功
            if($this->session->has_userdata('sxo_verifpay_authentication')===false){
                throw new Exception("支付超时，请刷新重试！");
            }
        
            $post_data = $this->input->post();
            $post_data = format_post_data($post_data);
            $payment_name = $post_data['payment_name'];
            $payment_account = $post_data['payment_account'];
            $scene = $post_data['scene'];
            $payment_type = $post_data['payment_type'];
            $payment_type = $payment_type ? (int)$payment_type : "";
            if (!in_array($payment_type, [EXCHANGE_MOBILE, EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO, EXCHANGE_SURPLUS, DON_BE_POLITE])) {
                throw new Exception("支付方式选择有误！");
            }
            if ($payment_account) {
                if (!check_mobile($payment_account) && !check_email($payment_account)) {//支付宝账号只有手机号码或者邮箱格式
                    throw new Exception("支付宝账号格式错误,请重新输入");
                }
            }
            if ($payment_name && !isAllChinese($payment_name)) {//支付宝账号只支持真实姓名
                throw new Exception("请输入真实姓名!");
            }
            if (!is_dir("./tmp/exchange/")) {
                mk_dir("./tmp/exchange/", 0777, true);
            }

            //兑换日志
            //设备信息
            $this->load->library('user_agent');
            $platform = $this->agent->platform();
            $browser = $this->agent->browser();
            $browser_version = $this->agent->version();
            $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
            $device_info = [
                "device_type" => $platform,
                "http_user_agent" => $http_user_agent,//设备信息
                "browser" => $browser,
                "browser_version" => $browser_version,
            ];
            $device_info = array_merge($device_info, $post_data);
            file_put_contents("./tmp/exchange/api_exchange_new_".date("Y")."_".date("m").".txt", "start:".date("Y-m-d H:i:s")."**".json_encode($device_info, JSON_UNESCAPED_UNICODE).PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);

            $arr_scene = explode("_", $scene);
            $pid = 2092;
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if ($pid!=2092 || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                throw new Exception("参数有误！");
            }
            $decrypt_scene = "ypsxovf_".$member_uid.'_'.$sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                throw new Exception("参数有误！");
            }
            
            //根据sxo-id查询是否存在记录信息
            $sxo_info = $this->db->select('id,dr_id,dr_name,dr_mobile,cash,tj_cach,unit_name,department,dr_job_title,province,city')
                    ->where(['id'=>$sxo_id,'sxo_status'=>3])->get('mb_sxo',1)->row_array();
            if(empty($sxo_info)){
                throw new Exception("未存在正确的调研记录！");
            }
            //查询核验支付请求
            $verifi_payment = $this->db->select('id,payment_amount,dr_mobile')->where(['sxo_id'=>$sxo_id,'status'=>1])
                    ->get('mb_sxo_verification_payment',1)->row_array();
            if(empty($verifi_payment)){
                throw new Exception("未存在正确的核验支付请求！");
            }
            if($verifi_payment['dr_mobile']!=$sxo_info['dr_mobile']){
                throw new Exception("未存在正确的核验支付请求【核验支付与调研记录手机号不相同】！");
            }
            
            $department = $sxo_info['department'];
            $job_title = $sxo_info['dr_job_title'];
            $unit_name = $sxo_info['unit_name'];
            $province = $sxo_info['province'];
            $city = $sxo_info['city'];
            $name = $sxo_info['dr_name'];
            $member_mobile = $sxo_info['dr_mobile'];
            $point = $verifi_payment['payment_amount']*100;  //礼金金额+推荐费
            if ($point < 100) {
                throw new Exception("兑换失败，兑换分值({$point}分)太低，无法进行兑换，100积分起兑！");
            }
                
            //事务开始
            $this->db->trans_start();
            //加锁处理
            if(Redis_lock::getInstance()->lockNoWait("payment_sub_sxoverif_{$member_uid}", 120) !== true) {
                //错误提示
                throw new Exception("请不要重复提交！");
            }
            //赛小欧-只能微信自动打款+支付宝方式
            if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
                throw new Exception("提现方式选择有误，请重新选择！");
            }

            //获取用户信息
            $member_info = getDataByConditionCi("app_member", " AND id=?", "id,country,indentity_code", true, [$member_uid]);
            if (!$member_info) {
                throw new Exception("用户不存在，记录有误！");
            }
            $country = $member_info['country'];//会员所在国家
            $certif_id = $member_info['indentity_code'];//身份证号
            $ip = getip();
            
            //检测pid与member_uid信息是否有误，是否已经做过兑换
            //查询订单表，查询是否已经有过兑换记录
            $check_order = getDataByConditionCi(SURVEY_TABLE_APP_PAYMENT_ORDER, " AND pid=? AND uid=? AND param=? AND log_code=? AND order_status=1","id", true, [2092, $member_uid, $sxo_id, ORDER_SOURCE_STATUS_PROSXO_VERIF]);
            if ($check_order) {
                throw new Exception("您已申请过兑换，不能重复操作！");
            }

            //检测提现账号是否正确
            $check_account = getDataByConditionCi("app_ex_payment_account", " AND uid=? AND payment_type=?","id,payment_name,payment_account", true, [$member_uid, $payment_type]);
            if (!$check_account) {//账号不存在，入库
                if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                    if (!$payment_name || !$payment_account) {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                    if ($payment_name=='undefined' || $payment_account=='undefined') {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                    //检测收款账号与领取名称是否一致
                    if($payment_name!=$name){
                        throw new Exception("提交支付宝名称与领取人名称不一致，请重试输入！");
                    }
                }
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信自动打款
                    throw new Exception("微信零钱收款，请先扫码！！");
                }
                $insert_account_data = [
                    "uid" => $member_uid,
                    "payment_type" => $payment_type,
                    "payment_name" => $payment_name ? $payment_name : ($name ? $name : ""),//支付账号表不存在支付名称时，用明细表的姓名填充
                    "payment_account" => $payment_account,
                    "add_time" => time(),
                ];
                $payment_name = $payment_name ? $payment_name : ($name ? $name : "");
                $this->db->insert("app_ex_payment_account", $insert_account_data);
                $payment_account_id = $this->db->insert_id();
                if (!$payment_account_id) {
                    file_put_contents("./tmp/fail_bk_exchange.txt", "支付账号创建失败：".$this->db->last_query()."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                    throw new Exception("支付账号存储失败！");
                }
            } else {//账号已存在
                $payment_name = $check_account['payment_name'];
                $payment_account = $check_account['payment_account'];
                $payment_account_id = $check_account['id'];
            }

            //先添加提现积分日志，把用户想提现的积分记录下来
            $insert_flow_log = [
                "log_code" => ORDER_SOURCE_STATUS_PROSXO_VERIF,
                "param_id" => $sxo_id,  //mb_sxo-id
                "point" => $point,//总积分,
                "member_uid" => $member_uid,
                "add_time" => time(),
            ];
            $this->db->insert("app_project_flow_log", $insert_flow_log);
            $flow_log_id = $this->db->insert_id();//流水编号
            if (!$flow_log_id) {
                throw new Exception("积分日志异常，请重试！");
            }

            ##############  所有积分都转换成人民币支付出去 start ##############
            $get_survey_point = getDataByConditionCi("app_survey_point", " AND country_id=?","*", true, [$country]);
            if (!$get_survey_point) {
                throw new Exception("积分与兑换比例未配置，请联系管理员！");
            }
            //积分转换成对应国家币种金额 round(5.055, 2)
            $exchange_amount = round(($point * $get_survey_point['china_money']) / $get_survey_point['country_point'], 2);
            //对应币种金额转换成提现金额
            $pay_exchange_amount = $exchange_amount * $get_survey_point['exchange_rate'];
            //对应币种金额转换成人民币金额
            $rmb_exchange_amount = $exchange_amount * $get_survey_point['to_rmb_exchange_rate'];
            if ($pay_exchange_amount < 1) {//小于1块钱，不能提现
                throw new Exception("兑换失败，您的积分不足1元，暂不能进行兑换！");
            }
            
            //公共订单表
            $insert_order = [
                'log_code'=>ORDER_SOURCE_STATUS_PROSXO_VERIF,
                "exchange_point" => $point,//积分
                'exchange_amount'=>$rmb_exchange_amount,//项目站点金额
                "pay_exchange_amount" => $pay_exchange_amount,//提现金额
                "pay_rmb_exchange_amount" => $rmb_exchange_amount,//对应人民币金额
                "examine_type" => 2,//默认财务已审核待支付
                'param'=>$sxo_id,
                "uid" => $member_uid,
                "payment_type" => $payment_type,
                "payment_account" => $payment_account ? $payment_account : "",
                "payment_name" => $payment_name ? $payment_name : "",
                "add_time" => time(),
                "adder_ip" => ip2long($ip),
                "adder_address" => ip2location($ip),
                "pid" => 2092,
                "pid_id" => $sxo_id,
                "currency" => $get_survey_point['currency'],
                "pay_currency" => $get_survey_point['pay_currency'],
                "pay_exchange_rate" => $get_survey_point['exchange_rate'],
                "pay_rmb_exchange_rate" => $get_survey_point['to_rmb_exchange_rate'],
                "id_card" => $certif_id,//从会员表获取
                "name" => $name,//从项目明细表获取
                "mobile" => $member_mobile,//从项目明细表获取
                "department" => $department,//从项目明细表获取
                "job_title" => $job_title,//从项目明细表获取
                "unit_name" => $unit_name,//从项目明细表获取
//                "unit_level" => $unit_level,//从项目明细表获取
                "province" => $province,//从项目明细表获取
                "city" => $city,//从项目明细表获取
            ];
            $this->db->insert("app_payment_order_new", $insert_order);
            $order_insid = $this->db->insert_id();
            if(!$order_insid){
                throw new Exception("提交失败，请重新操作【支付订单异常】!");
            }
            //更新赛小欧支付状态
            $up_res = $this->db->where(['sxo_id'=>$sxo_id])->update('mb_sxo_verification_payment',['status'=>2]);
            if(!$up_res){
                throw new Exception("提交失败，请重新操作【更新支付状态异常】!");
            }
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE){
                file_put_contents("./tmp/fail_bk_exchange.txt", $member_uid."**".$pid."**".$payment_type."**".$payment_name."**".$payment_account."**".$sxo_id."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                throw new Exception("兑换失败，请联系管理员！");
            }
            if (file_exists("./uploads/wechat/ylpj/{$pid}/{$scene}.png")) {
                unlink("./uploads/wechat/ylpj/{$pid}/{$scene}.png");
            }
            
            //支付成功删除支付认证标示
            $this->session->unset_userdata('sxo_verifpay_authentication');
            _back_msg("success", "");
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }
}
