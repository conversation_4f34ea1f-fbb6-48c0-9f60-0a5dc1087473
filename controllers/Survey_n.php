<?php
/**
 * Created by PhpStorm.
 * User: Amy
 * Date: 2017/11/02
 */

class Survey_n extends MY_Controller
{
    public $lang_ary;           //语言包
    public $question_number;    //题号
    public $now_finger;         //当前题目编号
    public $next_finger;        //下一题目编号
    public $go_code;            //外包链接url加密串
    public $query_string_uid;   //甄别问卷加密串
    public $res_survey_decrypt; //验证结果
    function __construct()
    {
//        ini_set('display_errors',1);            //错误信息
//        ini_set('display_startup_errors',1);    //php启动错误信息
//        error_reporting(-1);                    //打印出所有的 错误信息
        parent::__construct();
        $this->load->model('survey_model');
        $this->go_code = $this->uri->segment(3);
        $this->query_string_uid = $_SERVER["QUERY_STRING"];
        $survey_decrypt = $this->survey_model->survey_decrypt($this->go_code, $this->query_string_uid);
        $res_survey_decrypt = json_decode($survey_decrypt,true);
        if($res_survey_decrypt['code'] =='error'){
            die($res_survey_decrypt['msg']);
        }
        $this->res_survey_decrypt = $res_survey_decrypt;
        $pid = $this->res_survey_decrypt['msg']['pid'];
        //获取外包编号，通过外包编号获取国家默认语言
        $go_partner_id = $this->res_survey_decrypt['msg']['go_partner_id'];
        $go_groupno = $this->res_survey_decrypt['msg']['go_groupno'];
        $lang = 140;
        if ($go_groupno && $go_partner_id) {//存在外包信息
            $country_default_lang = $this->survey_model->get_country_default_lang($pid, $go_partner_id, $go_groupno);
            $lang =  $country_default_lang['lang'];
        }
        if (!$this->lang_version) {//语言版本有误
            set_sys_warning($pid, $go_partner_id, $go_groupno, "", "未设置语言版本！");
            redirect("/go/thanks?st=nquta");
        }
        //生成语言包
//        setting_lang($this->lang_version);
        //获取语言包
        $lang_info = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_info) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_info = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
//        $this->lang_ary = json_decode($lang_info, true);
        $this->lang_ary = $lang_info;
//        print_r($this->lang_ary);die;
//        $this->lang_ary = get_lang($lang);
        $this->lang = $lang;

        $session_info = isset($this->session->userdata["pid_".$pid]) ? $this->session->userdata["pid_".$pid] : array();
        //当前题目号
        $now_finger = isset($this->session->userdata["pid_".$pid]["now_finger"]) ? $this->session->userdata["pid_".$pid]["now_finger"] : 0;

        //下一题题目号
        $next_finger = isset($this->session->userdata["pid_".$pid]["next_finger"]) ? $this->session->userdata["pid_".$pid]["next_finger"] : 0;
        $question_number = $session_info ? $now_finger : 0;
        $this->question_number = "Q".($question_number + 1).":";
        $this->now_finger = $now_finger;
        $this->next_finger = $next_finger;
        unset($this->session->userdata["now_finger"]);
        unset($this->session->userdata["next_finger"]);
    }

    //回答问卷这进入甄别问卷
    public function index()
    {
        $pid = $this->res_survey_decrypt['msg']['pid'];
        $partner_uid = $this->res_survey_decrypt['msg']['partner_uid'];
        $groupno = $this->res_survey_decrypt['msg']['go_groupno'];
        $partner_id = $this->res_survey_decrypt['msg']['go_partner_id'];
        $from_where = $this->res_survey_decrypt['msg']['from_where'];
        if ($pid) {
            if (!$groupno || !$partner_id) {
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $this->lang_ary[LABEL_LINK_ERROR_GROUPNO_PARTNER_EMPTY]);
                redirect("/go/thanks?st=p");
            }
            $app_project_implement = "app_project_implement_".$pid; // 项目执行表
            //查询表结构是否存在
//            $check_table_info = $this->db->query("SHOW TABLES LIKE '%".$app_project_implement."%';")->row_array();
            $check_table_info = $this->db->query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE  TABLE_NAME = '{$app_project_implement}'")->row_array();

            if (!$check_table_info) {
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $this->lang_ary[LABEL_PROJECT_IMPLEMENT_NOT_EXIST]);
                redirect("/go/thanks?st=p");
            }
            $project_implement_finish_status = $this->db->where(array('partner_uid'=>$partner_uid))->get($app_project_implement)->row_array();
            if($project_implement_finish_status['finish_status']){//已存在状态
                //已经做过问卷不能重复参与
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $this->lang_ary[LABEL_SURVEY_FINISH_STATUS_EXIST]);
                redirect("/go/thanks?st=repeat");
            }
            $get_pro_info = $this->quta_select_project($pid);
            $app_project = $get_pro_info['app_project'];
            //验证答案与配额条件是否匹配
            $this->check_quta_setting_session_is_comm($pid, array(), false, $app_project);//配额编号返回
            $app_prizes = !empty($get_pro_info['app_prizes']) ? $get_pro_info['app_prizes'] : '';
//            $quta_setting = $get_pro_info['screen_option'];//甄别选项
            $quta_setting = $get_pro_info['quota_setting'];//甄别选项
            //题目及答案
            $project_relation = $get_pro_info['project_relation'];
            //自定义参数
            $new_attribute = trim($app_project['add_pr_26']);

            $quta_question_type = $get_pro_info['quta_question_type'];//题型
            $quta_question_title = $get_pro_info['quta_question_title'];//标题
            $quta_info = !empty($get_pro_info['quta_info']) ? $get_pro_info['quta_info'] : '';//附加字段题目选项
            $last_finger = max(array_keys($quta_setting));//取最后一题的key值
            //问卷是否开始
            $survey_one_page = isset($this->session->userdata['pid_'.$pid]['survey_one_page']) ? $this->session->userdata['pid_'.$pid]['survey_one_page'] : array();
            //已做好的甄别问卷
            $session_answer = isset($this->session->userdata['pid_'.$pid]['session_answer']) ? $this->session->userdata['pid_'.$pid]['session_answer'] : array();
            //甄别问卷的游标值
            $session_answer_finger = isset($this->session->userdata['pid_'.$pid]['session_answer_finger']) ? $this->session->userdata['pid_'.$pid]['session_answer_finger'] : array();
            if ($session_answer_finger) {//判断session是否有值 session_answer_finger
//                print_r($session_answer_finger);
                $max_finger = max(array_keys($session_answer_finger));  //判断甄别选项最大key值
                $now_finger = $max_finger + 1;  //当前题目的key值
            }else{//无值
                $now_finger = 0;    //当前题目的key值
            }
            //如果到达最后一题，跳转到真正的问卷
            if ($last_finger < $now_finger) {//最后一题
                // 能进入这里的，说明上面的答案与配额验证通过，这里直接存储会员的答案，并且更改会员的相关信息，最后分配链接，进入正式问卷
                $survey_link = $this->survey_model->get_survey_link($pid, $groupno, $partner_uid, $partner_id, $this->lang_ary);
                if(!$survey_link){
                    //记录警告信息
                    set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $this->lang_ary[LABEL_SURVEY_LINK_INSUFFICIENT]);
                    redirect("/go/thanks?st=ne");
                }

                //如果是测试地址，跳转到thanks页面
                if ($partner_uid == 'test=1') {
                    //清session
                    $this->session->unset_userdata("pid_".$pid);
                    redirect("/go/thanks?st=qutasuc");
                }else {
                    //获取符合条件的配额信息
                    //把答案转换成甄别问卷的实际答案才能与配额表的信息进行匹配
                    $res_project_relation = $this->quota_select_option($project_relation);
                    $res_session_answer = array();
                    foreach($session_answer as $k_option_field => $v_option_field) {
                        $res_session_answer[$k_option_field] = $res_project_relation[$k_option_field][$v_option_field];
                    }
//                    echo "<pre />";
//                    print_r($session_answer);
//                    print_r($res_session_answer);
                    $quota_id = $this->survey_model->get_by_quota_cond_and_member_info_control($pid, $quta_setting, $res_session_answer, $new_attribute, $this->res_survey_decrypt, false, $this->lang_ary);
                    //通过partner_uid查询是否存在记录， 不存在，直接添加
                    $this->db->query("UPDATE {$app_project_implement} SET quta_id='{$quota_id}' WHERE partner_uid='{$partner_uid}' AND quta_id=0 LIMIT 1");
                    ############  检测是否是测试链接，如果是测试链接，不记录信息  ############
                    //更新项目执行表相关信息
                    $data_implement = array(
                        'partner_id' => $partner_id,
                        'survey_uid' => $survey_link['survey_uid'],
                        'survey_answer_status' => '1',
                        'quta_survey_answer' => json_encode($session_answer, JSON_UNESCAPED_UNICODE),
                    );
                    if ($session_answer) {
                        $data_implement = array_merge($data_implement, $session_answer);
                    }
                    $this->survey_model->update_implement_a_s_link_table($pid, $data_implement, $this->res_survey_decrypt, true, $survey_link['id']);
                    ############  检测是否是测试链接，如果是测试链接，不记录信息  ############
//                    echo $quota_id;
//                    echo $survey_link['survey_link'];die;
                    redirect($survey_link['survey_link']);
                }
            }
            $last_page = false;
            if ($last_finger == $now_finger) {//最后一题
                $last_page = true;
            }
            //获取题目
            $page_quota_question_field = isset($quta_setting[$now_finger]) ? $quta_setting[$now_finger] : "";//甄别问卷题目选项
            //获取题目题型
            $page_quota_question_type = isset($quta_question_type[$page_quota_question_field]) ? $quta_question_type[$page_quota_question_field] : "";

            if(empty($page_quota_question_type)){
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $this->lang_ary[LABEL_SURVEY_SCREENING_QUESTIONS_NOT_SET]);
                redirect("/go/thanks?st=nquta");
            }
            //获取题目标题与选项
            $res_title_option = $this->quota_select_title_option($page_quota_question_field, $pid, $project_relation);
            if (!$res_title_option) {
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $page_quota_question_field."_".$pid."问卷参数有误！");
                redirect("/go/thanks?st=nquta");
            }
            if ($res_title_option == "NO_OPTION") {
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, "没有设置问卷选项！");
                redirect("/go/thanks?st=nquta");
            }
            if ($res_title_option == "NO_TITLE_SETTING") {
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, "没有设置问卷题目，请检查对应语言是否有设置！");
                redirect("/go/thanks?st=nquta");
            }
            $page_quta_question_title = $res_title_option['title'];
            $page_quta_question_option = $res_title_option['relation'];
            $question_option = array_keys($page_quta_question_option[$page_quota_question_field]);
            $question_view = $this->quta_select_verification($page_quta_question_title, $question_option, $page_quota_question_type, $page_quota_question_field);
        }

        $data = array(
            'survey_one_page' => $survey_one_page, // 开始答题
            'question_view' => $question_view, // 开始答题
            'last_page' => $last_page, // 甄别结束页
            'app_project' => $app_project, // 项目
            'app_prizes' => $app_prizes, // 礼品
            'go_code' => $this->go_code, // 礼品
            'query_string_uid' => $this->query_string_uid, // 礼品
        );
        $this->load->view('/survey/index', $data);
    }

    //获取题目及选项
    private function quota_select_title_option($field, $pid, $project_relation)
    {
        if (!$project_relation) {
            return "NO_OPTION";
        }
        if (!$field || !$pid) {return false;}
        $option_info = $this->quota_select_option($project_relation);
        if (!$option_info[$field]) {//不存在设置
            return "NO_OPTION";
        }
        $res_pro_survey_title = $this->db->query("SELECT * FROM app_project_survey_title WHERE pid='{$pid}' AND lang_id='{$this->lang}' LIMIT 1")->row_array();
        if (!$res_pro_survey_title) {//没有题目设置
            return "NO_TITLE_SETTING";
        }
        $title_info = json_decode($res_pro_survey_title["info"], true);
        if (!$title_info[$field]) {
            return "NO_TITLE_SETTING";
        }
        $res_title_option = array();
        $res_title_option['title'] = $title_info[$field];
        $res_title_option['relation'] = $option_info;
        return $res_title_option;
    }

    //获取选项信息
    private function quota_select_option($project_relation)
    {
        if (!$project_relation) {return false;}
        if (!$project_relation) {
            return "NO_OPTION";
        }

        //获取选项
        $option_info = json_decode($project_relation, true);
        $res_title_option = array();
        foreach ($option_info as $k_field => $v_info) {
            if ($v_info) {
                foreach ($v_info as $v) {
                    if (strpos($v, "%") !== false) {//存在正确答案
                        $arr_relation = explode("%", $v);
                        $option_key = $arr_relation[1];
                        $option_val = $arr_relation[0];
                    } else {
                        $option_key = $v;
                        $option_val = "";
                    }
                    $res_title_option[$k_field][$option_key] = $option_val;
                }
            }
        }
        return $res_title_option;
    }

    //通过项目编号查询项目表与配额表
    private function quta_select_project($pid)
    {
        if (!$pid) {return false;}
        //获取项目信息
        $app_project = $this->db->where(array('id'=>$pid))->get('app_project')->row_array();
        $res = array();
        if(!empty($app_project['prizes_id'])){
            $app_prizes =$this->db->where(array('id'=>$app_project['prizes_id']))->get('app_prizes')->row_array();
            $res['app_prizes'] = $app_prizes ?  $app_prizes : '';
        }
        //项目甄别选项
        $res['app_project'] = $app_project ?  $app_project : '';
        $res['quta_setting'] = $app_project['quta_setting'] ? json_decode($app_project['quta_setting'],true) : '';
        $res['quota_setting'] = $app_project['quota_setting'] ? json_decode($app_project['quota_setting'],true) : '';

        $pj_screen_question_option = $app_project['screen_option'] ? json_decode($app_project['screen_option'],true): '';
        if($pj_screen_question_option['y']){
            $res['screen_option'] = $pj_screen_question_option['y'];
        } else {
            $res['screen_option'] = '';
        }

        //项目甄别选项题目类型
        $res['quta_question_type'] = $app_project['quta_question_type'] ? json_decode($app_project['quta_question_type'],true) : '';
        //项目甄别选项题目标题
        $res['quta_question_title'] = $app_project['quta_question_title'] ? json_decode($app_project['quta_question_title'],true) : '';
        //项目配额信息
        $quta_info = $this->db->where("pid={$pid}")->get('app_project_quta')->result_array();
        //获取附加字段分组
        $res_new_attribute = array();
        if ($quta_info) {
            foreach ($quta_info as $v) {
                $new_attribute = $v['new_attribute'] ? json_decode($v['new_attribute'], true) : array();
                if ($new_attribute) {
                    foreach ($new_attribute as $k_attr => $v_attr) {
                        $res_new_attribute[$k_attr][$v_attr] = $v_attr;
                    }
                }
            }
        }
        if ($res_new_attribute) {
            $res['quta_info'] = $res_new_attribute;
        }
        $res['quta_base_data'] = $quta_info;
        $res['project_relation'] = $app_project['relation'] ? $app_project['relation'] : "";
        return $res ? $res : false;
    }

    //验证答案与配额条件是否匹配
    //is_ajax 如果是js，直接返回json格式字符串，如果不是，直接做页面跳转
    private function check_quta_setting_session_is_comm($pid, $post_data = array(), $is_ajax = true, $project_info)
    {
        //查询提交的答案是否符合甄别设置
        $session_info = $this->session->userdata;
        $session_info_pro = isset($session_info["pid_".$pid]) ? $session_info["pid_".$pid] : array();
        $session_info_project = $session_info_pro ? (isset($session_info_pro['session_answer']) ? $session_info_pro['session_answer'] : array()) : array();

        if (!$pid || !$project_info) {
            set_sys_warning($pid, "", "", "", $this->lang_ary[LABEL_PROJECT_NON_EXISTENT]);
            if ($is_ajax) {
                _back_msg("error", 'npros');
            } else {
                redirect("/go/thanks?st=npros");
            }
        }
        //检测配额是否符合条件，符合条件后，收集数量是否已满
        $session_info_project = $session_info_project ? array_merge($session_info_project, $post_data) : $post_data;
        if ($session_info_project) {
            //所有选项及答案
            $option_info = $this->quota_select_option($project_info['relation']);
            if ($session_info_project) {
                foreach ($session_info_project as $k => $v) {
                    if (!$option_info[$k][$v]) {//不存在正确答案，说明是被甄别的，直接结束
                        //记录甄别状态
                        $this->survey_model->update_implement_s_link_info($pid, $session_info_project, $this->res_survey_decrypt, $post_data, $is_ajax, $this->lang_ary);
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 题目与选项展示
     * @param string $question_title 题目
     * @param array $option 选项
     * @param int $question_type 题型
     * @param string $field 字段
     * @return bool|string
     */
    private function quta_select_verification($question_title, $option, $question_type, $field)
    {
        $question_view = $this->quta_answer_style($this->question_number.$question_title, $option, $question_type, $field);
        return $question_view ? $question_view : false;
    }

    /**
     * 表单输出
     * @param $title //题目标题
     * @param $option   //选项
     * @param $type //题型
     * @param $field    //字段名称
     * @return string
     */
    private function quta_answer_style($title, $option, $type, $field)
    {
        $go_code = $this->go_code;                      //外包链接url加密串
        $query_string_uid = $this->query_string_uid;    //甄别问卷加密串
        if (!$title || !$type || !$field || !$go_code || !$query_string_uid) {return "验证信息有误，无法显示问卷！";}
        $res = "";
        $is_open_questions = false;
        if ($type == 1 || $type == 2) {//单选题、多选题
            $input_type = $type == 1 ? "radio" : "checkbox";
            $res .= '<h3>'.$title.'</h3><table style="margin-left:10px; font-size:.6rem;" width="90%" border="0" cellspacing="0" cellpadding="0">';
            $i = 1;
            foreach ($option as $v) {
                $res .= '<tr><td height="27">';
                $res .= '<label for="'.$field.'_'.$i.'" class="checd_box">';
                $res .= '<input type="'.$input_type.'" id="'.$field.'_'.$i.'" name="'.$field.'" value="'.$v.'" />';
                $res .= '<span>'.$v.'</span>';
                $res .= '</label></td></tr>';
                $i++;
            }
            $res .= '</table>';
        }
        //开放题
        if ($type == 3) {
            $res .= '<h3>'.$title.'</h3><table style="margin-left:10px; font-size:.6rem;" width="90%" border="0" cellspacing="0" cellpadding="0">';
            // 输入框姓名
            if($field == 'name'){
                $res .= '<tr><td height="27"><input  onkeyup="value=value.replace(/[\d]/g,\'\') " id="'.$field.'" name="'.$field.'" type="text" size="" maxlength="500" value="" /></td></tr></table>';
            }

            // 输入手机号码
            if($field =='mobile'){
                $res .= '<tr><td height="27"><input onkeyup="value=value.replace(/[^\d]/g,\'\') " id="'.$field.'" name="'.$field.'" type="text" size="" maxlength="500" value="" /></td></tr></table>';
            }

            if($field =='email'){
                //输入E-mail
                $res .= '<tr><td height="27"><input   id="'.$field.'" name="'.$field.'" type="email" size="" maxlength="500" value="" /></td></tr></table>';
            }
            if($field =='home_addr'){
                //输入家庭地址
                $res .= '<tr><td height="27"><input id="'.$field.'" name="'.$field.'" type="text" size="" maxlength="500" value="" /></td></tr></table>';
            }

            if($field == 'pass_card'){
                // 输入身份证号
                $res .= '<tr><td height="27"><input  onkeyup="value=value.replace(/[^\d|chun]/g,\'\')" id="'.$field.'" name="'.$field.'" type="text" size="" maxlength="500" value="" /></td></tr></table>';
            }
            if($field == 'birthday'){ // 继续
                // 输入生日 <input onkeyup="this.value=this.value.replace(/\D/g,'')" type="text">

                $res .= '<tr><td height="27">';
                $res .= '<input type="text" onkeyup="this.value=this.value.replace(/\D/g,\'\')" maxlength="4" style="padding:.2rem; background-color:#f9f9f9; font-size:.75rem; font-weight:400; color:#333; text-align:center; max-width:3rem;" value="" class="'.$field.'_y"/><span>年</span>';
                $res .= '<input type="text" onkeyup="this.value=this.value.replace(/\D/g,\'\')" maxlength="2" style="padding:.2rem; background-color:#f9f9f9; font-size:.75rem; font-weight:400; color:#333; text-align:center; max-width:2rem;" value="" class="'.$field.'_m"/><span>月</span>';
                $res .= '<input type="text" onkeyup="this.value=this.value.replace(/\D/g,\'\')" maxlength="2" style="padding:.2rem; background-color:#f9f9f9; font-size:.75rem; font-weight:400; color:#333; text-align:center; max-width:2rem;" value="" class="'.$field.'_d"/><span>日</span></td></tr></table>';
//                $res .= '<tr><td height="27"><input id="'.$field.'" name="'.$field.'" type="text" size="" maxlength="500" value="" /></td></tr></table>';
            }
            $is_open_questions = true;
        }
        //js加载
        $res .= $this->get_survey_answer_session($field, $title, $is_open_questions);
        return $res;
    }

    //甄别问卷的js提交
    private function get_survey_answer_session($field, $title, $is_open_questions = false)
    {
        if (!$field || !$title) {return false;}
        $title = str_replace(array('?', '？'), array('!', '!'), $title);
        $go_code = $this->go_code;                //外包链接url加密串
        $query_string_uid = $this->query_string_uid;//甄别问卷加密串
        $res = "<script type='application/javascript'>";
        $res .= "$(document).ready(function(){";
        $res .= "$('.next_page').click(function () {";
        if ($is_open_questions) {//开放题
            $date_y = date('Y',time());
            if($field == 'birthday'){
                $res .= "var "."birthday_y =$('.".$field."_y').val();";
                $res .= "var "."birthday_m =$('.".$field."_m').val();";
                $res .= "var "."birthday_d =$('.".$field."_d').val();";
                $res .= "if(birthday_y =='' || birthday_y < 1930 || birthday_y > ".$date_y."){ alert('请填写正确年份') ;return false;}";
                $res .= "if(birthday_m =='' || birthday_m < 0 || birthday_m > 12){ alert('请填写正确月份') ;return false;}";
                $res .= "if(birthday_d =='' || birthday_d < 0 || birthday_d > 31){ alert('请填写正确日期') ;return false;}";
                $res .= "birthday_m = parseInt(birthday_m); if(birthday_m < 10){ birthday_m = '0'+birthday_m;}";
                $res .= "birthday_d = parseInt(birthday_d); if(birthday_d < 10){ birthday_d = '0'+birthday_d;}";
                $res .= "var str_b = birthday_y;";
                $res .= " str_b += birthday_m;";
                $res .= " str_b += birthday_d;";
                $res .= "var ".$field." = str_b ;";
            } else {
                $res .= "var ".$field." = $('#".$field."').val();";
            }
        }else{//单选、多选题
            $res .= "var ".$field." = $('input[name=".$field."]:checked').val();";
        }
        $res .= "if(".$field." == '' || ".$field." == undefined  ){";
        $res .= "alert('".$title."');";
        $res .= "return false;";
        $res .= "} else  {";
        $res .= "$.post('/survey/survey_answer_session/".$go_code."?".$query_string_uid."',{".$field.":".$field."},function(f) {";
        $res .= "if (f.rs_code == 'error') {";
        $res .= "alert(f.rs_msg);";
        $res .= "location.href='/go/thanks?st='+f.rs_msg;";
        $res .= "return false;";
        $res .= "}";
        $res .= "if (f.rs_code == 'survey_finish') {";
        $res .= "location.href='/go/thanks?st='+f.rs_msg;";
        $res .= "return false;";
        $res .= "}";
        $res .= "window.location.reload();";
        $res .= "},'json');";
        $res .= "}";
        $res .= "});";
        $res .= "});";
        $res .= "</script>";
        return $res;
    }

    ################  问卷提交 start  ################
    //问卷开始
    public function survey_one_page(){
        $go_code = $this->go_code;
        $query_string_uid = $this->query_string_uid;
        $survey_decrypt = $this->survey_model->survey_decrypt($go_code, $query_string_uid, true, true);
        $res_survey_decrypt = json_decode($survey_decrypt,true);
        if($res_survey_decrypt['code'] =='error'){
            _back_msg("error", $res_survey_decrypt['msg']);
        }
        $pid = $res_survey_decrypt['msg']['pid'];
        $this->merge_session_by_project($pid, array("survey_one_page" => 1,"now_finger" => 0, "next_finger" => 1));
        _back_msg("success", "操作成功！");
    }

    //点击"下一页"进入问卷
    public function survey_answer_session(){
        $post_data = $this->input->post();
        //判断录入是否有效，无效不允许提交
        $go_code = $this->uri->segment(3);
        $query_string_uid = $_SERVER["QUERY_STRING"];
        $survey_decrypt = $this->survey_model->survey_decrypt($go_code, $query_string_uid, true, true);
        $res_survey_decrypt = json_decode($survey_decrypt,true);
        if($res_survey_decrypt['code'] =='error'){
            _back_msg("error", $res_survey_decrypt['msg']);
        }
        $pid = $res_survey_decrypt['msg']['pid'];
        //通过pid获取项目信息
        $res_project_info = $this->db->query("SELECT * FROM app_project WHERE id='{$pid}'")->row_array();
        //问卷问题
        $quta_question_title = $res_project_info['quta_question_title'] ? json_decode($res_project_info['quta_question_title'], true) : array();
        $quta_key = key($post_data);
        $quta_val = trim($post_data[$quta_key]);
        $title = $quta_question_title[$quta_key];
        $title = str_replace(array('?', '？'), array('!', '!'), $title);
        if (!$quta_val) {//提交的结果为空
            _back_msg("error", $title);
        }
        if ($quta_key == "email") {// 邮箱验证
            if (!check_email($quta_val)) {
                _back_msg("error", "邮箱格式有误！");
            }
        }
        if ($quta_key == "birthday") {// 生日验证
            $date_y = date('Y',time());
            $date_birthday_y = date('Y',strtotime($quta_val));
            if($date_birthday_y =='' || $date_birthday_y < 1930 || $date_birthday_y > $date_y){
                _back_msg("error", "请填写正确年份！");
            }
            $date_birthday_m = date('m',strtotime($quta_val));
            if($date_birthday_m =='' || $date_birthday_m < 0 || $date_birthday_m > 12){
                _back_msg("error", "请填写正确月份！");
            }
            $date_birthday_d = date('d',strtotime($quta_val));
            if($date_birthday_d =='' || $date_birthday_d < 0 || $date_birthday_d > 31){
                _back_msg("error", "请填写正确日期！");
            }
        }
        if ($quta_key == "mobile") {
            if (!check_mobile($quta_val)) {
                _back_msg("error", "手机号码格式有误！");
            }
            //查询手机号码是否已经存在
            $p_table = "app_project_implement_".$pid;
            $check_mobile_res = $this->db->query("SELECT * FROM {$p_table} WHERE mobile='{$quta_val}' LIMIT 1")->row_array();

            if ($check_mobile_res) {
                _back_msg("error", "该手机号码已经参与过本次调查，不能再次参与！");
            }
        }

        //先存值
        $this->quta_survey_session($pid, $post_data);

        //再检测答案与配额条件是否相符
        $this->check_quta_setting_session_is_comm($pid, $post_data, true, $res_project_info);
        _back_msg("success", "提交成功！");
    }

    //存session值
    private function quta_survey_session($pid, $data)
    {
        if (!$pid || !$data) {return false;}
        $quta_key = key($data);
        $session_info = $this->session->userdata;
        $session_info_project = $session_info["pid_".$pid];
        $now_finger = $session_info_project['now_finger'];
        $next_finger = $session_info_project['next_finger'];
        $session_answer = isset($session_info_project['session_answer']) ? $session_info_project['session_answer'] : array();
        $session_answer_finger = isset($session_info_project['session_answer_finger']) ? $session_info_project['session_answer_finger'] : array();

        if ($session_answer) {
            $res_session_answer = array_merge($session_answer, $data);
        }else{
            $res_session_answer = $data;
        }

        if ($session_answer_finger) {
            $res_session_answer_finger = array_merge($session_answer_finger, array($now_finger => $quta_key));
        }else{
            $res_session_answer_finger = array($now_finger => $quta_key);
        }

        $res['now_finger'] = $now_finger + 1;
        $res['next_finger'] = $next_finger + 1;
        $res['session_answer'] = array_unique($res_session_answer);
        $res['session_answer_finger'] = array_unique($res_session_answer_finger);
        $this->merge_session_by_project($pid, $res);
        return true;
    }

    //合并session结果
    private function merge_session_by_project($pid, $push_session_data){
        if (!$push_session_data) {return false;}
        $session_info = $this->session->userdata;
        $session_info_pid = isset($session_info["pid_".$pid]) ? $session_info["pid_".$pid] : array();
        $res_session = array();
        if ($session_info_pid) {//该项目下存在相关的session信息
            $res_session["pid_".$pid] = array_merge($session_info_pid, $push_session_data);
        } else {
            $res_session["pid_".$pid] = $push_session_data;
        }
        $this->session->set_userdata($res_session);
        return true;
    }
    ################  问卷提交 end  ################
}