<?php
/**
 * 医师数据验证Demp
 */

class Dr extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    public function test()
    {
        $title = '数据检测';
        $data = ['title' => $title];

        $this->load->view('/demo/dr/test', $data);
    }

    public function index()
    {
        if ($_FILES['datafile']) {
            $files = pathinfo($_FILES['datafile']['name']);
            if ($files['extension'] == 'csv') {
                $url = '/uploads/web/demo/dr/';
                //是否存在上传路径
                if (!is_dir($url)) {
                    @mk_dir($url);
                }
                $path = $url . time() . '.' . 'csv';
                $file = $_FILES['datafile'];
                if (!move_uploaded_file($file['tmp_name'], '.' . $path)) {
                    _back_msg("error", "文件上传失败！");
                    return false;
                }
                $total_count =  $this->countFileLine($path);
                $datas = $this->csvGetLine($path, $total_count, 0);
                $header = $this->csvGetLine($path, 1, 0);
                $header = $datas[0];
                unset($datas[0]);
                foreach ($header as $k => $v) {
                    if ($v == '序号') {
                        $id_index = $k;
                    }
                    if ($v == '机构名字') {
                        $unit_index = $k;
                    }
                    if ($v == '机构所在省份') {
                        $prov_index = $k;
                    }
                    if ($v == '机构所在城市') {
                        $city_index = $k;
                    }
                    if ($v == '客户姓名') {
                        $drname_index = $k;
                    }
                    if ($v == '客户所在科室') {
                        $dep_index = $k;
                    }
                    if ($v == '客户职称') {
                        $jt_index = $k;
                    }
                }
                $plist = ["上海"=>"上海市","云南"=>"云南省","内蒙古"=>"内蒙古自治区","北京"=>"北京市","吉林"=>"吉林省","四川"=>"四川省","天津"=>"天津市","宁夏"=>"宁夏回族自治区","安徽"=>"安徽省","山东"=>"山东省","山西"=>"山西省","广东"=>"广东省","广西"=>"广西壮族自治区","新疆"=>"新疆维吾尔自治区","江苏"=>"江苏省","江西"=>"江西省","河北"=>"河北省","河南"=>"河南省","浙江"=>"浙江省","海南"=>"海南省","湖北"=>"湖北省","湖南"=>"湖南省","甘肃"=>"甘肃省","福建"=>"福建省","西藏"=>"西藏自治区","贵州"=>"贵州省","辽宁"=>"辽宁省","重庆"=>"重庆市","陕西"=>"陕西省","青海"=>"青海省","黑龙江"=>"黑龙江省","上海市"=>"上海市","云南省"=>"云南省","内蒙古自治区"=>"内蒙古自治区","北京市"=>"北京市","吉林省"=>"吉林省","四川省"=>"四川省","天津市"=>"天津市","宁夏回族自治区"=>"宁夏回族自治区","安徽省"=>"安徽省","山东省"=>"山东省","山西省"=>"山西省","广东省"=>"广东省","广西壮族自治区"=>"广西壮族自治区","新疆维吾尔自治区"=>"新疆维吾尔自治区","江苏省"=>"江苏省","江西省"=>"江西省","河北省"=>"河北省","河南省"=>"河南省","浙江省"=>"浙江省","海南省"=>"海南省","湖北省"=>"湖北省","湖南省"=>"湖南省","甘肃省"=>"甘肃省","福建省"=>"福建省","西藏自治区"=>"西藏自治区","贵州省"=>"贵州省","辽宁省"=>"辽宁省","重庆市"=>"重庆市","陕西省"=>"陕西省","青海省"=>"青海省","黑龙江省"=>"黑龙江省"];
                $sdata[0] = ['id'=>'序号', 'unit'=>'机构名字', 'province'=>'机构所在省份', 'city'=>'机构所在城市', 'drname'=>'客户姓名', 'department'=>'客户所在科室', 'job_title'=>'客户职称', 'jkt_hp_id' => '健康通机构编码', 'jkt_dr_id' => '健康通医师编码', 'jkt_province' => 'JKT省份', 'jkt_city' => 'JKT城市', 'jkt_unit' => 'JKT机构', 'jkt_drname' => 'JKT医师姓名', 'jkt_dr_department' => '科室', 'jkt_dr_job_title' => '职称', 'dep_match' => '科室匹配', 'jt_match' => '职称匹配', 'match_type' => '匹配状态'];
                $match_type_ary = [0=>'全不匹配', 2=>'2要素匹配', 3=>'3要素匹配', 4=>'全匹配'];
                foreach ($datas as $dk => $dv) {
                    $td['id'] = $dv[$id_index];
                    $td['unit'] = $dv[$unit_index];
                    $td['province'] = $dv[$prov_index];
                    $td['city'] = $dv[$city_index];
                    $td['drname'] = $dv[$drname_index];
                    $td['department'] = $dv[$dep_index];
                    $td['job_title'] = $dv[$jt_index];
                    $unit_where = array('province_name'=>$plist[$dv[$prov_index]], 'hname'=>$dv[$unit_index]);
                    $unit = $this->db->where($unit_where)->get('mb_hp_jkt_plus')->row_array();
                    if ($unit) {
                        $td['jkt_hp_id'] = $unit['jkt_hp_id'];
                        $td['jkt_unit'] = $unit['hname'];
                        $td['jkt_province'] = $unit['province_name'];
                        $td['jkt_city'] = $unit['city_name'];
                        $dr_where = array('ok_st'=>1, 'ok_gw'=>'01', 'jkt_hp_id'=> $unit['jkt_hp_id'], 'drname'=>$dv[$drname_index]);
                        $dr = $this->db->where($dr_where)->get('mb_dr_onekey')->row_array();
                        if ($dr) {
                            $match_type = 2;
                            $td['jkt_dr_id'] = $dr['jkt_dr_id'];
                            $td['jkt_drname'] = $dr['drname'];
                            $td['jkt_dr_department'] = $dr['final_department'];
                            $td['jkt_dr_job_title'] = $dr['final_job_title'];
                            if ($dv[$dep_index] == $dr['final_department'] || strpos($dv[$dep_index], $dr['final_department']) !== false || strpos($dr['final_department'], $dv[$dep_index]) !== false) {
                                $td['dep_match'] = 1;
                                $match_type = $match_type+1;
                                if ($dv[$jt_index] == $dr['final_job_title'] || ($dv[$jt_index] == '住院医师' && $dr['final_job_title']=='医师')) {
                                    $td['jt_match'] = 1;
                                    $match_type = $match_type+1;
                                } else {
                                    $td['jt_match'] = 0;
                                }
                            } else {
                                $td['dep_match'] = 0;
                                $td['jt_match'] = 0;
                            }

                            $td['match_type'] = $match_type_ary[$match_type];
                        } else {
                            // $td['jkt_hp_id'] = '';
                            $td['jkt_dr_id'] = '';
                            $td['jkt_drname'] = '';
                            $td['jkt_dr_department'] = '';
                            $td['jkt_dr_job_title'] = '';
                            $td['dep_match'] = 0;
                            $td['jt_match'] = 0;
                            $td['match_type'] = $match_type_ary[0];
                        }
                    } else {
                        $td['jkt_province'] = '';
                        $td['jkt_city'] = '';
                        $td['jkt_hp_id'] = '';
                        $td['jkt_dr_id'] = '';
                        $td['jkt_unit'] = '';
                        $td['jkt_drname'] = '';
                        $td['jkt_dr_department'] = '';
                        $td['jkt_dr_job_title'] = '';
                        $td['dep_match'] = 0;
                        $td['jt_match'] = 0;
                        $td['match_type'] = $match_type_ary[0];
                    }
                    $sdata[] = $td;
                }
                _back_msg("success", $sdata);
            }
        } else {
            $title = '数据检测';
            $data = ['title' => $title];

            $this->load->view('/demo/dr/index', $data);
        }
    }

    /**
     * 过滤请求数据
     * 去除左右空格
     * @param type $data
     */
    private function filter_data($data)
    {
        if (!is_array($data)) {
            return trim($data);
        }

        array_walk($data, function (&$value) {
            $value = trim($value);
        });
        return $data;
    }

    /**
     * countFileLine 获取文件行数
     * @param $file_url //文件路径
     * @return int
     */
    public static function countFileLine($file_url)
    {
        $file = '.' . $file_url;
        $line = 0;
        $fp = fopen($file, 'r');
        if ($fp) {
            //获取文件的一行内容，注意：需要php5才支持该函数；
            while (stream_get_line($fp, 8192, "\n")) {
                $line++;
            }
            fclose($fp); //关闭文件
        }
        // echo $line;
        return $line;
    }

    /**
     * csv_get_lines 读取CSV文件中的某几行数据
     * @param $file_url  //csv文件路径
     * @param $lines  //读取行数
     * @param $offset  //起始行数
     * @return array
     */
    public static function csvGetLine($file_url, $lines, $offset = 1)
    {
        $csvfile = '.' . $file_url;
        $spl_object = new \SplFileObject($csvfile, 'rb');
        $spl_object->seek($offset);
        $data = [];
        while ($lines-- && !$spl_object->eof()) {
            $data[] = $spl_object->fgetcsv();
            $spl_object->next();
        }
        $new_da = [];
        foreach ($data as $dk=>$dv) {
            foreach ($dv as $ck=>$cv) {
                $encode = mb_detect_encoding($cv, array("ASCII",'UTF-8',"GB2312","GBK",'BIG5'));
                if ($encode != 'UTF-8') {
                    $new_da[$dk][$ck] = str_replace(' ', '', mb_convert_encoding($cv, 'UTF-8', $encode));
                } else {
                    $new_da[$dk][$ck] = str_replace(' ', '', $cv);
                }
            }
        }
        return $new_da;
    }
}
