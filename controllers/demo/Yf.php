<?php
/**
 * 药房验证Demp
 */

class Yf extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    function index()
    {
        $addr_check = $shequ = $shangquan = false;
        $yfinfo=$shequinfo=$shangquaninfo='';
        $post_data = $this->input->post();
        if ($post_data) {
            //过滤左右空格
            $post_data = $this->filter_data($post_data);
            if (!empty($post_data['name']) && !empty($post_data['name'])) {
                $yf_name = $post_data['name'] ?? '';
                $yf_addr = $post_data['addr'] ?? '';

                $jwdurl = "https://restapi.amap.com/v3/geocode/geo?key=152dad0b6610679702298acaf0f7deef&address=";

                $yfurl = "https://restapi.amap.com/v5/place/around?key=152dad0b6610679702298acaf0f7deef&types=090601&radius=20&location=";

                $xqurl = "https://restapi.amap.com/v5/place/around?key=152dad0b6610679702298acaf0f7deef&types=120300|120301|120302|120303|120304&radius=200&location=";

                $squrl = "https://restapi.amap.com/v5/place/around?key=152dad0b6610679702298acaf0f7deef&types=060100|060101|060102&radius=500&location=";

                $addr_check = false;
                $shequ = false;
                $shangquan = false;
                $yfinfo=$shequinfo=$shangquaninfo='';

                $addr_res = $this->request_curl_get($jwdurl.$yf_addr);
                $addinfo = json_decode($addr_res, true);

                if ($addinfo['status'] == '1'){
                    $jwd = $addinfo['geocodes'][0]['location'];
                    $isyf_res = $this->request_curl_get($yfurl.$jwd);
                    $yfinfo = json_decode($isyf_res, true);
                    if ($yfinfo['status'] == '1'){
                        foreach($yfinfo['pois'] as $k=>$v){
                            if ($v['name'] == $yf_name) {
                                $addr_check = true;
                            }
                        }
                        if ($addr_check) {
                            $shequ_res = $this->request_curl_get($xqurl.$jwd);
                            $shequinfo = json_decode($shequ_res, true);
                            if ($shequinfo['status'] == '1' && count($shequinfo['pois']) > 0){
                                $shequ = true;
                            }
                            $shangquan_res = $this->request_curl_get($squrl.$jwd);
                            $shangquaninfo = json_decode($shangquan_res, true);
                            if ($shangquaninfo['status'] == '1' && count($shangquaninfo['pois']) > 0){
                                $shangquan = true;
                            }
                        }
                    }
                }
                $resmsg = '标注验证结果：';
                if ($addr_check){
                    $resmsg .= '<br/>地址：正确';
                    if ($shangquaninfo){
                        $resmsg .= '<br/>商圈店：是';
                    } else {
                        $resmsg .= '<br/>商圈店：否';
                    }
                    if ($shequ){
                        $resmsg .= '<br/>社区店：是';
                    } else {
                        $resmsg .= '<br/>社区店：否';
                    }
                } else {
                    $resmsg .= '<br/>地址：药店名称或药店地址错误';
                }
                $rs['rs_code'] = "success";
                $rs['rs_msg'] = ['jwd'=>$jwd, 'yfinfo'=>$yfinfo, 'shangquaninfo'=>$shangquaninfo, 'addr_check' => $addr_check, 'resmsg' => $resmsg];
                $rs['rs_backurl'] = '';
                die(json_encode($rs, JSON_UNESCAPED_UNICODE));
            }
        } else {
            $title = '药房 社区/商圈 属性检测';
            $data = ['title' => $title];

            $this->load->view('/demo/yf/index', $data);
        }
    }

    public function request_curl_get($url, $params = array(),$timeout=30){
        $ch = curl_init();
        curl_setopt ($ch, CURLOPT_URL, $url);
        curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, $timeout);

        $file_contents = curl_exec($ch);
        if($file_contents === false){
            throw new Exception('Http request message :'.curl_error($ch));
        }
        curl_close($ch);
        return $file_contents;
    }

    /**
     * 过滤请求数据
     * 去除左右空格
     * @param type $data
     */
    private function filter_data($data)
    {
        if (!is_array($data))
            return trim($data);

        array_walk($data, function (&$value) {
            $value = trim($value);
        });
        return $data;
    }

}
