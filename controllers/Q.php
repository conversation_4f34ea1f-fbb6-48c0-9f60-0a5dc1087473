<?php
/**
 * Created by PhpStorm.
 * User: AMY
 * Date: 2019-03-15
 * Time: 14:04
 */

class Q extends CI_Controller
{
    public $default_input;
    public function __construct()
    {
        parent::__construct();
        $this->default_input = [
            "single" => "radio",
            "multi" => "checkbox",
            "open" => "text",
        ];
//        error_reporting(-1);
//        ini_set('display_errors', 1);
    }

    //问卷开始
    public function s()
    {
//        var_dump(qf("quota1"));
//        die;
//        clear_session();
//        die;
        //问卷编号
        $session_survey_id = $this->session->userdata("survey_id");
        //问卷结果
        $session_respid = $this->session->userdata("respid");
        $survey_id = $this->uri->segment(3);
        $survey_id = (int)trim($survey_id);
        if (!$survey_id) {
            echo "问卷不存在！";
            die;
        }
        //查询问卷是否存在
        $q_info = getDataByConditionCi("app_questionnaire", " AND id=?", "*", true, [$survey_id]);
        if (!$q_info) {
            echo "问卷不存在！";
            die;
        }
        $check_survey_info = $this->add_field_to_q($survey_id);
        if (!$check_survey_info) {echo "数据表创建失败！";die;}

        $r = (int)trim($this->input->get("r"));
        $s = trim($this->input->get("s"));
        //检测问卷链接是否存在
        $s_link_info = getDataByConditionCi("q_link_{$survey_id}", " AND respid=? AND sid=?", "*", true, [$r, $s]);
        if (!$s_link_info) {
            echo "问卷链接无效！";
            die;
        }
        //检测问卷是否已完成
        $s_info = getDataByConditionCi("q_{$survey_id}", " AND respid=?", "*", true, [$r]);
        if ($s_info['finish_status'] != "") {
            echo "您已完成问卷，感谢您的参与！";
            die;
        }

        $respid = $s_link_info['respid'];
        if (!$session_respid) {
            $this->db->update("q_link_{$survey_id}", ["click_at"=>time()], ["respid"=>$respid]);
        }

        $post_data = $this->input->post();
        $error_info = "";
        $field_value = [$survey_id];
        if ($post_data) {//post提交的数据
            //当前问卷题
            $session_q = $this->session->userdata("now_q");
            $answer_q = $post_data[$session_q];
            if (!$answer_q) {
                $error_info = "请选择再提交！";
            } else {
                $default_error_info = "Please review your responses on this page. One or more questions require further input.";
                //获取账号信息，如果不限制需要登录账号密码的，随机分配一条记录存储入数据库中
                $question_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND id_json=?", "*", true, [$survey_id, $session_q]);
                $res_validation = true;//验证结果
                if ($question_info['validation_content']) {//存在验证编码
                    //请回答所有的问题. 请检查这一页中的所有问题的答案.
                    $this->res_check_script_content($question_info['validation_content']);
                    //获取错误信息
                    $err_msg = get_question_error_message("err1");
                    if ($err_msg) {
                        $error_info = $err_msg;
                        $res_validation = false;//验证不通过
                    }

                    //获取指令是否关闭顶部错误提示
                    $clear_default_err_msg = get_clear_error_message();
                    if ($clear_default_err_msg) {//关闭顶部错误提示
                        $default_error_info = "";
                    }
                }
                if ($res_validation) {//验证通过，存储session数据，进入下一题，否则报错误信息
                    $default_error_info = "";
                    session_answer_merge($session_q, $answer_q);
                }
            }
        }

        //session不存在，说明是第一次进入问卷，session与传输过来的题目不一致，说明已经换题目，需要重新种植session
        if (!$session_survey_id || $survey_id != $session_survey_id) {
            //查询问卷编号是否已存在
            $q_data_info = getDataByConditionCi("q_{$survey_id}", " AND respid=?", "*", true, [$respid]);
            //分配问卷链接
            if (!$q_data_info) {//不存在
                $this->db->insert("q_{$survey_id}",['respid'=>$respid,'created_at'=>date("Y-m-d H:i:s")]);
                $insert_id = $this->db->insert_id();
                if (!$insert_id) {
                    echo "分配链接失败！";
                    die;
                }
            }

            $survey_info = array(
                "survey_id" => $survey_id,
                "answer"=>array(),
                "respid"=>$respid
            );
            $this->session->set_userdata($survey_info);
        }


        //当前完成的题目
        $session_now_finish_q = $this->session->userdata("now_finish_q");
        if ($session_now_finish_q) {
            $cond = " AND id_json='{$session_now_finish_q}'";
            //通过上一题的答题结果，获取下一题的问卷题
            $now_finish_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=?{$cond}", "*", true, $field_value);
            if ($now_finish_info['logic_sign'] == 0) {//上一题是未被逻辑标记的题目
                $logic_cond = " AND logic_sign = 0";
            }
            $next_q = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=?  AND is_hidden=0 AND sort>'{$now_finish_info['sort']}' {$logic_cond} ORDER BY sort ASC LIMIT 1", "*", true, $field_value);
        } else {
            //通过上一题的答题结果，获取下一题的问卷题
            $next_q = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND is_hidden=0 ORDER BY sort ASC", "*", true, $field_value);
        }

        if (!$next_q) {
            redirect("/q/finish_redirect");
        }

        $is_next_btn = true;
        //当前题目为【Logic，script题】，需要处理这些code后，调取下一题，直到循环到正常的问卷题
        $next_q = $this->get_next_q($survey_id, $next_q);
        if (!is_array($next_q)) {//非数组,有报错信息
            $error_info = $next_q;
            $next_q = "";
        }else {
            //下一题是否存在
            $next_q_two = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND sort>'{$next_q['sort']}' ORDER BY sort ASC LIMIT 1", "*", true, $field_value);
            if (!$next_q_two) {//不存在，说明当前是最后一题
                $is_next_btn = false;
            }
            //当前题号，存入session中
            $this->session->set_userdata(["now_q"=>$next_q['id_json']]);
        }

        //上一题是否存在
        $s_answer = $this->session->userdata("answer");
        $answer_is_exist = false;
        if ($s_answer) {//非隐藏题
            $s_answer_keys = array_keys($s_answer);
            $pre_q_exist = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND is_hidden=0 AND question_type not in('Logic','script') AND id_json in('".implode("','",$s_answer_keys)."') ORDER BY sort ASC LIMIT 1", "*", true, $field_value);
            if ($pre_q_exist) {
                $answer_is_exist = true;
            }
        }
        $data = [
            "session_answer" => $this->session->userdata("answer"),
            "next_q" => $next_q,
            "q_option" => $next_q ? json_decode($next_q['options_json'], true) : [],
            "error_info" => $error_info,
            "default_error_info" => isset($default_error_info) ? $default_error_info : "",
            "is_next_btn" => $is_next_btn,
            "answer_is_exist" => $answer_is_exist,
        ];
        $this->load->view("/q/s", $data);
    }

    //完成重定向，用于没有设置的跳转
    public function finish_redirect()
    {
        $survey_id = $this->session->userdata("survey_id");
        $respid = $this->session->userdata("respid");

        //清除上一次的问卷结果
        clear_session();

        $this->db->update("q_{$survey_id}", ["finish_status"=>"complete", "finished_at"=>date("Y-m-d H:i:s")], ["id"=>$respid]);
        echo "感谢您的参与，您已完成所有问卷题！".'<a href="/q/s/'.$survey_id.'">返回</a>';
    }

    //代码语法是否正确检测
    public function check_php_code_correct($if_php_code)
    {
        //把if条件内容存储到临时文件中,检测语法是否正确
        $file_name = "./tmp/".time().".php";
        file_put_contents($file_name, "<?php".PHP_EOL.$if_php_code.";");
        $check_syntax = exec("php -l ".$file_name);
        if ($check_syntax == 'No syntax errors detected in '.$file_name) {
            //删除文件
            unlink($file_name);
            return true;
        } else {
            //删除文件
            unlink($file_name);
            return str_replace($file_name, "", $check_syntax);
        }
    }

    //检测if逻辑代码书写是否正确
    public function res_check_logic_if($if_php_code)
    {
        try {
            eval("\$info={$if_php_code};");
            $res_if_php = true;
        } catch (Throwable $t) {
            $res_if_php = false;
        }
        return $res_if_php;
    }

    //检测代码书写是否正确
    public function res_check_script_content($script_content)
    {
        try {
            $arr_script_content = explode("\n", $script_content);
            $rs_script_content = [];
            foreach ($arr_script_content as $v) {
                $v = trim($v);
                if (!(strpos($v, "//") === 0) && !(strpos($v, "/*") === 0) && !(strpos($v, "#") === 0)) {
                    $rs_script_content[] = $v;
                }
            }
            //把注释的代码行去掉再拼接代码验证代码有效性
            if ($rs_script_content) {
                $script_content = implode("", $rs_script_content);
                //报错的话，会抛异常
                eval("{$script_content}");
            }
            $res_script_content = true;
        } catch (Throwable $t) {
            //清除上一次的问卷结果
            clear_session();
            $res_script_content = false;
        }
        return $res_script_content;
    }

    //逻辑代码检测流程
    public function check_logic_if_code($if_php_code)
    {
        //执行代码编程语句
        eval("\$info={$if_php_code};");
        if ($info) {
            return true;
        } else {
            return false;
        }
    }

    //获取下一题
    public function get_next_q($survey_id, $next_q)
    {
        $next_q_info = [];
        $field_value = [$survey_id];
        if ($next_q['question_type'] == "Logic") {//逻辑题，需要检测逻辑代码,根据逻辑结果，决定往哪边执行
            if (!$next_q['if_content']) {
                return "if content is null";
            }

            if (!$next_q['else_content']) {
                return "else content is null";
            }
//            //代码书写是否正确,服务器不支持此检测方法
//            $check_php_code_correct = $this->check_php_code_correct($next_q['if_content']);
//            var_dump($check_php_code_correct);
//            if ($check_php_code_correct !== true) {//代码写法检测
//                return $check_php_code_correct;
//            }

            //eval内代码书写是否正确
            $res_check_logic_if = $this->res_check_logic_if($next_q['if_content']);
            if ($res_check_logic_if !== true) {//eval执行代码写法有误
                return "code syntax errors";
            }
            $check_logic_if_code = $this->check_logic_if_code($next_q['if_content']);
            switch($next_q['branche'])
            {
                case "If...Then":
                    if ($check_logic_if_code) {//符合if条件
                        $arr_else_content = explode(",",$next_q['else_content']);
                        //检测session里是否已经存在结果
                        foreach ($arr_else_content as $v_e_c) {
                            if ($this->session->userdata("answer")[$v_e_c]) {//已经存在
                                continue;
                            } else {
                                $next_q_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND id_json='{$v_e_c}'", "*", true, $field_value);
                                if ($next_q_info['question_type'] == "Logic") {
                                    $this->get_next_q($survey_id, $next_q_info);
                                }
                                break;
                            }
                        }
                    } else {//不符合if条件，if逻辑结束，进入下一题,调取未被逻辑题占用的题型
                        $next_q_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND sort>'{$next_q['sort']}' AND logic_sign=0 ORDER BY sort ASC LIMIT 1", "*", true, $field_value);
                        echo $this->db->last_query();
                    }
                    break;
                case "If...Then...else":
                    return "待开发";
                    break;

            }
        } else if($next_q['question_type'] == "script"){//script编程题
            //先赋值当前题型，否则，如果$next_q['script_content']没有内容，将导致死循环
            $this->session->set_userdata(["now_finish_q"=>$next_q['id_json']]);

            //eval内代码书写是否正确
            $res_check_script_content = $this->res_check_script_content($next_q['script_content']);
            if ($res_check_script_content !== true) {//eval执行代码写法有误,代码正确时，已经做了编程处理，直接调取下一题信息
                return "code syntax errors";
            }
            //当前完成的题目
            $session_now_finish_q = $this->session->userdata("now_finish_q");
            if ($session_now_finish_q) {
                $cond = " AND id_json='{$session_now_finish_q}'";
                //通过上一题的答题结果，获取下一题的问卷题
                $now_finish_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? {$cond}", "*", true, $field_value);
                $next_q_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=?  AND is_hidden=0 AND sort>'{$now_finish_info['sort']}' ORDER BY sort ASC LIMIT 1", "*", true, $field_value);
            } else {
                //通过上一题的答题结果，获取下一题的问卷题
                $next_q_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND is_hidden=0 ORDER BY sort ASC", "*", true, $field_value);
            }
            //继续获取下一题，直到得到真正需要显示的问卷题为止
            if ($next_q_info['question_type'] == "Logic" || $next_q_info['question_type'] == "script") {
                return $this->get_next_q($survey_id, $next_q_info);
            }
        }else{//非逻辑题
            $next_q_info = $next_q;
        }
        return $next_q_info ? $next_q_info : false;
    }

    //补充字段信息
    function add_field_to_q($survey_id)
    {
        if (!$survey_id) {return false;}
        $question_info = getDataByConditionCi(QUESTION_SETTING_TABLE, " AND surveyid=? AND question_type not in('Logic','script','Info')", "id_json", false, [$survey_id]);
        $q_info = [];
        if ($question_info) {
            foreach ($question_info as $v) {
                $q_info[] = $v['id_json'];
            }
        }
        //检测问卷链接表及问卷数据表是否存在
        $check_q_link = check_table_exist("q_link_{$survey_id}");
        $check_q = check_table_exist("q_{$survey_id}");
        //检查表是否存在
        if (!$check_q_link) {//不存在问卷链接表，生成
            $add_link_info = q_link($survey_id);
            if (!$add_link_info) {return false;}
        }

        if (!$check_q) {//不存在问卷数据表，生成
            $add_data_info = q_data($survey_id);
            if (!$add_data_info) {return false;}
        }

        //检查是否需要补充字段信息
        $get_columns = $this->db->query("show full columns from q_{$survey_id}")->result_array();
        $table_field = [];
        if ($get_columns) {
            foreach ($get_columns as $v) {
                $table_field[] = $v['Field'];
            }
        }
        $hav_field_add = array_diff($q_info, $table_field);
        $add_field_info = [];
        if ($hav_field_add) {
            $alert_table_info = "ALTER TABLE `q_{$survey_id}`";
            foreach ($hav_field_add as $v_field) {
                $add_field_info[] = "ADD COLUMN `{$v_field}` varchar(100) NOT NULL DEFAULT ''";
            }
            if ($add_field_info) {
                $alert_info = implode(",", $add_field_info);
                $alert_table_info = $alert_table_info." ".$alert_info;
                $this->db->query($alert_table_info);
            }
        }
        return true;
    }



}