<?php
/**
 * 调查通->江苏调研问卷点击记录
 * Created by PhpStorm.
 * User: AMY
 * Date: 2020-07-07
 * Time: 15:08
 */

class Sj extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    function g()
    {
//        if ($this->input->get("act", true) == "amy") {
//            unlink("./tmp/sj_g.txt");
//        }
        $ip = getip();
        $ip_address = ip2location($ip);
        $uid = $this->uri->segment(3);
        $click_time = date("Y-m-d H:i:s");
        file_put_contents("./tmp/sj_g.txt", "uid:{$uid}**ip:{$ip}**ip_address:{$ip_address}**"."add_time:{$click_time}".PHP_EOL, FILE_APPEND | LOCK_EX);
        $this->load->view('/sj/index.php');
    }
}
