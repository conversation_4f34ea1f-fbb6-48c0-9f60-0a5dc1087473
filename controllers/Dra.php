<?php

/**
 * 项目引流
 * 百度健康
 * 21-11-15
 */
use EasyWeChat\Factory;

class Dra extends MY_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('survey_model');
    }

    public function p() {
        $code = $this->input->get("st");
        $is_submit_account = $is_phone_type = $is_show_payment = false;
        if ($code) {
            $arr_code = explode("DRSAY", $code);
            if (count($arr_code) != 2) {
                redirect("/");
            }
            $encode_str = $arr_code[1];
            $st = $arr_code[0];

            //验证账号是否有误
            $arr_scene = explode("_", $encode_str);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $operat_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$operat_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                redirect("/");
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $operat_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                redirect("/");
            }
            $point = $filename = $scene = '';
            $payment_type = [];
        } else {

            $bk_code = $this->uri->segment(3);
            //验证账号是否有误
            $get_code_param = $this->dra_validity($bk_code, 1);
            if (!$get_code_param) {
                $this->survey_model->get_redirect_info("/dra/p", "l_errDRSAY" . $bk_code);
            }

            //支付信息
            $member_id = $get_code_param['member_id'];
            $operat_id = $get_code_param['operat_id'];
            $point = $get_code_param['point']; //获取支付金额
            $pid = $get_code_param['pid'];

            //支付前是否登录认证成功
            $auth_name = "drapay_auth_{$pid}_{$member_id}";
            if ($this->session->has_userdata($auth_name) === false) {
                $redirect_url = DRSAY_WEB . 'dra/dp_auth/' . $bk_code;
                redirect($redirect_url);
            }

            $config = [
                //            上医说
                'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
                'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
                'response_type' => 'array',
            ];

            $app = Factory::miniProgram($config);
            //临时二维码，由于小程序二维码参数最多只能传32位，因此要控制参数长度
            $scene = "drap_" . $member_id . '_' . $pid;
            $scene = $scene . "_" . substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
            $filename = "";
            if ($point >= 1) {
                $response = $app->app_code->getUnlimit($scene, [
                    //医map页面
                    //                'page'  => 'pages/queryScene/queryScene',
                    //上医说页面
                    'page' => 'pages/drsayExchange/drsayExchange',
                    'width' => 50,
                ]);
                // 或
                if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                    if (!is_dir("./uploads/wechat/drap/{$pid}/")) {
                        mk_dir("./uploads/wechat/drap/{$pid}/", 0777, true);
                    }
                    $filename = $response->saveAs("./uploads/wechat/drap/{$pid}/", $scene . '.png');
                } else {
                    echo "error_code:{$response['errcode']}";
                    die;
                }
            }
            $user_payment_info = []; //[206,8779];
            if ($member_id) {
                //通过会员编号，查询用户的支付宝账号及微信账号信息
                //查询用户的支付账号
                $payment_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type in(" . EXCHANGE_ALIPAY . ", " . EXCHANGE_WEBCHAT_AUTO . ", " . EXCHANGE_MOBILE . ") ORDER BY id ASC", [$member_uid])->result_array();
                if ($payment_info) {
                    foreach ($payment_info as $v_payment) {
                        $user_payment_info[$v_payment['payment_type']] = $v_payment;
                        if ($v_payment['is_default'] == 1) {
                            $lock_account = $v_payment['payment_type']; //默认支付宝
                        }
                    }
                }
            }

            $st = "c";
            $is_submit_account = true;
            $is_show_payment = true;
        }
        switch ($st) {
            case 'c': //返回状态为c的成功样本
                $st_msg_info = '';
                $st_msg = '<p>' . $st_msg_info . '</p>
            <img src="/theme/go/image/01.png" alt="">';
                break;
            case 'l_err': //支付链接错误不存在
                $st_msg = '<p>请求支付链接错误，请联系客服</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'finish': //项目结束
                $st_msg = '<p>项目已结束，感谢您的参与。</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'payment_err': //提现链接有误
                $st_msg = '<p class="tips">链接无效</p><p>支付流程非常严格,请按常规操作!</p>
            <img src="/theme/go/image/03.png" alt="">';
                break;
            case 'p_s': //礼金支付成功【即时支付】
                $st_msg = '<p class="tips">礼金支付成功</p>
                <p>请查看收款账户进行确认</p>
                <img src="/theme/go/image/01.png" alt="">';
                break;
            case 'p_s_b': //申请礼金成功【银行】
                $st_msg = '<p class="tips">申请礼金成功</p>
                <p>选择银行收款7个工作日到账</p>
                <img src="/theme/go/image/01.png" alt="">';
                break;
            case 'p_d': //操作过于频繁
                $st_msg = '<p class="tips">操作过于频繁</p>
                <p>请耐心等待,2分钟后重新操作!</p>
                <img src="/theme/go/image/03.png" alt="">';
                break;
            case 'p_f': //付款失败
                $st_msg = '<p class="tips">付款失败</p>
                <!--<p>收款账户信息有误</p>-->
                <p style="color:red;">非本人认证账户需重新更换账户</p>
                <p>请点击按钮重新设置</p>
                <img src="/theme/go/image/02.png" alt="">';
                break;
            default: //除以上之外的告警
                $st_msg = '非常感谢你参与本次参与，请继续支持我们！';
                break;
        }

        $data = array(
            "st_msg" => $st_msg,
            "scene" => $scene,
            "filename" => $filename,
            "point" => $point,
            'user_payment_info' => $user_payment_info,
            'lock_account' => $lock_account,
            "st" => $st,
            "pid" => $pid,
            "is_submit_account" => $is_submit_account,
            "is_phone_type" => $is_phone_type,
            "is_phone_val" => '', "gooddr_code" => '', "project_info" => '',
            "is_show_payment" => $is_show_payment,
            "bk_code" => $bk_code,
        );
//        print_r($data);
//        die;
        if ($code) {
            $this->load->view("/dra/to_p_finish.php", $data);
        } else {
            $this->load->view("/dra/to_p.php", $data);
        }
    }

    /**
     * 参数认证
     * $bk_code 认证参数
     * $pay_type 支付类型【1=>项目支付，2-推荐支付】
     */
    private function dra_validity($bk_code, $pay_type = 1) {
        if (!$bk_code) {
            return false;
        }
        $partner_paran = explode('_', $bk_code);
        $pid = $partner_paran[0];
        $mem_id = $partner_paran[1];
        $operat_id = $partner_paran[2];
        $old_certification = $partner_paran[3];
        $new_certification = substr(md5($pid . '_' . $mem_id . '_' . $operat_id . PROJECT_ENCODE_KEY), 8, 16);
        if (!$mem_id || !$operat_id || $old_certification !== $new_certification) {
            return false;
        }
        //检测-(pay_status-0 未支付)
        $where = ['type' => $pay_type, 'pid' => $pid, 'dr_id' => $mem_id, 'operat_id' => $operat_id];
        $check_payment = $this->db->select('id,source_code,payment_amount,dr_mobile,pay_status')->where($where)
                        ->get('mb_drainage_payment', 1)->row_array();
        //不存在支付申请记录
        if (empty($check_payment)) {
            return false;
        } elseif ($check_payment['pay_status'] == 1) {
            $this->survey_model->get_redirect_info("/dra/p", "p_sDRSAY" . $bk_code);
        }

        //查询是否存在项目记录
        $drainage_project = $this->db->select('id,project_table')
                        ->where(['id' => $check_payment['source_code'], 'status' => 1, 'is_delete' => 1])
                        ->get('mb_drainage_project', 1)->row_array();
        if (empty($drainage_project)) {
            return false;
        }

        //查询是否存在项目明细记录
        $dpi_where = [
            'data_status' => 1, 'verify_status' => 2, 'pay_status' => 2,
            'source_code' => $drainage_project['id'], 'pid' => $pid,
            'id' => $operat_id, 'dr_id' => $mem_id
        ];
        $drainage_fields = 'id,province,city,unit_name,unit_level,department,dr_job_title';
        $drainage_project_info = $this->db->select($drainage_fields)->where($dpi_where)
                        ->get($drainage_project['project_table'], 1)->row_array();
        if (empty($drainage_project_info)) {
            return false;
        }

        $point = $check_payment['payment_amount'];
        //实际金额小于1
        if ($point < 1) {
            return false;
        }
        $res_ary = [
            'point' => $point, 'pid' => $pid, 'member_id' => $mem_id, 'operat_id' => $operat_id,
            'dr_mobile' => $check_payment['dr_mobile'], 'drainage_project_info' => $drainage_project_info,
            'execution_details_table' => $drainage_project['project_table']
        ];
        return $res_ary;
    }

    //身份证+姓名，二要素信息有效性验证
    protected function check_name_id_card($name, $id_card, $source = 0) {
        $this->load->file(BASEPATH . 'libraries/Third_party/253_drsay_invite/Two_elements_api.php');
        $two_elements = new Two_elements_api();
        $result = $two_elements->two_elements($name, $id_card, $source);
        return $result;
    }

    /**
     * 提现短信验证
     * 核验信息
     */
    public function dp_auth() {
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            $bk_code = $post_data['bk_code'];
            $verify_mobile = $post_data['verify_mobile'];
            //验证数据有效性
            $verify_code = $post_data['verify_code'];

            try {
                $get_code_param = $this->dra_validity($bk_code, 1);
                if (!$get_code_param) {
                    throw new Exception("参数有误！");
                }
                $pid = $get_code_param['pid'];
                $member_uid = (int) trim($get_code_param['member_id']);
                $operat_id = $get_code_param['operat_id'];
                $dr_mobile = $get_code_param['dr_mobile'];

                if ($member_uid <= 0) {
                    throw new Exception("用户不存在，请联系客服处理！");
                }

                if (!check_mobile($dr_mobile)) {
                    throw new Exception("手机号有误，请确认后重新输入！[项目]");
                }
                if ($dr_mobile != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $member_info = $this->db->query("SELECT id FROM app_project_payment_confirm_user_info WHERE member_uid=? LIMIT 1", [$member_uid])->row_array();
                $project_evaluation = isset($post_data['project_evaluation']) ? $post_data['project_evaluation'] : ''; //项目评价
                if (!$member_info) {//不存在记录，添加入库
                    $province = isset($post_data['province']) ? $post_data['province'] : '';
                    $city = isset($post_data['city']) ? $post_data['city'] : '';
                    $dr_name = isset($post_data['dr_name']) ? $post_data['dr_name'] : '';
                    $unit_name = isset($post_data['unit_name']) ? $post_data['unit_name'] : '';
                    $indentity_code = isset($post_data['indentity_code']) ? $post_data['indentity_code'] : '';
                    $department = $post_data['department'];
                    if (empty($province)) {
                        _back_msg("error", "请选择省份！");
                    }
                    $area_id = $province;
                    if (empty($city)) {
                        _back_msg("error", "请选择城市！");
                    }
                    $area_id = $area_id . "->" . $city;
                    //通过省份城市区域获取对应的文字信息
                    $area_id = DIC_COUNTRY_CHINA_ID . "->" . $area_id;
                    $area_info = $this->db->query("SELECT id,area_parent_name FROM app_sys_dictionary WHERE big_class_id='" . APP_SYS_BIG_CLASS_AREA . "' AND area_parent_id=?", [$area_id])->row_array();
                    if (!$area_info) {
                        _back_msg("error", "地区信息选择有误，请重新选择！");
                    }
                    $area_info_content = $area_info['area_parent_name'];
                    $arr_area_info = explode("->", $area_info_content);
                    if (!$dr_name) {
                        _back_msg("error", "请输入姓名！");
                    }
                    if (!$unit_name) {
                        _back_msg("error", "请输入医院！");
                    }
                    if (!$indentity_code) {
                        _back_msg("error", "请输入身份证号！");
                    }
                    if (strlen($indentity_code) != 18 && strlen($indentity_code) != 15) {
                        _back_msg("error", "身份证号格式错误,请重新输入！");
                    }
                    if (!$department) {
                        _back_msg("error", "请输入科室！");
                    }
                    if (!$verify_code) {
                        _back_msg("error", "请输入验证码！");
                    }
                    $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_EXCHANGE);
                    if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                        _back_msg("error", '验证码错误！请重新输入！');
                    }

                    //身份证+姓名有效性验证
                    if ($_SERVER['HTTP_HOST'] != "dev.drsay.cn") {//非本地
                        $check_name_id_card = $this->check_name_id_card($dr_name, $indentity_code, SYS_TWO_ELEMENTS_SOURCE_DRAINAGE_PRO);
                        if ($check_name_id_card !== SYS_TWO_ELEMENTS_RESULT_AGREEMENT) {
                            _back_msg("error", "身份证与姓名不匹配，请确认后重新输入！");
                        }
                    }

                    $time = time();
                    //根据身份证号码查询医师表是否存在记录
                    $insert_data = [
                        "province" => isset($arr_area_info[1]) && $arr_area_info[1] ? $arr_area_info[1] : "",
                        "city" => isset($arr_area_info[2]) && $arr_area_info[2] ? $arr_area_info[2] : "",
                        "member_uid" => $member_uid,
                        "name" => $dr_name,
                        "unit_name" => $unit_name,
                        "id_card" => $indentity_code,
                        "mobile" => $verify_mobile,
                        "department" => $department,
                        "add_time" => $time,
                        'data_source' => 2
                    ];
                    $local_ip = getip();
                    $ip = ip2long($local_ip);
                    $ip_addr = ip2location($ip);
                    $insert_data_log = [
                        "pid" => $pid ? $pid : "",
                        "pid_id" => $operat_id ? $operat_id : 0,
                        "province" => isset($arr_area_info[1]) && $arr_area_info[1] ? $arr_area_info[1] : "",
                        "city" => isset($arr_area_info[2]) && $arr_area_info[2] ? $arr_area_info[2] : "",
                        "member_uid" => $member_uid,
                        "name" => $dr_name,
                        "unit_name" => $unit_name,
                        "id_card" => $indentity_code,
                        "mobile" => $verify_mobile,
                        "department" => $department,
                        "add_time" => $time,
                        "ip" => $local_ip,
                        "ip_address" => $ip_addr,
                        "project_evaluation" => $project_evaluation,
                        'data_source' => 2
                    ];

                    //开启事物
                    $this->db->trans_begin();
                    $this->db->insert("app_project_payment_confirm_user_info", $insert_data);
                    $this->db->insert("app_project_payment_confirm_log", $insert_data_log);
                    if ($this->db->trans_status() === FALSE) {
                        $this->db->trans_rollback();
                        throw new Exception("提交失败！");
                    } else {
                        $this->db->trans_commit();
                    }
                } else {
                    if (!$verify_code) {
                        _back_msg("error", "请输入验证码！");
                    }
                    $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_EXCHANGE);
                    if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                        _back_msg("error", '验证码错误！请重新输入！');
                    }

                    $project_payment_log = $this->db->query("SELECT id FROM app_project_payment_confirm_log WHERE member_uid=? AND pid>0 AND pid=? LIMIT 1", [$member_uid, $pid])->row_array();
                    if (!$project_payment_log) {
                        $local_ip = getip();
                        $ip = ip2long($local_ip);
                        $ip_addr = ip2location($ip);
                        $doctor_province = $member_info['province'];
                        $doctor_city = $member_info['city'];
                        $doctor_name = $member_info['name'];
                        $doctor_unit_name = $member_info['unit_name'];
                        $doctor_id_card = $member_info['id_card'];
                        $doctor_department = $member_info['department'];
                        $insert_data_log = [
                            "pid" => $pid ? $pid : "",
                            "pid_id" => $operat_id ? $operat_id : 0,
                            "province" => $doctor_province ? $doctor_province : "",
                            "city" => $doctor_city ? $doctor_city : "",
                            "member_uid" => $member_uid,
                            "name" => $doctor_name ? $doctor_name : "",
                            "unit_name" => $doctor_unit_name ? $doctor_unit_name : "",
                            "id_card" => $doctor_id_card ? $doctor_id_card : "",
                            "mobile" => $verify_mobile,
                            "department" => $doctor_department ? $doctor_department : "",
                            "add_time" => time(),
                            "ip" => $local_ip,
                            "ip_address" => $ip_addr,
                            "project_evaluation" => $project_evaluation,
                        ];
                        $res_data = $this->db->insert("app_project_payment_confirm_log", $insert_data_log);
                        if (!$res_data) {
                            throw new Exception("提交失败[e_l]！");
                        }
                    } else {
                        $res_up = $this->db->update("app_project_payment_confirm_log", ["project_evaluation" => $project_evaluation], ["id" => $project_payment_log['id']]);
                        if (!$res_up) {
                            throw new Exception("提交失败[e_l_u]！");
                        }
                    }
                }

                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                //查询订单状态
                $redirect_url = "/";
                $order_info = $this->db->query("SELECT id,order_status,status FROM app_payment_order_new WHERE log_code=? AND pid=? AND pid_id=? AND uid=? LIMIT 1", [ORDER_SOURCE_STATUS_DRAINAGE, $pid, $operat_id, $member_uid])->row_array();
                if ($order_info) {//已经存在订单
                    if ($order_info['order_status'] != 1) {//不是有效订单,跳到链接无效
                        $redirect_url = $this->survey_model->get_redirect_info("/dra/p", "payment_err", "", "", "", false);
                    }
                    if ($order_info['status'] == 2) {//已完成
                        $redirect_url = $this->survey_model->get_redirect_info("/dra/p", "p_s", "", "", "", false);
                    } else {
                        //存session，方便从短信或者问卷链接进入时，直接跳过验证
                        $auth_name = "drapay_auth_{$pid}_{$member_uid}";
                        $this->session->set_userdata([$auth_name => $member_uid]);
                        $redirect_url = $redirect_url = "/dra/p/{$bk_code}";
                    }
                } else {
                    //存session，方便从短信或者问卷链接进入时，直接跳过验证
                    $auth_name = "drapay_auth_{$pid}_{$member_uid}";
                    $this->session->set_userdata([$auth_name => $member_uid]);
                    $redirect_url = "/dra/p/{$bk_code}";
                }
                _back_msg("success", "验证通过", $redirect_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $bk_code = $this->uri->segment(3);

            //验证账号是否有误
            $get_code_param = $this->dra_validity($bk_code, 1);
            if (!$get_code_param) {
                $this->survey_model->get_redirect_info("/dra/p", "l_err");
            }
            $pid = $get_code_param['pid'];
            $member_uid = $get_code_param['member_id'];
            $operat_id = $get_code_param['operat_id'];
            $imp_mobile = $get_code_param['dr_mobile']; //执行表里的医师手机号码

            $order_where = ['log_code' => ORDER_SOURCE_STATUS_DRAINAGE, 'pid' => $pid, 'pid_id' => $operat_id, 'uid' => $member_uid];
            $order_info = $this->db->select('id,log_code,pid,pid_id,status')->where($order_where)->get('app_payment_order_new', 1)->row_array();
            if ($order_info) {//已经存在订单
                if ($order_info['status'] == 2) {//已完成
                    $redirect_url = $this->survey_model->get_redirect_info("/dra/p", "p_s", "", "", "", false);
                    redirect($redirect_url);
                }
            }

            $auth_name = "drapay_auth_{$pid}_{$member_uid}";
            $session_bk_exchange = $this->session->userdata($auth_name);
            if ($session_bk_exchange && $session_bk_exchange == $member_uid) {
                redirect("/dra/p/{$bk_code}");
            }

            $member_info = $this->db->query("SELECT * FROM app_project_payment_confirm_user_info WHERE member_uid=? LIMIT 1", [$member_uid])->row_array();

            //获取省份信息
            $province_list = $this->db->query("SELECT * FROM app_sys_dictionary WHERE big_class_id='" . APP_SYS_BIG_CLASS_AREA . "' AND `level`='" . AREA_LEVEL_PROVINCE . "' AND area_parent_id LIKE '68_%'")->result_array();

            $project_payment_log = $this->db->query("SELECT * FROM app_project_payment_confirm_log WHERE member_uid=? AND pid>0 AND pid=? LIMIT 1", [$member_uid, $pid])->row_array();

            $data = [
                "province_list" => array_column($province_list, "val", "id"),
                "bk_code" => $bk_code,
                "member_info" => $member_info,
                "imp_mobile" => $imp_mobile,
                "project_payment_log" => $project_payment_log,
            ];
            //print_r($data);die;

            $this->load->view("/dra/dp_auth", $data);
        }
    }

    /**
     * 确认短信发送
     * @throws Exception
     */
    public function pro_send_sms() {
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $bk_code = $post_data['bk_code'];
            $verify_mobile = $post_data['verify_mobile'];
            try {
                $country = 68;
                $get_code_param = $this->dra_validity($bk_code, 1);
                if (!$get_code_param) {
                    throw new Exception("参数有误！");
                }
                $pid = $get_code_param['pid'];
                $member_uid = $get_code_param['member_id'];
                $operat_id = $get_code_param['operat_id'];
                $dr_mobile = $get_code_param['dr_mobile'];

                //查询是否已经做过提现操作
                $payment_info = getDataByConditionCi("app_payment_order_new", " AND log_code='" . ORDER_SOURCE_STATUS_DRAINAGE . "' AND pid=? AND uid=? AND pid_id=?", "*", true, [$pid, $member_uid, $operat_id]);
                if ($payment_info && $payment_info['status'] == 2) {//订单已完成
                    throw new Exception("已存在提现记录，不能重复提现！");
                }
                if (!check_mobile($dr_mobile)) {
                    throw new Exception("手机号有误，请确认后重新输入！[项目]");
                }
                if ($dr_mobile != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入");
                }

                //发送短信验证码
                $vcode = rand(10000, 99999);
                $sms_log_code = SMS_LOG_CODE_DRAINAGE_PRO;
                $err_msg = "";
                if ($country == '68') {//中国短信，使用send_cloud发送
                    //创蓝
                    if ($_SERVER['HTTP_HOST'] == "dev.drsay.cn") {//本地
                        $st = true;
                    } else {
                        $sms_content = "申请支付验证码{$vcode}，有效时间是" . SMS_ACTIVE_TIME . "分钟。"; //国际短信通道使用
                        $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                    }
                    if ($st === true) {
                        $send_st = true;
                    } else {
                        $send_st = false;
                        $err_msg = $st;
                    }
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_EXCHANGE;
                if ($send_st) {//短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string) $type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);
                    if ($insert_id_sms_code > 0) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    // 保存验证码数据
    private function set_vcode($data) {
        if (!$data) {
            return false;
        }
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }

    //检测用户是否已经兑换成功
    public function check_order_info() {
        $scene = $this->input->post("scene", true);
        if (!$scene) {
            _back_msg("error", "无效");
        }
        $arr_scene = explode("_", $scene);
        $member_uid = $arr_scene[1];
        $p_id = $arr_scene[2];
        $encrypted_data = $arr_scene[3];
        if (!$p_id || !$member_uid || !$encrypted_data) {//参数有误
            _back_msg("error", "参数有误");
        }
        $decrypt_scene = "drap_" . $member_uid . '_' . $p_id;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            _back_msg("error", "参数有误");
        }
        //通过member_uid查询账号表里记录信息,查询微信唯一码是否存在
        $account_info = $this->db->query("SELECT id FROM app_ex_payment_account WHERE uid=? AND payment_type=? LIMIT 1", [$member_uid, EXCHANGE_WEBCHAT_AUTO])->row_array();
        if ($account_info) {
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }

    /**
     * @apiDescription 支付提交
     * @apiParam {string} scene （必填）提交验证加密串
     * @apiParam {string} bk_code （必填）提交验证加密串
     * @apiParam {int} payment_type （必填）提现方式
     * @apiParam {string} payment_name （必填）支付宝姓名 payment_type=206必填
     * @apiParam {string} payment_account （必填）支付宝账号 payment_type=206必填
     */
    public function payment_sub() {
        try {
            $post_data = $this->input->post();
            $post_data = format_post_data($post_data);
            $scene = $post_data['scene'];
            $privacy = (int) $post_data['privacy']; //隐私协议
            if ($privacy !== 1) {
                _back_msg("error", "请阅读并同意《个税代缴代付协议》！");
            }

            $payment_type = $post_data['payment_type'];
            $payment_type = $payment_type ? (int) $payment_type : "";
            if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
                throw new Exception("支付方式选择有误！");
            }

            $bk_code = $post_data['bk_code'];
            //验证账号是否有误
            $get_code_param = $this->dra_validity($bk_code, 1);
//            print_r($get_code_param);
//            die;
            if (!$get_code_param) {
                throw new Exception("请求失败，参数有误，请稍后重新操作！");
            }

            //支付信息
            $member_id = $get_code_param['member_id'];
            $operat_id = $get_code_param['operat_id'];
            $point = $get_code_param['point']; //获取支付金额
            $pid = $get_code_param['pid'];

            //支付前是否登录认证成功
            $auth_name = "drapay_auth_{$pid}_{$member_id}";
            $sess_member_id = $this->session->userdata[$auth_name];
            if ($this->session->has_userdata($auth_name) === false || $sess_member_id != $member_id) {
                $redirect_url = DRSAY_WEB . 'dra/dp_auth/' . $bk_code;
                redirect($redirect_url);
            }

            if (!is_dir("./tmp/exchange/")) {
                mk_dir("./tmp/exchange/", 0777, true);
            }

            //兑换日志
            //设备信息
            $this->load->library('user_agent');
            $platform = $this->agent->platform();
            $browser = $this->agent->browser();
            $browser_version = $this->agent->version();
            $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
            $device_info = [
                "device_type" => $platform,
                "http_user_agent" => $http_user_agent, //设备信息
                "browser" => $browser,
                "browser_version" => $browser_version,
            ];
            $device_info = array_merge($device_info, $post_data);
            file_put_contents("./tmp/exchange/api_exchange_new_" . date("Y") . "_" . date("m") . ".txt", "start:" . date("Y-m-d H:i:s") . "**" . json_encode($device_info, JSON_UNESCAPED_UNICODE) . PHP_EOL . PHP_EOL, FILE_APPEND | LOCK_EX);

            //事务开始
            $this->db->trans_start();
            //加锁处理
            if (Redis_lock::getInstance()->lockNoWait("drap_payment_sub_{$member_id}", 120) !== true) {
                //错误提示
                throw new Exception("请不要重复提交！");
            }

            if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信支付
                $payment_name = $post_data['wx_payment_name'];
            } else if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                $payment_name = $post_data['alipay_payment_name'];
                $payment_account = $post_data['alipay_payment_account'];
            }

            //检测提现账号是否正确
            $check_account = getDataByConditionCi("app_ex_payment_account", " AND uid=? AND payment_type=?", "*", true, [$member_id, $payment_type]);
            if (!$check_account) {//账号不存在，入库
                if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                    if (!$payment_name || !$payment_account) {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                    if ($payment_name == 'undefined' || $payment_account == 'undefined') {
                        throw new Exception("请提交支付宝名称及支付宝账号！");
                    }
                }
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信自动打款
                    throw new Exception("微信零钱收款，请先扫码！");
                }
                $insert_account_data = [
                    "uid" => $member_id,
                    "payment_type" => $payment_type,
                    "payment_name" => $payment_name ? $payment_name : ($name ? $name : ""), //支付名称不存在，用明细表的姓名填充
                    "payment_account" => $payment_account,
                    "add_time" => time(),
                ];
                $payment_name = $payment_name ? $payment_name : ($name ? $name : "");
                $this->db->insert("app_ex_payment_account", $insert_account_data);
                $payment_account_id = $this->db->insert_id();
                if (!$payment_account_id) {
                    file_put_contents("./tmp/fail_bk_exchange.txt", "支付账号创建失败：" . $this->db->last_query() . "**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
                    throw new Exception("支付账号存储失败！");
                }
            } else {//账号已存在,获取当前最新的记录进行更新
                //账号信息从表单中来,不过后面需要按照查询出来的编号进行更新
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信提现的，微信openid从表里来
                    $payment_account = $check_account['payment_account'];
                }
                $payment_account_id = $check_account['id'];
                $this->db->update("app_ex_payment_account", ["payment_name" => $payment_name, "payment_account" => $payment_account], ["id" => $payment_account_id]);
            }
            
            if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                if (!$payment_name || !$payment_account) {
                    _back_msg("error", "请输入【支付宝用户名】、【支付宝账号】");
                }
            }
            if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信
                if (!$payment_name) {
                    _back_msg("error", "请输入【微信用户名】");
                }
                if (!$payment_account) {
                    _back_msg("error", "您选择转账到微信零钱，请扫码授权再提交");
                }
            }

            //是否存在项目
            $pro_info = $this->db->query("SELECT * FROM app_project WHERE id=?", [$pid])->row_array();
            if (!$pro_info) {
                throw new Exception("项目不存在！");
            }

            //获取用户信息
            $member_info = getDataByConditionCi("app_member", " AND id=?", "*", true, [$member_id]);
            if (!$member_info) {
                throw new Exception("用户不存在，记录有误！");
            }
            $country = $member_info['country']; //会员所在国家
            $certif_id = $member_info['indentity_code']; //身份证号
            $ip = getip();

            $order_where = ['log_code' => ORDER_SOURCE_STATUS_DRAINAGE, 'pid' => $pid, 'pid_id' => $operat_id, 'uid' => $member_id, 'order_status' => 1];
            $order_info = $this->db->select('id,log_code,pid,pid_id,status')->where($order_where)->get('app_payment_order_new', 1)->row_array();
            if ($order_info) {
                throw new Exception("您已申请过支付，不能重复操作！");
            }

            //获取支付详情记录
            $pay_type = 1; //支付类型：1项目    2推荐
            $where = ['type' => $pay_type, 'pid' => $pid, 'dr_id' => $member_id, 'operat_id' => $operat_id];
            $payment_fields = 'id,operat_id,dr_id,dr_mobile,payment_amount,dr_name,dr_mobile';
            $payment_info = $this->db->select($payment_fields)->where($where)
                            ->get('mb_drainage_payment', 1)->row_array();

            //支付记录信息
            $drainage_project_info = isset($get_code_param['drainage_project_info']) ? $get_code_param['drainage_project_info'] : '';

            $pay_order_data = [
                "log_code" => ORDER_SOURCE_STATUS_DRAINAGE,
                "param" => $payment_info['id'],
                "uid" => $payment_info['dr_id'],
                "pid" => $pid,
                "pid_id" => $payment_info['operat_id'],
                "payment_type" => $payment_type,
                "payment_account" => $payment_account,
                "payment_name" => $payment_name,
                "currency" => 130, //站点积分币种单位，默认RMB
                "pay_currency" => 130, //字典表ID，实际支付的币种单位，默认RMB
                "exchange_point" => $payment_info['payment_amount'] * 100, //兑换积分
                "exchange_amount" => $payment_info['payment_amount'], //会员兑换金额
                "pay_exchange_amount" => $payment_info['payment_amount'], //实际支付金额，推荐奖励
                "pay_rmb_exchange_amount" => $payment_info['payment_amount'], //实际支付的人民币金额，推荐奖励
                "examine_type" => 2, //默认财务已审核待支付
                "pay_exchange_rate" => 1, //当前支付货币转换汇率，人民币汇率
                "pay_rmb_exchange_rate" => 1, //当前支付货币转换的人民币汇率，人民币汇率
                "add_time" => time(),
                "adder_ip" => ip2long($ip),
                "adder_address" => ip2location($ip),
                "id_card" => $certif_id,
                "name" => $payment_info['dr_name'] ?? '',
                "mobile" => $payment_info['dr_mobile'] ?? '',
                "province" => $drainage_project_info['province'] ?? '',
                "city" => $drainage_project_info['city'] ?? '',
                "unit_name" => $drainage_project_info['unit_name'] ?? '',
                "unit_level" => $drainage_project_info['unit_level'] ?? '',
                "department" => $drainage_project_info['department'] ?? '',
                "job_title" => $drainage_project_info['dr_job_title'] ?? '',
                "status" => 1, //推荐奖励，需要系统支付
            ];
//            print_r($pay_order_data);
//            die;
            $res_order = $this->db->insert("app_payment_order_new", $pay_order_data);
            $order_insid = $this->db->insert_id();
            if (!$order_insid) {
                throw new Exception("操作异常，请稍后重试【支付订单异常】!");
            }

            //更新支付信息状态
            $up_paystatus = $this->db->where(['id' => $payment_info['id']])->update('mb_drainage_payment', ['pay_status' => 1, 'pay_time' => time()]);
            if (!$up_paystatus) {
                throw new Exception("操作异常，请稍后重试【引流支付订单异常】!");
            }

            //更新执行明细状态-execution_details_table 表名
            $execution_details_table = $get_code_param['execution_details_table'];
            $up_execution_details = $this->db->where(['id' => $payment_info['operat_id']])->update($execution_details_table, ['pay_status' => 1]);
            if (!$up_execution_details) {
                throw new Exception("操作异常，请稍后重试【引流支付明细状态异常】!");
            }
            //事务结束
            $this->db->trans_complete();

            //删除临时图片
            if (file_exists("./uploads/wechat/drap/{$pid}/{$scene}.png")) {
                unlink("./uploads/wechat/drap/{$pid}/{$scene}.png");
            }
            //支付成功删除支付认证标示
            $this->session->unset_userdata($auth_name);

            _back_msg("success", "");
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }

}
