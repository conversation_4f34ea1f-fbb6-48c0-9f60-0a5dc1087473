<?php


class Train_course extends CI_Controller
{
    const TRAIN_ANSWER_TABLE = 'drsay_train_answer_';
    const TRAIN_PROJECY_STATUS_EXAM = 1;
    const TRAIN_PROJECY_STATUS_ANSWER = 2;
    public $table_name = "";
    function __construct(){
        parent::__construct();
        $this->table_name = self::TRAIN_ANSWER_TABLE;
    }

    public function test(){
        $pid = 20;
        $survey_uid = "gfuwffs";
        $str = $pid . "_" . $survey_uid;
        $encode_str = substr(md5($str. PROJECT_ENCODE_KEY), 8, 16);
        $data = [
            'pid' => $pid,
            'survey_uid' => $survey_uid,
            'encode_str' => $encode_str,
        ];
        // 加密
        $res = urlencode(base64_encode(openssl_encrypt($str . '_' . $encode_str, 'DES-EDE3', REVERSIBLE_ENCODE_KEY, OPENSSL_RAW_DATA)));
        // 解密
        // $member_id = openssl_decrypt(base64_decode(urldecode($res)), 'DES-EDE3', REVERSIBLE_ENCODE_KEY, OPENSSL_RAW_DATA);
        echo $res;
    }

    // 查看课件
    // 使用链接 http://www.drsay.cn/train_course/get_course?code=wYYqz3%2BFv6%2Fb6RLhx%2FL0%2BHsMwgJMR7vaphYKwWgy1bU%3D
    public function get_course(){
        $param = $this->input->get('code', true);
        if (!$param) {
            echo "param error";
            die;
        }
        // 解密
        $param = urlencode($param);
        $res = openssl_decrypt(base64_decode(urldecode($param)), 'DES-EDE3', REVERSIBLE_ENCODE_KEY, OPENSSL_RAW_DATA);
        if (!$res) {
            echo "param error";
            die;
        }
        // 校验
        $arr = explode("_", $res);
        $drsay_pid = isset($arr[0]) ? $arr[0] : '';
        $survey_uid = isset($arr[1]) ? $arr[1] : '';
        $encode_str = isset($arr[2]) ? $arr[2] : '';
        if (!$drsay_pid || !$survey_uid || !$encode_str) {
            echo "param error";
            die;
        }
        $str = $drsay_pid . "_" . $survey_uid;
        $check_encode_str = substr(md5($str. PROJECT_ENCODE_KEY), 8, 16);
        if (strcmp($encode_str, $check_encode_str) != 0) {
            echo "param error";
            die;
        }
        // 查询对应的项目信息
        $p_info = $this->get_train_project($drsay_pid, $survey_uid);
        if (!$p_info) {
            echo "param error";
            die;
        }
        if ($p_info['status'] == self::TRAIN_PROJECY_STATUS_ANSWER) {
            $this->exam_answer($p_info);
        }else {
            // 更新项目表为在答状态
            $this->db->where(['id' => $p_info['id'], 'status' => 0])->update('drsay_train_project', ['status' => self::TRAIN_PROJECY_STATUS_EXAM, 'update_time' => time()]);
            $this->exam($p_info);
        }
    }

    // 显示课件题目
    public function exam($p_info){
        $pid = $p_info['id'];
        // 查询项目-题目
        $answer_table = $this->get_train_answer_table($p_info['add_year']);
        if (!$answer_table) {
            echo "table not exists";
            die;
        }
        $q_ids = $this->get_project_question_ids($pid, $answer_table);
        if (!$q_ids) {
            echo "question error";
            die;
        }
        // 组装试卷显示信息
        $exam_info = $this->get_train_exam_info($q_ids, $pid);
        $paper_info = [
            'item_count' => 100,
            'total_minute' => 60
        ];
        $water_mark = "上医说-定考培训模拟卷";
        $data = [
            'title' => "定考培训模拟卷",
            'question' => json_encode($exam_info, JSON_UNESCAPED_UNICODE) ,
            'paper_info' => $paper_info,
            'water_mark' => $water_mark,
            'pid' => $pid,
        ];

        $this->load->view('/train_course/exam', $data);
    }

    // 是否已建立项目，不存在，新建
    private function get_train_project($drsay_pid, $survey_uid){
        if (!$drsay_pid || !$survey_uid) {
            return [];
        }
        $pro_info = $this->db->where(['drsay_pid' => $drsay_pid, 'survey_uid' => $survey_uid])->get('drsay_train_project')->row_array();
        if ($pro_info) {
            return $pro_info;
        }else{
            $insert_data = [
                'survey_uid' =>$survey_uid,
                'drsay_pid' =>$drsay_pid,
                'add_time' =>time(),
                'add_year' =>date('Y', time())
            ];
            $insert = $this->db->insert('drsay_train_project', $insert_data);
            if (!$insert) {
                return "";
            }
            $pro_info = $this->db->where(['drsay_pid' => $drsay_pid, 'survey_uid' => $survey_uid])->get('drsay_train_project')->row_array();
            return $pro_info;
        }
    }

    // 获取项目关联的题号
    private function get_project_question_ids($pid, $answer_table){
        $q_ids = [];
        $is_creat = $this->creat_train_answer_table($answer_table);
        if (!$is_creat) {
            echo "table not exists";
            die;
        }
        // 查询用户-项目对应题目信息
        $train_answer = $this->db->where(['pid' => $pid])->get($answer_table)->result_array();
        if ($train_answer) {
            foreach ($train_answer as $key_t => $v_t) {
                $q_ids[] = $v_t['main_qid'];
            }
        }else{
            // 随机拿到100个题目id加到关联表
            $add_q_ids = $this->db->query('SELECT id FROM drsay_train_question ORDER by RAND() LIMIT 100')->result_array();
            if ($add_q_ids && count($add_q_ids) == 100) {
                foreach ($add_q_ids as $key_q => $v_q) {
                    $insert_data = [
                        'pid' => $pid,
                        'main_qid' => $v_q['id']
                    ];
                    $insert = $this->db->insert($answer_table, $insert_data);
                    $q_ids[] = $v_q['id'];
                }
            }else{
                echo "question error";
                die;
            }
        }
        return $q_ids;
    }

    // 组装一个项目的试卷
    private function get_train_exam_info($q_ids, $pid){
        $option_num = [
            '0' => "A",
            '1' => "B",
            '2' => "C",
            '3' => "D",
            '4' => "E"
        ];
        // 组装题干信息
        $q_info = $this->db->where_in('id', $q_ids)->get('drsay_train_question')->result_array();
        $q_show_list = [];
        if ($q_info) {
            $sort = 1;
            foreach ($q_info as $key_q => $v_q) {
                $q_show_list[$v_q['id']] = [
                    'id' => $v_q['id'],
                    'pid' => $pid,
                    'serial_code' => $v_q['serial_code'],
                    'caption' => $v_q['caption'],
                    'answer' => $v_q['answer'],
                    'sort' => $sort
                ];
                $sort++;
            }
        }
        // 组装题目对应选项信息
        $option_info = $this->db->where_in('main_qid', $q_ids)->get('drsay_train_option')->result_array();
        $option_show_list = [];
        if ($option_info) {
            foreach ($option_info as $key_option => $v_option) {
                $option_show_list[$v_option['main_qid']][] = [
                    'id' => $v_option['id'],
                    'main_qid' => $v_option['main_qid'],
                    'text' => $v_option['text'],
                    'sort' => $v_option['sort'],
                    'q_num' => $option_num[$v_option['sort']],
                ];
            }
        }

        if (!$q_show_list || !$option_show_list) {
            echo "exam error";
            die;
        }

        $exam = [];
        foreach ($q_show_list as $key_exam => $v_exam) {
            $exam['list'][] = [
                'info' => $v_exam,
                'option' => $option_show_list[$key_exam]
            ];
        }
        // print_r($exam);die;
        return $exam;
    }

    //创建问卷链接表
    private function creat_train_answer_table($answer_table){
        $ci = &get_instance();
        $ci->load->dbforge();
        $tbname = $answer_table;
        if (!$ci->db->table_exists($tbname)) {
            $ci->dbforge->add_field('id');
            $inifields = array(
                'pid'         =>array('type'=>'INT','constraint'=>'10','default'=>null,'comment'=>'项目id'),
                'main_qid'         =>array('type'=>'INT','constraint'=>'10','default'=>null,'comment'=>'题目id'),
                'user_answer'         =>array('type'=>'INT','constraint'=>'10','default'=>null,'comment'=>'用户答案'),
            );
            $ci->dbforge->add_field($inifields);
            $ci->dbforge->create_table($tbname, true, array('ENGINE' => 'InnoDB', 'AUTO_INCREMENT' => 1, 'DEFAULT CHARSET'=> 'utf8', 'ROW_FORMAT' => 'COMPACT'));

            if ($ci->db->table_exists($tbname)) {
                $ci->db->query("ALTER TABLE {$tbname} COMMENT='培训题用户答题结果'");
                return true;
            }else{
                return false;
            }
        }else{
            return true;
        }
    }

    // 获取培训题用户答题结果 表名
    private function get_train_answer_table($p_add_year){
        $table_name = '';
        if ($p_add_year > 0) {
            $table_name = $this->table_name . $p_add_year;
        }
        return $table_name;
    }

    // 存储课件题目答案
    public function get_user_answer(){
        $post_data = $this->input->post();
        $pid = isset($post_data['pid']) ? intval($post_data['pid']) : 0;
        $user_answer = isset($post_data['answer']) ? $post_data['answer'] : "";
        if ($pid <= 0) {
            _back_msg("error", "param error");
            die;
        }
        $p_info = $this->db->where(['id' => $pid])->get('drsay_train_project')->row_array();
        if (!$p_info) {
            _back_msg("error", "param error");
            die;
        }
        $answer_table_name = $this->get_train_answer_table($p_info['add_year']);
        $answer_list = [];
        if ($user_answer !== 'null') {
            $answer_list = json_decode($user_answer, true);
        }

        try{
            $this->db->trans_start();//事务开始
            //更新项目表状态为 已交卷
            $update_p = $this->db->where(['id' => $pid])->update('drsay_train_project', ['status' => self::TRAIN_PROJECY_STATUS_ANSWER, 'update_time' => time()]);
            if (!$update_p) {
                throw new Exception("交卷失败");
            }
            // 存储用户答案
            if ($answer_list) {
                foreach ($answer_list as $key_answer => $v_answer) {
                    $update_data = [
                        'user_answer' => $v_answer['val']
                    ];
                    $update_answer = $this->db->where(['pid' => $pid, 'main_qid' => $v_answer['id']])->update($answer_table_name, $update_data);
                    if (!$update_answer) {
                        throw new Exception("交卷失败");
                    }
                }
            }
            $this->db->trans_complete();
            _back_msg("success", "交卷成功");
        }catch (Exception $e) {
            $this->db->trans_complete();
            _back_msg("error", $e->getMessage());
        }
        print_r($post_data);die;
    }

    // public function show_answer(){
    //     $pid = $this->uri->segment(3);
    //     if (!$pid) {
    //         echo "error";
    //         die;
    //     }
    //     $this->exam_answer($pid);
    // }
    // 查看答案
    public function exam_answer($p_info){
//         $q = '
//         {
//     "list": [
//         {
//             "info": {
//                 "id": "362220",
//                 "pid": "4376",
//                 "serial_code": "IITEM58PLMY9S8VYLM3K",
//                 "caption": "我国艾滋病防治工作坚持的方针是",
//                 "answer": "1807574",
//                 "sort": 1
//             },
//             "option": [
//                 {
//                     "id": "1807574",
//                     "main_qid": "362220",
//                     "text": "预防为主，防治结合",
//                     "sort": "0",
//                     "q_num": "A"
//                 },
//                 {
//                     "id": "1807575",
//                     "main_qid": "362220",
//                     "text": "政府领导，全社会共同参与",
//                     "sort": "1",
//                     "q_num": "B"
//                 },
//                 {
//                     "id": "1807576",
//                     "main_qid": "362220",
//                     "text": "宣传教育，行为干预",
//                     "sort": "2",
//                     "q_num": "C"
//                 },
//                 {
//                     "id": "1807577",
//                     "main_qid": "362220",
//                     "text": "行为干预，关怀救助",
//                     "sort": "3",
//                     "q_num": "D"
//                 },
//                 {
//                     "id": "1807578",
//                     "main_qid": "362220",
//                     "text": "统一协调，综合防治",
//                     "sort": "4",
//                     "q_num": "E"
//                 }
//             ]
//         },
//         {
//             "info": {
//                 "id": "362221",
//                 "pid": "4376",
//                 "serial_code": "IITEM58PLMSS3HC7U690",
//                 "caption": "未经批准擅自开办医疗机构行医应承担的行政责任主要是",
//                 "answer": "1807583",
//                 "sort": 2
//             },
//             "option": [
//                 {
//                     "id": "1807579",
//                     "main_qid": "362221",
//                     "text": "经济补偿",
//                     "sort": "0",
//                     "q_num": "A"
//                 },
//                 {
//                     "id": "1807580",
//                     "main_qid": "362221",
//                     "text": "赔礼道歉",
//                     "sort": "1",
//                     "q_num": "B"
//                 },
//                 {
//                     "id": "1807581",
//                     "main_qid": "362221",
//                     "text": "赔偿责任",
//                     "sort": "2",
//                     "q_num": "C"
//                 },
//                 {
//                     "id": "1807582",
//                     "main_qid": "362221",
//                     "text": "行政处分",
//                     "sort": "3",
//                     "q_num": "D"
//                 },
//                 {
//                     "id": "1807583",
//                     "main_qid": "362221",
//                     "text": "行政处罚",
//                     "sort": "4",
//                     "q_num": "E"
//                 }
//             ]
//         }
//     ]
// }
//         ';

//         $answer = '
//         [
//     {
//         "id": "118324447",
//         "main_qid": "362220",
//         "user_answer": "1807575",
//     },
//     {
//         "id": "118324448",
//         "main_qid": "362221",
//         "user_answer": "1807581",
//     }
// ]
//         ';
        $pid = $p_info['id'];
        // 查询项目-题目
        $answer_table_name = $this->get_train_answer_table($p_info['add_year']);
        $q_ids = $this->get_project_question_ids($pid, $answer_table_name);
        if (!$q_ids) {
            echo "question error";
            die;
        }
        // 组装试卷显示信息
        $question = $this->get_train_exam_info($q_ids, $pid);
        // 组装试卷用户答案
        $answer = $this->get_train_exam_answer($pid, $answer_table_name, $question);
        $water_mark = "上医说-定考培训模拟试题答案";
        $data = [
            'title' => "上医说-定考培训模拟试题答案",
            'question' => json_encode($question, JSON_UNESCAPED_UNICODE),
            'answer_detail' => json_encode($answer, JSON_UNESCAPED_UNICODE),
            'water_mark' => $water_mark,
            'pid' => $pid
        ];
        $this->load->view('/train_course/exam_answer', $data);
    }

    private function get_train_exam_answer($pid, $answer_table_name, $question){
        $correct_answer = [];
        if ($question) {
            foreach ($question['list'] as $key => $v) {
                $correct_answer[$v['info']['id']] = $v['info']['answer'];
            }
        }
        // $answer_info = $this->db->where(['pid' => $pid, 'user_answer >' => 0])->get($answer_table_name)->result_array();
        $answer_info = $this->db->where(['pid' => $pid, 'user_answer >' => 0])->get($answer_table_name)->result_array();
        $answer_list = [];
        if ($answer_info) {
            foreach ($answer_info as $key_answer => $v_answer) {
                $answer_list[] = [
                    'id' => $v_answer['id'],
                    'main_qid' => $v_answer['main_qid'],
                    'user_answer' => $v_answer['user_answer'],
                    'answer' => isset($correct_answer[$v_answer['main_qid']]) ? $correct_answer[$v_answer['main_qid']] : 0,
                ];
            }
        }
        return $answer_list;
    }
}
