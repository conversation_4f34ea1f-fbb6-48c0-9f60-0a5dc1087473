<?php
/**
 * 百度数据清洗
 * User: AMY
 * Date: 2021-10-13
 * Time: 13:39
 */
class Baidu_data_clean extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    private function encode_str($sign_info, $is_ajax = false){
        if (!$sign_info) {
            if ($is_ajax) {
                _back_msg("error", "账号错误！");
            } else {
                die("账号错误");
            }
        }
        //账号检测
//        $sign = $this->input->get("sign", true);
        $arr_sign = explode("_", $sign_info);
        $sign1 = $arr_sign[0];
        $sign = $arr_sign[1];
        $encode_str = $arr_sign[2];
        //SQL数据处理
        $str = $sign1 . "_" . $sign;
        $local_encode_str = substr(md5($str. "_" .PROJECT_ENCODE_KEY), 8, 16);
        if ($encode_str !== $local_encode_str) {
            if ($is_ajax) {
                _back_msg("error", "账号错误！");
            } else {
                die("账号错误");
            }
        }
        return true;
    }

    //数据清洗
    //http://www.drsay.cn/baidu_data_clean/data_clean_new?search_unit=&search_doctor=&code=jkt2021_hoyicaql_93f4486877f1bdd9
    public function data_clean_new() {
        $code = $this->input->get("code", true);
        $this->encode_str($code);
        $table_name = "mb_qkc";
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        if ($post_data) {
            $edit_data = $post_data['edit'];
            $code = $edit_data['code'];
            $back_url = $edit_data['back_url'];
            $this->encode_str($code, true);
            if (!$edit_data) {
                _back_msg("error", "数据有误！");
            }
            foreach ($edit_data as $k => &$v) {
                $v['id'] = $k;
                $v['baidu'] = 1;
            }
            $res = $this->db->update_batch($table_name, $edit_data, "id");
            if ($res) {
                _back_msg("success", "批量更新成功！", $back_url ? $back_url : "/baidu_data_clean/data_clean_new");
            } else {
                _back_msg("error", "批量更新失败！");
            }
        }
        $number_page = 100;
        $offset = $this->input->get('per_page', true);
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset) - 1) * $number_page : 0;
        $page = $number_page;
        $cond = " AND baidu=0";
        $get_data = $this->input->get();
        $get_data = format_data($get_data);
        $param = [];
        if ($get_data) {
            $search_unit = $get_data['search_unit'];
            if ($search_unit) {
                $param["search_unit"] = $search_unit;
                $cond .= " AND unit_name LIKE '%{$search_unit}%'";
            }
            $search_doctor = $get_data['search_doctor'];
            if ($search_doctor) {
                $param["search_doctor"] = $search_doctor;
                $cond .= " AND dr_name LIKE '%{$search_doctor}%'";
            }
        }
        //查询总条数
        $total = getDataNumByCondition($table_name, $cond);
        $list = getDataByCondition($table_name, " {$cond} limit {$offset},{$page}");
        $url = '/baidu_data_clean/data_clean_new';
        $pagination = getPagination($total, $url, $number_page);

        $default_province_link = [
            "北京市" => [
                "link" => "http://wjw.beijing.gov.cn/",
                "title" => "北京市卫生健康委员会",
            ],
            "天津市" => [
                "link" => "http://wsjk.tj.gov.cn/",
                "title" => "天津市卫生健康委员会",
            ],
            "河北省" => [
                "link" => "http://wsjkw.hebei.gov.cn/",
                "title" => "河北省卫生健康委员会",
            ],
            "山西省" => [
                "link" => "http://wjw.shanxi.gov.cn/",
                "title" => "山西省卫生健康委员会",
            ],
            "内蒙古自治区" => [
                "link" => "http://wjw.nmg.gov.cn/",
                "title" => "内蒙古自治区卫生健康委员会",
            ],
            "辽宁省" => [
                "link" => "http://wsjk.ln.gov.cn/",
                "title" => "辽宁省卫生健康委员会",
            ],
            "吉林省" => [
                "link" => "http://wsjkw.jl.gov.cn/",
                "title" => "吉林省卫生健康委员会",
            ],
            "黑龙江省" => [
                "link" => "http://wsjkw.hlj.gov.cn/",
                "title" => "黑龙江省卫生健康委员",
            ],
            "上海市" => [
                "link" => "http://wsjkw.sh.gov.cn/",
                "title" => "上海市卫生健康委员会",
            ],
            "江苏省" => [
                "link" => "http://wjw.jiangsu.gov.cn/",
                "title" => "江苏省卫生健康委员会",
            ],
            "浙江省" => [
                "link" => "http://www.zjwjw.gov.cn/",
                "title" => "浙江省卫生健康委员会",
            ],
            "安徽省" => [
                "link" => "http://wjw.ah.gov.cn/",
                "title" => "安徽省卫生健康委员会",
            ],
            "福建省" => [
                "link" => "http://wjw.fujian.gov.cn/",
                "title" => "福建省卫生健康委员会",
            ],
            "江西省" => [
                "link" => "http://www.jxhfpc.gov.cn/",
                "title" => "江西省卫生健康委员会",
            ],
            "山东省" => [
                "link" => "http://wsjkw.shandong.gov.cn/",
                "title" => "山东省卫生健康委员会",
            ],
            "河南省" => [
                "link" => "http://www.hnwsjsw.gov.cn/",
                "title" => "河南省卫生健康委员会",
            ],
            "湖北省" => [
                "link" => "http://wjw.hubei.gov.cn/",
                "title" => "湖北省卫生健康委员会",
            ],
            "湖南省" => [
                "link" => "http://wjw.hunan.gov.cn/",
                "title" => "湖南省卫生健康委员会",
            ],
            "广东省" => [
                "link" => "http://wsjkw.gd.gov.cn/",
                "title" => "广东省卫生健康委员会",
            ],
            "广西壮族自治区" => [
                "link" => "http://wsjkw.gxzf.gov.cn/",
                "title" => "广西壮族自治区卫生健康委员会",
            ],
            "海南省" => [
                "link" => "http://wst.hainan.gov.cn/swjw/index.html",
                "title" => "海南省卫生健康委员会",
            ],
            "重庆市" => [
                "link" => "http://wsjkw.cq.gov.cn/",
                "title" => "重庆市卫生健康委员会",
            ],
            "四川省" => [
                "link" => "http://wsjkw.sc.gov.cn/",
                "title" => "四川省卫生健康委员会",
            ],
            "贵州省" => [
                "link" => "http://wjw.guizhou.gov.cn/",
                "title" => "贵州省卫生健康委员会",
            ],
            "云南省" => [
                "link" => "http://ynswsjkw.yn.gov.cn",
                "title" => "云南省卫生健康委员会",
            ],
            "西藏自治区" => [
                "link" => "http://wjw.xizang.gov.cn/",
                "title" => "西藏自治区卫生健康委员会",
            ],
            "陕西省" => [
                "link" => "http://sxwjw.shaanxi.gov.cn/",
                "title" => "陕西省卫生健康委员会",
            ],
            "甘肃省" => [
                "link" => "http://wsjk.gansu.gov.cn",
                "title" => "甘肃省卫生健康委员会",
            ],
            "青海省" => [
                "link" => "https://wsjkw.qinghai.gov.cn/",
                "title" => "青海省卫生健康委员会",
            ],
            "宁夏回族自治区" => [
                "link" => "http://wsjkw.nx.gov.cn/",
                "title" => "宁夏回族自治区卫生健康委员会",
            ],
            "新疆维吾尔自治区" => [
                "link" => "https://wjw.xinjiang.gov.cn/",
                "title" => "新疆维吾尔自治区卫生健康委员会",
            ]
        ];
        //统计总量
        $res_tongji = $this->db->query("SELECT baidu,count(*) as num FROM {$table_name} GROUP BY baidu")->result_array();
        $tongji_data = [];
        foreach ($res_tongji as $v_tongji) {
            $tongji_data['total'] += $v_tongji['num'];
            $tongji_data[$v_tongji['baidu']] = $v_tongji['num'];
        }

        $data = [
            'title' => '数据清洗',
            'list' => $list,
            'offset' => $offset,
            'pagination' => $pagination,
            'default_province_link' => $default_province_link,
            'param' => $param,
            'tongji_data' => $tongji_data,
            'code' => $code,
        ];
//        $this->load->view('/templates/header.php', $data);
        $this->load->view('/baidu_data_clean/data_clean_new', $data);
//        $this->load->view('/templates/footer.php', $data);
    }

    //单条保存
    public function save_qkc_data(){
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        $id = (int)$post_data['id'];
        $back_url = $post_data['back_url'];
        if ($id <= 0) {
            _back_msg("error", "参数错误！");
        }
        $code = $post_data['code'];
        $this->encode_str($code, true);
        $update_data = [
            "department" => $post_data['department'] ? $post_data['department'] : "",
            "dr_name" => $post_data['dr_name'] ? $post_data['dr_name'] : "",
            "zgz" => $post_data['zgz'] ? $post_data['zgz'] : "",
            "dr_gender" => $post_data['dr_gender'] ? $post_data['dr_gender'] : "",
            "dr_job_title" => $post_data['dr_job_title'] ? $post_data['dr_job_title'] : "",
            "dr_position" => $post_data['dr_position'] ? $post_data['dr_position'] : "",
            "dr_lable" => $post_data['dr_lable'] ? $post_data['dr_lable'] : "",
            "dr_skill" => $post_data['dr_skill'] ? $post_data['dr_skill'] : "",
            "baidu" => 1,
        ];
        $res = $this->db->update("mb_qkc", $update_data, ["id" => $id]);
        if ($res) {
            _back_msg("success", "保存成功！", $back_url ? $back_url : "/mb/mbdrweb/data_clean_new");
        } else {
            _back_msg("error", "保存失败！");
        }
    }

    //百度快查数据导出
    public function qkc_data_import(){
        $filed_info = $this->db->query("select COLUMN_NAME,COLUMN_COMMENT from information_schema.COLUMNS where table_name = 'mb_qkc'")->result_array();
        $res_field_info = [];
        foreach ($filed_info as $v_f) {
            $res_field_info[] = $v_f['COLUMN_NAME']."({$v_f['COLUMN_COMMENT']})";
            $select_table_field[] = $v_f['COLUMN_NAME'];
        }
        $res_list = $this->db->query("SELECT `".implode("`,`", $select_table_field)."` FROM mb_qkc WHERE 1=1")->result_array();
        $rs_list = [];
        if ($res_list) {
            foreach ($res_list as $v_list) {
                $table_data = [];
                foreach ($v_list as $v_val) {
                    $table_data[] = "\t".strip_tags($v_val)."\t";
                }
                $rs_list[] = $table_data;
            }
        }
        $res_info = [
            "field" => $res_field_info,
            "data" => $rs_list,
        ];
        export_csv($rs_list, $res_info['field'], 'mb_qkc.csv');
    }
}
