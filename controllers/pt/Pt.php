<?php
use EasyWeChat\Factory;
class Pt extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        ####    微信公众号链接进入   ####
        $from_where = trim($this->input->get("from_where", true));
        if ($from_where == "wx") {
            $app = $this->wx_config();
            $user = $app->oauth->user();
            $insert_data = [
                "back_data" => json_encode($user, JSON_UNESCAPED_UNICODE),
                "add_time" => time(),
            ];
            $this->db->insert("wx_event", $insert_data);
            if (isset($user->id) && $user->id) {
                //存session
                $wx_session = array(
                    'wx_open_id' => $user->id,
                );
                $this->session->set_userdata($wx_session);
            }
        }
        ####    微信公众号链接进入   ####
    }

    private function common_view($view)
    {
        $this->load->view("/pt/head");
        $this->load->view("/pt/{$view}");
        $this->load->view("/pt/footer");
    }

    //首页
    public function index()
    {
        $this->common_view("index");
    }

    public function active()
    {
        $this->common_view("active");
    }

    public function consult()
    {
        $this->common_view("consult");
    }

    public function freque_question()
    {
        $this->common_view("freque_question");
    }

    public function health()
    {
        $this->common_view("health");
    }

    public function health_assess()
    {
        $this->common_view("health_assess");
    }

    public function health_assess_detail()
    {
        $this->common_view("health_assess_detail");
    }

    public function health_info()
    {
        $this->common_view("health_info");
    }

    public function login()
    {
        $this->load->view("/pt/login");
    }

    public function mine()
    {
        $this->common_view("mine");
    }

    public function mine_auth()
    {
        $this->common_view("mine_auth");
    }

    public function mine_qrcode()
    {
        $this->common_view("mine_qrcode");
    }

    public function msg_notify()
    {
        $this->common_view("msg_notify");
    }

    public function msg_notify_detail()
    {
        $this->common_view("msg_notify_detail");
    }

    public function online_service()
    {
        $this->common_view("online_service");
    }

    public function point()
    {
        $this->common_view("point");
    }

    public function point_details()
    {
        $this->common_view("point_details");
    }

    public function point_withdraw()
    {
        $this->common_view("point_withdraw");
    }

    public function question()
    {
        $this->common_view("question");
    }

    public function question_bank()
    {
        $this->common_view("question_bank");
    }

    public function question_finish()
    {
        $this->common_view("question_finish");
    }

    public function question_frist()
    {
        $this->common_view("question_frist");
    }

    public function question_pages()
    {
        $this->common_view("question_pages");
    }

    public function question_two()
    {
        $this->common_view("question_two");
    }

    public function questionnaire()
    {
        $this->common_view("questionnaire");
    }

    public function questionnaire_result()
    {
        $this->common_view("questionnaire_result");
    }

    public function science()
    {
        $this->common_view("science");
    }

    public function science_details()
    {
        $this->common_view("science_details");
    }

    public function setting()
    {
        $this->common_view("setting");
    }

    public function user_certificate()
    {
        $this->common_view("user_certificate");
    }

    public function withdrawal_process()
    {
        $this->common_view("withdrawal_process");
    }

    public function withdrawal_record()
    {
        $this->common_view("withdrawal_record");
    }
}
