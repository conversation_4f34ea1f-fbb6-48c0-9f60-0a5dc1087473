<?php
/*
 * 企微对接 
 * 企微回调处理程序
 * 2025-06-25
 */
class Qw extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    /**
     * 聊天消息回调处理程序
     * 回调链接配置地址：pt-mbridge.iweisale.com
     * 配置路径：设置->参数配置->聊天消息回调配置
     * 
     */
    public function index()
    {
        // 补充完善状态

        // 参数设置：ttn4qmv6gz2ydry7
        $auth_param = $this->uri->segment(4) ?? '';
        if (strcmp($auth_param, 'ttn4qmv6gz2ydry7') != 0) {
            echo 'auth error';
            exit;
        }
        $data = file_get_contents('php://input');
        // 查询消息回调表是否存在，不存在先创建表，再记录信息
        $now_year = date("Y");
        // $check_table = $this->db->query("select COLUMN_NAME from information_schema.COLUMNS where table_name = 'qw_member_message_callback_{$now_year}'")->row_array();
        $check_table = $this->db->query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE  TABLE_NAME = 'qw_member_message_callback_{$now_year}'")->row_array();
    
        if (!$check_table) {// 不存在
            $create_table = $this->db->query("CREATE TABLE `qw_member_message_callback_{$now_year}` (
            `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `back_result` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '聊天消息回调明细',
            `merchant_id` int(11) NOT NULL DEFAULT '0' COMMENT '商户ID，int类型',
            `merchant_name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商户名称',
            `enterprise_id` int(11) NOT NULL DEFAULT '0' COMMENT '企业ID，客户的企业ID',
            `enterprise_name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '企业名称',
            `personal_id` int(11) NOT NULL DEFAULT '0' COMMENT '企微号ID',
            `personal_name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '企微号名称',
            `contacts_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '联系人ID，long类型 (同客户详情中的contacts_record_id)',
            `contacts_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
            `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '客服ID',
            `user_name` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客服名称',
            `chat_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '聊天类型：1-接收，2-发送，int类型 接收的时候，发送者是客户，对象是企微号,发送的时候，发送者是企微号，接收者是客户',
            `from_remote_id` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '发送者企业微信ID，string类型 长度256 varchar(256)（同好友remoteid，可通过客户列表/详情接口获取）',
            `from_remote_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '发送者姓名',
            `to_remote_id` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接收者企业微信ID，string类型 长度256 varchar(256)（同企微号remoteid，可通过客户联系人信息列表接口获取）',
            `to_remote_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '接收者姓名',
            `msg_type` bigint(20) NOT NULL DEFAULT '0' COMMENT '消息类型',
            `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '聊天内容',
            `send_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送状态：0-未指定,1.待发送，2.发送中，4.发送成功，8-发送失败，16-撤回，32-删除,int类型',
            `locmsgid` bigint(20) NOT NULL DEFAULT '0' COMMENT '本地消息ID',
            `msg_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '企信官方消息ID',
            `msg_time` datetime NOT NULL COMMENT '企信官方消息时间，DateTime类型',
            `msg_source` tinyint(1) NOT NULL DEFAULT '0' COMMENT '消息来源：1-好友发送消息上报，2-消息发送结果，int类型',
            `task_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '扩展字段：任务ID',
            `isDelay` tinyint(1) NOT NULL COMMENT '业务拓展字段：是否延时处理的消息，bool类型',
            `delayCount` int(11) NOT NULL DEFAULT '0' COMMENT '业务拓展字段：延时处理次数',
            `sendQueueTime` datetime NOT NULL COMMENT '发送队列消息时间， DateTime类型',
            `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否解析back_result返回的数据 0、待解析 1、已解析 2、解析失败',
            `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `merchant_id` (`merchant_id`),
            KEY `enterprise_id` (`enterprise_id`),
            KEY `personal_id` (`personal_id`),
            KEY `contacts_id` (`contacts_id`),
            KEY `user_id` (`user_id`),
            KEY `locmsgid` (`locmsgid`),
            KEY `msg_id` (`msg_id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企微聊天消息回调记录'
            /*!50100 PARTITION BY HASH (id)
            (PARTITION p0 ENGINE = InnoDB,
            PARTITION p1 ENGINE = InnoDB,
            PARTITION p2 ENGINE = InnoDB,
            PARTITION p3 ENGINE = InnoDB,
            PARTITION p4 ENGINE = InnoDB,
            PARTITION p5 ENGINE = InnoDB,
            PARTITION p6 ENGINE = InnoDB,
            PARTITION p7 ENGINE = InnoDB,
            PARTITION p8 ENGINE = InnoDB,
            PARTITION p9 ENGINE = InnoDB) */");
            $table_exist = $create_table ? true : false;
        } else {// 表存在
            $table_exist = true;
        }
        if ($table_exist) {
            $info = json_decode($data, true);
            $analysis_res = '';
            if (isset($info['message'])) {
                $analysis_res = $this->aes_decrypt_string($info['message']);
                $status = 1; // 解析成功
            } else {
                $status = 2; // 解析失败
            }
            $analysis_res_arr = $analysis_res ? json_decode($analysis_res, true) : [];
            // 消息ID
            $locmsgid = $analysis_res_arr['locmsgid'] ?? '';
            $send_status = $analysis_res_arr['send_status'] ?? '';
            
            if (!empty($locmsgid)) {
                // 这个记录有可能比较后置，先发送才会记录到qw_member_send_detail表，所以可能已经接到回调信息了，还没存储至qw_member_send_detail表，所以需要先把之前缺失的send_status状态补充上
                $this->db->query("UPDATE qw_member_send_detail a,qw_member_message_callback_{$now_year} b SET a.send_status=b.send_status WHERE a.locmsgid >0 AND a.send_status=0 AND a.locmsgid=b.locmsgid");
                // 自定义发送的表是否存在
                $custom_send_table = $this->db->query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE  TABLE_NAME = 'qw_member_send_detail_{$now_year}'")->row_array();
                if ($custom_send_table) {// 表存在
                    $this->db->query("UPDATE qw_member_send_detail_{$now_year} a,qw_member_message_callback_{$now_year} b SET a.send_status=b.send_status WHERE a.locmsgid >0 AND a.send_status=0 AND a.locmsgid=b.locmsgid");
                }
                // 更新当前回调记录的状态，如果存在的话
                $this->db->query("UPDATE qw_member_send_detail SET send_status = {$send_status} WHERE locmsgid = {$locmsgid} AND send_status=0 LIMIT 1");
                $this->db->query("UPDATE qw_member_send_detail_{$now_year} SET send_status = {$send_status} WHERE locmsgid = {$locmsgid} AND send_status=0 LIMIT 1");

                // // 查询消息是否已存在
                // $msg_info = $this->db->query("SELECT locmsgid FROM qw_member_send_detail WHERE locmsgid = {$locmsgid} LIMIT 1")->row_array();
                // if ($msg_info) {//  存在记录，更新发送状态
                //     $this->db->query("UPDATE qw_member_send_detail SET send_status = {$send_status} WHERE locmsgid = {$locmsgid} LIMIT 1");
                // }
            }
            $this->db->insert("qw_member_message_callback_{$now_year}", [
                "back_result" => $data,
                "merchant_id" => $analysis_res_arr['merchant_id'] ?? '',
                "merchant_name" => $analysis_res_arr['merchant_name'] ?? '',
                "enterprise_id" => $analysis_res_arr['enterprise_id'] ?? '',
                "enterprise_name" => $analysis_res_arr['enterprise_name'] ?? '',
                "personal_id" => $analysis_res_arr['personal_id'] ?? '',
                "personal_name" => $analysis_res_arr['personal_name'] ?? '',
                "contacts_id" => $analysis_res_arr['contacts_id'] ?? '',
                "contacts_name" => $analysis_res_arr['contacts_name'] ?? '',
                "user_id" => $analysis_res_arr['user_id'] ?? '',
                "user_name" => $analysis_res_arr['user_name'] ?? '',
                "chat_type" => $analysis_res_arr['chat_type'] ?? '',
                "from_remote_id" => $analysis_res_arr['from_remote_id'] ?? '',
                "from_remote_name" => $analysis_res_arr['from_remote_name'] ?? '',
                "to_remote_id" => $analysis_res_arr['to_remote_id'] ?? '',
                "to_remote_name" => $analysis_res_arr['to_remote_name'] ?? '',
                "msg_type" => $analysis_res_arr['msg_type'] ?? '',
                "content" => $analysis_res_arr['content'] ?? '',
                "send_status" => $analysis_res_arr['send_status'] ?? '',
                "locmsgid" => $analysis_res_arr['locmsgid'] ?? '',
                "msg_id" => $analysis_res_arr['msg_id'] ?? '',
                "msg_time" => $analysis_res_arr['msg_time'] ?? '',
                "msg_source" => $analysis_res_arr['msg_source'] ?? '',
                "task_id" => $analysis_res_arr['task_id'] ?? '',
                "isDelay" => $analysis_res_arr['isDelay'] ?? '',
                "delayCount" => $analysis_res_arr['delayCount'] ?? '',
                "sendQueueTime" => $analysis_res_arr['sendQueueTime'] ?? '',
                "status" => $status,
                "add_time" => time(),
            ]);
        }
    }

    // 解析回调数据：$key在供应商平台获取，获取路径看index方法上的注释
    private function aes_decrypt_string($decrypt_str, $key = 'j9OmqhMN15HtnEFNnOy') {
        // Base64解码 
        $aes_bytes = base64_decode($decrypt_str);
        
        // 密钥处理（补空格至32字节）
        $aes_key = str_pad($key, 32, " ", STR_PAD_RIGHT);
        
        // 解密处理（使用openssl扩展）
        $result = openssl_decrypt(
            $aes_bytes,
            'AES-256-ECB',
            $aes_key,
            OPENSSL_RAW_DATA 
        );
        
        // 移除可能的填充空格 
        return rtrim($result);
    }
}