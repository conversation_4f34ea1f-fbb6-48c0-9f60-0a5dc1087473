<?php
/**
 * 得助外呼，人工外呼回调数据记录
 * User: Amy
 * Date: 8/15/22
 * Time: 5:22 PM
 */

class Dz_callback extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    //获取呼叫返回的数据
    public function get_sound_data()
    {
        $data_post = file_get_contents("php://input");

        //打印数据
        file_put_contents("./tmp/dz_sound_data.txt", $data_post . "**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);

        // *********** 回调数据处理 操作部分 Start.... ***********

        $json_data = json_decode($data_post, true);
        $back_res = json_decode($json_data['data'], true);
        // $back_res['call_back_id'] = $back_res['id'];
        // unset($back_res['id']);
        // 新增或是更新 app_project_cdr_dz、app_project_cdr_dz_log 表
        $iccCallId = $back_res['iccCallId'];

        $callId_isSet = $this->db->select("callId")->where(['callId' => $iccCallId])
            ->limit(1)->get("app_project_cdr_dz")->row_array();

        $this->db->trans_begin();

        if ($callId_isSet) { // 记录id存在更新
            $up_data = [
                "call_back_id"=>$back_res['id'],
                "acw"=>$back_res['acw'],
                "agentAnswerTime"=>$back_res['agentAnswerTime'],
                "agentLoginType"=>$back_res['agentLoginType'],
                "callResult"=>$back_res['callResult'],
                "callResultType"=>$back_res['callResultType'],
                "callType"=>$back_res['callType'],
                "calledNumber"=>$back_res['calledNumber'],
                "callingNumber"=>$back_res['callingNumber'],
                "communicateBeginTime"=>$back_res['communicateBeginTime'],
                "communicateDuration"=>$back_res['communicateDuration'],
                "communicateEndTime"=>$back_res['communicateEndTime'],
                "communicateSource"=>$back_res['communicateSource'],
                "communicateVoiceId"=>$back_res['communicateVoiceId'],
                "createTime"=>$back_res['createTime'],
                "custIspName"=>$back_res['custIspName'],
                "custNumberAttribution"=>$back_res['custNumberAttribution'],
                "customerId"=>$back_res['customerId'],
                "customerName"=>$back_res['customerName'],
                "customerRingTime"=>$back_res['customerRingTime'],
                "evaluationScore"=>$back_res['evaluationScore'],
                "expansionFields"=>$back_res['expansionFields'],
                "hangUpDirectionType"=>$back_res['hangUpDirectionType'],
                "iccCallId"=>$back_res['iccCallId'],
                "iccRecordDate"=>$back_res['iccRecordDate'],
                "noteStatus"=>$back_res['noteStatus'],
                "sipphoneAccount"=>$back_res['sipphoneAccount'],
                "sipphoneEndpoint"=>$back_res['sipphoneEndpoint'],
                "skillInCustomerNum"=>$back_res['skillInCustomerNum'],
                "skillInOnceCustomerNum"=>$back_res['skillInOnceCustomerNum'],
                "skillSetId"=>$back_res['skillSetId'],
                "speechCallResult"=>$back_res['speechCallResult'],
                "staffId"=>$back_res['staffId'],
                "staffInCustomerNum"=>$back_res['staffInCustomerNum'],
                "staffInOnceCustomerNum"=>$back_res['staffInOnceCustomerNum'],
                "staffName"=>$back_res['staffName'],
                "taskId"=>$back_res['taskId'],
                "tenantId"=>$back_res['tenantId'],
                "tenantInCustomerNum"=>$back_res['tenantInCustomerNum'],
                "tenantInOnceCustomerNum"=>$back_res['tenantInOnceCustomerNum'],
                "transferId"=>$back_res['transferId']
            ];
            $this->db->where(['callId' => $iccCallId])->update('app_project_cdr_dz', $up_data);
            $this->db->where(['callId' => $iccCallId])->update('app_project_cdr_dz_log', [
                'drop_call_data' => $json_data['data'],
                'add_time' => time(),
            ]);
        } else {  // 不存在记录，说明呼叫没有存入..
            //打印数据
            file_put_contents("./tmp/dz_sound_data_error.txt", $data_post . "**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);

            /*$this->db->insert('app_project_cdr_dz', $back_res);
            $this->db->insert('app_project_cdr_dz_log', [
                'callId' => $iccCallId,
                'drop_call_data' => $json_data['data'],
                'add_time' => time(),
            ]);*/
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
        } else {
            $this->db->trans_commit();
        }

        // *********** 回调数据处理 操作部分 End.... ***********

    }

    //获取呼叫返回的数据 - 新回调地址
    public function get_sound_data_new()
    {
        $data_post = file_get_contents("php://input");

        //打印数据
        file_put_contents("./tmp/dz_sound_data_new.txt", $data_post . "**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);

        // *********** 回调数据处理 操作部分 Start.... ***********

        // $json_data = json_decode($data_post, true);
        $back_res = json_decode($data_post, true);
        // $back_res = json_decode($json_data['data'], true);
        // $back_res['call_back_id'] = $back_res['id'];
        // unset($back_res['id']);
        // 新增或是更新 app_project_cdr_dz、app_project_cdr_dz_log 表
        $iccCallId = $back_res['iccCallId'];

        $callId_isSet = $this->db->select("callId")->where(['callId' => $iccCallId])
            ->limit(1)->get("app_project_cdr_dz")->row_array();

        $this->db->trans_begin();

        if ($callId_isSet) { // 记录id存在更新

            $up_data = [

                "callResult" => $back_res['callResult'],    //回调返回 - 通话结果：0, "未知";1, "无法接通";2, "已接通";3, "客户振铃未接";4, "队列放弃";5, "非服务时间";6, "坐席振铃未接";7, "客户速挂";8, "IVR中放弃";9, "被叫振铃主叫挂机";10, "禁止呼叫"
                "callType" => $back_res['callType'], //回调返回 - 通话类型：1-呼入;2-外呼;3-自动外呼任务;
                "iccCallId" => $back_res['iccCallId'],  //回调返回 - 通话ID
                "iccRecordDate" => $back_res['participantList'][0]['iccRecordDate'], // 回调返回 - 录音时间戳，录音下载接口或早期媒体下载接口参数
                "agentAnswerTime" => $back_res['participantList'][0]['answerTime'], //回调返回 - 应答时间 格式：yyyy-MM-dd HH:mm:ss
                "communicateVoiceId" => $back_res['participantList'][0]['iccRecordingId'], // 回调返回 - 通话接通时录音ID，participantRole=【ROBOT,AGENT,EXTERNAL_PHONE】时有值（以客服方录制），录音下载接口参数
                "agentLoginType" => $back_res['participantList'][0]['loginType'],    //回调返回 - 坐席签入类型；WEB-web坐席，SIPPHONE-SIP话机，OUTPHONE-外部电话
                "customerRingTime" => $back_res['participantList'][0]['ringTime'],    //调返回 - 振铃时间 格式：yyyy-MM-dd HH:mm:ss
                "transferId" => $back_res['participantList'][0]['transferId'],	//调返回 - 内部转接ID
                "communicateSource" => $back_res['source'], // 回调返回 - 通话来源	NORMAL("普通来源"),  MANUAL_OUTBOUND_TASK_PEOPLE("人工外呼任务-手动外呼"), AUTO_OUTBOUND_TASK_PEOPLE("人工外呼任务-自动外呼"), OUTBOUND_TASK_ROBOOT("机器人智能外呼任务");
                "call_back_id" => $back_res['callRoomId'], // 回调返回 - 聊天室ID（现同iccCallId）
                "acw" => $back_res['participantList'][0]['acwDuration'],//回调返回 - 当前坐席所处技能组配置的话后处理时长 - 秒
                "createTime" => $back_res['participantList'][0]['bridgeTime'],    //回调返回 - 真正桥接成功通话开始时间 格式：yyyy-MM-dd HH:mm:ss
                "communicateEndTime" => $back_res['hangUpTime'], //回调返回 - 通话结束时间 格式：yyyy-MM-dd HH:mm:ss
                "calledNumber" => $back_res['calleePhone'], //回调返回 - 原始被叫
                "callingNumber" => $back_res['callerPhone'], // 回调返回 - 原始主叫

                "customerId" => $back_res['calleeParticipantId'],//回调返回 - 原始被叫对应的会话参与ID，关联字段Participant#participantId
                "taskId" => $back_res['callerParticipantId'],    //回调返回 - 主叫对应的会话参与ID，关联字段Participant#participantId
                "speechCallResult" => $back_res['hangUpResult'], //回调返回 - 挂机类型

            ];
            if ($back_res['callResult'] == 2 && $back_res['participantList'][0]['bridgeTime']){
                $end_tm = strtotime($back_res['hangUpTime']);
                $sta_tm = strtotime($back_res['participantList'][0]['bridgeTime']);
                $up_data["communicateDuration"] = $end_tm-$sta_tm;// 回调返回 - 通话时长(单位类型:秒)【hangUpTime - bridgeTime】
            }

            $this->db->where(['callId' => $iccCallId])->update('app_project_cdr_dz', $up_data);
            $this->db->where(['callId' => $iccCallId])->update('app_project_cdr_dz_log', [
                'drop_call_data' => $data_post,
                'add_time' => time(),
            ]);
        } else {  // 不存在记录，说明呼叫没有存入..
            //打印数据
            file_put_contents("./tmp/dz_sound_data_error_new.txt", $data_post . "**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
        } else {
            $this->db->trans_commit();
        }

        // *********** 回调数据处理 操作部分 End.... ***********

    }

}