<?php
/**
 * 得助外呼，人工外呼回调数据记录
 * User: Amy
 * Date: 8/15/22
 * Time: 5:22 PM
 */

class Holly_callback extends CI_Controller
{

    const APP_PROJECT_CDR_HOLLYCRM_TABLE = 'app_project_cdr_hollycrm';
    const APP_PROJECT_CDR_HOLLYCRM_LOG_TABLE = 'app_project_cdr_hollycrm_log';
    const APP_PROJECT_CDR_HOLLYCRM_PUSH_LOG_TABLE = 'app_project_cdr_hollycrm_push_log';
    function __construct()
    {
        parent::__construct();
    }


    /**
     * 通话API--接通事件
     */
    public function get_holly_outbound_call_data(){

        $param = $this->input->get();

        $TimestampKey = $param['TimestampKey'];
        $EncryptionToken = $param['EncryptionToken'];

        $push_log = [
            'hollycrm_id' => isset($param['id']) ? $param['id'] : 0,
            'event' => 'answer',
            'ip' => getip(),
            'source_url' => $_SERVER["HTTP_HOST"],
            'param' => $param ? json_encode($param,JSON_UNESCAPED_UNICODE) : '',
            'add_time' => time(),
        ];

        $push_log_id = saveData(self::APP_PROJECT_CDR_HOLLYCRM_PUSH_LOG_TABLE,$push_log);
        //打印数据
        file_put_contents("./tmp/holly_outbound_call_data.txt", json_encode($param,JSON_UNESCAPED_UNICODE) ."**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
        $msg = '';
        if(empty($TimestampKey)){
            $msg .= "TimestampKey参数为空；";
        }
        if(empty($EncryptionToken)){
            $msg .= "EncryptionToken参数为空；";
        }
        $verify_EncryptionToken = md5($TimestampKey.'f085b6e211');
        if($verify_EncryptionToken !=$EncryptionToken){
            $msg .= "验证错误：EncryptionToken::".$EncryptionToken."::verify_EncryptionToken::".$verify_EncryptionToken;
        }
        if($msg){
            $this->db->where(['id' => $push_log_id])->update(self::APP_PROJECT_CDR_HOLLYCRM_PUSH_LOG_TABLE,['remark' => $msg]);
            echo $msg;
            die;
        }
        $hollycrm_info = isset($param['id']) ? $this->db->query("select * from ".self::APP_PROJECT_CDR_HOLLYCRM_TABLE." where id = '{$param['id']}'")->row_array() : [];
        if(empty($hollycrm_info)){
            file_put_contents("./tmp/holly_outbound_call_data.txt",  "自定义参数id::".$param['id']."没有记录，**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
            $this->db->where(['id' => $push_log_id])->update(self::APP_PROJECT_CDR_HOLLYCRM_PUSH_LOG_TABLE,['remark' => '点击外呼，没有记录']);
            echo 'success';die;
        }
        if(!empty($hollycrm_info['call_id'])){
            echo 'success';die;
        }
        //接通时，推送过来的数据，通过自定义id，更新通话信息
        $arr = [
            'call_sheet_id' => $param['CallSheetID'] ? $param['CallSheetID'] : '',
            'call_id' => $param['CallID'] ? $param['CallID'] : '',
            'call_type' => $param['CallType'] ? $param['CallType'] : '',
            'call_no' => $param['CallNo'] ? $param['CallNo'] : '',
            'called_no' => $param['CalledNo'] ? $param['CalledNo'] : '',
            'start_time' => $param['Ring'] ? strtotime($param['Ring']) : '',
            'ringing_time' => $param['RingingTime'] ? strtotime($param['RingingTime']) : '',
            'begin' => $param['Begin'] ? strtotime($param['Begin']) : '',
            'queue_time' => $param['QueueTime'] ? $param['QueueTime'] : '',
            'queue' => $param['Queue'] ? $param['Queue'] : '',
            'agent' => $param['Agent'] ? $param['Agent'] : '',
            'exten' => $param['Exten'] ? $param['Exten'] : '',
            'state' => $param['State'] ? $param['State'] : '',
            'monitor_file_name' => $param['MonitorFilename'] ? $param['MonitorFilename'] : '',
            'agent_name' => $param['AgentName'] ? $param['AgentName'] : '',
            'call_state' => $param['CallState'] ? $param['CallState'] : '',
            'province' => $param['Province'] ? $param['Province'] : '',
            'district' => $param['District'] ? $param['District'] : '',
            'department_name' => $param['DepartmentName'] ? $param['DepartmentName'] : '',
            'action_id' => $param['action_id'] ? $param['action_id'] : '',
        ];
        $this->db->where(['id'=>$param['id']])->update(self::APP_PROJECT_CDR_HOLLYCRM_TABLE,$arr);

        $log_data = [
            'hollycrm_id' => isset($param['id']) ? $param['id'] : 0,
            'call_id' => $param['CallID'] ? $param['CallID'] : '',
            'make_call_data'=>json_encode($param,JSON_UNESCAPED_UNICODE),
            'drop_call_data'=>'',
            'add_time'=>time(),
        ];
        $this->db->insert(self::APP_PROJECT_CDR_HOLLYCRM_LOG_TABLE,$log_data);

        echo 'success';
    }

    /**
     * 通话API--通话结束事件
     */
    public function get_holly_hang_up_data(){

        $param = $this->input->get();

        $TimestampKey = $param['TimestampKey'];
        $EncryptionToken = $param['EncryptionToken'];

        $push_log = [
            'hollycrm_id' => isset($param['id']) ? $param['id'] : 0,
            'event' => 'end',
            'ip' => getip(),
            'source_url' => $_SERVER["HTTP_HOST"],
            'param' => $param ? json_encode($param,JSON_UNESCAPED_UNICODE) : '',
            'add_time' => time(),
        ];

        $push_log_id = saveData(self::APP_PROJECT_CDR_HOLLYCRM_PUSH_LOG_TABLE,$push_log);
        //打印数据
        file_put_contents("./tmp/holly_end_data.txt",'file::'.$param['MonitorFilename'].'::'. json_encode($param,JSON_UNESCAPED_UNICODE) ."**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
        $msg = '';
        if(empty($TimestampKey)){
            $msg .= "TimestampKey参数为空；";
        }
        if(empty($EncryptionToken)){
            $msg .= "EncryptionToken参数为空；";
        }
        $verify_EncryptionToken = md5($TimestampKey.'f085b6e211');
        if($verify_EncryptionToken !=$EncryptionToken){
            $msg .= "验证错误：EncryptionToken::".$EncryptionToken."::verify_EncryptionToken::".$verify_EncryptionToken;
        }
        if($msg){
            $this->db->where(['id' => $push_log_id])->update(self::APP_PROJECT_CDR_HOLLYCRM_PUSH_LOG_TABLE,['remark' => $msg]);
            echo $msg;
            die;
        }
        $hollycrm_info = isset($param['id']) ? $this->db->query("select * from ".self::APP_PROJECT_CDR_HOLLYCRM_TABLE." where id = '{$param['id']}'")->row_array() : [];
        if(empty($hollycrm_info)){
            file_put_contents("./tmp/holly_end_data.txt",  "自定义参数id::".$param['id']."没有记录，**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
            $this->db->where(['id' => $push_log_id])->update(self::APP_PROJECT_CDR_HOLLYCRM_PUSH_LOG_TABLE,['remark' => '点击外呼，没有记录']);
            echo 'success';die;
        }
        if(!empty($hollycrm_info['end'])){
            echo 'success';die;
        }
        //挂断电话时，推送过来的数据，通过自定义id，更新通话信息end，call_time_length，state，monitor_file_name，call_state
        $arr = [
            'end' => $param['End'] ? strtotime($param['End']) : '',
            'call_time_length' => $param['CallTimeLength'] ? $param['CallTimeLength'] : '',
            'state' => $param['State'] ? $param['State'] : '',
            'monitor_file_name' => $param['MonitorFilename'] ? $param['MonitorFilename'] : '',
            'call_state' => $param['CallState'] ? $param['CallState'] : '',
        ];
        $this->db->where(['id'=>$param['id']])->update(self::APP_PROJECT_CDR_HOLLYCRM_TABLE,$arr);

        $log_data = [
            'hollycrm_id' => isset($param['id']) ? $param['id'] : 0,
            'call_id' => $param['CallID'] ? $param['CallID'] : '',
            'drop_call_data'=> json_encode($param,JSON_UNESCAPED_UNICODE),
            'hollycrm_sound_file_data'=>  $param['MonitorFilename'] ? $param['MonitorFilename'] : '',
            'add_time'=> time(),
        ];
        $log_info = $this->db->query("select * from ".self::APP_PROJECT_CDR_HOLLYCRM_LOG_TABLE." where hollycrm_id = '{$param['id']}'")->row_array();
        if($log_info){
            unset($log_data['add_time']);
            $this->db->where(['hollycrm_id'=>$param['id']])->update(self::APP_PROJECT_CDR_HOLLYCRM_LOG_TABLE,$log_data);
        }else{
            $this->db->insert(self::APP_PROJECT_CDR_HOLLYCRM_LOG_TABLE,$log_data);
        }
        echo 'success';
    }

}