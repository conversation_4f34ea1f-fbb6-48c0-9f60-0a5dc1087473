<?php

/**
 * Remark: 后台复制问卷链接映射跳转真实问卷地址
 * User: Test-lu
 * Date: 2021/12/29
 */
class Go_c extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('survey_model');
    }

    // 检查链接是否正确....
    public function c()
    {

        //验证链接有效性
        $c_code = $this->uri->segment(3);
        $decode_data = decode_param($c_code);
        // 错误 跳转链接
        $jkt_thanks_url = "/bk/rs";
        // 加密串解密失败 结束！
        if(!$decode_data){
            $this->survey_model->get_redirect_info($jkt_thanks_url, "l_err");
        }
        $params = explode('_', $decode_data);
        // 解密字符串规则不符 结束！
        if (count($params) != 2) {
            $this->survey_model->get_redirect_info($jkt_thanks_url, "l_err");
        }
        $pro_id = (int)$params[1];
        $imp_id = (int)$params[0];
        // 参数不符 结束！
        if($pro_id <= 0 || $imp_id <= 0){
            $this->survey_model->get_redirect_info($jkt_thanks_url, "l_err");
        }
        // 数据表是否存在，不存在 结束
        $tb = "app_project_implement_" . $pro_id;
        $tb_sql = "select CONCAT(COLUMN_NAME,',') from information_schema.COLUMNS where table_name = '$tb' ";
        $tb_isset = $this->db->query($tb_sql);
        if($tb_isset->conn_id->affected_rows == 0){
            $this->survey_model->get_redirect_info($jkt_thanks_url, "l_err");
        }
        $data_msg = $this->db->select("partner_id,groupno,partner_uid")->where(['id' => $imp_id])->get($tb)->row_array();
        if (!$data_msg) {
            $this->survey_model->get_redirect_info($jkt_thanks_url, "l_err");
        }
        $uid_str = project_link_encode($pro_id, $data_msg['partner_id'], $data_msg['groupno']);
        $partner_link = DRSAY_WEB . 'go/s/' . $uid_str . '?' . $data_msg['partner_uid'];
        redirect($partner_link);

    }

    // 邀请管理 - 添加数据，非会员、会员身份变更 发送短信验证
    public function allow()
    {
        //验证链接有效性
        $c_code = $this->uri->segment(3);
        $decode_data = decode_param($c_code);
        $res_data = [
            'msg'=>"授权失败，请联系邀请人员！"
        ];
        // 加密串解密失败 结束！
        if (!$decode_data) {
            $res_data = [
                'msg' => "请检查是否复制了正确的授权连接！"
            ];
            return $this->load->view("/bk/member_allow_error.php", $res_data);
        }
        $params = explode('_', $decode_data);
        $pid = (int)$params[0];
        $mobile = (int)$params[1];
        if($pid <= 0){
            $res_data = [
                'msg' => "请检查是否复制了正确的授权连接！"
            ];
            return $this->load->view("/bk/member_allow_error.php",$res_data);
        }
        if(!check_mobile($mobile)){
            $res_data = [
                'msg' => "请检查是否复制了正确的授权连接！"
            ];
            return $this->load->view("/bk/member_allow_error.php",$res_data);
        }
        $where = ['pid'=>$pid,'mobile'=>$mobile];
        // 是否有发送记录
        $data_isset = $this->db->where($where)->get("app_patient_fwy_add_sms")->last_row('array');
        if (!$data_isset) {
            $res_data = [
                'msg' => "没有授权发送记录，请联系邀请人员！"
            ];
            return $this->load->view("/bk/member_allow_error.php",$res_data);
        }
        // 数据是否存在邀请管理
        $btjr_patient_data = $this->db->select("pid,identity_source,mobile,btjr_is_auth,add_uid")->where($where)
            ->limit(1)->get("app_patient_fwy_add")->row_array();
        $tjr_patient_data = $this->db->select("pid,tjr_identity_source,tjr_mobile,tjr_is_auth,add_uid")
            ->where(['pid' => $pid, 'tjr_mobile' => $mobile])->limit(1)->get("app_patient_fwy_add")->row_array();
        if (!$btjr_patient_data && !$tjr_patient_data) {
            $res_data = [
                'msg' => "没有邀请数据记录，请联系邀请人员！"
            ];
            return $this->load->view("/bk/member_allow_error.php", $res_data);
        }
        
        // 注册默认地址
        $wy_register_url = "https://api.idr.cn/web/wangyi/register.html";
        // 获取网医的访问员专属邀请链接..
        $get_wy_chat_url = "https://api.idr.cn/dkt/other/sys_chat";
        $chat_url = $this->curl_post($get_wy_chat_url, []);
        if ($chat_url['code'] != 200) {
            $wy_register_url = "https://api.idr.cn/web/wangyi/register.html";
        }


        if($btjr_patient_data && $btjr_patient_data['btjr_is_auth'] == 2){
            if($chat_url['data'][$btjr_patient_data['add_uid']]){
                $wy_register_url = $chat_url['data'][$btjr_patient_data['add_uid']];
            }
            $this->db->where($where)->update('app_patient_fwy_add',['btjr_is_auth'=>1]);
        }
        if($tjr_patient_data && $tjr_patient_data['tjr_is_auth'] == 2){
            if($chat_url['data'][$tjr_patient_data['add_uid']]){
                $wy_register_url = $chat_url['data'][$tjr_patient_data['add_uid']];
            }
            $this->db->where(['pid' => $pid, 'tjr_mobile' => $mobile])->update('app_patient_fwy_add',['tjr_is_auth'=>1]);
        }

        if ($data_isset['is_click'] == 1) {
            $this->db->where($where)->update('app_patient_fwy_add_sms',['is_click'=>2]);
        }

        // 是否已经注册网医，注册不显示邀请注册连接
        $mobile_is_wy = $this->db->select("mobile")->where(['mobile'=>$mobile])->limit(1)->get("api_wy_doctors_message")->row_array();
        $show = 2; // 存在
        if (!$mobile_is_wy) {
            $show = 2; // 不存在
        }
        if(isset($btjr_patient_data)&&$btjr_patient_data['identity_source'] != RELATION_TYPE_DOCTOR){
            $show = 1; // 存在
        }
        if(isset($tjr_patient_data)&&$tjr_patient_data['tjr_identity_source'] != RELATION_TYPE_DOCTOR){
            $show = 1; // 存在
        }
        $data = [
            'wy_register_url'=>$wy_register_url,
            'show'=>$show,
        ];
        return $this->load->view("/bk/member_allow.php",$data);

    }

    /**
     * CURL  post 请求
     * @param $url //请求地址
     * @param $parameter // 参数
     * @return mixed|string
     */
    private function curl_post($url, $parameter)
    {
        $header = array(
            'Accept: application/json',
        );

        //初始化
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        // 超时设置
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);

        // 设置请求头
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        // https请求 不验证证书和hosts
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $parameter);
        //执行命令
        $data = curl_exec($curl);

        // 显示错误信息
        if (curl_error($curl)) {
            return "Error: " . curl_error($curl);
        } else {
            // 打印返回的内容
            curl_close($curl);
            return json_decode($data, true);
        }
    }


}