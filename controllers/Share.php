<?php
/**
 * Created by PhpStorm.
 * 用途：上医说分享
 * User: Amy
 * Date: 2017/12/29
 */
class Share extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('member_model');
        $this->load->model('share_model');
    }

    public function index()
    {
        die();
    }

    //邀请注册二维码
    public function share_inviter()
    {
        $uid = (int)$this->uri->segment(3) ? : 0;
        $member_info = $this->member_model->get_member_info($uid);
        if (!$member_info) {
            echo "User doesn't exist.";
            die;
        }
        $lang = $member_info['lang'];
        $get_lang = get_lang($lang);
        //查询是否有推广码
        $qr_code = $member_info['qr_code'];
        $share_qr_code = $member_info['share_qr_code'];
//        $country = $member_info['country'];
        if ($share_qr_code && file_exists('.'.$share_qr_code)){//有推广码
//            获取推荐奖励配置
//            $inviting_awards = get_sys_setting("`key`='inviting_awards' AND `country`='{$country}'");
            $data = array(
                'share_qr_code' => DRSAY_WEB.$share_qr_code.'?t='.time(),
                'tip' => $get_lang[LABEL_SHARE_DRSAY_CN_INFO],
            );
            $this->load->view('/share/share_inviter', $data);
        } else {//没有推广码
            //不存在二维码
            if (!$qr_code) {
                //生成二维码
                $qr = "./theme/images/qr_code.png";
                $avatar = DRSAY_WEB.LOGO_PIC;
                $qr_url = '/invite/index/'.$uid;
                $out_path = "qr_".$uid.".png";
                $qr_code = set_qr_code($qr_url, $qr, $avatar, $out_path);
                //更新会员信息
                $this->member_model->update_member_plus($uid, array("qr_code" => $qr_code));
            }
            //生成推广图片
            $share_qr_code = create_qr_code($qr_code, "share_qr_".$uid.".png");
            //更新会员信息
            $this->member_model->update_member_plus($uid, array("share_qr_code" => $share_qr_code));
            redirect("/share/share_inviter/".$uid);
        }
    }


    //分享朋友圈信息
    public function share_blog()
    {
        $id = $this->uri->segment(3);
        $id = (int)trim($id);
        $id = $id ? $id : 0;
        if (!$id) {die;}

        //通过博客编号获取博客信息
        $blogs_info = $this->db->query("SELECT a.*,b.avatar,b.nickname FROM app_blogs a left join app_member b on a.adder_id=b.id WHERE a.id='{$id}' LIMIT 1")->row_array();
        //博客信息
        $blogs_content = $this->db->query("SELECT * FROM app_blogs_content WHERE id='{$id}' LIMIT 1")->row_array();
        //评论信息
        $blogs_criticism = $this->db->query("SELECT a.*,b.avatar,b.nickname FROM app_blogs_criticism a left join  app_member b on a.critic=b.id WHERE blog_id='{$id}'")->result_array();
        $res_blogs_criticism = array();
        if ($blogs_criticism) {
            foreach ($blogs_criticism as $v) {
                $res_blogs_criticism[] = array(
                    "avatar" => empty($v['avatar']) ? USER_IMG_URL_NOTS.PROJECT_DEFAULT_AVATAR_PIC : USER_IMG_URL_NOTS.$v['avatar'],
                    "nickname" => $v['nickname'],
                    "content" => $v['criticism'],
                    "comment_time" => date('Y-m-d H:i:s',$v['comment_time']),
                );
            }
        }

        $res_blogs = array(
            "avatar" => empty($blogs_info['avatar']) ? USER_IMG_URL_NOTS.PROJECT_DEFAULT_AVATAR_PIC : USER_IMG_URL_NOTS.$blogs_info['avatar'],
            "nickname" => $blogs_info['nickname'],
            "add_time" => date('Y-m-d H:i:s',$blogs_info['add_time']),
            "content" => $blogs_content['content'],
            "images" => $blogs_content['image'] ? json_decode($blogs_content['image'], true) : array(),
            "blogs_criticism" => $res_blogs_criticism,
        );

        $data = array(
           "res_blogs" => $res_blogs,
        );
        $this->load->view('/share/share_blog', $data);
    }

    //隐私条款
    public function get_protocol()
    {
        $lang = (int)trim($this->uri->segment(3)) ? : DIC_DEFAULT_LANG;
        $protocol = $this->share_model->get_protocol($lang);
        $data = array(
            "protocol" => $protocol,
        );
        $this->load->view('/share/protocol', $data);
    }


}