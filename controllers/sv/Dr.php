<?php
defined('BASEPATH') or exit('No direct script access allowed');

use EasyWeChat\Factory;

class Dr extends CI_Controller
{

    public function __construct()
    {
        // error_reporting(-1);
        // ini_set('display_errors', 1);
        parent::__construct();
        $this->load->model('n_model/qk_dr_pj_model');
        $this->load->model('n_model/qk_dr_struct_model');
        $this->load->model('n_model/qk_struct_json_model');
        $this->load->model('n_model/qk_dr_val_model');
        $this->load->model('n_model/qk_dr_pj_mb_model');
        $this->load->model('n_model/web_zd_child_model');
    }

    //问卷
    public function index()
    {
        $project_id = $this->uri->segment(2);
        $r = $this->input->get('r');
        $s = $this->input->get('s');

        // 参数错误
        if (!$project_id || !$r || !$s) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }

        // 项目信息
        $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);
        if ($info['status'] != 2) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=o");
        }
        if ($info['close_time'] < time()) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=o");
        }
        $count = $this->qk_dr_val_model->count(['pid' => $project_id, 'finish_status' => 'c']);
        if ($info['sample'] > 0 && $count >= $info['sample']) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=o");
        }

        $type_list = $this->web_zd_child_model->get(['big_class_id' => 34]);
        foreach ($type_list as $k => $v) {
            if ($info['type_id'] == $v['id']) {
                $info['label_cn'] = $v['label_cn'];
            }
        }

        // 问卷结构json
        $survey = $this->qk_dr_val_model->get_one(['pid' => $project_id, 'r' => $r, 's' => $s]);
        if ($info['invite_st'] > 0) {
            $invite = $this->db->from('qk_dr_pj_mb')->where(['qk_id' => $project_id, 'mobile' => $survey['mobile']])->get();
            $invite = $invite ? $invite->row_array() : [];
            if (!$invite) {
                redirect(DRSAY_WEB . "sv/dr/finish?st=u");
            }
        }
        if ($survey) {
            if ($survey['finish_status']) {
                if ($survey['finish_status'] == 'c' && $survey['payment'] == 0 && $survey['point'] >= 100) {
                    // redirect(DRSAY_WEB . "sv/dr/reward/{$project_id}?r={$r}&s=$s");
                    redirect(DRSAY_WEB . "sv/dr/finish?st=" . $survey['finish_status'] . "&project_id=" . $project_id);
                } else {
                    redirect(DRSAY_WEB . "sv/dr/finish?st=" . $survey['finish_status'] . "&project_id=" . $project_id);
                }
            }
            if ($survey['survey_val']) {
                $question_list = json_decode($survey['survey_val'], true);
            } else {
                $result = $this->qk_struct_json_model->get_one(['qk_struct_table' => TABLE_QK_DR_STRUCT, 'project_id' => $project_id]);
                $question_list = json_decode($result['struct_json'], true);
            }
        } else {
            $result = $this->qk_struct_json_model->get_one(['qk_struct_table' => TABLE_QK_DR_STRUCT, 'project_id' => $project_id]);
            $question_list = json_decode($result['struct_json'], true);
            $survey_link = DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}";
            $data = [
                // 'survey_structure' => $result['struct_json'],
                'survey_val' => $result['struct_json'],
                'r' => $r,
                's' => $s,
                'pid' => $project_id,
                'survey_link' => $survey_link,
                'point' => $info['reward'],
                'start_time' => date('Y-m-d H:i:s'),
            ];
            $this->qk_dr_val_model->add($data);
        }

        //结束题的题号
        $finish_id = '';
        // 排序
        foreach ($question_list as $key => &$val) {
            if (count($val['list']) > 0) {
                $k_arr = [];
                $v_arr = [];
                foreach ($val['list'] as $k => $v) {
                    // 随机排序
                    if ($v['answer_sort'] > 0) {
                        $k_arr[] = $k;
                        $v_arr[] = $v;
                    }
                    if ($v['answer_jump_touch'] == '1' & $v['answer_jump_question_id'] == 'c') {
                        $finish_id = $val['question_id'];
                    }
                }
                shuffle($v_arr);
                $arr = array_combine($k_arr, $v_arr);
                foreach ($val['list'] as $k => $v) {
                    // 随机排序
                    if ($v['answer_sort'] > 0) {
                        $val['list'][$k] = $arr[$k];
                    }
                }
            }
        }

        $this->load->library('user_agent');
        $platform = $this->agent->platform();
        $browser = $this->agent->browser();
        $browser_version = $this->agent->version();
        // 设备型号
        $MobileBrand = $this->getClientMobileBrand($_SERVER['HTTP_USER_AGENT']);

        $home_chart = [
            'title' => '问卷开始',
            'info' => $info,
            'question_list' => json_encode($question_list, true),
            'ip' => getIP(),
            'platform' => $platform,
            'browser_version' => $browser_version,
            'browser' => $browser,
            'MobileBrand' => $MobileBrand,
            'survey' => $survey, // 答卷信息
            'finish_id' => $finish_id,
            'r' => $r,
            's' => $s,
        ];
        $this->load->view('/sv/dr/index.php', $home_chart);
    }

    // 医师调研绑定
    public function survey()
    {
        $project_id = $this->uri->segment(4);
        $uid = $this->uri->segment(5);
        $param = $this->uri->segment(6);

        // 参数错误
        if (!$project_id || !$uid) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }

        $user = $this->db->from(TABLE_MB_DR)->where(['dr_id' => $uid])->get();
        $user = $user ? $user->row_array() : [];

        if (!$user) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }

        $this->load->helper('cookie');
        set_cookie('project_id', $project_id, 7 * 24 * 3600);
        set_cookie('uid', $uid, 7 * 24 * 3600);
        if ($param) {
            set_cookie('param', $param, 7 * 24 * 3600);
        }

        // 问卷结构json
        $info = $this->qk_dr_val_model->get_one(['pid' => $project_id, 'uid' => $uid]);

        if ($info) {
            if ($info['finish_status']) {
                if ($info['finish_status'] == 'c' && $info['payment'] == 0 && $info['point'] >= 100) {
                    // redirect(DRSAY_WEB . "sv/dr/reward_auth");
                    redirect(DRSAY_WEB . "sv/dr/finish?st=" . $info['finish_status'] . "&project_id=" . $project_id);
                } else {
                    redirect(DRSAY_WEB . "sv/dr/finish?st=" . $info['finish_status'] . "&project_id=" . $project_id);
                }
            } else {
                $this->qk_dr_pj_mb_model->edit(['dr_id' => $uid, 'qk_id' => $project_id], ['st' => 1]);
                redirect($info['survey_link']);
            }
        } else {
            $r = 1;
            $s = strtoupper(substr(md5('jiangkangtong' . time() . rand(1000, 9999)), 0, 8));
            $struct = $this->qk_struct_json_model->get_one(['project_id' => $project_id]);
            $survey_link = DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}";
            $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);
            $data = [
                'pid' => $project_id,
                'survey_link' => $survey_link,
                'r' => $r,
                's' => $s,
                // 'survey_structure' => $struct['struct_json'],
                'survey_val' => $struct['struct_json'],
                'point' => $info['reward'],
                'uid' => $uid,
                'name' => $user['dr_name'],
                'nice' => $user['dr_nice'],
                'gender' => $user['dr_gender'],
                'mobile' => $user['dr_mobile'],
                'unit' => $user['unit_name'],
                'department' => $user['dr_department'],
                'dr_standard_department_big' => $user['dr_standard_department_big'],
                'dr_standard_department_small' => $user['dr_standard_department_small'],
                'title' => $user['dr_job_title'],
                'province' => $user['province'],
                'city' => $user['city'],
                'city_level' => $user['city_level'],
                'district' => $user['district'],
                'unit_area_type' => $user['unit_area_type'],
                'unit_level' => $user['unit_level'],
                'unit_type' => $user['unit_type'],
                'dr_sfz' => $user['dr_sfz'],
                'dr_zyz' => $user['dr_zyz'],
                'dr_scope_of_practice' => $user['dr_scope_of_practice'],
                'dr_profession' => $user['dr_profession'],
                'start_time' => date('Y-m-d H:i:s'),
            ];
            $this->qk_dr_val_model->add($data);
            $this->qk_dr_pj_mb_model->edit(['dr_id' => $uid, 'qk_id' => $project_id], ['st' => 1]);

            redirect(DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}");
        }
    }

    // 不绑定医师
    public function project()
    {
        $this->load->helper('cookie');
        $project_id = $this->uri->segment(4);
        $mobile = $this->input->get('mobile');
        $channel = $this->input->get('channel');

        $r = get_cookie('r');
        $s = get_cookie('s');
        $pid = get_cookie('project_id');
        if (!$project_id) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }

        if ($mobile) {
            // 问卷结构json
            $info = $this->qk_dr_val_model->get_one(['pid' => $project_id, 'mobile' => $mobile]);
            if ($info) {
                redirect(DRSAY_WEB . "survey/{$project_id}?r={$info['r']}&s={$info['s']}");
            }
        }

        if ($r && $s && $pid == $project_id) {
            $survey_link = DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}";
            redirect($survey_link);
        } else {
            $r = $channel ? $channel : 2;
            $s = strtoupper(substr(md5('jiangkangtong' . time() . rand(1000, 9999)), 0, 8));
            $survey_link = DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}";
            $struct = $this->qk_struct_json_model->get_one(['project_id' => $project_id]);
            $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);
            $data = [
                'pid' => $project_id,
                'survey_link' => $survey_link,
                'r' => $r,
                's' => $s,
                // 'survey_structure' => $struct['struct_json'],
                'survey_val' => $struct['struct_json'],
                'mobile' => $mobile,
                'point' => $info['reward'],
                'start_time' => date('Y-m-d H:i:s'),
            ];
            $this->qk_dr_val_model->add($data);
            redirect(DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}");
        }
    }

    // 登录
    public function login()
    {
        $project_id = $this->uri->segment(4);
        // 项目信息
        $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);

        if ($info['close_time'] < time()) {
            redirect(DRSAY_WEB . "sv/dr/baier?st=o");
        }

        $home_chart = [
            'project_id' => $project_id,
            'info' => $info,
        ];
        $this->load->view('/sv/dr/login', $home_chart);
    }

    // 刷新问卷
    public function fresh()
    {
        $project_id = $this->uri->segment(4);
        $channel = $this->input->get('channel');
        $r = $channel ? $channel : 2;
        $s = strtoupper(substr(md5('jiangkangtong' . time() . rand(1000, 9999)), 0, 8));
        $survey_link = DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}";
        $struct = $this->qk_struct_json_model->get_one(['project_id' => $project_id]);
        $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);
        $data = [
            'pid' => $project_id,
            'survey_link' => $survey_link,
            'r' => $r,
            's' => $s,
            'survey_val' => $struct['struct_json'],
            'point' => $info['reward'],
            'start_time' => date('Y-m-d H:i:s'),
        ];
        $this->qk_dr_val_model->add($data);
        redirect(DRSAY_WEB . "survey/{$project_id}?r={$r}&s={$s}");
    }

    // 提交
    public function submit()
    {
        $post = $this->input->post();

        $info = json_decode($post['survey_val'], true);
        $data = [
            'survey_val' => $post['survey_val'],
            'last_question_id' => $post['last_question_id'],
            'status' => $post['status'],
            'finish_status' => $post['finish_status'],
            'start_time' => $info['interview_start']['list'][0]['answer_value'] . ' ' . $info['interview_start']['list'][1]['answer_value'],
            'end_time' => $info['interview_end']['list'][0]['answer_value'] . ' ' . $info['interview_end']['list'][1]['answer_value'],
            'answer_differ' => intval($info['Timediff']['list'][0]['answer_value']),
        ];

        $this->qk_dr_val_model->edit(['pid' => intval($post['pid']), 'r' => $post['r'], 's' => $post['s']], $data);
        echo json_encode(['err_msg' => 'success', 'result' => '']);exit;
    }

    //结束
    public function finish()
    {
        $st = $this->input->get('st');
        $project_id = $this->input->get('project_id');
        $this->load->model('n_model/qk_dr_reback_model');
        $msg = $this->qk_dr_reback_model->get_one(['type' => $st, 'qk_id' => $project_id]);
        // 项目信息
        $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);

        $this->load->helper('cookie');
        $r = get_cookie('r');
        $s = get_cookie('s');
        $pid = get_cookie('project_id');
        $uid = get_cookie('uid');
        
        $st_msg = $msg['cn'] ?? '';
        if (!$st_msg) {
            switch ($st) {
                case 's':
                    $st_msg = "很遗憾不符合条件被甄别了，请继续参与其他问卷，谢谢！";
                    break;
                case 'q':
                    $st_msg = "您来晚了，符合你的配额收集已满，下次早点参与，谢谢！";
                    break;
                case 'c':
                    $st_msg = "您已经成功完本次成问卷，请继续参与其他问卷，谢谢！";
                    break;
                case 'o':
                    $st_msg = "您来晚了该问卷已经结束，请继续参与其他问卷，谢谢！";
                    break;
                case 'f':
                    $st_msg = "您已经回答过该问卷，不能重复参与，请继续回答其他问卷，谢谢！";
                    break;
                case 'u':
                    $st_msg = "您使用的链接地址无效操作失败！";
                    break;
                default: //除以上之外的告警
                    $st_msg = "您已完成问卷，谢谢！";
                    break;
            }
        }
        $home_chart = [
            'title' => '问卷结束',
            'st_msg' => $st_msg,
            'project_id' => $project_id,
            'r' => $r,
            's' => $s,
            'st' => $st,
            'info' => $info,
        ];
        $this->load->view('/sv/dr/finish.php', $home_chart);
    }

    // 拜尔项目结束
    public function baier()
    {
        $st = $this->input->get('st');
        $project_id = $this->input->get('project_id');
        $this->load->model('n_model/qk_dr_reback_model');
        $msg = $this->qk_dr_reback_model->get_one(['type' => $st, 'qk_id' => $project_id]);

        $st_msg = $msg['cn'] ?? '';
        if (!$st_msg) {
            switch ($st) {
                case 's':
                    $st_msg = "很遗憾不符合条件被甄别了，请继续参与其他问卷，谢谢！";
                    break;
                case 'q':
                    $st_msg = "您来晚了，符合你的配额收集已满，下次早点参与，谢谢！";
                    break;
                case 'c':
                    $st_msg = "您已经成功完本次成问卷，请继续参与其他问卷，谢谢！";
                    break;
                case 'o':
                    $st_msg = "您来晚了该问卷已经结束，请继续参与其他问卷，谢谢！";
                    break;
                case 'f':
                    $st_msg = "您已经回答过该问卷，不能重复参与，请继续回答其他问卷，谢谢！";
                    break;
                case 'u':
                    $st_msg = "您使用的链接地址无效操作失败！";
                    break;
                default: //除以上之外的告警
                    $st_msg = "您已完成问卷，谢谢！";
                    break;
            }
        }
        $home_chart = [
            'title' => '问卷结束',
            'st_msg' => $st_msg,
            'project_id' => $project_id,
            'st' => $st,
        ];
        $this->load->view('/sv/dr/baier.php', $home_chart);
    }

    // 81号项目结束页
    public function bird(){
        $this->load->view('/sv/dr/bird.php', []);
    }
    // 81号项目结束页
    public function birds(){
        $this->load->view('/sv/dr/birds.php', []);
    }

    // 问卷数据核验
    public function view()
    {
        $project_id = $this->uri->segment(4);
        $r = $this->input->get('r');
        $s = $this->input->get('s');

        // 项目信息
        $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);

        // 问卷结构json
        $survey = $this->qk_dr_val_model->get_one(['pid' => $project_id, 'r' => $r, 's' => $s]);
        if ($survey) {
            if ($survey['survey_val']) {
                $question_list = json_decode($survey['survey_val'], true);
            } else {
                $result = $this->qk_struct_json_model->get_one(['qk_struct_table' => TABLE_QK_DR_STRUCT, 'project_id' => $project_id]);
                $question_list = json_decode($result['struct_json'], true);
            }
        } else {
            redirect("/sv/dr/finish?st=u");
        }

        //结束题的题号
        $finish_id = '';
        // 排序
        foreach ($question_list as $key => &$val) {
            if (count($val['list']) > 0) {
                $k_arr = [];
                $v_arr = [];
                foreach ($val['list'] as $k => $v) {
                    // 随机排序
                    if ($v['answer_sort'] > 0) {
                        $k_arr[] = $k;
                        $v_arr[] = $v;
                    }
                    if ($v['answer_jump_touch'] == '1' & $v['answer_jump_question_id'] == 'c') {
                        $finish_id = $val['question_id'];
                    }
                }
                shuffle($v_arr);
                $arr = array_combine($k_arr, $v_arr);
                foreach ($val['list'] as $k => $v) {
                    // 随机排序
                    if ($v['answer_sort'] > 0) {
                        $val['list'][$k] = $arr[$k];
                    }
                }
            }
        }

        $this->load->library('user_agent');
        $platform = $this->agent->platform();
        $browser = $this->agent->browser();
        $browser_version = $this->agent->version();
        // 设备型号
        $MobileBrand = $this->getClientMobileBrand($_SERVER['HTTP_USER_AGENT']);

        $home_chart = [
            'title' => '问卷开始',
            'info' => $info,
            'question_list' => json_encode($question_list, true),
            'ip' => getIP(),
            'platform' => $platform,
            'browser_version' => $browser_version,
            'browser' => $browser,
            'MobileBrand' => $MobileBrand,
            'survey' => $survey, // 答卷信息
            'finish_id' => $finish_id,
            'r' => $r,
            's' => $s,
        ];
        $this->load->view('/sv/dr/index.php', $home_chart);
    }

    // 简易问卷
    public function simple()
    {
        $home_chart = [
            'title' => '问卷开始',
        ];
        $this->load->view('/sv/dr/simple.php', $home_chart);
    }

    // 查看授权书图片
    public function img()
    {
        error_reporting(-1);
        ini_set('display_errors', 1);
        $project_id = $this->uri->segment(4);
        $r = $this->input->get('r');
        $s = $this->input->get('s');

        // 项目信息
        $info = $this->qk_dr_pj_model->get_one(['id' => $project_id]);

        // 问卷结构json
        $survey = $this->qk_dr_val_model->get_one(['pid' => $project_id, 'r' => $r, 's' => $s]);
        if ($survey) {
            $question_list = json_decode($survey['survey_val'], true);
        } else {
            redirect("/sv/dr/finish?st=u");
        }

        $cert = [];
        $sign_path = '';
        foreach ($question_list as $key => $value) {
            if ($value['type'] == "certificate") {
                $cert['name'] = $value['list'][0]['answer_value'];
                $cert['mobile'] = $value['list'][1]['answer_value'];
                $cert['hospital'] = $value['list'][2]['answer_value'] ?? '';
                $cert['department'] = $value['list'][3]['answer_value'] ?? '';
                $cert['card_id'] = $value['list'][4]['answer_value'] ?? '';
                $cert['y'] = $value['sign_y'];
                $cert['m'] = $value['sign_m'];
                $cert['d'] = $value['sign_d'];
                $sign_path = $value['sign_value'];
            }
        }

        $path = "/uploads/survey/certificate/{$project_id}/";
        if (!is_dir($path)) {
            mk_dir($path);
        }
        // 图片地址
        $filename = $path . $r . '_' . $s . ".png";

        if (file_exists(substr($filename, 1))) {
            // redirect($filename);
        }

        $backgroundPath = SYSTEMPATH . "/theme/sv/img/survey_{$project_id}_bg.png"; //背景图
        $img = imagecreatefrompng($backgroundPath);

        // 签名图
        $sign_path = SYSTEMPATH . substr($sign_path, 20);
        $img_sign = imagecreatefrompng($sign_path);
        // $background_color = imagecolorallocate($img_sign, 255, 255, 255);
        // imagefill($img_sign, 0, 0, $background_color); //替换成白色
        // imagesavealpha($img_sign, true);

        // 签名图片合并到授权书上
        imagecopy($img, $img_sign, 90, 725, 0, 0, 600, 260);
        // imagecopymerge($img, $img_sign, 90, 725, 0, 0, 600, 260, 100);

        $arr = [
            [
                "name" => $cert["name"],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/PingFang Medium_downcc.otf",
                "x" => 420,
                "y" => 285,
                "size" => 17,
                "color" => "0",
            ],
            [
                "name" => $cert['mobile'],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/PingFang Medium_downcc.otf",
                "x" => 420,
                "y" => 347,
                "size" => 17,
                "color" => "44",
            ],
            [
                "name" => $cert['hospital'],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/PingFang Medium_downcc.otf",
                "x" => 420,
                "y" => 410,
                "size" => 17,
                "color" => "44",
            ],
            [
                "name" => $cert['department'],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/PingFang Medium_downcc.otf",
                "x" => 420,
                "y" => 470,
                "size" => 17,
                "color" => "0",
            ],
            [
                "name" => $cert['card_id'],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/PingFang Medium_downcc.otf",
                "x" => 420,
                "y" => 532,
                "size" => 17,
                "color" => "0",
            ],
            [
                "name" => $cert['y'],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/Arial Rounded MT Bold.ttf",
                "x" => 430,
                "y" => 595,
                "size" => 17,
                "color" => "0",
            ],
            [
                "name" => $cert['m'],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/Arial Rounded MT Bold.ttf",
                "x" => 540,
                "y" => 595,
                "size" => 17,
                "color" => "0",
            ],
            [
                "name" => $cert['d'],
                "font" => SYSTEMPATH . "/theme/project_point/fonts/Arial Rounded MT Bold.ttf",
                "x" => 640,
                "y" => 595,
                "size" => 17,
                "color" => "0",
            ],

        ];
        foreach ($arr as $v) {
            //设置字体颜色
            $black = imagecolorallocate($img, $v["color"], $v["color"], $v["color"]);
            //将ttf文字写到图片中
            imagettftext($img, $v["size"], 0, $v["x"], $v["y"], $black, $v["font"], $v["name"]);
        }
        imagepng($img, SYSTEMPATH . $filename);
        header("Content-type: image/png");
        imagepng($img);
        imagedestroy($img);
    }

    // 统计
    public function statistic()
    {
        error_reporting(-1);
        ini_set('display_errors', 1);
        $this->load->helper('cookie');
        $project_id = $this->uri->segment(4);
        $r = get_cookie('r');
        $s = get_cookie('s');

        // 选择题列表
        $question_list = $this->qk_dr_struct_model->get_all_choose($project_id);
        // 所有问卷结果
        $list = $this->qk_dr_val_model->get(['pid' => $project_id], 'id,pid,province,unit_level,title,city_level,unit_type');
        // 描述题列表
        $describe_list = $this->qk_dr_struct_model->get_all_describe($project_id);
        // 我的结果
        $my_list = $this->db->from('qk_dr_answer_questions_time_log')->where(['pid' => $project_id, 'r' => $r, 's' => $s])->get();
        $my_list = $my_list ? $my_list->result_array() : [];

        foreach ($question_list as $key => &$val) {
            $item = $this->qk_dr_struct_model->get(['project_id' => $project_id, 'question_id' => $val['question_id'], 'type' => $val['type']], 'variable_id,answer_code,answer_label,count_size,question_view_time');
            $val['list'] = $item;
        }

        $time_control = [];
        foreach ($describe_list as $k => $v) {
            // 答题时间
            $time_control[$v['question_id']] = ['value' => intval($v['question_view_time'] / count($list)), 'name' => $v['answer_label']];
        }

        $area = [];
        $hospital_grade = [];
        $doctor_title = [];
        $customer = [];
        $patient = [];
        $grade_statistics = [];
        $city_level = [];
        $unit_type = [];
        foreach ($list as $k => $v) {
            // 地区(省份)
            if ($v['province'] && !in_array($v['province'], array_keys($area))) {
                $area[$v['province']] = 1;
            } else if ($v['province']) {
                $area[$v['province']] = $area[$v['province']] + 1;
            }
            // 医院等级(一级二级三级)
            if ($v['unit_level'] && !in_array($v['unit_level'], array_keys($hospital_grade))) {
                $hospital_grade[$v['unit_level']] = 1;
            } else if ($v['unit_level']) {
                $hospital_grade[$v['unit_level']] = $hospital_grade[$v['unit_level']] + 1;
            }
            // 医师职称(主任，主治等)
            if ($v['title'] && !in_array($v['title'], array_keys($doctor_title))) {
                $doctor_title[$v['title']] = 1;
            } else if ($v['title']) {
                $doctor_title[$v['title']] = $doctor_title[$v['title']] + 1;
            }
            // 城市类别(一线，二线等)
            if ($v['city_level'] && !in_array($v['city_level'], array_keys($city_level))) {
                $city_level[$v['city_level']] = 1;
            } else if ($v['city_level']) {
                $city_level[$v['city_level']] = $city_level[$v['city_level']] + 1;
            }
            // 医院类别(公立，私立等)
            if ($v['unit_type'] && !in_array($v['unit_type'], array_keys($unit_type))) {
                $unit_type[$v['unit_type']] = 1;
            } else if ($v['unit_type']) {
                $unit_type[$v['unit_type']] = $unit_type[$v['unit_type']] + 1;
            }

        }

        ksort($hospital_grade);

        $home_chart = [
            'title' => '问卷开始',
            'project_id' => $project_id,
            'question_list' => $question_list,
            'list' => $list,
            'my_list' => $my_list,
            'area' => $area,
            'hospital_grade' => $hospital_grade,
            'doctor_title' => $doctor_title,
            'customer' => $customer,
            'patient' => $patient,
            'grade_statistics' => $grade_statistics,
            'city_level' => $city_level,
            'unit_type' => $unit_type,
            'time_control' => $time_control,
        ];
        $this->load->view('/sv/dr/statistic.php', $home_chart);
    }

    /**
     * 获取客户端手机型号
     * @param $agent    //$_SERVER['HTTP_USER_AGENT']
     * @return array[mobile_brand]      手机品牌
     * @return array[mobile_ver]        手机型号
     */
    private function getClientMobileBrand($agent = '')
    {
        // $agent = strtolower($agent);
        $start = strpos($agent, '(') + 1;
        $end = strpos($agent, ')');
        $mobile_brand = substr($agent, $start, $end - $start);
        $mobile_ver = '';
        return $mobile_brand . $mobile_ver;
    }

    public function baiduSound()
    {
        error_reporting(-1);
        ini_set('display_errors', 1);
        //定义尺寸和大小
        $config['upload_path'] = "./uploads/survey/" . date("Y") . "/" . date("m") . "/" . date("d") . "/";
        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0777, true);
        }
        $config['encrypt_name'] = true;
        $config['allowed_types'] = 'wav';
        $config['remove_spaces'] = true;
        $config['max_size'] = 1024 * 10;
        $this->load->library('upload', $config);
        $this->upload->initialize($config);

        if ($this->upload->do_upload('file')) {
            $upload_data = $this->upload->data();
            $upload_filename = "./uploads/survey/" . date("Y") . "/" . date("m") . "/" . date("d") . "/" . $upload_data['file_name'];
        } else {
            $upload_filename = "";
            echo json_encode(['err_msg' => 'fail', 'result' => $this->upload->display_errors()]);exit;
        }

        $this->load->library('AipSpeech');
        $client = new AipSpeech();
        // 识别本地文件
        $res = $client->asr(file_get_contents($upload_filename), 'wav', 16000, ['dev_pid' => 1537]);

        $link = DRSAY_WEB . "uploads/survey/" . date("Y") . "/" . date("m") . "/" . date("d") . "/" . $upload_data['file_name'];

        echo json_encode(['err_msg' => 'success', 'result' => $res['result'] ?? [], 'link' => $link]);exit;

    }

    public function upload()
    {
        error_reporting(-1);
        ini_set('display_errors', 1);
        //定义尺寸和大小
        $config['upload_path'] = "./uploads/survey/" . date("Y") . "/" . date("m") . "/" . date("d") . "/";
        if (!file_exists($config['upload_path'])) {
            mkdir($config['upload_path'], 0777, true);
        }
        $config['encrypt_name'] = true;
        $config['allowed_types'] = 'mp4|rmvb|avi|wmv|mp3|wma|cda|png|jpg|jpeg|gif|bmp';
        $config['remove_spaces'] = true;
        $config['max_size'] = 1024 * 10;
        $this->load->library('upload', $config);
        $this->upload->initialize($config);

        if ($this->upload->do_upload('file')) {
            $upload_data = $this->upload->data();
            $upload_filename = DRSAY_WEB . "uploads/survey/" . date("Y") . "/" . date("m") . "/" . date("d") . "/" . $upload_data['file_name'];
            echo json_encode(['err_msg' => 'success', 'result' => $upload_filename]);exit;
        } else {
            echo json_encode(['err_msg' => 'fail', 'result' => $this->upload->display_errors()]);exit;
        }
    }

    public function uploadBase64()
    {
        error_reporting(-1);
        ini_set('display_errors', 1);
        $base64 = $this->input->post('file');
        $upload_path = "./uploads/survey/" . date("Y") . "/" . date("m") . "/" . date("d") . "/";
        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0777, true);
        }
        $file_name = md5(time() . rand(10000, 9999)) . '.png';
        $upload_filename = DRSAY_WEB . "uploads/survey/" . date("Y") . "/" . date("m") . "/" . date("d") . "/" . $file_name;

        file_put_contents($upload_path . $file_name, base64_decode($base64));
        echo json_encode(['err_msg' => 'success', 'result' => $upload_filename]);exit;
    }

    public function add_comment()
    {
        $post = $this->input->post();
        $data = [
            'pid' => $post['pid'],
            'question_id' => $post['question_id'],
            'message' => $post['message'],
            'r' => $post['r'],
            's' => $post['s'],
        ];
        $this->db->insert('qk_dr_comment', $data);
        echo json_encode(['err_msg' => 'success', 'result' => $this->db->insert_id()]);exit;
    }

    public function del_comment()
    {
        $post = $this->input->post('id');
        $this->db->where(['id' => $post])->update('qk_dr_comment', ['message' => '']);
        echo json_encode(['err_msg' => 'success', 'result' => '']);exit;
    }

    public function shumb_up()
    {
        $r = $this->input->post('r');
        $s = $this->input->post('s');
        $pid = $this->input->post('pid');
        $question_id = $this->input->post('question_id');
        $shumb_up = $this->input->post('shumb_up');
        $info = $this->db->from('qk_dr_comment')->where(['r' => $r, 's' => $s, 'pid' => $pid, 'question_id' => $question_id])->get();
        $info = $info ? $info->row_array() : [];
        if ($info) {
            $this->db->where(['id' => $info['id']])->update('qk_dr_comment', ['shumb_up' => $shumb_up]);
        } else {
            $this->db->insert('qk_dr_comment', ['r' => $r, 's' => $s, 'pid' => $pid, 'question_id' => $question_id, 'shumb_up' => $shumb_up]);
        }
        echo json_encode(['err_msg' => 'success', 'result' => '']);exit;
    }

    public function time_log()
    {
        $r = $this->input->post('r');
        $s = $this->input->post('s');
        $pid = $this->input->post('pid');
        $question_id = $this->input->post('question_id');
        $uid = $this->input->post('uid');
        $name = $this->input->post('name');
        $unit_name = $this->input->post('unit_name');
        $unit_level = $this->input->post('unit_level');
        $department = $this->input->post('department');
        $title = $this->input->post('title');
        $view_time_diffe = $this->input->post('view_time_diffe');
        $start_time = date('Y-m-d H:i:s', $this->input->post('start_time'));

        if($question_id){
            $this->db->insert('qk_dr_answer_questions_time_log', ['r' => $r, 's' => $s, 'pid' => $pid, 'question_id' => $question_id, 'uid' => $uid, 'name' => $name, 'unit_name' => $unit_name, 'unit_level' => $unit_level, 'department' => $department, 'title' => $title, 'view_time_diffe' => $view_time_diffe, 'start_time' => $start_time]);
            $total = intval($view_time_diffe);
            $sql = "update qk_dr_struct set question_view_time=question_view_time+" . $total . "  where project_id='{$pid}' and question_id='{$question_id}'";
            $this->db->query($sql);
        }else{
            $sql = "update qk_dr_val set answer_differ=" . $view_time_diffe . "  where pid='{$pid}' and r='{$r}'and s='{$s}'";
            $this->db->query($sql);
        }

        echo json_encode(['err_msg' => 'success', 'result' => '']);exit;
    }

    public function count_size()
    {
        $pid = $this->input->post('pid');
        $question_id = $this->input->post('question_id');
        $code = $this->input->post('code');
        $count_size = $this->input->post('count_size');
        if ($count_size) {
            $sql = "update qk_dr_struct set count_size=count_size" . $count_size . "  where project_id='{$pid}' and question_id='{$question_id}' and answer_code='{$code}'";
            $this->db->query($sql);
        }
        // $this->qk_dr_struct_model->edit(['project_id'=>$pid,'question_id'=>$question_id,'answer_code'=>$code],[]);
        echo json_encode(['err_msg' => 'success', 'result' => '']);exit;
    }

    // home页
    public function home()
    {
        // 总条数
        $total = $this->qk_dr_pj_model->all_count($type_id, $search_key);
        $list = [];
        if ($total > 0) {
            $list = $this->qk_dr_pj_model->all_get($type_id, $search_key, $offset);
        }

        $this->load->library('pagination');
        $config['use_page_numbers'] = true;
        $config['page_query_string'] = true;
        $config['enable_query_strings'] = true;
        $config['base_url'] = '/sv/dr/home?search_key=' . $search_key . '&type_id=' . $type_id;
        $config['total_rows'] = $total;
        $config['per_page'] = PAGE_NUM;
        $this->pagination->initialize($config);

        $home_chart = [
            'title' => '项目',
            'total' => $total,
            'list' => $list,
        ];
        $this->load->view('/sv/dr/home.php', $home_chart);
    }

    // 邀请
    public function invite()
    {
        $page_num = 50;
        $offset = $this->input->get('per_page', true);
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset) - 1) * $page_num : 0;
        $total = $this->db->where(['pid' => 40, 'finish_status' => 'c'])->count_all_results('qk_dr_val');

        $list = $this->db->select('survey_val')->where(['pid' => 40, 'finish_status' => 'c'])->from('qk_dr_val')->limit($page_num, $offset)->get();
        $list = $list ? $list->result_array() : [];
        foreach ($list as $key => $val) {
            $question_list = json_decode($val['survey_val'], true);
            foreach ($question_list as $k => $v) {
                if ($k == 'Q1') {
                    $list[$key]['province'] = $v['list'][0]['answer_value'];
                    $list[$key]['city'] = $v['list'][1]['answer_value'];
                    $list[$key]['hospital'] = $v['list'][2]['answer_value'];
                    $list[$key]['department'] = $v['list'][3]['answer_value'];
                    $list[$key]['name'] = $v['list'][4]['answer_value'];
                    $list[$key]['mobile'] = $v['list'][5]['answer_value'];
                    $list[$key]['pro_num'] = $v['list'][6]['answer_value']; //资格证
                    $list[$key]['duty'] = $v['list'][7]['answer_value']; //职务
                }
                if ($k == 'Q2') {
                    foreach ($v['list'] as $k2 => $v2) {
                        if ($v2['answer_value'] == 1) {
                            $list[$key]['title'] = $v['list'][$k2]['answer_label']; //职称
                            if ($k2 == 3) {
                                $list[$key]['title'] = $v['list'][4]['answer_value'] ?? ''; //职称
                            }
                        }
                    }
                }
                if ($k == 'Q3') {
                    foreach ($v['list'] as $k2 => $v2) {
                        if ($v2['answer_value'] == 1) {
                            $list[$key]['type'] = $v['list'][$k2]['answer_label']; //执业类别
                        }
                    }
                }
                if ($k == 'Q4') {
                    foreach ($v['list'] as $k2 => $v2) {
                        if ($v2['answer_value'] == 1) {
                            $list[$key]['auth'] = $v['list'][$k2]['answer_label']; //授权
                        }
                    }
                }
            }
            unset($list[$key]['survey_val']);
        }

        $this->load->library('pagination');
        $config['use_page_numbers'] = true;
        $config['page_query_string'] = true;
        $config['enable_query_strings'] = true;
        $config['base_url'] = '/sv/dr/invite/';
        $config['total_rows'] = $total;
        $config['per_page'] = $page_num;
        $this->pagination->initialize($config);

        $data = [
            'title' => '邀请',
            'list' => $list,
            'total' => $total,
            'pages' => $this->pagination->create_links(),
        ];
        $this->load->view('/sv/dr/invite', $data);
    }

    public function up_invite_status()
    {
        $id = $this->input->post('id');
        $this->db->where(['id' => $id])->update('qk_dr_pj_mb', ['st' => 1]);
    }

    public function time()
    {
        $id = 1;
        for ($i = 0; $i < 221; $i++) {
            $a = rand(0, 59);
            $a = $a < 10 ? ('0' . $a) : $a;
            $b = rand(0, 59);
            $b = $b < 10 ? ('0' . $b) : $b;
            $start = "2020-06-01 " . rand(14, 22) . ":" . $a . ":" . $b;
            echo $id . '  ';
            echo $start;
            echo "<br>";
            $this->db->where(['id' => $id])->update('qk_dr_val', ['time1' => $start]);
            $id++;
        }
        for ($i = 0; $i < 63; $i++) {
            $h = rand(6, 14);
            $h = $h < 10 ? ('0' . $h) : $h;
            $a = rand(0, 59);
            $a = $a < 10 ? ('0' . $a) : $a;
            $b = rand(0, 59);
            $b = $b < 10 ? ('0' . $b) : $b;
            $start = "2020-06-02 " . $h . ":" . $a . ":" . $b;

            echo $id . '  ';
            echo $start;
            echo "<br>";
            $this->db->where(['id' => $id])->update('qk_dr_val', ['time1' => $start]);
            $id++;
        }
        for ($i = 0; $i < 364; $i++) {
            $h = rand(14, 23);
            $h = $h < 10 ? ('0' . $h) : $h;
            $a = rand(0, 59);
            $a = $a < 10 ? ('0' . $a) : $a;
            $b = rand(0, 59);
            $b = $b < 10 ? ('0' . $b) : $b;
            $start = "2020-06-02 " . $h . ":" . $a . ":" . $b;

            echo $id . '  ';
            echo $start;
            echo "<br>";
            $this->db->where(['id' => $id])->update('qk_dr_val', ['time1' => $start]);
            $id++;
        }
        for ($i = 0; $i < 82; $i++) {
            $h = rand(6, 14);
            $h = $h < 10 ? ('0' . $h) : $h;
            $a = rand(0, 59);
            $a = $a < 10 ? ('0' . $a) : $a;
            $b = rand(0, 59);
            $b = $b < 10 ? ('0' . $b) : $b;
            $start = "2020-06-03 " . $h . ":" . $a . ":" . $b;

            echo $id . '  ';
            echo $start;
            echo "<br>";
            $this->db->where(['id' => $id])->update('qk_dr_val', ['time1' => $start]);
            $id++;
        }
        for ($i = 0; $i < 228; $i++) {
            $h = rand(14, 23);
            $h = $h < 10 ? ('0' . $h) : $h;
            $a = rand(0, 59);
            $a = $a < 10 ? ('0' . $a) : $a;
            $b = rand(0, 59);
            $b = $b < 10 ? ('0' . $b) : $b;
            $start = "2020-06-03 " . $h . ":" . $a . ":" . $b;

            echo $id . '  ';
            echo $start;
            echo "<br>";
            $this->db->where(['id' => $id])->update('qk_dr_val', ['time1' => $start]);
            $id++;
        }
        for ($i = 0; $i < 98; $i++) {
            $h = rand(6, 14);
            $h = $h < 10 ? ('0' . $h) : $h;
            $a = rand(0, 59);
            $a = $a < 10 ? ('0' . $a) : $a;
            $b = rand(0, 59);
            $b = $b < 10 ? ('0' . $b) : $b;
            $start = "2020-06-04 " . $h . ":" . $a . ":" . $b;

            echo $id . '  ';
            echo $start;
            echo "<br>";
            $this->db->where(['id' => $id])->update('qk_dr_val', ['time1' => $start]);
            $id++;
        }
        for ($i = 0; $i < 382; $i++) {
            $h = rand(14, 23);
            $h = $h < 10 ? ('0' . $h) : $h;
            $a = rand(0, 59);
            $a = $a < 10 ? ('0' . $a) : $a;
            $b = rand(0, 59);
            $b = $b < 10 ? ('0' . $b) : $b;
            $start = "2020-06-04 " . $h . ":" . $a . ":" . $b;

            echo $id . '  ';
            echo $start;
            echo "<br>";
            $this->db->where(['id' => $id])->update('qk_dr_val', ['time1' => $start]);
            $id++;
        }
    }

    // 绑定医师的二维码
    public function qrcode()
    {
        $pid = $this->uri->segment(4);
        $id = $this->uri->segment(5);
        $info = $this->qk_dr_pj_model->get_one(['id' => $pid]);
        if ($id) {
            $list = $this->qk_dr_pj_mb_model->get(['qk_id' => $pid, 'id' => $id]);
        } else {
            $list = $this->qk_dr_pj_mb_model->get(['qk_id' => $pid]);
        }
        $home_chart = [
            'title' => '二维码',
            'info' => $info,
            'list' => $list,
        ];
        $this->load->view('/sv/dr/qrcode.php', $home_chart);
    }

    // 不绑定医师的二维码
    public function qrcode2()
    {
        $pid = $this->uri->segment(4);
        $info = $this->qk_dr_pj_model->get_one(['id' => $pid]);
        $home_chart = [
            'title' => '二维码',
            'info' => $info,
        ];
        $this->load->view('/sv/dr/qrcode2.php', $home_chart);
    }

    // 带短信验证的二维码
    public function qrcode3()
    {
        $pid = $this->uri->segment(4);
        $info = $this->qk_dr_pj_model->get_one(['id' => $pid]);
        $home_chart = [
            'title' => '二维码',
            'info' => $info,
        ];
        $this->load->view('/sv/dr/qrcode3.php', $home_chart);
    }

    // 刷新问卷的二维码
    public function qrcode4()
    {
        $pid = $this->uri->segment(4);
        $info = $this->qk_dr_pj_model->get_one(['id' => $pid]);
        $home_chart = [
            'title' => '二维码',
            'info' => $info,
        ];
        $this->load->view('/sv/dr/qrcode4.php', $home_chart);
    }

    // 发送短信验证码
    public function send_code()
    {
        $mobile = $this->input->post('mobile');

        $phone_regular = login_regular();
        if (!preg_match($phone_regular, $mobile)) {
            echo json_encode(['err_msg' => 'fail', 'result' => '手机号不正确！']);exit;
        }

        $vcode = rand(10000, 99999);
        //创蓝单个发送短信
        $msg = "健康通验证码:{$vcode}，5分钟内有效，请勿泄漏！";
        $this->load->library("/253_drsay_invite/Chuanglan_api");
        $send_single_sms = sms_chuang_single(SMS_LOG_CODE_SURVEY_VERIFICATION_CODE, $msg, $mobile, []);
        if ($send_single_sms) {
            $verify_data = [
                'mobile' => !empty($mobile) ? $mobile : '',
                'vcode' => $vcode,
                'create_time' => time(),
                'type' => '10',
            ];
            $this->db->insert('app_member_verify', $verify_data);
            echo json_encode(['err_msg' => 'success', 'result' => $send_single_sms]);exit;
        } else {
            echo json_encode(['err_msg' => 'fail', 'result' => '验证码发送失败！']);exit;
        }
    }

    // 检查验证码
    public function check_code()
    {
        $mobile = $this->input->post('mobile');
        $vcode = $this->input->post('vcode');
        //短信验证
        $res_verify = $this->db->from('app_member_verify')->where(['type' => '10', 'mobile' => $mobile, 'status' => 1])->limit(1)->order_by('id desc')->get();
        $res_verify = $res_verify ? $res_verify->row_array() : [];

        // 发送时间
        $start_date = date('Y-m-d H:i:s', $res_verify['create_time']);
        $minute = floor((time() - strtotime($start_date)) % 86400 / 60);
        if ($vcode == $res_verify['vcode']) {
            if ($minute >= 5) {
                echo json_encode(['err_msg' => 'fail', 'result' => '验证码已超时！']);exit;
            } else {
                $this->db->where(['id' => $res_verify['id']])->update('app_member_verify', ['status' => 2, 'verify_time' => time()]);
                echo json_encode(['err_msg' => 'success', 'result' => '']);exit;
            }
        } else {
            echo json_encode(['err_msg' => 'fail', 'result' => '您输入的验证码不正确！']);exit;
        }
    }

    // 兑换登录页
    public function reward_auth()
    {
        $pid = $this->uri->segment(4);
        $r = $this->input->get('r');
        $s = $this->input->get('s');

        if (!$r || !$s) {
            $this->load->helper('cookie');
            $r = get_cookie('r');
            $s = get_cookie('s');
        }

        if (!$pid || !$r || !$s) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }
        // 答卷信息
        $answer = $this->qk_dr_val_model->get_one(['pid' => $pid, 'r' => $r, 's' => $s]);
        // 未完成问卷
        if (!$answer || $answer['finish_status'] != 'c') {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }
        // 已兑换或者无兑换积分
        if ($answer['payment'] > 0 || $answer['point'] == 0) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
        }
        // 无有效个人信息
        if (!$answer['mobile']) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
        }

        // 项目信息
        $info = $this->qk_dr_pj_model->get_one(['id' => $pid]);
        // 项目未开启兑换
        if ($info['reward_st'] != 1) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
        }
        // 无有效兑换积分
        if ($info['reward'] < 100) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
        }

        // 生成scene
        $encrypt_scene = $pid . '_' . $r . '_' . $s;
        $encrypt_data = substr(md5($encrypt_scene . PROJECT_ENCODE_KEY), 8, 16);

        $home_chart = [
            'title' => '积分兑换',
            'scene' => $encrypt_data,
            'pid' => $pid,
            'r' => $r,
            's' => $s,
            'mobile' => $answer['mobile'],
        ];
        $this->load->view("/sv/dr/reward_auth", $home_chart);
    }

    // 发送短信验证码
    public function reward_code()
    {
        $mobile = $this->input->post('mobile');
        $pid = $this->input->post('pid');
        $r = $this->input->post('r');
        $s = $this->input->post('s');

        $phone_regular = login_regular();
        if (!preg_match($phone_regular, $mobile)) {
            echo json_encode(['err_msg' => 'fail', 'result' => '手机号不正确！']);exit;
        }

        // 答卷信息
        $answer = $this->qk_dr_val_model->get_one(['pid' => $pid, 'r' => $r, 's' => $s]);
        // 未完成问卷
        if (!$answer || $answer['mobile'] != $mobile) {
            echo json_encode(['rs_code' => 'fail', 'rs_msg' => '手机号码不正确！']);exit;
        }

        $vcode = rand(10000, 99999);
        //创蓝单个发送短信
        $msg = "健康通验证码:{$vcode}，5分钟内有效，请勿泄漏！";
        $this->load->library("/253_drsay_invite/Chuanglan_api");
        $send_single_sms = sms_chuang_single(SMS_LOG_CODE_SURVEY_VERIFICATION_CODE, $msg, $mobile, []);
        if ($send_single_sms) {
            $verify_data = [
                'mobile' => !empty($mobile) ? $mobile : '',
                'vcode' => $vcode,
                'create_time' => time(),
                'type' => '10',
            ];
            $this->db->insert('app_member_verify', $verify_data);
            echo json_encode(['err_msg' => 'success', 'result' => $send_single_sms]);exit;
        } else {
            echo json_encode(['err_msg' => 'fail', 'result' => '验证码发送失败！']);exit;
        }
    }

    public function reward_code_check()
    {
        $scene = $this->input->post('scene');
        $mobile = $this->input->post('verify_mobile');
        $verify_code = $this->input->post('verify_code');

        $pid = $this->uri->segment(4);
        $r = $this->input->get('r');
        $s = $this->input->get('s');
        if (!$pid || !$r || !$s) {
            echo json_encode(['rs_code' => 'fail', 'rs_msg' => '参数错误！']);exit;
        }

        // 答卷信息
        $answer = $this->qk_dr_val_model->get_one(['pid' => $pid, 'r' => $r, 's' => $s]);
        // 未完成问卷
        if (!$answer || $answer['mobile'] != $mobile) {
            echo json_encode(['rs_code' => 'fail', 'rs_msg' => '手机号码不正确！']);exit;
        }

        // 生成scene
        $encrypt_scene = $pid . '_' . $r . '_' . $s;
        $encrypt_data = substr(md5($encrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
        if ($encrypt_data != $scene) {
            echo json_encode(['rs_code' => 'fail', 'rs_msg' => '参数错误！']);exit;
        }

        //短信验证
        $res_verify = $this->db->from('app_member_verify')->where(['type' => '10', 'mobile' => $mobile, 'status' => 1])->limit(1)->order_by('id desc')->get();
        $res_verify = $res_verify ? $res_verify->row_array() : [];

        // 发送时间
        $start_date = date('Y-m-d H:i:s', $res_verify['create_time']);
        $minute = floor((time() - strtotime($start_date)) % 86400 / 60);
        if ($verify_code == $res_verify['vcode']) {
            if ($minute >= 5) {
                echo json_encode(['rs_code' => 'fail', 'rs_msg' => '验证码已超时！']);exit;
            } else {
                $this->db->where(['id' => $res_verify['id']])->update('app_member_verify', ['status' => 2, 'verify_time' => time()]);
                // 兑换密钥
                $reward_key = md5($encrypt_scene . QUICK_SURVEY_REWARD_KEY . date('Ymd'));
                echo json_encode(['rs_code' => 'success', 'rs_msg' => $reward_key]);exit;
            }
        } else {
            echo json_encode(['rs_code' => 'fail', 'rs_msg' => '您输入的验证码不正确！']);exit;
        }
    }

    // 支付礼金页
    public function reward()
    {
        error_reporting(-1);
        ini_set('display_errors', 1);
        $pid = $this->uri->segment(4);
        $r = $this->input->get('r');
        $s = $this->input->get('s');
        $reward_key = $this->input->get('reward_key');
        if (!$pid || !$r || !$s || !$reward_key) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }

        // 生成兑换密钥
        $encrypt_scene = $pid . '_' . $r . '_' . $s;
        $encrypt_data = md5($encrypt_scene . QUICK_SURVEY_REWARD_KEY . date('Ymd'));
        if ($encrypt_data != $reward_key) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }

        // 答卷信息
        $answer = $this->qk_dr_val_model->get_one(['pid' => $pid, 'r' => $r, 's' => $s]);
        // 未完成问卷
        if (!$answer || $answer['finish_status'] != 'c') {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }
        // 已兑换或者无兑换积分
        if ($answer['payment'] > 0 || $answer['point'] < 100) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
        }
        // 无有效个人信息
        if (!$answer['mobile']) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=u");
        }

        // 项目信息
        $info = $this->qk_dr_pj_model->get_one(['id' => $pid]);
        // 项目未开启兑换
        if ($info['reward_st'] != 1) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
        }
        // 无有效兑换积分
        if ($info['reward'] < 100) {
            redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
        }

        $st_msg = '';
        if ($answer['finish_status']) {
            $this->load->model('n_model/qk_dr_reback_model');
            $msg = $this->qk_dr_reback_model->get_one(['type' => $answer['finish_status'], 'qk_id' => $pid]);
            $st_msg = $msg['cn'] ? $msg['type_title'] : '';
        }

        $uid = intval($answer['uid']);
        $payment_type = [];
        $filename = "";
        $scene = "";
        $point = intval($answer['point']);

        $account_info = [];
        if ($uid) {
            // 获取用户的手机充值，支付宝，微信自动打款账号
            $account_info = $this->db->from('app_ex_payment_account')->where(['uid' => $uid])->where_in('payment_type', [EXCHANGE_MOBILE, EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])->order_by('id asc')->get();
            $account_info = $account_info ? $account_info->result_array() : [];
            if ($account_info) {
                foreach ($account_info as $v) {
                    $payment_type[$v['payment_type']] = $v['payment_type'];
                }
            }
            if ($point >= 100) {
                // 小程序二维码参数最多只能传32位
                $scene = $pid . "_" . $uid;
                $scene = "sv_" . $scene . "_" . substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
                $config = [
                    // 上医说
                    'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
                    'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
                    // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
                    'response_type' => 'array',
                ];
                $app = Factory::miniProgram($config);
                $response = $app->app_code->getUnlimit($scene, [
                    //上医说页面
                    'page' => 'pages/drsayExchange/drsayExchange',
                    'width' => 50,
                ]);
                // 或
                if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                    if (!is_dir("./uploads/quick_survey/{$pid}/")) {
                        mk_dir("./uploads/quick_survey/{$pid}/", 0777, true);
                    }
                    $filename = $response->saveAs("./uploads/quick_survey/{$pid}/", $pid . "_" . $uid . '.png');
                } else {
                    redirect(DRSAY_WEB . "sv/dr/finish?st=" . $answer['finish_status'] . "&project_id=" . $pid);
                }
            }
        } else {
            if (!$answer['mobile']) {
                redirect(DRSAY_WEB . "sv/dr/finish?st=c&project_id=" . $pid);
            }
        }

        //检测手机号码是否符合提现操作，不拆单
        $is_phone_type = false;
        // 可以手机充值的金额
        if (in_array($point / 100, [20, 30, 50, 100, 200, 300, 500])) {
            $is_phone_type = true;
        }

        //健康通官网code组装
        $gooddr_code = $uid . "_" . $pid . "_" . substr(md5($uid . "_" . $pid . "_" . API_ACCOUNT_APP_ID_GOODDR . "_" . API_ACCOUNT_APP_KEY_GOODDR), 8, 16);

        $home_chart = [
            'title' => '问卷结束',
            'st_msg' => $st_msg,
            "scene" => $scene,
            'reward_key' => $reward_key,
            'pid' => $pid,
            'r' => $r,
            's' => $s,
            'info' => $info,
            'account_info' => $account_info,
            'answer' => $answer,
            'st' => $answer['finish_status'],
            "point" => $point,
            "filename" => $filename,
            "is_phone_type" => $is_phone_type,
            'payment_type' => $payment_type,
            'gooddr_code' => $gooddr_code,
        ];

        $this->load->view("/sv/dr/reward", $home_chart);
    }

    // 兑换提交
    public function payment_sub()
    {
        try {
            $post_data = $this->input->post();
            $post_data = format_post_data($post_data);
            $pid = $post_data['pid'];
            $r = $post_data['r'];
            $s = $post_data['s'];
            $scene = $post_data['scene'];
            $reward_key = $post_data['reward_key'];
            $payment_type = intval($post_data['payment_type']);
            $other_payment_type = $post_data['other_payment_type'];

            $alipay_payment_name = $post_data['alipay_payment_name'];
            $alipay_payment_account = $post_data['alipay_payment_account'];
            $wx_payment_name = $post_data['wx_payment_name'];
            $mobile_payment_account = $post_data['mobile_payment_account'];
            $privacy = $post_data['privacy'];
            // 真实姓名
            if (!$privacy) {
                throw new Exception("请阅读并同意《个税代缴代付协议》!");
            }

            $payment_name = "";
            $payment_account = "";

            if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {
                // 真实姓名
                if (!$wx_payment_name || !isAllChinese($wx_payment_name)) {
                    throw new Exception("请输入真实姓名!");
                }
                //微信支付
                $payment_name = $wx_payment_name;
            } else if ($payment_type == EXCHANGE_ALIPAY) {
                //支付宝
                if (!$alipay_payment_name || !$alipay_payment_account) {
                    throw new Exception("请提交支付宝姓名及支付宝账号！");
                }
                // 支付宝账号只有手机号码或者邮箱格式
                if (!check_mobile($alipay_payment_account) && !check_email($alipay_payment_account)) {
                    throw new Exception("支付宝账号格式错误,请重新输入");
                }
                // 支付宝账号只支持真实姓名
                if ($alipay_payment_name && !isAllChinese($alipay_payment_name)) {
                    throw new Exception("请输入真实姓名!");
                }
                $payment_name = $alipay_payment_name;
                $payment_account = $alipay_payment_account;
            } else if ($payment_type == "other") {
                //其它
                $payment_type = $other_payment_type;
                if ($other_payment_type == EXCHANGE_MOBILE) {
                    //手机充值
                    if (!$mobile_payment_account || !check_mobile($mobile_payment_account)) {
                        throw new Exception("手机号码格式错误！");
                    }
                    $payment_account = $mobile_payment_account;
                }
            }

            //验证数据有效性 reward_key
            $encrypt_scene = $pid . '_' . $r . '_' . $s;
            $encrypt_data = md5($encrypt_scene . QUICK_SURVEY_REWARD_KEY . date('Ymd'));
            if ($encrypt_data != $reward_key) {
                throw new Exception("兑换密钥有误！");
            }

            // 支付方式
            if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO, EXCHANGE_MOBILE, DON_BE_POLITE])) {
                throw new Exception("支付方式选择有误！");
            }

            // 项目信息
            $project = $this->db->from('qk_dr_pj')->where(['id' => $pid])->limit(1)->get();
            $project = $project ? $project->row_array() : [];
            // 项目未开启兑换
            if ($project['reward_st'] != 1) {
                throw new Exception("调研问卷有误！");
            }
            // 无有效兑换积分
            if ($project['reward'] < 100) {
                throw new Exception("兑换失败，兑换分值({$project['reward']}分)太低，无法进行兑换，100积分起兑！");
            }

            //查询快速调研结果表，是否存在此用户信息
            $answer_info = $this->db->from('qk_dr_val')->where(['pid' => $pid, 'r' => $r, 's' => $s, 'finish_status' => 'c'])->limit(1)->get();
            $answer_info = $answer_info ? $answer_info->row_array() : [];
            if (!$answer_info) {
                throw new Exception("调研问卷有误！");
            }
            if ($answer_info['payment'] > 0) {
                throw new Exception("积分已兑换！");
            }
            if ($answer_info['point'] < 100) {
                throw new Exception("兑换失败，兑换分值({$answer_info['point']}分)太低，无法进行兑换，100积分起兑！");
            }
            if ($answer_info['point'] != $project['reward']) {
                throw new Exception("兑换分有误！");
            }

            //查询订单表，查询是否已经有过兑换记录
            $user_order = $this->db->from('app_payment_order_new')->where(['log_code' => ORDER_SOURCE_STATUS_QK_SURVEY, 'pid' => $pid, 'pid_id' => $answer_info['id']])->get();
            $user_order = $user_order ? $user_order->row_array() : [];
            if ($user_order) {
                // 订单存在
                throw new Exception("您已申请过兑换，不能重复操作！");
            }

            //积分转换成人民币
            $exchange_amount = round($answer_info['point'] / 100, 2);
            if ($exchange_amount < 1) {
                //小于1块钱，不能提现
                throw new Exception("兑换失败，您的积分不足1元，暂不能进行兑换！");
            }

            // 是医师会员
            if ($answer_info['uid'] > 0) {
                //获取用户信息
                $member_info = $this->db->from('app_member')->where(['id' => $answer_info['uid']])->limit(1)->get();
                $member_info = $member_info ? $member_info->row_array() : [];
                if (!$member_info) {
                    throw new Exception("用户不存在，记录有误！");
                }

                // 验证scene
                $arr_scene = explode("_", $scene);
                $pid = $arr_scene[1];
                $member_uid = $arr_scene[2];
                $encrypted_data = $arr_scene[3];
                if ($member_uid != $member_info['id']) {
                    throw new Exception("参数有误！");
                }

                $decrypt_scene = $pid . "_" . $member_uid;
                $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
                // 解密值不等
                if ($encrypted_data !== $decrypt_data) {
                    throw new Exception("身份密钥有误！");
                }

                //微信自动打款
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {
                    // 获取提现账号
                    $check_account = $this->db->from('qk_dr_pay_account')->where(['uid' => $answer_info['uid'], 'pid' => $pid])->limit(1)->get();
                    $check_account = $check_account ? $check_account->row_array() : [];
                    // 有医师支付账号
                    if ($check_account) {
                        // 取已保存的支付账号
                        $payment_account = $check_account['openid'];
                        $payment_name = $wx_payment_name;
                    } else {
                        // 没有医师账号
                        throw new Exception("微信零钱收款，请先扫码！！");
                    }
                }
            } else {
                // 安全考虑，不是医师会员需要问卷绑定手机号
                if (!$answer_info['mobile'] || !check_mobile($answer_info['mobile'])) {
                    throw new Exception("问卷未绑定手机号！");
                }
                // 非会员不能微信自动打款
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {
                    throw new Exception("支付方式选择有误！");
                }
            }

            // dd($post_data);
            //加锁处理
            if (Redis_lock::getInstance()->lockNoWait("qk_survey_sub_{$r}_{$s}", 120) !== true) {
                //错误提示
                throw new Exception("请不要重复提交！");
            }

            // 设备请求日志
            $this->device_log($post_data);

            //事务开始
            $this->db->trans_start();

            //提现记录提交
            if ($payment_type == DON_BE_POLITE) {
                //不要礼金
                $this->db->where(['pid' => $pid, 'r' => $r, 's' => $s])->update('qk_dr_val', ['payment' => 1, 'payment_type' => DON_BE_POLITE]);
            } else {
                // 申请状态
                $this->db->where(['pid' => $pid, 'r' => $r, 's' => $s])->update('qk_dr_val', ['payment' => 1, 'payment_type' => $payment_type]);

                $ip = getip();
                $certif_id = $member_info['indentity_code'] ?? ''; //身份证号
                //公共订单表
                $insert_order = [
                    "log_code" => ORDER_SOURCE_STATUS_QK_SURVEY, //103
                    "param" => $answer_info['id'],
                    "uid" => $answer_info['uid'],
                    "pid" => $pid,
                    "payment_type" => $payment_type,
                    "payment_account" => $payment_account,
                    "payment_name" => $payment_name,
                    "pay_currency" => 130,
                    "pay_exchange_amount" => $exchange_amount, //提现金额
                    "pay_exchange_rate" => 1,
                    "pay_rmb_exchange_rate" => 1,
                    "add_time" => time(),
                    "adder_ip" => ip2long($ip),
                    "adder_address" => ip2location($ip),
                    "id_card" => $certif_id, //从会员表获取
                    "name" => $answer_info['name'],
                    "mobile" => $answer_info['mobile'],
                    "province" => $answer_info['province'],
                    "city" => $answer_info['city'],
                    "unit_name" => $answer_info['unit'],
                    "unit_level" => $answer_info['unit_level'],
                    "department" => $answer_info['department'],
                    "job_title" => $answer_info['title'],
                    "examine_type" => 2,
                    "exchange_point" => $exchange_amount * 100,
                    "exchange_amount" => $exchange_amount,
                    "pay_rmb_exchange_amount" => $exchange_amount,
                    'sales_id'=>$project['sales_id'],
                    'open_invoice_company'=>$project['kp'],
                    'pro_price'=>$project['price'],
                    'pro_name'=>$project['title'],
                ];
                // 兑换订单
                $this->db->insert('app_payment_order_new', $insert_order);
                $insert_order_id = $this->db->insert_id();
                if (!$insert_order_id) {
                    throw new Exception("[明细]兑换失败，请重试！");
                }
            }
            // 删除小程序图片
            if (file_exists("./uploads/quick_survey/" . $pid . "/" . $pid . "_" . $answer_info['uid'] . ".png")) {
                unlink("./uploads/quick_survey/" . $pid . "/" . $pid . "_" . $answer_info['uid'] . ".png");
            }

            // 删除临时微信账号
            $this->db->from('qk_dr_pay_account')->where(['pid' => $pid])->delete();

            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === false) {
                file_put_contents("./tmp/fail_bk_exchange.txt", $answer_info['uid'] . "**qk_survey_" . $pid . "**" . $payment_type . "**" . $payment_name . "**" . $payment_account . "**" . $answer_info['id'] . "**" . date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
                throw new Exception("兑换失败，请联系管理员！");
            }
            _back_msg("success", "");
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }

    // 检测用户是否已经绑定成功
    public function check_order_info()
    {
        $scene = $this->input->post("scene", true);
        if (!$scene) {
            _back_msg("error", "无效");
        }
        $arr_scene = explode("_", $scene);
        $pid = $arr_scene[1];
        $member_uid = $arr_scene[2];
        $encrypted_data = $arr_scene[3];
        if (!$pid || !$member_uid || !$encrypted_data) { //参数有误
            _back_msg("error", "参数有误");
        }
        $decrypt_scene = $pid . "_" . $member_uid;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) { //解密值不等
            _back_msg("error", "参数有误");
        }
        //通过member_uid查询账号表里记录信息,查询微信唯一码是否存在
        $account_info = $this->db->from('qk_dr_pay_account')->where(['uid' => $member_uid, 'pid' => $pid])->limit(1)->get();
        $account_info = $account_info ? $account_info->row_array() : [];

        if ($account_info) {
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }

    // 设备请求日志
    private function device_log($post_data)
    {
        //设备信息
        $this->load->library('user_agent');
        $platform = $this->agent->platform();
        $browser = $this->agent->browser();
        $browser_version = $this->agent->version();
        $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
        $device_info = [
            "device_type" => $platform,
            "http_user_agent" => $http_user_agent, //设备信息
            "browser" => $browser,
            "browser_version" => $browser_version,
        ];
        $device_info = array_merge($device_info, $post_data);

        if (!is_dir("./tmp/exchange/")) {
            mk_dir("./tmp/exchange/", 0777, true);
        }
        // 兑换日志
        file_put_contents("./tmp/exchange/qk_survey_exchange_" . date("Y") . "_" . date("m") . ".txt", "start:" . date("Y-m-d H:i:s") . "**" . json_encode($device_info, JSON_UNESCAPED_UNICODE) . PHP_EOL . PHP_EOL, FILE_APPEND | LOCK_EX);
    }

    // curl post接口
    private function curl_post($url, $param)
    {
        $ch = curl_init();
        $header = array();
        $header[] = 'version:1.0.1';
        $header[] = 'flag:0';
        // 设置header
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($param, JSON_UNESCAPED_UNICODE));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //要求结果为字符串且输出到屏幕上
        // https请求 不验证证书和hosts
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        // 最大执行时间
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        $data = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        //显示获得的数据
        return json_decode($data, true);
    }

}
