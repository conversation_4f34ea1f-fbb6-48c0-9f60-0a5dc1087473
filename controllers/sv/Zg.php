<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Zg extends CI_Controller {

    function test()
    {
//
        //        题号  标题  题型  显示号 是否必答  字符串长度


        //结构json
        $survey_struct_json = [
            [
                "question_id" => "responseid",//问题ID
                "variable_id" => "",//变量ID
                "type" => "date",//类型
                "start" => "",//站位起点
                "finish" => "",//站位完成
                "answer_code" => "",//答案code
                "question_label" => "",//问题标签
                "answer_label" => "",//答案标签
                "question_sort" => "",//问题排序
                "question_must" => "",//问题必答
                "question_hide" => "",//问题隐藏
                "answer_must" => "",//答案必须
                "answer_sort" => "",//答案排序
                "answer_hide" => "",//答案隐藏
                "answer_result" => "",//题目答案最后结果
            ],
            [
                "question_id" => "responseid",//问题ID
                "variable_id" => "",//变量ID
                "type" => "date",//类型
                "start" => "",//站位起点
                "finish" => "",//站位完成
                "answer_code" => "",//答案code
                "question_label" => "",//问题标签
                "answer_label" => "",//答案标签
                "question_sort" => "",//问题排序
                "question_must" => "",//问题必答
                "question_hide" => "",//问题隐藏
                "answer_must" => "",//答案必须
                "answer_sort" => "",//答案排序
                "answer_hide" => "",//答案隐藏
                "answer_result" => "",//题目答案最后结果
            ]

        ];


        $question_json = array(
            [
                "question_id" => "responseid",//问题ID
                "title" => "",
                "question_type" => "date",
                "sort" => 1,
                "is_required" => 1,//1、必填 0，不必填
                "str_len" => 1,//字符串长度【开放题使用】
                "text_type" => 0,//文本类型【开放题使用】
            ],

            [
                "question_id" => "Q1",
                "title" => "请您确认本次抽查是否在岗",
                "question_type" => "SINGLE",
                "sort" => 1,
                "is_required" => 1,//1、必填 0，不必填
                "str_len" => 1,//字符串长度【开放题使用】
                "text_type" => 0,//文本类型【开放题使用】
            ],
//            [
//                "question_id" => "Q2",
//                "title" => "请填写你的中文名字",
//                "question_type" => "OPENTEXT",
//                "sort" => 2,
//                "is_required" => 1,
//                "str_len" => 1,//字符串长度【开放题使用】
//                "text_type" => 0,//文本类型【开放题使用】
//            ],
        );

//        Question ID Variable ID Type    Start   Finish  Answer Code Question Label  Answer Label
//        option_id(选项号)、option_name(标题)、sort(顺序)、is_answer(是否是答案)、jump_question(跳转到的题号)
        $answer_json = [
            0 => [
                [
                    "question_id" => "interview_start",
                    "variable_id" => "interview_start",
                    "type" => "date",
                    "start" => "",
                    "finish" => "",
                    "answer_code" => "interview_start",
                    "question_label" => "请您确认本次抽查是否在岗",
                    "answer_label" => "是",

//                    "option_id" => "",
//                    "option_name" => "",
                    "sort" => "",
                    "is_answer" => "", //是否是答案，1、是正确答案，0、不是正确答案
                    "jump_question" => "", //跳转到的题号
                    "val" => "", //答案选择后，的选中标识
                ],
            ],
            1 => [
                [
                    "question_id" => "Q1",
                    "variable_id" => "Q1",
                    "type" => "SINGLE",
                    "start" => "",
                    "finish" => "",
                    "answer_code" => "Q1_1",
                    "question_label" => "请您确认本次抽查是否在岗",
                    "answer_label" => "是",

                    "option_id" => "1",
                    "option_name" => "是",
                    "sort" => 1,
                    "is_answer" => 0, //是否是答案，1、是正确答案，0、不是正确答案
                    "jump_question" => "", //跳转到的题号
                    "val" => "", //答案选择后，的选中标识
                ],
            ],
        ];
        echo json_encode($question_json, JSON_UNESCAPED_UNICODE);
        echo "<br />";
        echo json_encode($answer_json, JSON_UNESCAPED_UNICODE);
        die;
    }

    //问卷
    public function z1()
    {
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") {
            $request_scheme = "https";
        } else {
            $request_scheme = "http";
        }

        error_reporting(-1);
        ini_set('display_errors', 1);
        $visit_url = $request_scheme.'://'.$_SERVER["SERVER_NAME"].$_SERVER["REQUEST_URI"];
        file_put_contents("./tmp/zg_visit_info.txt", $visit_url."**".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);

        $post_data = $this->input->post();

        $question_is_empty = false;
        if ($post_data) {
            file_put_contents("./tmp/zg_click_info.txt", json_encode($post_data, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
            $val_encode_str = $post_data['val_encode_str'];
            $val_arr_code = explode("_", $val_encode_str);
            if (count($val_arr_code) != 5) {
                die("param error!");
            }
            $pid = $val_arr_code[0];
            $pid_turn = $val_arr_code[1];
            $zhgh = $val_arr_code[2];
            $val_id = $val_arr_code[3];
            $check_val_encode_str = $val_arr_code[4];
            $check_local_encode_code = substr(md5($pid."_{$pid_turn}_".$zhgh."_".$val_id. PROJECT_ENCODE_KEY), 8, 16);
            if ($check_val_encode_str != $check_local_encode_code) {
                die("验证失败，无法提交!");
            }

//            $val_id = $post_data['val_id'];
//            $pid_turn = $post_data['pid_turn'];
//            $zhgh = $post_data['zhgh'];
            if (!$val_id) {
                die("参数错误！");
            }
            //通过val_id获取问卷明细
            $survey_val = $this->db->query("SELECT * FROM sv_survey_val WHERE id=?", [$val_id])->row_array();
            if (!$survey_val) {
                die("参数错误！");
            }
            $sv_survey_question_answer_val_jason = $survey_val['sv_survey_question_answer_val_jason'];
            $arr_answer_val = json_decode($sv_survey_question_answer_val_jason, true);
            $question_answer = [];
            $question_trigger_info = [];//此题型被哪道题触发
            foreach ($arr_answer_val as $k_q_check => $v_q_check) {
                foreach ($v_q_check['list'] as $v_q_info) {
                    if ($v_q_info['answer_jump_question_id'] != "" && $v_q_info['answer_jump_touch'] == 0) {//存在触发题型,并且是需要显示的题型
                        $question_trigger_info[$v_q_info['answer_jump_question_id']][$v_q_info['variable_id']][$v_q_info['answer_code']] = $v_q_info['answer_code'];
                    }
                }
            }

            foreach ($arr_answer_val as $k_q => &$v_q) {
                $answer_info = [];
                foreach ($v_q['list'] as &$v_op) {
                    if ($v_q['type'] == "single") {//单选题
                        //存在值，值是answer_code
                        if (isset($post_data[$v_op['variable_id']]) && $post_data[$v_op['variable_id']] == $v_op['answer_code']) {
                            $v_op['answer_result'] = $post_data[$v_op['variable_id']];
                            $answer_info[] = $post_data[$v_op['variable_id']];
                        } else {
                            $v_op['answer_result'] = "";
                        }
                    }
                }
                $question_answer[$v_q['question_id']] = [
                    "question_must" => $v_q['question_must'],
                    "answer_list" => $answer_info,
                ];
            }

            foreach ($question_answer as $k_q_a => $v_q_a) {
                if (isset($question_trigger_info[$k_q_a])) {//有触发设置
                    foreach ($question_trigger_info[$k_q_a] as $k_variable => $v_trigger) {
                        if (isset($post_data[$k_variable]) && in_array($post_data[$k_variable], $v_trigger)) {//存在触发的题型答案
                            if ($v_q_a['question_must'] == 1 && !$v_q_a['answer_list']) {//触发题型也要验证是否必填
                                $question_is_empty = true;
                            }
                        }
                    }
                } else {
                    if ($v_q_a['question_must'] == 1 && !$v_q_a['answer_list']) {//必填
                        $question_is_empty = true;
                    }
                }
            }


            if (!$question_is_empty) {//符合提交标准
                $update_data = [
                    "sv_survey_question_answer_val_jason" => $arr_answer_val ? json_encode($arr_answer_val, JSON_UNESCAPED_UNICODE) : "",
                    "finished_time" => time(),
                    "finished_st" => 'c',
                ];
                $this->db->where("id", $val_id);
                $res = $this->db->update("sv_survey_val", $update_data);
                if ($res) {
                    //查询work_check_report表是否存在记录
                    $worker_info = $this->db->query("SELECT * FROM sv_com_job_worker WHERE zhgh=?", [$zhgh])->row_array();
                    $report_year = date("Y");
                    $report_month = date("n");
                    $report_day = date("j");
                    $get_report = $this->db->query("SELECT * FROM work_check_report WHERE zhgh=? AND `year`=? AND `month`=? AND `checkdate`=? LIMIT 1", [$zhgh, $report_year, $report_month, $report_day])->row_array();
                    if ($get_report) {
                        $this->db->where("id", $get_report['id']);
                        $this->db->update("work_check_report", ["dayturn{$pid_turn}" => "在岗"]);
                    } else {
                        //节假日查询
                        $report_holidays_setting = $this->db->query("SELECT * FROM sv_com_job_holidays_setting WHERE `cid`=? AND `year`=? AND `month`=? AND `day`=? LIMIT 1", [$worker_info['country_id'], $report_year, $report_month, $report_day])->row_array();

                        $insert_report = [
                            "zhgh" => $zhgh,
                            "name" => $worker_info['zg_cn_name'],
                            "year" => $report_year,
                            "month" => $report_month,
                            "checkdate" => $report_day,
                            "dayturn{$pid_turn}" => "在岗",
                            "cid" => $worker_info['country_id'],
                            "is_holiday" => $report_holidays_setting ? 1 : 0,
                        ];
                        $this->db->insert("work_check_report", $insert_report);
                    }
                    redirect("/sv/zg/finish?st=c");
                } else {//提交失败
                    die("提交失败，请联系管理员");
                }
            }

        }




        $default_question_type = [
            "single" => "单选题",
            "multi" => "多选题",
        ];
        $default_type_name = [
            1 => "企业员工查岗",
            2 => "企业员工调研",
        ];
        $code = trim($this->uri->segment(4));
        $arr_code = explode("_", $code);
        if (count($arr_code) != 5) {
            die("param error!");
        }
        $pid = $arr_code[0];
        $pid_turn = $arr_code[1];
        $zhgh = $arr_code[2];
        $url_redirect_time = $arr_code[3];
        $encode_str = $arr_code[4];
        $local_encode_str = substr(md5($pid."_{$pid_turn}_".$zhgh."_{$url_redirect_time}". PROJECT_ENCODE_KEY), 8, 16);
        if ($encode_str !== $local_encode_str) {
            die("param error!");
        }

        $project_info = $this->db->query("SELECT * FROM sv_survey_project WHERE id=?", [$pid])->row_array();
        $type_id = $project_info['type_id'];
        $user_info = $this->db->query("SELECT a.*,b.name as company_name,b.short_name FROM sv_com_job_worker a left join work_check_company b on a.job_seconded_company=b.com_id  WHERE a.zhgh=?", [$zhgh])->row_array();

        if (!$user_info) {
            redirect("/sv/zg/finish?st=zhgh_error");
        }

        if ($user_info['job_guyogn_type'] != 1) {//不是全职人员
            die("不是全职人员，不能打卡，请检查此账号是否已离职!");
        }

        //通过轮次及日期，查询打卡有效时间
        $now_date = date("Y-m-d");
        $get_check_work_setting = $this->db->query("SELECT * FROM sv_check_work_st WHERE wk_ck_pid=? AND ck_date=?", [$pid, $now_date])->row_array();
        if (!$get_check_work_setting) {//存在
            die("打卡配置不存在!");
        }
        //检测打卡时间是否正确
        $now_time = time();
        $click_start_time = $get_check_work_setting["t{$pid_turn}_open_time"];
        $click_end_time = $get_check_work_setting["t{$pid_turn}_close_time"];
//        if (!($url_redirect_time>=$click_start_time && $url_redirect_time <= $click_end_time)) {
//            file_put_contents("./tmp/zg_click.txt", $request_scheme."://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."\n", FILE_APPEND | LOCK_EX);
//            redirect("/sv/zg/finish?st=in_time_error");
//        }
        if (!($now_time >= $click_start_time && $now_time <= $click_end_time)) {
            die("打卡时间有误!<br />当前打卡时间：".date("Y-m-d H:i:s")."<br />开始打卡：".date("Y-m-d H:i:s", $click_start_time)."<br />截止打卡：".date("Y-m-d H:i:s", $click_end_time));
        }

        //查询是否已经有过打卡记录
        //添加入库
        $now_year = date("Y");
        $now_month = date("n");
        $now_day = date("j");
        $survey_info = $this->db->query("SELECT * FROM sv_survey_val WHERE vs_project_id=? AND user_id=? AND type_id=? AND turns='{$pid_turn}' AND s_year=? AND s_month=? AND s_day=? LIMIT 1", [$pid, $zhgh, $type_id, $now_year, $now_month, $now_day])->row_array();
        if ($survey_info && $survey_info['finished_st'] != "") {//已完成问卷
            redirect("/sv/zg/finish?st=re_c");
        }

        //在职员工
        $worker_total = $this->db->query("SELECT count(*) as num FROM sv_com_job_worker WHERE job_guyogn_type=1 AND work_type=1")->row_array();
        $question_json = $option_info = [];
        $question_info = $this->db->query("SELECT * FROM sv_survey WHERE pid=? LIMIT 1", [$pid])->row_array();

        //结构信息
        $struct_info = $this->db->query("SELECT * FROM sv_survey_struct WHERE project_id='{$pid}' ORDER BY question_sort ASC,answer_sort ASC")->result_array();
        $question_trigger = [];
        $question_and_type = [];//题目类型
        if ($struct_info) {
            foreach ($struct_info as $v_struct) {
                $question_and_type[$v_struct['question_id']] = strtolower($v_struct['type']);
                $question_json[$v_struct['question_id']] = [
                    "question_id" => $v_struct['question_id'],
                    "question_label" => $v_struct['question_label'],
                    "type" => $v_struct['type'],
                    "question_sort" => $v_struct['question_sort'],
                    "question_must" => $v_struct['question_must'],//1、必填 0，不必填
                    "str_len" => 1,//字符串长度【开放题使用】
                    "text_type" => 0,//文本类型【开放题使用】
                ];

                $option_info[$v_struct['question_id']][] = [
                    "answer_code" => $v_struct['answer_code'],
                    "variable_id" => $v_struct['variable_id'],
                    "answer_label" => $v_struct['answer_label'],
                    "answer_sort" => $v_struct['answer_sort'],
                    "is_answer" => 0, //是否是答案，1、是正确答案，0、不是正确答案
                    "answer_jump_question_id" => $v_struct['answer_jump_question_id'], //跳转到的题号
                    "answer_result" => $v_struct['answer_result'], //答案选择后，的选中标识
                    "answer_jump_touch" => $v_struct['answer_jump_touch'], //1，不显示answer_jump_question_id里的题 0、显示
                ];

                //隐藏题型,通过触发展示的题型
                if ($v_struct['answer_jump_question_id']) {
                    $arr_trigger_question = explode(",", $v_struct['answer_jump_question_id']);
                    foreach ($arr_trigger_question as $v_trigger_pro) {
                        $question_trigger[$v_trigger_pro] = $v_trigger_pro;
                    }
                }
            }
        }

        if ($question_json) {
            foreach ($question_json as $k_q => &$v_q) {
                $v_q['list'] = $option_info[$k_q];
            }
        }


        if (!$survey_info) {//不存在
            $s_year = date("Y");
            $s_month = date("n");
            $s_day = date("j");
            //节假日查询
            $holidays_setting = $this->db->query("SELECT * FROM sv_com_job_holidays_setting WHERE `cid`=? AND `year`=? AND `month`=? AND `day`=? LIMIT 1", [$user_info['country_id'], $s_year, $s_month, $s_day])->row_array();
            $ip = getip(); //获取ip
            $ip_address = ip2location($ip);  //将IP转换成真实地址
            $this->load->library('user_agent');
            $platform = $this->agent->platform();
            $browser = $this->agent->browser();
            $browser_version = $this->agent->version();
            $insert_data = [
                "source_id" => 1,
                "type_id" => $type_id,
                "vs_project_id" => $pid,
                "user_id" => $zhgh,
                "name" => $user_info['zg_cn_name'],
                "sv_survey_link" => $request_scheme."://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'],
                "sv_survey_question_answer_val_jason" => $question_json ? json_encode($question_json, JSON_UNESCAPED_UNICODE) : "",
                "click_time" => time(),
                "ip" => $ip,
                "ip_address" => $ip_address,
                "device_type" => $platform,
                "device_title" => $this->get_device(),
                "browser" => $browser,
                "browser_version" => $browser_version,
                "turns" => $pid_turn,
                "s_year" => $s_year,
                "s_month" => $s_month,
                "s_day" => $s_day,
                "cid" => $user_info['country_id'],
                "is_holiday" => $holidays_setting ? 1 : 0,
            ];
            $this->db->insert("sv_survey_val", $insert_data);
            $val_id = $this->db->insert_id();
            if (!$val_id) {
                die("打卡记录生成失败，请手动刷新当前页面，如还无法继续，请联系技术人员查看！");
            }
        } else {
            $val_id = $survey_info['id'];
        }


        $val_encode_code = substr(md5($pid."_{$pid_turn}_".$zhgh."_{$val_id}". PROJECT_ENCODE_KEY), 8, 16);


        $home_chart = array(
            'title'=>'问卷开始',
            'project_info' => $project_info,
            'user_info' => $user_info,
            'zhgh' => $zhgh,
            'worker_total' => $worker_total,
            'q_info' => $question_json ? $question_json : [],
            'default_question_type' => $default_question_type,
            "question_trigger" => $question_trigger,
            "default_type_name" => $default_type_name,
            "pid_turn" => $pid_turn,
            "val_id" => $val_id,
            "question_and_type" => $question_and_type,
            "val_encode_str" => $pid."_{$pid_turn}_".$zhgh."_{$val_id}_{$val_encode_code}",
            "question_is_empty" => $question_is_empty,
            "post_data" => $post_data,
        );
        $this->load->view('/sv/zg/index.php', $home_chart);
    }

    public function z1_test()
    {
        $post_data = $this->input->post();
        if ($post_data) {
            $val_encode_str = $post_data['val_encode_str'];
            $val_arr_code = explode("_", $val_encode_str);
            if (count($val_arr_code) != 5) {
                die("param error!");
            }
            $pid = $val_arr_code[0];
            $pid_turn = $val_arr_code[1];
            $zhgh = $val_arr_code[2];
            $val_id = $val_arr_code[3];
            $check_val_encode_str = $val_arr_code[4];
            $check_local_encode_code = substr(md5($pid."_{$pid_turn}_".$zhgh."_".$val_id. PROJECT_ENCODE_KEY), 8, 16);
            if ($check_val_encode_str != $check_local_encode_code) {
                die("验证失败，无法提交!");
            }

//            if (!$val_id) {
//                die("参数错误！");
//            }
//            //通过val_id获取问卷明细
//            $survey_val = $this->db->query("SELECT * FROM sv_survey_val WHERE id=?", [$val_id])->row_array();
//            if (!$survey_val) {
//                die("参数错误！");
//            }
            $sv_survey_question_answer_val_jason = '{"Q1":{"question_id":"Q1","question_label":"Q1、请您确认本次抽查是否在岗 ","type":"single","question_sort":"1","question_must":"1","str_len":1,"text_type":0,"list":[{"answer_code":"1","variable_id":"Q1","answer_label":"A：是","answer_sort":"1","is_answer":0,"answer_jump_question_id":"Q2","answer_result":"1","answer_jump_touch":"0"},{"answer_code":"2","variable_id":"Q1","answer_label":"B：否","answer_sort":"2","is_answer":0,"answer_jump_question_id":"Q2","answer_result":"","answer_jump_touch":"1"}]},"Q2":{"question_id":"Q2","question_label":"Q2、请问你的工作饱和吗?","type":"single","question_sort":"2","question_must":"1","str_len":1,"text_type":0,"list":[{"answer_code":"1","variable_id":"Q2","answer_label":"A：是","answer_sort":"1","is_answer":0,"answer_jump_question_id":"","answer_result":"1","answer_jump_touch":"0"},{"answer_code":"2","variable_id":"Q2","answer_label":"B：否","answer_sort":"2","is_answer":0,"answer_jump_question_id":"","answer_result":"","answer_jump_touch":"0"}]}}';

            $arr_answer_val = json_decode($sv_survey_question_answer_val_jason, true);
            $question_answer = [];
            $question_trigger_info = [];//此题型被哪道题触发
            foreach ($arr_answer_val as $k_q_check => $v_q_check) {
                foreach ($v_q_check['list'] as $v_q_info) {
                    if ($v_q_info['answer_jump_question_id'] != "" && $v_q_info['answer_jump_touch'] == 0) {//存在触发题型,并且是需要显示的题型
                        $question_trigger_info[$v_q_info['answer_jump_question_id']][$v_q_info['variable_id']][$v_q_info['answer_code']] = $v_q_info['answer_code'];
                    }
                }
            }

            foreach ($arr_answer_val as $k_q => &$v_q) {
                $answer_info = [];
                foreach ($v_q['list'] as &$v_op) {
                    if ($v_q['type'] == "single") {//单选题
                        //存在值，值是answer_code
                        if (isset($post_data[$v_op['variable_id']]) && $post_data[$v_op['variable_id']] == $v_op['answer_code']) {
                            $v_op['answer_result'] = $post_data[$v_op['variable_id']];
                            $answer_info[] = $post_data[$v_op['variable_id']];
                        } else {
                            $v_op['answer_result'] = "";
                        }
                    }
                }
                $question_answer[$v_q['question_id']] = [
                    "question_must" => $v_q['question_must'],
                    "answer_list" => $answer_info,
                ];
            }

            foreach ($question_answer as $k_q_a => $v_q_a) {
                if (isset($question_trigger_info[$k_q_a])) {//有触发设置
                    foreach ($question_trigger_info[$k_q_a] as $k_variable => $v_trigger) {
                        if (isset($post_data[$k_variable]) && in_array($post_data[$k_variable], $v_trigger)) {//存在触发的题型答案
                            if ($v_q_a['question_must'] == 1 && !$v_q_a['answer_list']) {//触发题型也要验证是否必填
                                $question_is_empty = true;
                            }
                        }
                    }
                } else {
                    if ($v_q_a['question_must'] == 1 && !$v_q_a['answer_list']) {//必填
                        $question_is_empty = true;
                    }
                }
            }


            if (!$question_is_empty) {//符合提交标准
                die("正常提交");
            }

        }

        $default_question_type = [
            "single" => "单选题",
            "multi" => "多选题",
        ];
        $default_type_name = [
            1 => "企业员工查岗",
            2 => "企业员工调研",
        ];
        $code = trim($this->uri->segment(4));
        $arr_code = explode("_", $code);
        if (count($arr_code) != 5) {
            die("param error!");
        }
        $pid = $arr_code[0];
        $pid_turn = $arr_code[1];
        $zhgh = $arr_code[2];
        $url_redirect_time = $arr_code[3];
        $encode_str = $arr_code[4];
        $local_encode_str = substr(md5($pid."_{$pid_turn}_".$zhgh."_{$url_redirect_time}". PROJECT_ENCODE_KEY), 8, 16);
        if ($encode_str !== $local_encode_str) {
            die("param error!");
        }

        $project_info = $this->db->query("SELECT * FROM sv_survey_project WHERE id=?", [$pid])->row_array();
        $type_id = $project_info['type_id'];
        $user_info = $this->db->query("SELECT a.*,b.name as company_name,b.short_name FROM sv_com_job_worker a left join work_check_company b on a.job_seconded_company=b.com_id  WHERE a.zhgh=?", [$zhgh])->row_array();

        if (!$user_info) {
            redirect("/sv/zg/finish?st=zhgh_error");
        }

        //结构信息
        $struct_info = $this->db->query("SELECT * FROM sv_survey_struct WHERE project_id='{$pid}' ORDER BY question_sort ASC,answer_sort ASC")->result_array();
        $question_trigger = [];
        $question_and_type = [];//题目类型
        if ($struct_info) {
            foreach ($struct_info as $v_struct) {
                $question_and_type[$v_struct['question_id']] = strtolower($v_struct['type']);
                $question_json[$v_struct['question_id']] = [
                    "question_id" => $v_struct['question_id'],
                    "question_label" => $v_struct['question_label'],
                    "type" => $v_struct['type'],
                    "question_sort" => $v_struct['question_sort'],
                    "question_must" => $v_struct['question_must'],//1、必填 0，不必填
                    "str_len" => 1,//字符串长度【开放题使用】
                    "text_type" => 0,//文本类型【开放题使用】
                ];

                $option_info[$v_struct['question_id']][] = [
                    "answer_code" => $v_struct['answer_code'],
                    "variable_id" => $v_struct['variable_id'],
                    "answer_label" => $v_struct['answer_label'],
                    "answer_sort" => $v_struct['answer_sort'],
                    "is_answer" => 0, //是否是答案，1、是正确答案，0、不是正确答案
                    "answer_jump_question_id" => $v_struct['answer_jump_question_id'], //跳转到的题号
                    "answer_result" => $v_struct['answer_result'], //答案选择后，的选中标识
                    "answer_jump_touch" => $v_struct['answer_jump_touch'], //1，不显示answer_jump_question_id里的题 0、显示
                ];

                //隐藏题型,通过触发展示的题型
                if ($v_struct['answer_jump_question_id']) {
                    $arr_trigger_question = explode(",", $v_struct['answer_jump_question_id']);
                    foreach ($arr_trigger_question as $v_trigger_pro) {
                        $question_trigger[$v_trigger_pro] = $v_trigger_pro;
                    }
                }
            }
        }

        if ($question_json) {
            foreach ($question_json as $k_q => &$v_q) {
                $v_q['list'] = $option_info[$k_q];
            }
        }

        $val_encode_code = substr(md5($pid."_{$pid_turn}_".$zhgh."_{$val_id}". PROJECT_ENCODE_KEY), 8, 16);


        $home_chart = array(
            'title'=>'测试',
            'project_info' => $project_info,
            'user_info' => $user_info,
            'zhgh' => $zhgh,
            'q_info' => $question_json ? $question_json : [],
            'default_question_type' => $default_question_type,
            "question_trigger" => $question_trigger,
            "default_type_name" => $default_type_name,
            "pid_turn" => $pid_turn,
            "val_id" => $val_id,
            "question_and_type" => $question_and_type,
            "val_encode_str" => $pid."_{$pid_turn}_".$zhgh."_{$val_id}_{$val_encode_code}",
            "question_is_empty" => $question_is_empty,
            "post_data" => $post_data,
        );
        $this->load->view('/sv/zg/z1_test.php', $home_chart);
    }


    protected function get_device(){
        $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
        if(stristr($http_user_agent,'iPad')) {
            $user_agent = "iPad";
        }else if(stristr($http_user_agent,'Android')) {
            $user_agent = 'Android';
        }else if(stristr($http_user_agent,'Linux')){
            $user_agent = 'Linux';
        }else if(stristr($http_user_agent,'iPhone')){
            $user_agent = 'iPhone';
        }else{
            $user_agent = 'PC';
        }
        return $user_agent;
    }


    //问卷
    public function index()
    {
        $home_chart = array(
            'title'=>'问卷开始',
        );
        $this->load->view('/sv/zg/index.php', $home_chart);
    }

    //结束
    public function finish()
    {
        $st = isset($_REQUEST['st']) ? $_REQUEST['st'] : "";
        switch($st) {
            case 'c': //返回状态为c的成功样本
                $st_msg = "您已完成问卷，谢谢！";
                break;
            case 're_c': //返回状态为c的成功样本
                $st_msg = "您已完成问卷，不能再次提交，谢谢！";
                break;
            case 'zhgh_error':
                $st_msg = "职工号有误！";
                break;
            case 'in_time_error':
                $st_msg = "进入打卡时间有误！！";
                break;
            default: //除以上之外的告警
                $st_msg = "您已完成问卷，谢谢！";
                break;
        }
        $home_chart = array(
            'title' => '问卷结束',
            'st_msg' => $st_msg,
            'st' => $st,
        );
        $this->load->view('/sv/zg/finish.php', $home_chart);
    }

    //结束
    public function finish1()
    {
        $home_chart = array(
            'title'=>'问卷结束',
        );
        $this->load->view('/sv/zg/finish1.php', $home_chart);
    }

}
