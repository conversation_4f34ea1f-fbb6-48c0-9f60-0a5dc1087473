<?php

class Delivery extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        $username = trim($this->input->post('username', true));
        $password = trim($this->input->post('password', true));
        $act = trim($this->input->post('act', true));
        if (isset($act) && $act == 'jktdownload') {
            if (empty($password) || empty($username)) {
                $data = array('msg' => "账号、密码都必须填写！", 'username' => $username, 'password' => $password);
                $this->load->view('/data/delivery/login', $data);
                return false;
            }
            $res = $this->db->query('select * from lc_upload where client_email!="" and client_email = "'.$username.'" and download_pwd!="" and download_pwd = "'.$password.'"')->row_array();
            if (!$res) {
                $data = array('msg' => "账号或密码错误！", 'username' => $username, 'password' => $password);
                $this->load->view('/data/delivery/login', $data);
                return false;
            }
            if (!is_readable($res['download_file'])) {
                $data = array('msg' => "文件不存在", 'username' => $username, 'password' => $password);
                $this->load->view('/data/delivery/login', $data);
                return false;
            }
            $this->db->query('update lc_upload set download_count=download_count+1 where id='.$res['id']);
            $filename = basename($res['download_file']);
            // 以只读和二进制模式打开文件
            $file = fopen($res['download_file'], "rb");
            header("Cache-control: private");
            header("Pragma: public");
            header('Content-type: application/x-csv');
            if (strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE 5')) {
                header("Content-Disposition: inline; filename=". $filename);
            } else {
                header("Content-Disposition: attachment; filename=" . $filename);
            }
            echo fread($file, filesize($res['download_file']));
            fclose($file);
            exit;
        } else {
            $data = array();
            $this->load->view('/data/delivery/login', $data);
        }
    }
}
