<?php

/**
 * 项目引流
 * 百度健康
 * 21-11-15
 */
use EasyWeChat\Factory;

class Drainage extends MY_Controller {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 活动页
     */
    public function active() {
        $data = [];
        $authen = $this->input->get('authen', true);
        if ($authen) {
            $arr_code = explode("DRSAY", $authen);
            if (count($arr_code) != 5) {
                redirect("/");
            }
            $source_code = $arr_code[0];    //数据编码引流项目ID
            $pid = $arr_code[1];            //项目-id
            $operat_id = $arr_code[2];      //执行记录-id
            $duid = $arr_code[3];           //访问员id
            $authen_code = $arr_code[4];    //认证code
            $authen_scene = $source_code . '_' . $pid . '_' . $operat_id . '_' . $duid;
            $authen_code_new = substr(md5($authen_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($authen_code_new !== $authen_code) {//解密值不等
                redirect("/");
            }
            $data['authen'] = '/drainage/aqrcode?authen=' . $authen;
        }
        $this->load->view("/drainage/active", $data);
    }

    /**
     * 项目活动背景
     */
    public function aqrcode() {
        $data = [];
        $authen = $this->input->get('authen', true);
        if ($authen) {
            $arr_code = explode("DRSAY", $authen);

            if (count($arr_code) != 5) {
                redirect("/");
            }
            $source_code = $arr_code[0];    //数据编码引流项目ID
            $pid = $arr_code[1];            //项目-id
            $operat_id = $arr_code[2];      //执行记录-id
            $duid = $arr_code[3];           //访问员id
            $authen_code = $arr_code[4];    //认证code
            $authen_scene = $source_code . '_' . $pid . '_' . $operat_id . '_' . $duid;
            $authen_code_new = substr(md5($authen_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($authen_code_new !== $authen_code) {//解密值不等
                redirect("/");
            }

            $where = ['id' => $operat_id, 'pid' => $pid, 'source_code' => $source_code];
            //查询记录是否存在-是否确认参与
            $exist_record = $this->db->select('id,participation')->where($where)->get('mb_baidu_health', 1)->row_array();
            if (empty($exist_record)) {
                redirect("/");
            }
            //如果初次打开-设置为participation-2-参与
            if ($exist_record['participation'] == 3) {
                $updata = ['is_intention' => 1, 'participation' => 2, 'participation_time' => time()];
                $upres = $this->db->where($where)->update('mb_baidu_health', $updata);
                if (!$upres) {
                    redirect("/");
                }
            }
            //是否存在访问员二维码
            $where_code = ['visitor_id' => $duid, 'pid' => $pid, 'source_code' => $source_code];
            $exist_code = $this->db->select('id,code')->where($where_code)->get('mb_drainage_visitor_code', 1)->row_array();
            if (empty($exist_code)) {
                redirect("/");
            }
            $data['code'] = $exist_code['code'];
        } else {
            $vid = $this->uri->segment(3);
            if ($vid) {
                $vcode = $this->db->select('code')->where('id', $vid)->get('mb_drainage_visitor_code', 1)->row_array();
                if (empty($vcode)) {
                    redirect("/");
                }
                $data['code'] = $vcode['code'];
            } else {
                redirect("/");
            }
        }
        $this->load->view("/drainage/aqrcode", $data);
    }

}
