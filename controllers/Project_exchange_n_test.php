<?php
die;//###############  AMY 2024-08-27 调整用户自己进入的解密规则
/**
 * 兑换申请及积分明细，问卷完成，微信自动提现接口地址
 * User: AMY
 * Date: 2019/12/12
 * Time: 14:07
 *
 * AMY 2023-05-26 增加了拆单支付新流程改版
 */
use EasyWeChat\Factory;
class Project_exchange_n_test extends CI_Controller
{
    public $lang_ary;
    public $lang_version;
    public $lang_info;
    function __construct()
    {
        parent::__construct();

        //获取当前最新的语言版本
        $this->lang_version = get_lang_version();
        $this->lang_info = 140;//默认中文简体
        $this->lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$this->lang_info);
        if (!$this->lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $this->lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$this->lang_info);
        }
        $this->load->model('survey_model');
    }

    /**
     * @apiDescription 兑换提交
     *
     * @apiParam {string} scene （必填）提交验证加密串
     * @apiParam {int} payment_type （必填）提现方式
     * @apiParam {string} payment_name （必填）支付宝姓名 payment_type=206必填
     * @apiParam {string} payment_account （必填）支付宝账号 payment_type=206必填
     * @apiParam {string} mobile_payment_account （必填）手机号码 payment_type=209必填
     *
     * @apiParamExample {jsonp} Request Example
     *  POST /api_exchange_wx/payment_sub
     *  // 请求成功
     *  {
     *      "re_st": "success",
     *      "re_info": "info content" // 对应内容String类型
     *  }
     *
     *  // 无数据
     *  {
     *     "re_st": "empty",
     *     "re_info": ""
     *  }
     *
     *  // 请求记录错误
     *  {
     *     "re_st": "error",
     *     "re_info": "请求无效，请联系客服！"
     *  }
     */
    function payment_sub()
    {
        try {
            $post_data = $this->input->post();
            $post_data = format_post_data($post_data);
            $payment_name = $post_data['payment_name'] ?? "";
            $payment_account = $post_data['payment_account'] ?? "";
            $mobile_payment_account = $post_data['mobile_payment_account'] ?? "";
            $scene = $this->input->post("scene", true);
            $payment_type = $this->input->post("payment_type", true);
            $payment_type = $payment_type ? (int)$payment_type : "";

            $privacy = (int)$post_data['privacy'];//隐私协议
            if ($privacy !== 1) {
                _back_msg("error", "请阅读并同意《个税代缴代付协议》！");
            }
            $payment_name = $payment_account = $bank_type = $opening_bank = "";
            if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信支付
                $payment_name = $post_data['wx_payment_name'];
                //AMY 2022-12-31 使用网医小程序扫码
                file_put_contents("./tmp/exchange/idr_wx.txt", $scene."**".date("Y-m-d H:i:s")."**".PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
            } else if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                $payment_name = $post_data['alipay_payment_name'];
                $payment_account = $post_data['alipay_payment_account'];
            } else if ($payment_type == "other") {//其它
                $other_payment_type = $post_data['other_payment_type'];
                if ($other_payment_type == EXCHANGE_MOBILE) {//手机充值
                    $mobile_payment_account = $post_data['mobile_payment_account'];
                    $payment_account = $mobile_payment_account;
                }
                $payment_type = $other_payment_type;
            }

            ### AMY 2020-10-26 已经增加短信验证流程，不需要做这步骤验证，验证session值即可
            $bk_code = $this->input->post("bk_code", true);
            $survey_uid_code = $this->input->post("survey_uid_code", true);
            $get_code_param = $this->account_validity($bk_code, $survey_uid_code, true);
            if (!$get_code_param) {
                throw new Exception("参数有误！");
            }
            $pid = $get_code_param['pid'];
            $member_uid = $get_code_param['member_uid'];
            $m_id = $get_code_param['m_id'];
            $imp_name = $get_code_param['imp_name'];
            if ($payment_type == EXCHANGE_WEBCHAT_AUTO || $payment_type == EXCHANGE_ALIPAY) {
                if ($imp_name != $payment_name) {
                    throw new Exception("仅能提现至本人账号,请不要修改用户真实姓名！");
                }
            }

            if (!isset($this->session->userdata["bk_exchange_{$pid}_{$m_id}"]) || $this->session->userdata["bk_exchange_{$pid}_{$m_id}"] != $member_uid) {
                redirect("/bk/exchange_sms_sub/{$bk_code}/{$survey_uid_code}");
            }
            ### AMY 2020-10-26 已经增加短信验证流程，不需要做这步骤验证，验证session值即可

            if (!in_array($payment_type, [EXCHANGE_MOBILE, EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO, EXCHANGE_SURPLUS, DON_BE_POLITE])) {
                throw new Exception("支付方式选择有误！");
            }

            if (!is_dir("./tmp/exchange/")) {
                mk_dir("./tmp/exchange/", 0777, true);
            }

            //兑换日志
            //设备信息
            $this->load->library('user_agent');
            $platform = $this->agent->platform();
            $browser = $this->agent->browser();
            $browser_version = $this->agent->version();
            $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
            $device_info = [
                "device_type" => $platform,
                "http_user_agent" => $http_user_agent,//设备信息
                "browser" => $browser,
                "browser_version" => $browser_version,
            ];
            $device_info = array_merge($device_info, $post_data);
            file_put_contents("./tmp/exchange/api_exchange_new_".date("Y")."_".date("m").".txt", "start:".date("Y-m-d H:i:s")."**".json_encode($device_info, JSON_UNESCAPED_UNICODE).PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);

            //事务开始
            $this->db->trans_start();
            //加锁处理
            if(Redis_lock::getInstance()->lockNoWait("payment_sub_{$member_uid}", 120) !== true) {
                //错误提示
                throw new Exception("请不要重复提交！");
            }

            #####   AMY 2020-07-31  AZ项目【100% no cash】#####
            $pro_info = $this->db->query("SELECT * FROM app_project WHERE id=?", [$pid])->row_array();
            if (!$pro_info) {
                throw new Exception("项目不存在！");
            }

            //只能微信自动打款+支付宝方式，别的不能提交
            if ($pro_info['is_az'] == 1 && !in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
                throw new Exception("提现方式选择有误，请重新选择！");
            }
            #####   AMY 2020-07-31  AZ项目【100% no cash】#####

            //获取用户信息
            $member_info = getDataByConditionCi("app_member", " AND id=?", "*", true, [$member_uid]);
            if (!$member_info) {throw new Exception("用户不存在，记录有误！");}
            $country = $member_info['country'];//会员所在国家
            $certif_id = $member_info['indentity_code'];//身份证号
            $name = $member_info['name'];
            $member_mobile = $member_info['mobile'];
            $ip = getip();

            //查询项目执行表，是否存在此用户信息
            $check_implement = getDataByConditionCi("app_project_implement_{$pid}", " AND member_uid=? AND finish_status='c'","*", true, [$member_uid]);
            if (!$check_implement) {
                throw new Exception("参数有误！");
            }
            $pid_id = $check_implement['id'];
            $partner_id = $check_implement['partner_id'];
            $groupno = $check_implement['groupno'];
            $name = $check_implement['name'];
            $member_mobile = $check_implement['mobile'];
            $department = $check_implement['department'];
            $job_title = $check_implement['job_title'];
            $unit_name = $check_implement['unit_name'];
            $unit_level = $check_implement['unit_level'];
            $province = $check_implement['province'];
            $city = $check_implement['city'];
            $member_mobile = $member_mobile && check_mobile($member_mobile) ? $member_mobile : "";

            //通过查询积分明细表id获取项目流水表信息，检测项目流水表信息
            $check_detail = getDataByConditionCi("app_member_point_detail", " AND log_code in(".POINT_CHANGE_CODE_SURVEY.",".POINT_REISSUE_INTEGRAL.") AND finish_status='c' AND param=? AND uid=?","*", true, [$pid, $member_uid]);
            if (!$check_detail) {
                _back_msg("error", "参数有误！");
            }
            if ($check_detail['apply_status'] == 1) {//已提交过支付申请
                throw new Exception("您已申请过兑换，不能重复操作！");
            }
            $p_detail_id = $check_detail['id'];
            $country = $check_detail['country'];//完成问卷时，外包记录的国家编号
            $money_rate = $check_detail['money_rate'];//完成问卷时，外包记录的相对人民币的汇率
            $point = $check_detail['point'];
            if (!$country) {throw new Exception("您的国家属性缺失，不能进行兑换，请联系管理员补充完整信息再进行兑换！");}
            if ($payment_type == EXCHANGE_MOBILE) {//手机充值方式
                $exchange_amount = $point / 100;
                if (!in_array($exchange_amount, [20, 30, 50, 100, 200, 300, 500])) {//不存在符合条件的支付方式
                    throw new Exception("请选择别的提现方式，此方式不符合提现要求！");
                }
            }

            if ($payment_type != EXCHANGE_SURPLUS) {//不是结余积分提现时
                if ($point < 100) {
                    throw new Exception("兑换失败，兑换分值({$point}分)太低，无法进行兑换，100积分起兑！");
                }
            }

            $payment_type = (int)$payment_type;
            $payment_from = "";
            if ($payment_type !== EXCHANGE_SURPLUS && $payment_type !== DON_BE_POLITE) {//积分转结余、不要礼金，不需要操作账号信息
                //检测提现账号是否正确
                $check_account = getDataByConditionCi("app_ex_payment_account", " AND uid=? AND payment_type=?","*", true, [$member_uid, $payment_type]);
                if (!$check_account) {//账号不存在，入库
                    if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                        if (!$payment_name || !$payment_account) {
                            throw new Exception("请提交支付宝名称及支付宝账号！");
                        }
                        if ($payment_name=='undefined' || $payment_account=='undefined') {
                            throw new Exception("请提交支付宝名称及支付宝账号！");
                        }
                    }
                    if ($payment_type == EXCHANGE_MOBILE) {//手机充值
                        if (!$payment_account) {
                            throw new Exception("您选择的是手机充值兑换方式，请提交手机号码！！");
                        }
                        if ($payment_account=='undefined') {
                            throw new Exception("您选择的是手机充值兑换方式，请提交手机号码！！");
                        }
                    }
                    if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信自动打款
                        throw new Exception("微信零钱收款，请先扫码！！");
                    }
                    ####    AMY 2022-07-06 支付账号存在，查询该账号是否绑定在别的账号名下，如果是，需要用户重置
                    $check_p_a = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid!=? AND payment_type=? AND payment_account=?", [$member_uid, $payment_type, $payment_account])->row_array();
                    if ($check_p_a) {//支付账号已存在
                        file_put_contents('./tmp/payment_account_exist.txt',$member_uid."_".$payment_type."_".$payment_account."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                        _back_msg("error", "您输入的支付账号已被其他人占用！");
                    }
                    ####    AMY 2022-07-06 支付账号存在，查询该账号是否绑定在别的账号名下，如果是，需要用户重置

                    $insert_account_data = [
                        "uid" => $member_uid,
                        "payment_type" => $payment_type,
                        "payment_name" => $payment_name ? $payment_name : ($name ? $name : ""),//支付名称不存在，用明细表的姓名填充
                        "payment_account" => $payment_account,
                        "add_time" => time(),
                    ];
                    $payment_name = $payment_name ? $payment_name : ($name ? $name : "");
                    $this->db->insert("app_ex_payment_account", $insert_account_data);
                    $payment_account_id = $this->db->insert_id();
                    if (!$payment_account_id) {
                        file_put_contents("./tmp/fail_bk_exchange.txt", "支付账号创建失败：".$this->db->last_query()."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                        throw new Exception("支付账号存储失败！");
                    }
                } else {//账号已存在,获取当前最新的记录进行更新
                    //账号信息从表单中来,不过后面需要按照查询出来的编号进行更新
                    if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信提现的，微信openid从表里来
                        $payment_account = $check_account['payment_account'];
                        $payment_from = $check_account['payment_from'];
                        if ($payment_from != 2) {//老师未重新扫码
                            throw new Exception("微信零钱收款，请先扫码！！");
                        }
                    }
                    $payment_account_id = $check_account['id'];
                    $this->db->update("app_ex_payment_account", ["payment_name" => $payment_name, "payment_account" => $payment_account], ["id" => $payment_account_id]);
                }
            }

            ####    AMY 2022-07-06 支付账号存在，查询该账号是否绑定在别的账号名下，如果是，需要用户重置
            $check_p_a = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid!=? AND payment_type=? AND payment_account=?", [$member_uid, $payment_type, $payment_account])->row_array();
            if ($check_p_a) {//支付账号已存在
                file_put_contents('./tmp/payment_account_exist.txt',$member_uid."_".$payment_type."_".$payment_account."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                _back_msg("error", "您输入的支付账号已存在！");
            }
            ####    AMY 2022-07-06 支付账号存在，查询该账号是否绑定在别的账号名下，如果是，需要用户重置

            if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
                if (!$payment_name || !$payment_account) {
                    _back_msg("error", "请输入【支付宝用户名】、【支付宝账号】");
                }
            }
            if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信
                if (!$payment_name) {
                    _back_msg("error", "请输入【微信用户名】");
                }
                if (!$payment_account) {
                    _back_msg("error", "您选择转账到微信零钱，请扫码授权再提交");
                }
            }
            if ($payment_type == EXCHANGE_MOBILE) {//手机充值
                if (!$payment_account) {
                    _back_msg("error", "请输入【手机号码】");
                }
                if (!check_mobile($payment_account)) {
                    _back_msg("error", "手机号码格式错误");
                }
            }
            if ($payment_name && !isAllChinese($payment_name)) {//支付宝账号只支持真实姓名
                throw new Exception("请输入真实姓名!");
            }

            //检测pid与member_uid信息是否有误，是否已经做过兑换
            //查询流水表，是否已存在流水信息
            $check_flow = getDataByConditionCi("app_project_flow", " AND log_code=? AND param_id=? AND `status`=1","*", true, [FLOW_SOURCE_PROJECT_POINT_DETAIL, $p_detail_id]);

            //查询订单表，查询是否已经有过兑换记录
            $check_order = getDataByConditionCi(SURVEY_TABLE_APP_PAYMENT_ORDER, " AND log_code=? AND pid=? AND uid=? AND order_status=1","*", true, [ORDER_SOURCE_STATUS_PROJECT, $pid, $member_uid]);
            if ($check_flow || $check_order) {
                throw new Exception("您已申请过兑换，不能重复操作！");
            }

//            $user_order = $this->db->query("SELECT * FROM app_payment_order_new WHERE pid='{$pid}' AND uid='{$member_uid}' AND order_status=1 LIMIT 1")->row_array();
//            if ($user_order) {//订单存在
//                throw new Exception("已经提交过审核！");
//            }

            //检测积分总表是否存在记录
            $member_point = getDataByConditionCi("app_member_point_sum", " AND uid=?","*", true, [$member_uid]);
            if (!$member_point) {throw new Exception("用户不存在，记录有误！");}

            //先添加提现积分日志，把用户想提现的积分记录下来
            $insert_flow_log = [
                "log_code" => FLOW_SOURCE_PROJECT_POINT_DETAIL,
                "param_id" => $p_detail_id,//项目积分明细表编号
                "point" => $point,//总积分,
                "member_uid" => $member_uid,
                "add_time" => time(),
            ];
            $this->db->insert("app_project_flow_log", $insert_flow_log);
            $flow_log_id = $this->db->insert_id();//流水编号
            if (!$flow_log_id) {
                throw new Exception("兑换失败，请重试！");
            }

            ##############  所有积分都转换成人民币支付出去 start ##############
            $get_survey_point = getDataByConditionCi("app_survey_point", " AND country_id=?","*", true, [$country]);
            if (!$get_survey_point) {throw new Exception("积分与兑换比例未配置，请联系管理员！");}
            //积分转换成对应国家币种金额 round(5.055, 2)
            $exchange_amount = round(($point * $get_survey_point['china_money']) / $get_survey_point['country_point'], 2);
            //对应币种金额转换成提现金额
            $pay_exchange_amount = $exchange_amount * $get_survey_point['exchange_rate'];
            //对应币种金额转换成人民币金额
            $rmb_exchange_amount = $exchange_amount * $get_survey_point['to_rmb_exchange_rate'];
            if ($pay_exchange_amount < 1) {//小于1块钱，不能提现
                throw new Exception("兑换失败，您的积分不足1元，暂不能进行兑换！");
            }

            //通过会员编号获取授权信息
            $payment_confirm_info = $this->db->query("SELECT * FROM app_project_payment_confirm_user_info WHERE member_uid=?", [$member_uid])->row_array();
            $auth_province = $auth_city = $auth_name = $auth_id_card = $auth_unit_name = $auth_department = $auth_mobile = "";
            if ($payment_confirm_info) {
                $auth_province = $payment_confirm_info['province'];
                $auth_city = $payment_confirm_info['city'];
                $auth_name = $payment_confirm_info['name'];
                $auth_id_card = $payment_confirm_info['id_card'];
                $auth_unit_name = $payment_confirm_info['unit_name'];
                $auth_department = $payment_confirm_info['department'];
                $auth_mobile = $payment_confirm_info['mobile'];
            }

            //公共流水表
            $insert_flow = [
                "member_uid" => $member_uid,
                "payment_type" => $payment_type,//提现方式
                "payment_account" => $payment_account ? $payment_account : "",
                "payment_name" => $payment_name ? $payment_name : "",
                "add_time" => time(),
                "certif_id" => $certif_id,
                "exchange_uid_phone" => $member_mobile,
                "adder_ip" => ip2long($ip),
                "adder_address" => ip2location($ip),
                "pid" => $pid ? $pid : 0,
                "pid_id" => $pid_id,
                "currency" => $get_survey_point['currency'],
                "pay_currency" => $get_survey_point['pay_currency'],
                "pay_exchange_rate" => $get_survey_point['exchange_rate'],
                "pay_rmb_exchange_rate" => $get_survey_point['to_rmb_exchange_rate'],
                "flow_log_id" => $flow_log_id,//提现日志编号
                "pay_status" => 2,//提现成功
                "pay_time" => time()//提现成功时间
            ];
            //公共订单表
            $insert_order = [
                "uid" => $member_uid,
                "payment_type" => $payment_type,
                "payment_account" => $payment_account ? $payment_account : "",
                "payment_name" => $payment_name ? $payment_name : "",
                "add_time" => time(),
                "adder_ip" => ip2long($ip),
                "adder_address" => ip2location($ip),
                "pid" => $pid,
                "pid_id" => $pid_id,
                "currency" => $get_survey_point['currency'],
                "pay_currency" => $get_survey_point['pay_currency'],
                "pay_exchange_rate" => $get_survey_point['exchange_rate'],
                "pay_rmb_exchange_rate" => $get_survey_point['to_rmb_exchange_rate'],


                "id_card" => $certif_id,//从会员表获取

                "name" => $name,//从项目明细表获取
                "mobile" => $member_mobile,//从项目明细表获取
                "department" => $department,//从项目明细表获取
                "job_title" => $job_title,//从项目明细表获取
                "unit_name" => $unit_name,//从项目明细表获取
                "unit_level" => $unit_level,//从项目明细表获取
                "province" => $province,//从项目明细表获取
                "city" => $city,//从项目明细表获取

                //授权填写的信息
                "auth_province" => $auth_province,
                "auth_city" => $auth_city,
                "auth_name" => $auth_name,
                "auth_id_card" => $auth_id_card,
                "auth_unit_name" => $auth_unit_name,
                "auth_department" => $auth_department,
                "auth_mobile" => $auth_mobile,

                //即时支付流程，订单状态改成支付中
//                "status" => in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO]) ? 5 : 1,
                //AMY 2023-05-23 支付流程调整，增加拆分支付流程状态，即时支付流程，订单状态改成支付中:6、拆分支付处理中
                "status" => in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO]) ? 6 : 1,
                "payment_from" => $payment_from,//针对微信支付，支付来源：1、上医说小程序 2、网医小程序
                "is_privacy" => $privacy, //是否已阅读并同意《个税代缴代付协议》
            ];

            $payment_info = [
                "pay_exchange_amount" => $pay_exchange_amount,
                "rmb_exchange_amount" => $rmb_exchange_amount,
                "is_project" => true,
                "get_survey_point" => $get_survey_point,
                "money_rate" => $money_rate,
                "insert_flow" => $insert_flow,
                "insert_order" => $insert_order,
                "p_detail_id" => $p_detail_id,
                "payment_account_id" => $payment_account_id,
                "member_point" => $member_point,
                "member_uid" => $member_uid,
                "point" => $point,//提现积分
                "ip" => $ip,//ip
                "exchange_amount" => $exchange_amount,//会员所在国家，提现积分对应的所在国家货币价值金额
            ];
            //提现记录提交，流水
            $insert_id_order = 0;
            switch ($payment_type)
            {
                case EXCHANGE_SURPLUS://积分转结余
                    $this->exchange_surplus($payment_info);
                    break;
                case DON_BE_POLITE://不要礼金
                    $this->exchange_donation($payment_info);
                    break;
                default://非手机兑换，如微信，支付宝，银行等,把积分转换成对应的人民币金额,进流水表，兑换订单表
                    $insert_id_order = $this->exchange_other($payment_info);
                    break;
            }

            ####    AMY 2021-02-07 查询外部邀请流程，兑换名单是否从此流程来，是否有推荐人，如有推荐人，还得根据项目完成情况，给推荐人发放奖励
            //根据被推荐人信息查询是否有推荐人
            $invite_info = $this->db->query("SELECT * FROM app_patient_fwy_add WHERE pid=? AND btjr_member_id=? LIMIT 1", [$pid, $member_uid])->row_array();
            if ($invite_info) {
                //直接更新推荐记录为已提现操作
                $res_fwy = $this->db->update("app_patient_fwy_add", [
                    "apply_status" => 1,    //被推荐人提现状态
                    "apply_time" => time(), //被推荐人提现时间
                ], ["id" => $invite_info['id']]);
                if (!$res_fwy) {
                    throw new Exception("兑换失败，请重试[推荐]！");
                }
            }
            ####    AMY 2021-02-07 查询外部邀请流程，兑换名单是否从此流程来，是否有推荐人，如有推荐人，还得根据项目完成情况，给推荐人发放奖励

            if ((int)$payment_type === EXCHANGE_SURPLUS || $payment_type == DON_BE_POLITE) {//项目转结余积分、不要礼金时，兑换流程结束
                $update_detail_where = [
                    "m_pay_type" => $payment_type,
                    "apply_status" => 1,
                    "apply_time" => time(),
                    "pay_status" => 2,
                    "pay_time" => time(),
                    "cash_status" => 2,//老流程，提现申请成功标识状态
                ];
            } else {
                $update_detail_where = [
                    "m_pay_type" => $payment_type,
                    "apply_status" => 1,
                    "apply_time" => time(),
                    "cash_status" => 2,//老流程，提现申请成功标识状态
                ];
            }
            $res_update_detail = $this->db->update("app_member_point_detail", $update_detail_where, ["id"=>$p_detail_id]);
            if (!$res_update_detail) {throw new Exception("兑换失败，请重试！");}

            //不是结余积分、不要礼金、手机充值时，需要设置默认提现账号
            if ($payment_type !== EXCHANGE_SURPLUS && $payment_type != DON_BE_POLITE && $payment_type != EXCHANGE_MOBILE) {
                //设置默认账号
                if ($payment_account_id) {
                    //再把最新的记录变更成默认账号
                    $this->db->query("UPDATE app_ex_payment_account SET is_default=0 WHERE uid=?", [$member_uid]);
                    //再把最新的记录变更成默认账号
                    $this->db->update("app_ex_payment_account", ["is_default"=>1], ["id"=>$payment_account_id]);
                }
            }
            //AMY 2020-05-13 需要医生本人手机扫描提现，所以微信自动提现不能作为默认提现账号
            $res_pro_partner_info = $this->db->query("SELECT * FROM app_project_partner WHERE project_id='{$pid}' AND partner_id='{$partner_id}' AND groupno='{$groupno}' LIMIT 1")->row_array();
            $open_c = $res_pro_partner_info['openc'];
            $this->load->model('survey_model');
            //问卷调度
            $project_list = $this->investigate_survey($member_uid);
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                file_put_contents("./tmp/fail_bk_exchange.txt", $member_uid."**".$pid."**".$payment_type."**".$payment_name."**".$payment_account."**".$p_detail_id."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
                throw new Exception("兑换失败，请联系管理员！");
            }
            if (file_exists("./uploads/wechat/{$pid}/{$scene}.png")) {//删除上医说扫码二维码
                unlink("./uploads/wechat/{$pid}/{$scene}.png");
            }
            if (file_exists("./uploads/wechat/{$pid}/{$scene}_idr.png")) {//删除网医扫码二维码
                unlink("./uploads/wechat/{$pid}/{$scene}_idr.png");
            }
            ### AMY 2020-10-26 已经增加短信验证流程，不需要做这步骤验证，验证session值即可
            file_put_contents('./tmp/project_exchange.txt',"payment:".'_'.$pid.'_'.$m_id.'_'.$member_uid."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
            //删除session
            $this->session->unset_userdata("bk_exchange_{$pid}_{$m_id}");
            ### AMY 2020-10-26 已经增加短信验证流程，不需要做这步骤验证，验证session值即可

            ####    AMY 2021-02-07 查询外部邀请流程，兑换名单是否从此流程来，是否有推荐人，如有推荐人，还得根据项目完成情况，给推荐人发放奖励
            if ($invite_info) {//存在推荐人，则获取推荐人信息
                //查询推荐人提现信息是否存在
                if ($invite_info['is_recommend'] == 1) {//有推荐人
                    //AMY 2022-02-24 增加推荐人是否短信授权状态及PM操作审核状态验证tjr_is_auth:1（已授权）,pm_status:1(已审核通过)
                    if ($invite_info['tjr_member_id'] > 0 && $invite_info['tjr_payment_name'] && $invite_info['tjr_payment_account'] && $invite_info['pm_status'] == 1 && $invite_info['tjr_apply_status'] == 0 && $invite_info['tjr_is_auth'] == 1) {
                        $invite_order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE log_code=? AND pid=? AND uid=? AND param=? LIMIT 1", [ORDER_SOURCE_STATUS_INVITE, $pid, $invite_info['tjr_member_id'], $invite_info['id']])->row_array();
                        if (!$invite_order_info) {//不存在，新增推荐支付订单
                            $arr_tjr_area_parent_name = $invite_info['tjr_area_parent_name'] ? explode("/", $invite_info['tjr_area_parent_name']) : [];
                            $invite_order_data = [
                                "log_code" => ORDER_SOURCE_STATUS_INVITE,
                                "param" => $invite_info['id'],
                                "uid" => $invite_info['tjr_member_id'],
                                "pid" => $pid,
                                "payment_type" => $invite_info['tjr_payment_type'],
                                "payment_account" => $invite_info['tjr_payment_account'],
                                "payment_name" => $invite_info['tjr_payment_name'],
                                "currency" => 130,//站点积分币种单位，默认RMB
                                "pay_currency" => 130,//字典表ID，实际支付的币种单位，默认RMB
                                "exchange_point" => $invite_info['dr_reward'] * 100,//兑换积分
                                "exchange_amount" => $invite_info['dr_reward'],//会员兑换金额
                                "pay_exchange_amount" => $invite_info['dr_reward'],//实际支付金额，推荐奖励
                                "pay_rmb_exchange_amount" => $invite_info['dr_reward'],//实际支付的人民币金额，推荐奖励
                                "examine_type" => 2,//默认财务已审核待支付
                                "pay_exchange_rate" => 1,//当前支付货币转换汇率，人民币汇率
                                "pay_rmb_exchange_rate" => 1,//当前支付货币转换的人民币汇率，人民币汇率
                                "add_time" => time(),
                                "adder_ip" => ip2long($ip),
                                "adder_address" => ip2location($ip),
                                "id_card" => $invite_info['tjr_indentity_code'] ?? "",
                                "name" => $invite_info['tjr_name'] ?? "",
                                "mobile" => $invite_info['tjr_mobile'] ?? "",
                                "province" => $arr_tjr_area_parent_name[0] ?? "",
                                "city" => $arr_tjr_area_parent_name[1] ?? "",
                                "unit_name" => $invite_info['tjr_unit_info'] ?? "",
                                "unit_level" => $invite_info['tjr_unit_level'] ?? "",
                                "department" => $invite_info['tjr_department_name'] ?? "",
                                "job_title" => $invite_info['tjr_job_title_name'] ?? "",
                                "status" => 1,//推荐奖励，需要系统支付
                            ];
                            $res_pro_invite = $this->db->insert("app_payment_order_new", $invite_order_data);
                            if ($res_pro_invite) {//项目推荐提现成功，更新推荐提现状态
                                $this->db->update("app_patient_fwy_add", [
                                    "tjr_apply_status" => 1,    //推荐人提现状态
                                    "tjr_apply_time" => time()  //推荐人提现时间
                                ], ["id" => $invite_info['id']]);
                            }
                        }
                    }
                    file_put_contents("./tmp/project_invite_res.txt", "web:fwy_id:".$invite_info['id']."**".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
                    //更新处理结果，以便后台根据此状态确定是否给推荐人发放奖励
                    $this->db->update("app_patient_fwy_add", [
                        "web_is_pay" => 1,    //前台订单处理完成
                    ], ["id" => $invite_info['id'], "web_is_pay" => 0]);
                }
            }
            ####    AMY 2021-02-07 查询外部邀请流程，兑换名单是否从此流程来，是否有推荐人，如有推荐人，还得根据项目完成情况，给推荐人发放奖励

            //跳到即时支付流程进行支付
            if ($payment_type == EXCHANGE_ALIPAY || $payment_type == EXCHANGE_WEBCHAT_AUTO) {
                //跳到即时支付流程
                $this->session->set_userdata(["auto_payment_order_id" => $insert_id_order]);
                $scene = "order_".$insert_id_order;
                $scene = $insert_id_order."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
//                _back_msg("success_to_pay", "提交成功！", "/project_exchange_n/auto_payment?scene={$scene}");
                //AMY 2023-07-03 测试微信支付提交
                _back_msg("success_to_pay", "提交成功！", "/project_exchange_n_test/auto_payment?scene={$scene}");
            } else {
                _back_msg("success", ($project_list ? $project_list : ""));
            }
        } catch (Exception $e) {
            if ($e->getMessage() == "re_account") {
                _back_msg("re_account", "您已提交过账号，请直接点击确认提交申请！");
            } else {
                _back_msg("error", $e->getMessage());
            }
        }
        ##############  所有积分都转换成人民币支付出去 end ##############
    }

    ####    AMY 2020-10-26 短信验证通过后，才能进入提现页面
    //账号合法性验证
    private function account_validity($bk_code, $survey_uid_code, $is_ajax = false)
    {
        if (!$bk_code){return false;}
        $partner_paran = explode('_',$bk_code);
        $pid = $partner_paran[0];
        $bk_info = $partner_paran[1];
        //问卷状态
        $st = $this->survey_status($pid, $bk_info);
        if (!$st) {
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "p", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        if (!$bk_code || !$survey_uid_code) {
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $arr_survey_uid = explode("_", $survey_uid_code);
        $survey_uid = $arr_survey_uid[0];
        $survey_uid_encryption = $arr_survey_uid[1];
        $local_survey_uid = md5($survey_uid.PROJECT_ENCODE_KEY);
        if (!$survey_uid || $local_survey_uid != $survey_uid_encryption) {
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }

        $res = $this->db->query("SELECT * FROM app_project_s_link WHERE pid = '{$pid}' AND survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        if (!$res) {//查询记录不存在
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $partner_id = $res['partner_id'];
        $group_no = $res['link_group'];
        //语言包
        $country_default_lang = $this->survey_model->get_country_default_lang($pid, $partner_id, $group_no);
        $lang = $country_default_lang['lang'];
        $country = $country_default_lang['country'];
        $lang_ary = get_lang($lang);
        //通过survey_uid查询对应的项目执行表的member_uid
        $res_project_info = $this->db->query("SELECT * FROM app_project WHERE id='{$pid}' LIMIT 1")->row_array();
        $p_table = $res_project_info['p_table'];
        $res_p_table_info = $this->db->query("SELECT * FROM {$p_table} WHERE survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        if (!$res_p_table_info) {
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $res = array(
            "pid" => $pid,
            "survey_uid" => $survey_uid,
            "member_uid" => $res_p_table_info['member_uid'],
            "m_id" => $res_p_table_info['id'],
            "imp_name" => $res_p_table_info['name'],
            "st" => $st,
            "lang_ary" => $lang_ary,
            "country" => $country,
            "groupno" => $group_no,
            "partner_id" => $partner_id,
        );
        return $res ? $res : false;
    }

    //问卷状态解析
    private function survey_status($pid, $bk_info){
        if (!$pid) {return false;}
        $c_info = $pid ."_c";
        $q_info = $pid ."_q";
        $s_info = $pid ."_s";
        $res_c_info = str_md5_code($c_info.PROJECT_ENCODE_KEY);
        $res_q_info = str_md5_code($q_info.PROJECT_ENCODE_KEY);
        $res_s_info = str_md5_code($s_info.PROJECT_ENCODE_KEY);
        $st = "";
        switch ($bk_info){
            case $res_c_info:
                $st = 'c';
                break;
            case $res_q_info:
                $st = 'q';
                break;
            case $res_s_info:
                $st = 's';
                break;
        }
        return $st ? $st : false;
    }
    ####    AMY 2020-10-26 短信验证通过后，才能进入提现页面

    private function investigate_survey($uid)
    {
        if (!$uid) {return false;}

        //通过uid获取会员相关信息
        $user_info = getDataByCondition("app_member a left join info_department b on a.department=b.id left join app_unit c on a.unit_id=c.id left join app_unit_plus d on c.id=d.unit_id", " AND a.id='{$uid}'", "a.*,b.department as department_name,c.name as unit_name,d.unit_level as u_level", true);
        if (!$user_info) {
            return false;
        }
        $user_info_dic = [];
        //根据编号查询字典表
        $user_info['country'] && $user_info_dic[$user_info['country']] = $user_info['country'];
        $user_info['province'] && $user_info_dic[$user_info['province']] = $user_info['province'];
        $user_info['city'] && $user_info_dic[$user_info['city']] = $user_info['city'];
        $user_info['district'] && $user_info_dic[$user_info['district']] = $user_info['district'];
        $user_info['gender'] && $user_info_dic[$user_info['gender']] = $user_info['gender'];
        $user_info['top_education'] && $user_info_dic[$user_info['top_education']] = $user_info['top_education'];
        $user_info['top_degree'] && $user_info_dic[$user_info['top_degree']] = $user_info['top_degree'];
        $user_info['doctor_profession'] && $user_info_dic[$user_info['doctor_profession']] = $user_info['doctor_profession'];
        $user_info['practice_sort'] && $user_info_dic[$user_info['practice_sort']] = $user_info['practice_sort'];
        $user_info['u_level'] && $user_info_dic[$user_info['u_level']] = $user_info['u_level'];
        $user_info['grade'] && $user_info_dic[$user_info['grade']] = $user_info['grade'];
        $user_info['job_title'] && $user_info_dic[$user_info['job_title']] = $user_info['job_title'];
        $user_info['position'] && $user_info_dic[$user_info['position']] = $user_info['position'];
        $dic_info = [];
        if ($user_info_dic) {
            $sys_dic_info = $this->db->query("SELECT * FROM app_sys_dictionary WHERE id in(".implode(",",$user_info_dic).")")->result_array();
            if ($sys_dic_info) {
                foreach ($sys_dic_info as $v_dic) {
                    $dic_info[$v_dic['id']] = $v_dic['val'];
                }
            }
        }

        //查询用户预付款
        $user_prepay = $this->db->query("SELECT pid,sum(prepay_point) as prepay_point FROM app_project_prepay WHERE member_uid=? GROUP BY pid", [$uid])->result_array();
        $rs_user_prepay = [];
        if ($user_prepay) {
            foreach ($user_prepay as $v_prepay) {
                $v_prepay['prepay_point'] && $rs_user_prepay[$v_prepay['pid']] = $v_prepay['prepay_point'];
            }
        }
        //账号审核中
        $error_msg = "";
        if ($user_info['status'] == MEMBER_STATUS_AUDITING) {
            $error_msg = $this->lang_ary[LABEL_ACCOUNT_AUDITING];
        } else if ($user_info['status'] == MEMBER_STATUS_FORBIDDEN) {//审核不通过
            $error_msg = "账号审核不通过";
        }

        //获取域名地址,通过域名与来源获取来源的sys_id编号
        $api_url_link = $_SERVER['HTTP_HOST'];
        $web_url_id = getDataByCondition("app_sys_survey_domain", " AND domain='{$api_url_link}' AND from_where=1", "*", true);
        if (!$web_url_id) {
            return false;
        }
        $sys_id = $web_url_id['sys_id'];

        //通过uid获取会员信息
        $res = $no_link_pro = [];

        $page_num = 500;

        //进行的项目,过滤会员站不展示的项目
        $pro_info = $this->db->query("SELECT * FROM app_project WHERE pro_status='".PROJECT_OPENING."' AND open_type!=3 ORDER BY id DESC LIMIT 0,".$page_num)->result_array();


        if ($pro_info) {
            //提取项目编号
            $project_ids = $project_partners = [];
            foreach($pro_info as $v_pro){
                $project_ids[] = $v_pro['id'];
            }
            //通过项目编号，获取内部资源的外包
            $res_project_ids = $this->db->query("SELECT * FROM app_project_partner WHERE project_id in(".implode(",", $project_ids).") AND property='".VENDOR_INTERNAL."' AND groupno>0 GROUP BY project_id")->result_array();
            if ($res_project_ids) {
                foreach($res_project_ids as $v_pro_partner){
                    $project_partners[$v_pro_partner['project_id']] = $v_pro_partner;
                }
            }

            foreach ($pro_info as $v) {
                //检查是否存在项目明细，不存在时，创建项目明细表
                $app_implement = $this->db->query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE  TABLE_NAME = 'app_project_implement_{$v['id']}'")->row_array();
                if (!$app_implement) {
                    //添加项目明细表
                    $res_add_project_imp = $this->add_project_implement($v['id'], $v);
                    if (!$res_add_project_imp) {continue;}
                }


                //在抽样名单里
                $pro_imp_info = getDataByCondition("app_project_implement_{$v['id']}", " AND member_uid='{$uid}' LIMIT 1", "*", true);
                //有问卷状态,或者医师【信息不符】，4、电话不存在(结束) 6、条件不符合(结束) 7、配额已满(结束) 9、中途拒访(结束) 10、直接拒访(结束)
                if ($pro_imp_info['finish_status'] != '' || in_array($pro_imp_info['screening_status'], array(4,6,7,9,10))) {
                    continue;
                }
                if (isset($pro_imp_info['my']) && $pro_imp_info['my'] == 1) {//集团答卷，不显示给医生
                    continue;
                }
                if ($v['open_type'] == SURVEY_PROJECT_OPEN_TYPE_ALL) {//对所有会员开放
                    if (!$pro_imp_info) {//不存在项目执行表的记录
                        //通过国家+内部资源，获取一条外包链接,是多站点发布的项目才能显示，否则问卷完成无法返回
                        $pro_partner = $this->db->query("SELECT * FROM app_project_partner WHERE project_id='{$v['id']}' AND property='".VENDOR_INTERNAL."' AND groupno>0 AND is_more_site_release='".PROJECT_PARTNER_SETTING_MORE_SITE_RELEASE."' LIMIT 1")->row_array();
                        if (!$pro_partner) {
                            continue;
                        }
                        //随机分配一个配额编号给用户
                        $pro_quota = $this->db->query("SELECT * FROM app_project_quta WHERE pid='{$v['id']}' AND quta_status='".QUOTA_STATUS_OPENING."' LIMIT 1")->row_array();
                        if (!$pro_quota) {
                            continue;
                        }
                        //先添加入库，再分配链接
                        $sampling_cond_arr = [
                            "country" => $user_info['country'] ? $dic_info[$user_info['country']] : "",
                            "province" => $user_info['province'] ? $dic_info[$user_info['province']] : "",
                            "city" => $user_info['city'] ? $dic_info[$user_info['city']] : "",
                            "district" => $user_info['district'] ? $dic_info[$user_info['district']] : "",
                            "unit_name" => $user_info['unit_name'] ?? "",
                            "name" => $user_info['name'],
                            "birthday" => $user_info['birthday'],
                            "pass_card" => $user_info['indentity_code'],
                            "email" => $user_info['email'],
                            "home_addr" => $user_info['address'],
                            "gender" => $user_info['gender'] ? $dic_info[$user_info['gender']] : "",
                            "office_phone" => $user_info['office_phone'],
                            "top_education" => $user_info['top_education'] ? $dic_info[$user_info['top_education']] : "",
                            "top_degree" => $user_info['top_degree'] ? $dic_info[$user_info['top_degree']] : "",
                            "doctor_profession" => $user_info['doctor_profession'] ? $dic_info[$user_info['doctor_profession']] : "",
                            "practice_sort" => $user_info['practice_sort'] ? $dic_info[$user_info['practice_sort']] : "",
                            "unit_level" =>$user_info['u_level'] ? $dic_info[$user_info['u_level']] : "",
                            "grade" => $user_info['grade'] ? $dic_info[$user_info['grade']] : "",
                            "job_title" => $user_info['job_title'] ? $dic_info[$user_info['job_title']] : "",
                            "position" => $user_info['position'] ? $dic_info[$user_info['position']] : "",
                            "department" => $user_info['department_name'] ? $user_info['department_name'] : "",

                            "quta_id" => $pro_quota['id'],
                            "partner_id" => $pro_partner['partner_id'],
                            "groupno" => $pro_partner['groupno'],
                            "data_sources" => 4,
                            "partner_uid" => 'uid='.$v['id'].'_'.$uid,
                            "mobile" => $user_info['mobile'],
                            "member_uid" => $uid,
                        ];
                        $rs_insert = $this->db->insert("app_project_implement_{$v['id']}", $sampling_cond_arr);
                        if (!$rs_insert) {
                            continue;
                        }
                        $pro_imp_info = $sampling_cond_arr;
                    }
                }

                if ($pro_imp_info) {//存在抽样名单里
                    //通过抽样名单获取问卷链接
                    //生成问卷链接
                    $uid_str = project_link_encode($v['id'], $pro_imp_info['partner_id'], $pro_imp_info['groupno']);
                    $partner_link = DRSAY_WEB . 'go/s/' . $uid_str."_".$sys_id . '?' . $pro_imp_info['partner_uid'];
                    //通过partner_id与groupno获取外包问卷链接
                    $get_pro_partner = getDataByCondition("app_project_partner", " AND project_id='{$v['id']}' AND partner_id='{$pro_imp_info['partner_id']}' AND groupno='{$pro_imp_info['groupno']}'","*", true);

                    $res = [
                        "id" => $v['id'],
                        "project_id" => cus_pid($v['id']),
                        "pro_name" => $v['pro_name'],
                        "pro_info" => strip_tags($v['pro_info']),
                        "pro_start_time" => $v['pro_start_time'],
                        "pro_end_time" => date("Y-m-d", $v['pro_end_time']),
                        "point" => $get_pro_partner['point_c'] / 100,//转成金额RMB，金额与积分的比例为:1:100
                        "pro_prepay" => isset($rs_user_prepay[$v['id']]) ? $rs_user_prepay[$v['id']] : 0,
                        "partner_link" => $error_msg ? "" : $partner_link,
                        "error" => $error_msg,
                    ];
                    break;
                }
            }
        }


        if ($res) {
            return $res;
        } else {
            return false;
        }
    }


    //其它兑换(消耗)
    private function exchange_other($payment_info)
    {
        $pay_exchange_amount = $payment_info['pay_exchange_amount'];
        $rmb_exchange_amount = $payment_info['rmb_exchange_amount'];
        $is_project = $payment_info['is_project'];
        $get_survey_point = $payment_info['get_survey_point'];
        $money_rate = $payment_info['money_rate'];
        $insert_flow = $payment_info['insert_flow'];
        $insert_order  = $payment_info['insert_order'];

        $payment_type = $insert_order['payment_type'];
        $payment_account = $insert_order['payment_account'];
        $payment_name = $insert_order['payment_name'];
        $payment_from = $insert_order['payment_from'];
        $member_uid  = $insert_order['uid'];

        $p_detail_id  = $payment_info['p_detail_id'];
        $payment_account_id  = $payment_info['payment_account_id'];
        $point  = $payment_info['point'];
        $ip  = $payment_info['ip'];
        $insert_p_detail_id = "";
        //记录消耗日志
        $pid = isset($insert_flow['pid']) ? $insert_flow['pid'] : "";//项目编号
        $pid_id = isset($insert_flow['pid_id']) ? $insert_flow['pid_id'] : "";//项目执行表编号
        $insert_p_detail_id = $this->insert_use_point_detail_info($member_uid, $point, POINT_CHANGE_CODE_USED_EXCHANGE, $ip, $insert_flow['payment_type'], $pid, $pid_id);
        if (!$insert_p_detail_id) {throw new Exception("[明细]兑换失败，请重试1！");}
        $insert_flow_other = [
            "log_code" => $is_project ? FLOW_SOURCE_PROJECT_POINT_DETAIL : FLOW_SOURCE_SURPLUS,
            "param_id" => $is_project ? $p_detail_id : $member_uid,
            "point" => $point,//项目总积分或结余总积分
            "ex_payment_account_id" => $payment_account_id,//提现账号唯一编号
            "p_detail_id" => $insert_p_detail_id,//消耗积分日志编号
            "exchange_amount" => $rmb_exchange_amount,//项目站点金额
            "pay_exchange_amount" => $pay_exchange_amount,//提现金额
            "pay_rmb_exchange_amount" => $rmb_exchange_amount,//对应人民币金额
        ];

        ## AMY 2020-09-01 提现流水状态要去掉
        if (isset($insert_flow['pay_status'])) {
            unset($insert_flow['pay_status']);
        }
        if (isset($insert_flow['pay_time'])) {
            unset($insert_flow['pay_time']);
        }
        ## AMY 2020-09-01 提现流水状态要去掉

        $insert_flow = array_merge($insert_flow, $insert_flow_other);
        $insert_flow_id = $this->insert_flow($insert_flow);
        if (!$insert_flow_id) {throw new Exception("[流水]兑换失败，请重试！");}

        ####    AMY 2023-05-23 支付笔数计算
        //拆单列表
        $payment_loop = $this->get_payment_loop($payment_type, $rmb_exchange_amount);
        if (!$payment_loop) {
            throw new Exception("[拆分]兑换失败，请重试！");
        } else {
            $payment_mod = $payment_loop['payment_mod'];//最后一笔金额
            $payment_num = $payment_loop['payment_num'];//支付笔数
            $payment_wx_amount = $payment_loop['payment_wx_amount'];//每次付款金额
        }
        ####    AMY 2023-05-23 支付笔数计算
        //批次号
        $payment_batch = $this->get_payment_batch($payment_type);
        if (!$payment_batch) {throw new Exception("[pc]兑换失败，请重试！");}

        //结余积分订单
        $insert_order_other = [
            "log_code" => ORDER_SOURCE_STATUS_PROJECT,
            "param" => $insert_flow_id,
            "exchange_point" => $point,//项目总积分或结余总积分
            "exchange_amount" => $rmb_exchange_amount,//项目站点金额
            "pay_exchange_amount" => $pay_exchange_amount,//提现金额
            "pay_rmb_exchange_amount" => $rmb_exchange_amount,//对应人民币金额
            "examine_type" => 2,//默认财务已审核待支付
            "payment_num" => $payment_num,//支付笔数
            "payment_batch" => $payment_batch,//支付批次号
        ];
        $insert_order = array_merge($insert_order, $insert_order_other);
        $this->insert_order($insert_order);
        $insert_order_id = $this->db->insert_id();
        if (!$insert_order_id) {throw new Exception("[兑换]兑换失败，请重试！");}
        $update_project_flow = [
            "order_id" => $insert_order_id,
        ];
        $res = $this->update_flow($update_project_flow, ["id"=>$insert_flow_id]);
        ####    AMY 2023-05-23 生成兑换拆分明细
        $insert_order['payment_order_id'] = $insert_order_id;
        $res_order_split = $this->order_split($insert_order, $payment_loop);
        if (!$res_order_split) {
            throw new Exception("[明细]兑换失败，请重试！");
        }
        ####    AMY 2023-05-23 生成兑换拆分明细
        if (!$res) {throw new Exception("[流水]兑换失败，请重试！");}
        return $insert_order_id;
    }

    //批次号生成
    private function get_payment_batch($payment_type)
    {
        if (!$payment_type) {return false;}
        //批次号
//        $random_bytes = openssl_random_pseudo_bytes(16);
//        $unique_id = bin2hex($random_bytes);
//        $payment_batch = $payment_type.$unique_id;
        $unique_id = md5(uniqid().rand(1, 99999));
        $payment_batch = $unique_id;//微信支付要求最大32位，不能超过，因此去掉增加类型的想法
        //查询该批次号是否已经存在，存在继续生成
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE payment_batch=? LIMIT 1", [$payment_batch])->row_array();
        if ($order_info) {
            return $this->get_payment_batch($payment_type);
        } else {
            return $payment_batch;
        }
    }

    //兑换笔数
    private function get_payment_loop($payment_type, $rmb_exchange_amount)
    {
        if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO]) || $rmb_exchange_amount <= 0) {return false;}
        if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信
            $payment_wx_amount = 500;//每次付款金额
            $calculate_loop_num = $this->calculate_loop_num($rmb_exchange_amount, $payment_wx_amount);
            if ($calculate_loop_num) {
                $payment_mod = $calculate_loop_num['last_val'];
                $payment_num = $payment_mod > 0 ? ($calculate_loop_num['loop'] + 1) : $calculate_loop_num['loop'];
                return [
                    "payment_num" => $payment_num,//支付笔数
                    "payment_mod" => $payment_mod,//取余
                    "payment_wx_amount" => $payment_wx_amount,//每次付款金额
                ];
            } else {
                return false;
            }
        } else {
            return [
                "payment_num" => 1,//支付笔数,默认一笔
                "payment_mod" => 0,//取余
                "payment_wx_amount" => $rmb_exchange_amount,//每次付款金额
            ];
        }

    }

    //给定两个值，算出循环次数及最后剩余数据
    private function calculate_loop_num($total, $multiple)
    {
        if ($total <= 0 || $multiple <= 0) {return false;}
        if ($multiple >= $total) {
            $total_multiple = 0;
            $last_val = $total;
        } else {
            $total_multiple = floor($total / $multiple);//向下取整
            $last_val = $total - $total_multiple * $multiple;//最后一次值
        }
        return [
            "loop" => $total_multiple,//循环次数
            "last_val" => $last_val,//循环完以后，最后剩余的值
        ];
    }

    /**
     * 拆分支付列表
     * @param {array} $order_info 订单明细
     * @param {array} $payment_loop 拆分支付明细
     * @return void
     * @throws Exception
     */
    private function order_split($order_info, $payment_loop)
    {
        if (!$order_info || !$payment_loop) {
            return false;
        }
        file_put_contents("./tmp/project_exchange_n.txt", "循环次数：".json_encode($payment_loop, true).PHP_EOL, FILE_APPEND | LOCK_EX);
        ####    AMY 2023-05-23 生成兑换拆分明细
        $payment_num = $payment_loop['payment_num'];
        $payment_mod = $payment_loop['payment_mod'];
        $payment_wx_amount = $payment_loop['payment_wx_amount'];
        $member_uid = $order_info['uid'];
        $payment_from = $order_info['payment_from'];
        $payment_order_id = $order_info['payment_order_id'];
        $payment_type = $order_info['payment_type'];
        $payment_account = $order_info['payment_account'];
        $payment_name = $order_info['payment_name'];
        $payment_batch = $order_info['payment_batch'];
        $pid = $order_info['pid'];
        $pid_id = $order_info['pid_id'];
        $split_order = [];//兑换拆分明细
        $alipay_remark = payment_remark($order_info);//发送至支付平台的备注信息
//        ####    AMY 2023-07-03 定义好商户明细订单号
//        //商户订单号
//        $payment_qz = payment_out_biz_no($order_info);//前缀
//        $payment_qz = $payment_qz ? $payment_qz : "HCPE";//没有返回前缀信息
//        ####    AMY 2023-07-03 定义好商户明细订单号

        if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信支付
            for ($p_num = 1; $p_num <= $payment_num; $p_num++) {
                $split_exchange_amount = $payment_wx_amount;
                if ($payment_mod > 0) {
                    if ($p_num == $payment_num) {
                        $split_exchange_amount = $payment_mod;
                    }
                }
//                ####    AMY 2023-07-03 定义好商户明细订单号
//                $out_detail_no = "DSW".$payment_qz.$payment_order_id.substr(md5(time().rand(1,99)), 0, 16);
//                ####    AMY 2023-07-03 定义好商户明细订单号
                $split_order[] = [
                    "log_code" => ORDER_SOURCE_STATUS_PROJECT,
                    "uid" => $member_uid,
                    "payment_from" => $payment_from,
                    "payment_order_id" => $payment_order_id,
                    "payment_type" => $payment_type,
                    "payment_account" => $payment_account,
                    "payment_name" => $payment_name,
                    "exchange_amount" => $split_exchange_amount,
                    "status" => 1,//1、准备中
                    "start_time" => time(),
                    "add_time" => time(),
                    "payment_batch" => $payment_batch,//批次号
//                    "out_detail_no" => $out_detail_no,//商家明细单号
                    "pid" => $pid,//项目编号
                    "pid_id" => $pid_id,//项目执行编号
                    "remark" => $alipay_remark,//备注信息
                ];
            }
        } else {//其他支付方式：如，支付宝
//            ####    AMY 2023-07-03 定义好商户明细订单号
//            $out_detail_no = "DSA".$payment_qz.$payment_order_id.substr(md5(time().rand(1,99)), 0, 16);
//            ####    AMY 2023-07-03 定义好商户明细订单号
            $split_order[] = [
                "log_code" => ORDER_SOURCE_STATUS_PROJECT,
                "uid" => $member_uid,
                "payment_from" => $payment_from,
                "payment_order_id" => $payment_order_id,
                "payment_type" => $payment_type,
                "payment_account" => $payment_account,
                "payment_name" => $payment_name,
                "exchange_amount" => $payment_wx_amount,
                "status" => 1,//1、准备中
                "start_time" => time(),
                "add_time" => time(),
                "payment_batch" => $payment_batch,//批次号
//                "out_detail_no" => $out_detail_no,//商家明细单号
                "pid" => $pid,//项目编号
                "pid_id" => $pid_id,//项目执行编号
                "remark" => $alipay_remark,//备注信息
            ];
        }
        if ($split_order) {
            $res_split_order = $this->db->insert_batch("app_payment_order_new_split", $split_order);
            if (!$res_split_order) {
                return false;
            }
        }
        return true;
        ####    AMY 2023-05-23 生成兑换拆分明细
    }

    //积分转结余(非消耗)
    private function exchange_surplus($payment_info)
    {
        $pay_exchange_amount = $payment_info['pay_exchange_amount'];
        $rmb_exchange_amount = $payment_info['rmb_exchange_amount'];
        $is_project = $payment_info['is_project'];
        $get_survey_point = $payment_info['get_survey_point'];
        $money_rate = $payment_info['money_rate'];
        $insert_flow = $payment_info['insert_flow'];
        $insert_order  = $payment_info['insert_order'];
        $p_detail_id  = $payment_info['p_detail_id'];
        $payment_account_id  = $payment_info['payment_account_id'];
        $member_point  = $payment_info['member_point'];
        $member_uid  = $payment_info['member_uid'];
        $point  = $payment_info['point'];
        //结余积分流水
        $insert_flow_other = [
            "log_code" => FLOW_SOURCE_PROJECT_POINT_DETAIL,
            "param_id" => $p_detail_id,//积分明细表唯一编号
            "point" => $point,//项目总积分或结余总积分
            "ex_payment_account_id" => $payment_account_id ? $payment_account_id : "",//提现账号唯一编号
            "pay_status" => 2,//后台兑换成功状态
            "pay_time" => time(),//兑换成功时间
        ];
        $insert_flow = array_merge($insert_flow, $insert_flow_other);
        $insert_flow_id = $this->insert_flow($insert_flow);
        if (!$insert_flow_id) {throw new Exception("兑换失败，请重试！");}
        $update_member_sum = [
            "total_point" => $member_point['total_point'] + $point,
            "survey_point" => $member_point['survey_point'] + $point,
        ];

        file_put_contents('./tmp/project_exchange_surplus.txt', "uid:".$member_uid."**p_detail_id:".$p_detail_id."**".json_encode($update_member_sum, JSON_UNESCAPED_UNICODE)."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);

        $res = $this->update_member_sum($member_uid, $update_member_sum, ["uid"=>$member_uid]);
        if (!$res) {throw new Exception("兑换失败，请重试！2");}
    }

    //不要礼金(非消耗)
    private function exchange_donation($payment_info)
    {
        $pay_exchange_amount = $payment_info['pay_exchange_amount'];
        $rmb_exchange_amount = $payment_info['rmb_exchange_amount'];
        $is_project = $payment_info['is_project'];
        $get_survey_point = $payment_info['get_survey_point'];
        $money_rate = $payment_info['money_rate'];
        $insert_flow = $payment_info['insert_flow'];
        $insert_order  = $payment_info['insert_order'];
        $p_detail_id  = $payment_info['p_detail_id'];
        $payment_account_id  = $payment_info['payment_account_id'];
        $member_point  = $payment_info['member_point'];
        $member_uid  = $payment_info['member_uid'];
        $point  = $payment_info['point'];
        //结余积分流水
        $insert_flow_other = [
            "log_code" => FLOW_SOURCE_PROJECT_POINT_DETAIL,
            "param_id" => $p_detail_id,//积分明细表唯一编号
            "point" => $point,//项目总积分或结余总积分
            "ex_payment_account_id" => $payment_account_id ? $payment_account_id : "",//提现账号唯一编号
            "pay_status" => 2,//后台兑换成功状态
            "pay_time" => time(),//兑换成功时间
        ];
        $insert_flow = array_merge($insert_flow, $insert_flow_other);
        $insert_flow_id = $this->insert_flow($insert_flow);
        if (!$insert_flow_id) {throw new Exception("兑换失败，请重试！");}
    }

    //更新会员积分总表
    private function update_member_sum($member_uid, $update_member_sum, $update_where)
    {
        if (!$member_uid || !$update_member_sum || !$update_where) {return false;}
        //查询会员是否存在
        $member_point_sum = $this->db->query("SELECT * FROM app_member_point_sum WHERE uid=?", [$member_uid])->row_array();
        if ($member_point_sum) {//存在
            $res = $this->db->update("app_member_point_sum", $update_member_sum, $update_where);
        } else {
            $update_member_sum = array_merge($update_member_sum, $update_where);
            //添加
            $res = $this->db->insert("app_member_point_sum", $update_member_sum);
        }
        return $res ? true : false;
    }

    //新增兑换流水信息
    private function insert_flow($insert_flow)
    {
        if (!$insert_flow) {return false;}
        $this->db->insert("app_project_flow", $insert_flow);
        return $this->db->insert_id();
    }

    //新增订单信息
    private function insert_order($insert_order)
    {
        if (!$insert_order) {return false;}
        $this->db->insert(SURVEY_TABLE_APP_PAYMENT_ORDER, $insert_order);
        return $this->db->insert_id();
    }

    //编辑兑换流水信息
    private function update_flow($update_flow, $update_where)
    {
        if (!$update_flow || !$update_where) {return false;}
        $res = $this->db->update("app_project_flow", $update_flow, $update_where);
        return $res ? true : false;
    }

    //项目或者剩余积分提现时，消耗的积分记录
    private function insert_use_point_detail_info($uid, $point, $log_code, $ip, $payment_type, $pid="", $pid_id="")
    {
        if (!$uid || !$point || !$log_code || !$payment_type) {return false;}
        $insert_point_detail = array(
            "uid" => $uid,
            "point" => -$point,
            "log_code" => $log_code,
            "param" => $pid,
            "mid_id" => $pid_id,
            "add_time" => time(),
            "adder_ip" => ip2long($ip),
            "adder_ip_address" => ip2location($ip),
            "a_year" => date("Y"),
            "a_month" => (int)date("m"),
            "a_day" => (int)date("d"),
            "m_pay_type" => $payment_type,
        );
        $insert_point_detail = $this->insert_point_detail($insert_point_detail);
        return $insert_point_detail ? $insert_point_detail : false;
    }

    //新增消耗积分日志信息
    private function insert_point_detail($insert_point_detail)
    {
        if (!$insert_point_detail) {return false;}
        $this->db->insert("app_member_point_detail", $insert_point_detail);
        return $this->db->insert_id();
    }



    /**
     * @param $pid
     * @param $data_project
     * @return int
     * 添加项目明细表、附加字段
     */
    private function add_project_implement($pid, $data_project){
        $res_additional_setting = $create_pro_table =[];
        if(!empty($data_project['add_pr_26']) && !empty($data_project['add_pr_27'])){
            $add_pr_26 = explode("\n",$data_project['add_pr_26']);
            $add_pr_27 = explode("\n",$data_project['add_pr_27']);
            $arr_additional =[];
            $i = 1;
            foreach ($add_pr_26 as $k => $v){
                $arr_additional[trim($v)] = trim($add_pr_27[$k]);
                $res_additional_setting[$i] = ['pro_param'=>trim($v) ,'pro_param_name' =>trim($add_pr_27[$k])];
                $i ++;
            }
            //判断附加字段
            foreach ($arr_additional as $k_f => $v_f){
                $preg='"^[A-Za-z0-9-_]+$"';
                if(!preg_match($preg,$k_f,$arr)){
                    return _back_msg('error','附加字段只能是英文、数字');
                }
            }
            //判断附加字段是否重复
            $implement_field = $this->implement_field(array_keys($arr_additional), DIC_DEFAULT_LANG);
            if ($implement_field !== true) {
                return _back_msg("error", _("附加字段跟表字段重复，" . $implement_field));
            }
            //组装附加字段
            foreach ($arr_additional as $k_col => $v_col){
                $create_pro_table[] = "`" . $k_col . "` VARCHAR( 200 ) NOT NULL DEFAULT '' COMMENT '" . $v_col . "'";
            }
        }
        //添加附加字段表
        $data_setting = array(
            'project_id' => $pid,
            'pro_param' => !empty($res_additional_setting) ? json_encode($res_additional_setting,JSON_UNESCAPED_UNICODE) : '',
        );
        $project_setting = getDataByCondition('app_project_setting',"and project_id = {$pid}","*",true);
        if(!empty($project_setting)){
            upData('app_project_setting',$project_setting['id'],$data_setting);
        } else {
            saveData('app_project_setting',$data_setting);
        }
        //添加项目明细表
        $app_project_implement = $this->project_implement_field($pid, $create_pro_table);
        if ($app_project_implement) {
            $this->db->query($app_project_implement);
            $arr = $this->db->affected_rows();
            return $arr;
        }
    }

    /**
     * @param $pro_param
     * @param $lang
     * @return bool
     * 判断附加字段跟明细表是否有重复
     */
    private function implement_field($pro_param,$lang){
        if(!$pro_param){return true;}
        $where_practice_sort  = " b.sys_dictionary_lang_id = {$lang}  and a.big_class_id=16 ";
        $implement_field = $this->get_translate_by_condition($where_practice_sort);
        if($implement_field){
            foreach ($pro_param as $v_pro){
                if(in_array($v_pro,$implement_field)){
                    return $v_pro;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * @param $condition
     * @return array|bool
     * 通过条件获取翻译信息
     */
    private function get_translate_by_condition($condition, $is_flag = true)
    {
        if (!$condition) {
            return false;
        }
        $ci = &get_instance();
        $dictionary_translate = $this->db->query(" select b.* ,a.big_class_id,a.pid from app_sys_dictionary_translate as b left join app_sys_dictionary as a on b.sys_dictionary_id = a.id where {$condition}")->result_array();
        $ci->db->last_query();
        $arr_hospital = array();
        foreach ($dictionary_translate as $k_h => $v_h) {
            if ($is_flag) {
                $arr_hospital[$v_h['sys_dictionary_id']] = $v_h['val_translate'];
            } else {
                $arr_hospital[$v_h['sys_dictionary_id']] = $v_h;
            }
        }
        return $arr_hospital ? $arr_hospital : array();
    }

    /**
     * @param $id
     * @param $create_pro_table
     * @return string
     * 项目明细表生成
     */
    private function project_implement_field($id,$create_pro_table){
        $create_pro = !empty($create_pro_table) ? implode(",", $create_pro_table) . "," : '';
        $table = 'app_project_implement_' . $id;
        $app_project_implement = "CREATE TABLE IF NOT EXISTS " . $table . " (
                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增【N】',
                      `country` varchar(50) NOT NULL DEFAULT '0' COMMENT '国家', 
                      `province` varchar(50) NOT NULL DEFAULT '0' COMMENT '省份',
                      `city` varchar(50) NOT NULL DEFAULT '0' COMMENT '城市',
                      `district` varchar(50) NOT NULL DEFAULT '0' COMMENT '区域',
                      `unit_name` varchar(100) NOT NULL DEFAULT '0' COMMENT '医院',
                      `department` varchar(100) NOT NULL DEFAULT '' COMMENT '所属科室',
                      `name` varchar(150) NOT NULL DEFAULT '' COMMENT '姓名',
                      `gender` varchar(50) NOT NULL DEFAULT '' COMMENT '性别',
                      `birthday` int(11) NOT NULL DEFAULT '0' COMMENT '生日',
                      `mobile` varchar(50) NOT NULL DEFAULT '' COMMENT '手机',
                      `office_phone` varchar( 50 ) NOT NULL DEFAULT '0' COMMENT '办公电话',
                      `pass_card` varchar(100) NOT NULL DEFAULT '' COMMENT '身份证',
                      `email` varchar(200) NOT NULL DEFAULT '' COMMENT 'E-mail',
                      `home_addr` varchar(200) NOT NULL DEFAULT '' COMMENT '家庭住址',
                      `screening_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '受访者回答甄别问卷的状态：1、初访电话不通(可再访) 2、再访电话不通(可再访) 3、三访电话不通(结束) 4、电话不存在(结束) 5、预约成功待访(待访)  6、条件不符合(结束) 7、配额已满(结束) 8、临时中断(可再访) 9、中途拒访(结束) 10、直接拒访(结束)【N】【Y】',
                      `qc_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '品控状态：1、未检 2、已检，质量劣，数据无效，删除 3、已检，质量良，需重坊 4、已检，质量优，有效【N】【Y】',
                      `app_reservation_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'APP预约状态：1、邀请预约中 2、邀请已接受 3、邀请已确认 4、预约成功【N】【Y】',
                      `survey_answer_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '受访者进入正式问卷的状态：1、答卷中 2、答卷结束 【N】【Y】',
                      `survey_uid` varchar(250) NOT NULL DEFAULT '' COMMENT '问卷链接唯一参数 ',
                      `sound_record_url` varchar(200) NOT NULL DEFAULT '' COMMENT '录音路径【N】',
                      `quta_id` int(11) NOT NULL DEFAULT '0' COMMENT '配额控制表ID【N】',
                      `partner_id` int(10) NOT NULL DEFAULT '0' COMMENT '供应商【N】',
                      `partner_uid` varchar(255) NOT NULL DEFAULT '' COMMENT '合作伙伴的会员uid【N】',
                      `groupno` int(11) NOT NULL DEFAULT '0' COMMENT '链接组号【N】【Y】', 
                      `member_uid` int(11) NOT NULL DEFAULT '0' COMMENT '受访者【N】',
                      `data_sources` tinyint(2) NOT NULL DEFAULT '1' COMMENT '数据源 1：客户提供 2：系统抽样 3：外包 4:系统会员自行进入【N】【Y】', 
                      `interview_address` varchar(100) NOT NULL DEFAULT '' COMMENT '面访地址',
                      `top_education` varchar(100) NOT NULL DEFAULT '' COMMENT '学历【N】',
                      `top_degree` varchar(100) NOT NULL DEFAULT '' COMMENT '学位【N】',
                      `doctor_profession` varchar(100) NOT NULL DEFAULT '' COMMENT '专业【N】',
                      `certificate_type` varchar(100) NOT NULL DEFAULT '' COMMENT '技术资格证书类别【N】【Y】',
                      `practice_sort` varchar(100) NOT  NULL DEFAULT '' COMMENT '执业类别',
                      `working_status` varchar(100) NOT NULL DEFAULT '' COMMENT '执业情况【Y】',
                      `unit_level` varchar(100) NOT NULL DEFAULT '' COMMENT '医院等级',
                      `grade` varchar(100) NOT NULL DEFAULT '' COMMENT '医师级别',
                      `job_title` varchar(100) NOT NULL DEFAULT '' COMMENT '医师职称',
                      `position` varchar(100) NOT NULL DEFAULT '' COMMENT '医师职务',
                      `app_user_received_time` int(11) NOT NULL DEFAULT '0' COMMENT 'APP受访者接收预约时间【N】【Y】',
                      `app_user_received_time_start` int(11) NOT NULL DEFAULT '0' COMMENT 'APP受访者约定的起始时间【N】【Y】',
                      `app_user_received_time_end` int(11) NOT NULL DEFAULT '0' COMMENT 'APP受访者约定的结束时间【N】【Y】',
                      `app_reservation_status_user_id` int(11) NOT NULL DEFAULT '0'  COMMENT 'APP预约的处理人ID【N】【Y】',
                      `app_reservation_status_edit_time` int(11) NOT NULL DEFAULT '0'  COMMENT 'APP预约处理时间【N】【Y】',
                      `mobile_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '受访者手机状态：1、手机有效 2：手机无效【N】【Y】',
                      `original_screening_status` tinyint(1) NOT NULL DEFAULT '0'  COMMENT 'pm修改的受访者回答甄别问卷的状态，记录最初的状态【N】【Y】',
                      `visit_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '访问员回访状态：1 回访成功 状态为qc_status=1 ， 2回访失败状态为finish_status=b, qc_status=2【N】【Y】',
                      `screening_status_user_id` int(11) NOT NULL DEFAULT '0'  COMMENT '受访者回答甄别问卷状态的处理人ID【N】【Y】',
                      `screening_status_edit_time` int(11) NOT NULL DEFAULT '0'  COMMENT '受访者回答甄别问卷状态的处理时间【N】【Y】',
                      `original_finish_status` varchar(10) NOT NULL DEFAULT '' COMMENT '如果qc把finish_status状态为c的改成b,则记录原始状态【N】【Y】',
                      `click_time` int(11) NOT NULL DEFAULT '0' COMMENT '受访者进入正式问卷时间【N】【Y】',
                      `finish_time` int(11) NOT NULL DEFAULT '0' COMMENT '受访者完成正式问卷时间【N】【Y】',
                      `original_qc_status` tinyint(1) NOT NULL DEFAULT '0'  COMMENT 'pm修改的品控状态，记录最初的状态【N】【Y】',
                      `qc_uid` int(11) NOT NULL DEFAULT '0' COMMENT 'QC【N】',
                      `share_qc_uid` int(11) NOT NULL DEFAULT '0' COMMENT '共享QC【N】',
                      `qc_time` int(11) NOT NULL DEFAULT '0' COMMENT 'QC时间【N】',
                      `qc_reason_info` text  COMMENT 'QC评语【N】',
                      `prizes_id` int(11) NOT NULL DEFAULT '0' COMMENT '礼品编号',
                       " . $create_pro . "
                       `uid` int(11) NOT NULL DEFAULT '0' COMMENT '访问员【N】',
                      `share_uid` text     COMMENT '共享访问员【N】',  
                      `start_appointment_time` int(11) NOT NULL DEFAULT '0'  COMMENT '预约的起始时间【N】',
                      `end_appointment_time` int(11) NOT NULL DEFAULT '0'  COMMENT '预约的结束时间【N】',
                      `operator_user_id` int(11) NOT NULL DEFAULT '0' COMMENT '执行人【N】',
                      `operator_time` int(11) NOT NULL DEFAULT '0'  COMMENT '操作时间【N】',
                      `interviewer_info` text   COMMENT '访问记评【N】',
                      `pm_edit_user_id` int(11) NOT NULL DEFAULT '0' COMMENT '状态修改的项目负责人【N】',
                      `pm_edit_time` int(11) NOT NULL DEFAULT '0' COMMENT '项目负责人修改状态时间【N】',
                      `pm_reason_info` text   COMMENT '项目负责人的批注【N】',
                      `doc_id` int(11) NOT NULL DEFAULT '0' COMMENT '医师编号【N】',
                      `unit_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属卫生机构ID【N】',
                      `is_delete` tinyint(2) NOT NULL DEFAULT '1' COMMENT '是否删除：1：正常 2：删除【N】',
                      `is_send_msg` tinyint(2) NOT NULL DEFAULT '1' COMMENT '邀请信息是否推送：1：Y 2：N【N】',
                      `dr_from_where` tinyint(2) NOT NULL DEFAULT '1' COMMENT '进入正式问卷的来源：1：PC 2：APP 3、短信 4、EMAIL【N】',
                      `last_send_time` int(11) NOT NULL DEFAULT '0' COMMENT '上一次发送时间【N】',
                      `send_msg_num` tinyint(2) NOT NULL DEFAULT '0' COMMENT '发送短信次数【N】',
                      `sms_cl_plan_id` int(11) NOT NULL DEFAULT '0' COMMENT '计划id',
                      `sms_cl_finished_send_time` int(11) NOT NULL DEFAULT '0' COMMENT '明细表提交给发送机的时间',
                      `sms_pg_st` tinyint(1) NOT NULL DEFAULT '0' COMMENT '明细表提交给发送机的状态',
                      `sms_cl_send_times` int(11) NOT NULL DEFAULT '0' COMMENT '发送机推给创蓝的频次',
                      `sms_cl_plan_send_time` int(11) NOT NULL DEFAULT '0' COMMENT '计划发送机提交创蓝的时间（创蓝）',
                      `sms_cl_send_time` int(11) NOT NULL DEFAULT '0' COMMENT '发送机推给创蓝的时间',
                      `ip` bigint(20) NOT NULL DEFAULT '0' COMMENT 'ip地址【N】',
                      `ip_addr` varchar(100) NOT NULL DEFAULT '0' COMMENT 'ip物理地址【N】',
                      `bk_ip` bigint(20) NOT NULL DEFAULT '0' COMMENT '返回ip地址【N】',
                      `bk_ip_addr` varchar(100) NOT NULL DEFAULT '0' COMMENT '返回ip物理地址【N】',
                      `quta_survey_answer` text   COMMENT '甄别问卷答案【N】',
                      `clent_confirm_status` TINYINT(1) not null default'0' COMMENT'数据有效确认状态：0未确认 1已确认',
                      `point_status` TINYINT(1) not null default'0' COMMENT'积分处理状态：0 未处理  1 已处理	',
                      `confirm_point_status` TINYINT(1) not null default'1' COMMENT'项目确认积分、礼品是否通过 1：未通过 2已通过 3数据确认中',
                      `reason` VARCHAR(200) not null default'' COMMENT'当积分审核失败时，把失败原因记录下来',
                      `payment_account` varchar(200) NOT NULL DEFAULT '' COMMENT '支付宝收款账号/打款手机号/微信二维码',
                      `payment_name` varchar(255) DEFAULT '' COMMENT '收款人',
                      `payment_type` int(11) NOT NULL DEFAULT 0 COMMENT '支付方式',
                      `finish_status` varchar(10) NOT NULL DEFAULT '' COMMENT '受访者回答正式问卷的完成状态c、有效完成 q、配额已满 s、被甄别 b、坏数据，QC未过【N】【Y】',
                      `get_point` int(10) NOT NULL DEFAULT '0' COMMENT '项目积分【N】【Y】',
                      `pay_point` int(10) NOT NULL DEFAULT '0' COMMENT '支出积分【N】【Y】',
                      `pay_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '支付状态：1、待支付 2、支付成功 3、支付失败【N】【Y】',
                      `pay_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后支付时间',
                      `real_pay` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际付款的金额【N】【Y】',
                      `apply_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '医生是否申请支付 0：未申请 1：已申请',
                      `apply_time` int(11) NOT NULL DEFAULT 0 COMMENT '医生申请支付时间',
                      `apply_perpay` tinyint(1) NOT NULL DEFAULT'0' COMMENT '选中申请预支付',
                      `export_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '配额未满，缺少导出数据标识：1：已导出，0：未导出',
                      `join_wx` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加入微信 1:加入微信',
                      `my` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1、集团进入【N】',
                      PRIMARY KEY (`id`),
                      UNIQUE KEY `mobile` (`mobile`),
                      KEY `survey_uid` (`survey_uid`),
                      KEY `uid` (`uid`),
                      KEY `partner_uid` (`partner_uid`),
                      KEY `screening_status` (`screening_status`),
                      KEY `member_uid` (`member_uid`),
                      KEY `quta_id` (`quta_id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='具体项目执行表（添加项目时自动建立，规则：project_implement_项目总表ID）'";
        return $app_project_implement;
    }

    ####    即时支付流程  ####
    //登录
    public function login() {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            $scene = $post_data['scene'];
            $verify_mobile = $post_data['verify_mobile'];
            //验证数据有效性
            $verify_code = $post_data['verify_code'];
            try {
                ####    验证账号有效性
                $order_info = $this->check_fail_order_scene($scene);
                ####    验证账号有效性

                if (!check_mobile($order_info['mobile'])) {
                    throw new Exception("手机号有误，请确认后重新输入！[订单]");
                }
                if ($order_info['mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_EXCHANGE);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                    throw new Exception($lang_ary[LABEL_EXCHANGE_VERIFICATION_CODE_ERROR]);
                }
                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                //存session，方便从短信或者问卷链接进入时，直接跳过验证
                $this->session->set_userdata(["modify_payment_order_id" => $order_info['id']]);
                $redirect_url = "/project_exchange_n/modify_payment/?scene={$scene}";
                _back_msg("success", "验证通过", $redirect_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $sys_id = (int)trim($this->uri->segment(3));
            $sys_id = $sys_id > 0 ? $sys_id : 1;
//            $this->session->unset_userdata('project_sys_id');
            if (in_array($sys_id, [7,8,9])) {//是网医
                $this->session->set_userdata(["project_sys_id" => $sys_id]);
            }
            $scene = trim($this->input->get("scene", true));
            ####    验证账号有效性
            $order_info = $this->check_fail_order_scene($scene, false);
            ####    验证账号有效性
            file_put_contents('./tmp/fail_order_login.txt', $scene."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);

            //通过pid及uid查询是否已经做过提现操作
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
//            if ($order_info['status'] !=3 || $order_info['order_status'] != 1) {
//                redirect($redirect_url);
//            }
            if ($order_info['order_status'] != 1) {
                redirect($redirect_url);
            }

            if ($order_info['status'] != 3 && $order_info['status'] != 6) {//不是支付失败或者拆单支付中
                redirect($redirect_url);
            }

            if ($order_info['status'] == 2) {//跳到完成页
                $this->survey_model->get_redirect_info("/bk/rs", "p_s");
            }

            if (isset($this->session->userdata["modify_payment_order_id"]) && $this->session->userdata["modify_payment_order_id"] == $order_info['id']) {
                redirect("/project_exchange_n/modify_payment/?scene={$scene}");
            }

            $data = array(
                "scene" => $scene,
            );
            $this->load->view("/project_exchange/login",$data);
        }
    }

    //获取短信验证码
    public function pro_send_sms()
    {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $scene = $post_data['scene'];
            $verify_mobile = $post_data['verify_mobile'];
            try {
                $country = 68;

                ####    验证账号有效性
                $order_info = $this->check_fail_order_scene($scene);
                ####    验证账号有效性
                if ($order_info['mobile'] != $verify_mobile) {
                    throw new Exception("您输入的手机号码有误,请重新输入！");
                }
                //发送短信验证码
                $vcode = rand(10000,99999);
                $sms_log_code = SMS_LOG_CODE_FAIL_ORDER;
                $content = $lang_ary[LABEL_COMM_MOB_TEMPLATE];//国际短信通道使用
                $err_msg = "";
                if ($country == '68') {//中国短信，使用send_cloud发送
                    //创蓝
                    if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") {//本地
                        $st = true;
                    } else {
                        $sms_content = "上医说验证码:{$vcode}，".SMS_ACTIVE_TIME."分钟内有效，请勿泄漏！";//国际短信通道使用
                        $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                    }
                    if ($st === true) {
                        $send_st = true;
                    } else {
                        $send_st = false;
                        $err_msg = $st;
                    }
                }else{//国外短信，使用国际短信通道
                    $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                    $area = getDataByID("app_sys_dictionary", $country);
                    $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                    if ($st == "OK") {
                        $send_st = true;
                    }else{
                        $send_st = false;
                    }
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_EXCHANGE;
                if($send_st) {//短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);
                    if($insert_id_sms_code > 0) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    //修改支付方式
    function modify_payment()
    {
//        $this->session->set_userdata(["modify_payment_order_id" => 405858]);
        //禁止页面缓存
//        header("Cache-control:no-cache,no-store,must-revalidate");
//        header("Pragma:no-cache");
//        header("Expires:0");
        $sys_id = (int)trim($this->uri->segment(3));
        $sys_id = $sys_id > 0 ? $sys_id : 1;
//        $this->session->unset_userdata('project_sys_id');
        if (in_array($sys_id, [7,8,9])) {//是网医
            $this->session->set_userdata(["project_sys_id" => $sys_id]);
        }

        //调取支付失败的订单
        $scene = trim($this->input->get("scene", true));
//        file_put_contents("./tmp/web_project_exchange_modify_start.txt", $scene."**". date("Y-m-d H:i:s") . PHP_EOL . PHP_EOL, FILE_APPEND | LOCK_EX);
        ####    验证账号有效性
        $order_info = $this->check_fail_order_scene($scene, false);
        $order_id = $order_info['id'];
        $member_uid = $order_info['uid'];

        ####    AMY 2023-05-31 查询订单是否有支付成功的记录
        if ($order_info['payment_batch']) {
            $info_detail = $this->db->query("SELECT count(*) as num FROM app_payment_order_detail_new WHERE payment_order_id=? AND payment_batch=? AND status=2", [$order_id, $order_info['payment_batch']])->row_array();
            if ($info_detail && $info_detail['num'] > 0) {//从失败流程中进入，存在成功记录
                if ($info_detail['num'] == $order_info['payment_num']) {
                    $status = 2;
                } else {
                    $status = 7;
                }
                $this->db->update("app_payment_order_new", [
                    "success_num" => $info_detail['num'],
                    "status" => $status,
                ], ["id" => $order_id]);

                $update_flow_data = array(
                    "pay_status" => $status,
                    "pay_time" => time(),
                );
                //查询流水表是否存在【非项目无流水信息】
                $flow_info = $this->db->query("SELECT * FROM app_project_flow WHERE order_id=? LIMIT 1", [$order_id])->row_array();
                if ($flow_info) {
                    //更新流水表
                    $this->db->where(['order_id' => $order_id])->update('app_project_flow', $update_flow_data);
                }
                //支付完成
                $this->survey_model->get_redirect_info("/bk/rs", "p_s");
            }
            ####    AMY 2023-05-31 查询订单是否有支付成功的记录
        }
        ####    验证账号有效性

        $modify_payment_order_id = $this->session->userdata("modify_payment_order_id");
        if (!$modify_payment_order_id || $modify_payment_order_id != $order_id) {
            file_put_contents('./tmp/modify_payment.txt', $order_id . "_" .  date("Y-m-d H:i:s") . "\n", FILE_APPEND | LOCK_EX);
            redirect("/project_exchange_n/login/{$sys_id}?scene={$scene}");
        }

        if ($order_info['status'] == 2) {//支付完成
            $this->survey_model->get_redirect_info("/bk/rs", "p_s");
        }

        if ($order_info['status'] == 6) {//支付中，直接跳转到支付流程
            //跳到即时支付流程
            $this->session->set_userdata(["auto_payment_order_id" => $order_id]);
            $scene = "order_".$order_id;
            $scene = $order_id."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
//            redirect("/project_exchange/auto_payment?scene={$scene}");
            redirect("/project_exchange_n/auto_payment?scene={$scene}");
        }
        // 支付方式
        $sys_dictionary_phone = getDataByCondition('app_sys_dictionary', "and big_class_id =11");
        $res_dictionary = [];
        foreach ($sys_dictionary_phone as $v_dic) {
            $res_dictionary[$v_dic['id']] = $v_dic['val'];
        }
        $user_payment_info = $member_info = [];
        $wx_name = $alipay_name = $alipay_account = "";
        if ($member_uid > 0) {
            //查询用户的支付账号
            $payment_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? ORDER BY id DESC", [$member_uid])->result_array();
            if ($payment_info) {
                foreach ($payment_info as $v_payment) {
                    if (!isset($user_payment_info[$v_payment['payment_type']])) {
                        $user_payment_info[$v_payment['payment_type']] = $v_payment;
                    }
                }
            }

            ####    AMY 2022-07-12 内部会员时，不能修改个人姓名，只能提交给自己
            //查询会员信息
            $member_info = $this->db->query("SELECT * FROM app_member WHERE id=?", [$member_uid])->row_array();
            $wx_name = $alipay_name = $member_info['name'];
            $alipay_account = isset($user_payment_info[EXCHANGE_ALIPAY]) ? $user_payment_info[EXCHANGE_ALIPAY]['payment_account'] : "";
            ####    AMY 2022-07-12 内部会员时，不能修改个人姓名，只能提交给自己
        }
        ####    AMY 2022-07-12 内部会员时，不能修改个人姓名，只能提交给自己
        $wx_name = $wx_name ? $wx_name : (isset($user_payment_info[EXCHANGE_WEBCHAT_AUTO]) ? $user_payment_info[EXCHANGE_WEBCHAT_AUTO]['payment_name'] : "");
        $alipay_name = $alipay_name ? $alipay_name : (isset($user_payment_info[EXCHANGE_ALIPAY]) ? $user_payment_info[EXCHANGE_ALIPAY]['payment_name'] : "");
        ####    AMY 2022-07-12 内部会员时，不能修改个人姓名，只能提交给自己

        //订单编号
        $pid_info = payment_pid_format($order_info);
        //微信提现二维码
        $file_name = $this->wx_payment_qrcode($order_info['id']);
        //获取当前选择的支付方式
        $lock_account = $order_info['payment_type'];//默认支付失败的账号

        $data = array(
            "scene" => $scene,
            "order_info" => $order_info,
            "res_dictionary" => $res_dictionary,
            "pid_info" => $pid_info,
            "lock_account" => $lock_account,
            "user_payment_info" => $user_payment_info,
            "file_name" => $file_name ? file_get_contents("./uploads/wechat/project_exchange/{$file_name}") : "",
            "member_uid" => $member_uid,
            "wx_name" => $wx_name,
            "alipay_name" => $alipay_name,
            "alipay_account" => $alipay_account,
        );
        $this->load->view("/project_exchange/modify_payment", $data);
    }

    //支付方式变更提交
    function modify_payment_sub()
    {
        $data_post = $this->input->post();
        $data_post = format_data($data_post);
        if (!$data_post) {
            _back_msg("error", "参数错误！");
        }
        $privacy = (int)$data_post['privacy'];//隐私协议
        $payment_type = $data_post['payment_type'];
        $scene = $data_post['scene'];
        ####    验证账号有效性
        $payment_order = $this->check_fail_order_scene($scene);
        $pid = $payment_order['pid'];
        $order_id = $payment_order['id'];
        $member_uid = $payment_order['uid'];
        ####    验证账号有效性
        //加锁处理
        if (Redis_lock::getInstance()->lockNoWait("payment_sub_{$order_id}", 120) !== true) {
            //错误提示
            _back_msg("error", "请不要重复提交！");
        }
        if ($privacy !== 1) {
            _back_msg("error", "请阅读并同意《个税代缴代付协议》！");
        }

        //支付方式默认标识
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=? LIMIT 1", [$order_id])->row_array();
        if (!$order_info) {
            _back_msg("error", "订单有误!");
        }
        $order_new_info = $order_info;
        // 支付方式
        if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO, EXCHANGE_BANK])) {
            file_put_contents('./tmp/payment_sub.txt', $scene."：请选择支付方式！".PHP_EOL,FILE_APPEND | LOCK_EX);
            _back_msg("error", "请选择支付方式");
        }

        $payment_name = $payment_account = $bank_type = $opening_bank = "";
        if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信支付
            $payment_name = $data_post['wx_payment_name'];
        } else if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
            $payment_name = $data_post['alipay_payment_name'];
            $payment_account = $data_post['alipay_payment_account'];
        } else if ($payment_type == EXCHANGE_BANK) {//银行卡
            $payment_name = $data_post['bank_payment_name'];
            $payment_account = $data_post['bank_payment_account'];
            $bank_type = $data_post['bank_type'];
            $opening_bank = $data_post['bank_opening_bank'];
        }
        $payment_type = $payment_type ? $payment_type : "";
        $payment_name = $payment_name ? $payment_name : "";
        $payment_account = $payment_account ? $payment_account : "";
        $bank_type = $bank_type ? $bank_type : "";
        $opening_bank = $opening_bank ? $opening_bank : "";


        if ($payment_order['order_status'] != 1) {//不是有效订单
            _back_msg("error", "订单无效，请联系管理员");
        }

        if ($payment_order['status'] != 3 && $payment_order['status'] != 5 && $payment_order['status'] != 6) {//不是支付失败记录或者支付中的记录
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            _back_msg("error", "订单信息不存在", $redirect_url);
        }

        $local_data = [
            "payment_type" => $payment_order['payment_type'],
            "payment_name" => $payment_order['payment_name'],
            "payment_account" => $payment_order['payment_account'],
            "payment_from" => $payment_order['payment_from'],
            "is_privacy" => $payment_order['is_privacy'],
        ];

        // 支付方式
        if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
            _back_msg("error", "请选择支付方式");
        }

        if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
            if (!$payment_name || !$payment_account) {
                _back_msg("error", "请输入【真实姓名】、【支付宝账号】");
            }
        }
        $payment_from = 1;//默认是上医说小程序支付
        if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信
            $account_info = $this->db->query("SELECT * FROM app_payment_fail_order WHERE order_id=? LIMIT 1", [$order_id])->row_array();
            //查询微信唯一码是否存在
            if (!$account_info) {
                _back_msg("error", "您选择转账到微信零钱，请扫码授权再提交");
            }
            if (!$payment_name) {
                _back_msg("error", "请输入【微信实名姓名】");
            }
            $payment_account = $account_info['payment_account'];//微信扫码账号从扫码记录中获取
            $payment_from = $account_info['payment_from'];//主要用于微信支付标记（openid来源于哪个小程序）：1、上医说小程序支付 2、网医小程序支付
        }

        try {
            //事务开始
            $this->db->trans_start();
            //先删除存在的订单失败信息，再提交提现信息【可能是微信扫码产生的记录】
            $delete_fail_order = $this->db->query("DELETE FROM app_payment_fail_order WHERE order_id=? LIMIT 1", [$order_id]);
            if (!$delete_fail_order) {
                throw new Exception("提交失败，请重新操作[重置]！");
            }
            $insert_data = [
                "order_id" => $order_id,
                "pid" => $pid,
                "uid" => $member_uid,
                "payment_type" => $payment_type,
                "payment_name" => $payment_name,
                "payment_account" => $payment_account,
                "add_time" => time(),
                "local_data" => json_encode($local_data, JSON_UNESCAPED_UNICODE),
                "payment_from" => $payment_from,
            ];
            $insert_fail_order_log = $this->db->insert("app_payment_fail_order_log", $insert_data);
            if (!$insert_fail_order_log) {
                throw new Exception("提交失败，请重新操作[log]！");
            }

            ####    AMY 2023-05-23 支付笔数计算

            //重新生成批次号
            $payment_batch = $this->get_payment_batch($payment_type);
            if (!$payment_batch) {throw new Exception("[pc]兑换失败，请重试！");}

//            #### AMY 2023-06-30，微信批次号不变，支付宝批次号需要变更
//            if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
//                //重新生成批次号
//                $payment_batch = $this->get_payment_batch($payment_type);
//                if (!$payment_batch) {throw new Exception("[pc]兑换失败，请重试！");}
//            } else {
//                $payment_batch = $order_info['payment_batch'];
//            }
//            #### AMY 2023-06-30，微信批次号不变，支付宝批次号需要变更


            //拆单列表
            $exchange_amount = $order_info['exchange_amount'];
            $payment_loop = $this->get_payment_loop($payment_type, $exchange_amount);
            if (!$payment_loop) {
                throw new Exception("[拆分]兑换失败，请重试！");
            } else {
                $payment_num = $payment_loop['payment_num'];//支付笔数
            }
            ####    AMY 2023-05-23 支付笔数计算

            //更新支付账号到订单表中
            $update_data = [
                "payment_type" => $payment_type,
                "payment_name" => $payment_name,
                "payment_account" => $payment_account,
                "payment_from" => $payment_from,
                "status" => 6,//订单支付中状态
                "is_privacy" => $privacy,//授权同意申报个税
                "payment_batch" => $payment_batch,//兑换批次号
                "payment_num" => $payment_num,//支付笔数
            ];
            $res_update_order = $this->db->update("app_payment_order_new", $update_data, ["id" => $order_id]);
            if (!$res_update_order) {
                throw new Exception("提交失败，请重新操作[订单]！");
            }

            //拆分明细
            $order_new_info['payment_type'] = $payment_type;
            $order_new_info['payment_name'] = $payment_name;
            $order_new_info['payment_account'] = $payment_account;
            $order_new_info['payment_from'] = $payment_from;
            $order_new_info['payment_batch'] = $payment_batch;
            $order_new_info['payment_order_id'] = $order_new_info['id'];
            $res_order_split = $this->order_split($order_new_info, $payment_loop);
            if (!$res_order_split) {
                throw new Exception("[明细]兑换失败，请重试2！");
            }
            #### AMY 2023-06-30，微信批次号不变，所以更新支付状态即可
//            if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
//                $res_order_split = $this->order_split($order_new_info, $payment_loop);
//                if (!$res_order_split) {
//                    throw new Exception("[明细]兑换失败，请重试2！");
//                }
//            } else {
//                $res_order_split = $this->db->query("UPDATE app_payment_order_new_split SET status=1 WHERE payment_order_id=? AND payment_batch=?", [$order_new_info['id'], $payment_batch]);
//                if (!$res_order_split) {
//                    throw new Exception("[明细]兑换失败，请重试2！");
//                }
//            }
            #### AMY 2023-06-30，微信批次号不变，所以更新支付状态即可


            #### AMY 2020-12-21 检查是否是项目支付,新的支付方式同步到流水表中
            //订单来源是流水表的记录时，需要同步信息到流水表中
            if ($payment_order['log_code'] == ORDER_SOURCE_STATUS_PROJECT && $payment_order['param'] > 0) {
                $flow_id = $payment_order['param'];
                //通过流水编号查询流水信息
                $flow_info = $this->db->query("SELECT * FROM app_project_flow WHERE id=?", [$flow_id])->row_array();
                if (!$flow_info) {
                    throw new Exception("提交失败，请联系管理员处理[流水]！");
                }
                //流水表里的记录是失败的并且状态为正常的记录才能变更
                if ($flow_info['pay_status'] == FLOW_PAY_STATUS_FAIL && $flow_info['status'] == PROJECT_FLOW_STATUS_NORMAL) {
                    $old_flow_data = [
                        "pid" => $flow_info['pid'],
                        "uid" => $flow_info['member_uid'],
                        "payment_type" => $flow_info['payment_type'],
                        "payment_name" => $flow_info['payment_name'],
                        "payment_account" => $flow_info['payment_account'],
                        "pay_status" => $flow_info['pay_status'],
                    ];
                    $update_flow_data = [
                        "payment_type" => $payment_type,
                        "payment_name" => $payment_name,
                        "payment_account" => $payment_account,
                        "pay_status" => 1,//流水改成待支付状态
                    ];
                    $insert_flow_log_data = [
                        "flow_id" => $flow_id,
                        "pid" => $pid,
                        "uid" => $member_uid,
                        "payment_type" => $payment_type,
                        "payment_name" => $payment_name,
                        "payment_account" => $payment_account,
                        "add_time" => time(),
                        "local_data" => json_encode($old_flow_data, JSON_UNESCAPED_UNICODE),
                    ];
                    $insert_fail_flow_log = $this->db->insert("app_payment_fail_flow_log", $insert_flow_log_data);
                    if (!$insert_fail_flow_log) {
                        throw new Exception("提交失败，请重新操作[流水log]！");
                    }
                    $res_update_flow = $this->db->update("app_project_flow", $update_flow_data, ["id" => $flow_id]);
                    if (!$res_update_flow) {
                        throw new Exception("提交失败，请重新操作[流水]！");
                    }
                }
            }
            #### AMY 2020-12-21 检查是否是项目支付,新的支付方式同步到流水表中

            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                throw new Exception("提交失败，请重新操作[操作]！");
            }
            $first_name = $payment_name ? mb_substr($payment_name , 0 , 1) : "";
            $redirect_to_rs = $this->survey_model->get_redirect_info("/bk/rs", "fail_order_suc", "", "", "doctor_name={$first_name}老师", false);
            if (file_exists("./uploads/wechat/project_exchange/order_{$scene}.png")) {
                unlink("./uploads/wechat/project_exchange/order_{$scene}.png");
            }

            ##  AMY 2020-12-09 默认支付账号设置
            $this->payment_account_setting($member_uid, $update_data, $order_id);
            ##  AMY 2020-12-09 默认支付账号设置

            //删除session
            $this->session->unset_userdata("modify_payment_order_id");

            //跳到即时支付流程
            $this->session->set_userdata(["auto_payment_order_id" => $order_id]);
            $scene = "order_".$order_id;
            $scene = $order_id."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
//            _back_msg("success_to_pay", "提交成功！", "/project_exchange_n/auto_payment?scene={$scene}");
            //AMY 2023-07-03 测试微信支付提交
            _back_msg("success_to_pay", "提交成功！", "/project_exchange_n_test/auto_payment?scene={$scene}");

//            _back_msg("success", "操作成功！", $redirect_to_rs);
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }

    //检测微信扫码结果
    function check_order_info()
    {
        $scene = $this->input->post("scene", true);
        ####    验证账号有效性
        $order_info = $this->check_fail_order_scene($scene);
        ####    验证账号有效性
        $order_id = $order_info['id'];
        //查询微信唯一码是否存在
        $account_info = $this->db->query("SELECT * FROM app_payment_fail_order WHERE order_id=? AND payment_type=? LIMIT 1", [$order_id, EXCHANGE_WEBCHAT_AUTO])->row_array();
        if ($account_info) {
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }

    //自动支付流程
    public function auto_payment()
    {
        $scene = $this->input->get('scene', true);
        $scene_info = $this->check_fail_order_scene($scene, false);
        // 加锁处理
        if(Redis_lock::getInstance()->lockNoWait("system_wx_to_pay_".$scene) !== true ) {
            $this->survey_model->get_redirect_info("/bk/rs", "p_d");
        }
        $order_id = $this->session->userdata("auto_payment_order_id");
        if (!$order_id) {//操作有误，跳到登录页，重新登录操作
            redirect("/project_exchange_n/login?scene={$scene}");
        }
        $pid = (int)$scene_info['pid'];
        $pid_id = (int)$scene_info['pid_id'];
        //根据订单号查询pid与pid_id是否一致
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=? AND order_status=1", [$order_id])->row_array();
        if (!$order_info) {
            $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
        }

        $payment_batch = $order_info['payment_batch'];
        $exchange_amount = $order_info['exchange_amount'];//会员兑换金额
        $exchange_amount_divide = $exchange_amount * 100;//转换成分，1元=10角，1角=10分
        if (!$payment_batch) {
            $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
        }
        if ((int)$order_info['pid'] !== $pid || (int)$order_info['pid_id'] !== $pid_id) {
            $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
        }
        if (!in_array($order_info['payment_type'], [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
            if ($order_info['payment_type'] == EXCHANGE_BANK) {
                $this->survey_model->get_redirect_info("/bk/rs", "p_s_b");
            } else {
                $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
            }
        }

        //查询是否已经支付
        if ($order_info['status'] == 2) {//已完成支付
            $this->survey_model->get_redirect_info("/bk/rs", "p_s");
        }
        if ($order_info['status'] == 3) {//处理失败，跳到修改支付页面
            $this->session->set_userdata(["modify_payment_order_id" => $order_id]);
            redirect("/project_exchange_n/modify_payment?scene={$scene}");
        }
        if ($order_info['status'] == 5) {//处理中，执行转账操作
            $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
        }

        ####    AMY 2023-05-23 兑换明细记录
        if ($order_info['status'] == 6) {//6、拆分支付处理中
            $split_order_info = $this->db->query("SELECT * FROM app_payment_order_new_split WHERE payment_order_id=? AND payment_batch=?", [$order_id, $payment_batch])->result_array();
            if (!$split_order_info) {
                $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
            }
            $total_exchange_amount = 0;
            $split_order_data = [];//需要支付的记录信息
            foreach ($split_order_info as $v_check) {
                $total_exchange_amount += $v_check['exchange_amount'];
                if ($v_check['status'] == 1) {//准备中
                    $split_order_data[] = $v_check;
                }
            }
            if ($total_exchange_amount != $order_info['exchange_amount']) {
                $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
            }
            if (!$split_order_data) {
                $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
            }

//            foreach ($split_order_info as $v) {
//                $res_pay = false;
//                $this->db->update("app_payment_order_new_split", ["status" => 4], ["id" => $v['id']]);//处理中
//                if ($v['payment_type'] == EXCHANGE_ALIPAY) {//支付宝支付流程
//                    $res_pay = $this->alipay_payment_money($scene, $v);
//                }
//                if ($v['payment_type'] == EXCHANGE_WEBCHAT_AUTO) {//微信支付流程
//                    $res_pay = $this->wx_to_pay($scene, $v);
//                }
//                 if ($res_pay) {//支付成功
//                     $this->db->query("UPDATE app_payment_order_new SET success_num=success_num+1 WHERE id=?", [$order_id]);
//                 }
//            }
//            $order_new = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=?", [$order_id])->row_array();
//            if (($order_new['payment_num'] > 0 && $order_new['payment_num'] == $order_new['success_num']) || $order_new['success_num'] == 0) {//全部成功或全部失败流程
//                $failure_cause = "";
//                if ($order_new['payment_num'] > 0 && $order_new['payment_num'] == $order_new['success_num']) {
//                    $status = 2;
//                    $p_msg = "p_s";
//                } else if($order_new['success_num'] == 0) {
//                    //查询拆分明细表最新一条失败记录，填充至住兑换表中
//                    $order_split = $this->db->query("SELECT * FROM app_payment_order_new_split WHERE payment_order_id=? ORDER BY id DESC LIMIT 1", [$order_id])->row_array();
//                    $failure_cause = $order_split ? $order_split['error_msg'] : "";
//                    $status = 3;
//                    $p_msg = "p_f";
//                }
//                try {
//                    //事务开始
//                    $this->db->trans_start();
//                    //SQL数据处理
//                    //更新流水表
//                    $update_flow_data = array(
//                        "pay_status" => $status,
//                        "pay_time" => time(),
//                        'error_reason' => $failure_cause ? $failure_cause : "",
//                    );
//                    //查询流水表是否存在【非项目无流水信息】
//                    $flow_info = $this->db->query("SELECT * FROM app_project_flow WHERE order_id=? LIMIT 1", [$order_id])->row_array();
//                    if ($flow_info) {
//                        //更新流水表
//                        $res_flow = $this->db->where(['order_id' => $order_id])->update('app_project_flow', $update_flow_data);
//                        if (!$res_flow) {
//                            throw new Exception("流水表更新失败！");
//                        }
//                    }
//                    //更新订单表
//                    $update_order_data = [
//                        "status" => $status,
//                        'finance_id' => "",
//                        'finance_time' => time(),
//                        'failure_cause' => $failure_cause ? $failure_cause : "",
//                    ];
//                    $res_order = $this->db->where(['id' => $order_id])->update('app_payment_order_new', $update_order_data);
//                    if (!$res_order) {
//                        throw new Exception("更新失败1！".$this->db->last_query());
//                    }
//                    //事务结束
//                    $this->db->trans_complete();
//                    if ($this->db->trans_status() === FALSE)
//                    {
//                        throw new Exception("更新失败2！");
//                    }
//                    if ($order_new['success_num'] == 0) {//支付失败
//                        //支付失败，需要传一个指令给到下一步骤去更改订单的指令
//                        $this->session->set_userdata(["modify_payment_order_id" => $order_id]);
//
//                        $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", "p_f");
//                    }
//                    //支付成功
//                    $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", $p_msg);
//                } catch (Exception $e) {
//                    $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
//                }
//            } else {//部分成功，部分失败
//                //更新订单表
//                $update_order_data = [
//                    "status" => 7,//部分成功，部分失败
//                    'finance_id' => "",
//                    'finance_time' => time(),
//                ];
//                $res_order = $this->db->where(['id' => $order_id])->update('app_payment_order_new', $update_order_data);
//                if (!$res_order) {
//                    $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
//                }
//                $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", "p_s_p");
//            }

            ####    AMY 2023-06-30 微信【商户转账到零钱】接口整合
            //新微信支付接口对接
            ####    AMY 2023-07-03 支付前生成【商户明细号】
            //商户订单号
            $payment_qz = payment_out_biz_no($order_info);//前缀
            $payment_qz = $payment_qz ? $payment_qz : "HCPE";//没有返回前缀信息
            ####    AMY 2023-07-03 支付前生成【商户明细号】
            if ($order_info['payment_type'] == EXCHANGE_ALIPAY) {//支付宝
                foreach ($split_order_info as $v) {
                    $res_pay = false;
                    ####    AMY 2023-07-03 支付前生成【商户明细号】
                    //商户订单号
                    $payment_order_id = $v['payment_order_id'];
                    $out_detail_no = "DSA".$payment_qz.$payment_order_id.$v['id'].substr(md5(time().rand(1,99)), 0, 16);
                    $v['out_detail_no'] = $out_detail_no;
                    ####    AMY 2023-07-03 支付前生成【商户明细号】

//                    $this->db->update("app_payment_order_new_split", ["status" => 4], ["id" => $v['id']]);//处理中
                    ####    AMY 2023-07-03 支付前生成【商户明细号】
                    $this->db->update("app_payment_order_new_split", ["status" => 4, "out_detail_no" => $out_detail_no], ["id" => $v['id']]);//处理中
                    if ($v['payment_type'] == EXCHANGE_ALIPAY) {//支付宝支付流程
                        $res_pay = $this->alipay_payment_money($scene, $v);
                    }
//                    if ($v['payment_type'] == EXCHANGE_WEBCHAT_AUTO) {//微信支付流程
//                        $res_pay = $this->wx_to_pay($scene, $v);
//                    }
                    if ($res_pay) {//支付成功
                        $this->db->query("UPDATE app_payment_order_new SET success_num=success_num+1 WHERE id=?", [$order_id]);
                    }
                }
                $order_new = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=?", [$order_id])->row_array();
                if (($order_new['payment_num'] > 0 && $order_new['payment_num'] == $order_new['success_num']) || $order_new['success_num'] == 0) {//全部成功或全部失败流程
                    $failure_cause = "";
                    if ($order_new['payment_num'] > 0 && $order_new['payment_num'] == $order_new['success_num']) {
                        $status = 2;
                        $p_msg = "p_s";
                    } else if($order_new['success_num'] == 0) {
                        //查询拆分明细表最新一条失败记录，填充至住兑换表中
                        $order_split = $this->db->query("SELECT * FROM app_payment_order_new_split WHERE payment_order_id=? ORDER BY id DESC LIMIT 1", [$order_id])->row_array();
                        $failure_cause = $order_split ? $order_split['error_msg'] : "";
                        $status = 3;
                        $p_msg = "p_f";
                    }
                    try {
                        //事务开始
                        $this->db->trans_start();
                        //SQL数据处理
                        //更新流水表
                        $update_flow_data = array(
                            "pay_status" => $status,
                            "pay_time" => time(),
                            'error_reason' => $failure_cause ? $failure_cause : "",
                        );
                        //查询流水表是否存在【非项目无流水信息】
                        $flow_info = $this->db->query("SELECT * FROM app_project_flow WHERE order_id=? LIMIT 1", [$order_id])->row_array();
                        if ($flow_info) {
                            //更新流水表
                            $res_flow = $this->db->where(['order_id' => $order_id])->update('app_project_flow', $update_flow_data);
                            if (!$res_flow) {
                                throw new Exception("流水表更新失败！");
                            }
                        }
                        //更新订单表
                        $update_order_data = [
                            "status" => $status,
                            'finance_id' => "",
                            'finance_time' => time(),
                            'failure_cause' => $failure_cause ? $failure_cause : "",
                        ];
                        $res_order = $this->db->where(['id' => $order_id])->update('app_payment_order_new', $update_order_data);
                        if (!$res_order) {
                            throw new Exception("更新失败1！".$this->db->last_query());
                        }
                        //事务结束
                        $this->db->trans_complete();
                        if ($this->db->trans_status() === FALSE)
                        {
                            throw new Exception("更新失败2！");
                        }
                        if ($order_new['success_num'] == 0) {//支付失败
                            //支付失败，需要传一个指令给到下一步骤去更改订单的指令
                            $this->session->set_userdata(["modify_payment_order_id" => $order_id]);

                            $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", "p_f");
                        }
                        //支付成功
                        $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", $p_msg);
                    } catch (Exception $e) {
                        $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
                    }
                } else {//部分成功，部分失败
                    //更新订单表
                    $update_order_data = [
                        "status" => 7,//部分成功，部分失败
                        'finance_id' => "",
                        'finance_time' => time(),
                    ];
                    $res_order = $this->db->where(['id' => $order_id])->update('app_payment_order_new', $update_order_data);
                    if (!$res_order) {
                        $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
                    }
                    $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", "p_s_p");
                }
            } else if ($order_info['payment_type'] == EXCHANGE_WEBCHAT_AUTO) {//微信支付流程
                $this->load->library("/Third_party/wechatpay/Wx_order");
                $transfer_detail_list = [];
                ####    AMY 2023-07-03 设置【商家明细单号】
                //【批次名称、批次备注、转账备注】
                $alipay_remark = payment_remark($order_info);
                //商户订单号
                $payment_qz = payment_out_biz_no($order_info);//前缀
                $payment_qz = $payment_qz ? $payment_qz : "HCPE";//没有返回前缀信息
                ####    AMY 2023-07-03 设置【商家明细单号】
                foreach ($split_order_info as $v) {
                    ####    AMY 2023-07-03 设置【商家明细单号】
                    $out_detail_no = $this->get_out_detail_no();
//                    $out_detail_no = "DSW".$payment_qz.$order_id.$v['id'].substr(md5(time().rand(1,99)), 0, 16);
                    $this->db->update("app_payment_order_new_split", ["status" => 4, "out_detail_no" => $out_detail_no], ["id" => $v['id']]);//处理中
                    ####    AMY 2023-07-03 设置【商家明细单号】
                    $transfer_detail_list[] = [
                        'out_detail_no' => $out_detail_no,//【商家明细单号】 商户系统内部区分转账批次单下不同转账明细单的唯一标识，要求此参数只能由数字、大小写字母组成
                        'transfer_amount' => $v['exchange_amount'] * 100,//【转账金额】 转账金额单位为“分”
                        'transfer_remark' => $alipay_remark,//【转账备注】 单条转账备注（微信用户会收到该备注），UTF8编码，最多允许32个字符
                        'openid' => $v['payment_account'],//amy 网医小程序openid
                        'user_name' => $this->wx_order->getEncrypt($v['payment_name']),
                    ];
                }
                //批量付款
                $batches_order_data = [
                    "out_batch_no" => $payment_batch,//【商家批次单号】 商户系统内部的商家批次单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
                    "batch_name" => $alipay_remark,//【批次名称】 该笔批量转账的名称
                    "batch_remark" => $alipay_remark,//【批次备注】 转账说明，UTF8编码，最多允许32个字符
                    "total_amount" => $exchange_amount_divide,//【转账总金额】 转账金额单位为“分”
                    "total_num" => count($split_order_info),//【转账总笔数】 一个转账批次单最多发起一千笔转账
                    "transfer_detail_list" => $transfer_detail_list,
                ];
                $res_wx = $this->wx_order->batches($batches_order_data);
                if (isset($res_wx['code'])) {//返回报错信息
                    $status = 3;
                    $p_msg = "p_f";
                    //更新订单表
                    $update_order_data = [
                        "status" => $status,
                        'finance_id' => "",
                        'finance_time' => time(),
                        'failure_cause' => $res_wx['message'] ? $res_wx['message'] : "",
                    ];

                } else {//受理成功
                    $status = 8;
                    $p_msg = "p_s_a";
                    //更新订单表
                    $update_order_data = [
                        "status" => $status,//已受理成功或者已完成，待后续处理
                        'finance_id' => "",
                        'finance_time' => time(),
                    ];
                }
                $res_order = $this->db->update("app_payment_order_new", $update_order_data, ["id" => $order_id]);
                if (!$res_order) {
                    $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
                }
                if ($status == 3) {
                    //支付失败，需要传一个指令给到下一步骤去更改订单的指令
                    $this->session->set_userdata(["modify_payment_order_id" => $order_id]);

                    $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", "p_f");
                } else {
                    $this->survey_model->get_redirect_info("/bk/rs?scene={$scene}", $p_msg);//支付已受理状态
                }
            }
            ####    AMY 2023-06-30 微信【商户转账到零钱】接口整合
        }
        ####    AMY 2023-05-23 兑换明细记录
        $this->survey_model->get_redirect_info("/bk/rs", "payment_err");
    }

    // 保存验证码数据F
    private function set_vcode($data)
    {
        if (!$data) {return false;}
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }

    // 支付宝提现
    private function alipay_payment_money($scene, $payment_order){
        if (!$scene || !$payment_order) {return false;}
        $id = $payment_order['id'];//拆单明细编号
        $payment_order_id = $payment_order['payment_order_id'];//支付表编号
        $member_uid = $payment_order['uid'];
//        //商户订单号
//        $payment_qz = payment_out_biz_no($payment_order);//前缀
//        $payment_qz = $payment_qz ? $payment_qz : "HCPE";//没有返回前缀信息
//        $out_biz_no = "DSA".$payment_qz.$payment_order_id.$id.substr(md5(time().rand(1,99)), 0, 16);
        ####    AMY 2023-07-03 提取记录的订单号
        if ($payment_order['out_detail_no']) {
            $out_biz_no = $payment_order['out_detail_no'];
        } else {
            //商户订单号
            $payment_qz = payment_out_biz_no($payment_order);//前缀
            $payment_qz = $payment_qz ? $payment_qz : "HCPE";//没有返回前缀信息
            $out_biz_no = "DSA".$payment_qz.$payment_order_id.$id.substr(md5(time().rand(1,99)), 0, 16);
        }
        ####    AMY 2023-07-03 提取记录的订单号

        $split_order_table = "app_payment_order_new_split";//拆单支付明细表
        // 订单明细
        $payment_order_detail_data = array(
            "uid" => $member_uid ? $member_uid : "",
            "payment_order_id" => $payment_order_id,
            "split_order_id" => $id,
            "out_biz_no" => $out_biz_no,
            "add_time" => time(),
            "pay_date" => time(),
            "payment_batch" => $payment_order['payment_batch'],
        );
        //查询支付明细最新一条，未处理的id
        $order_detail = $this->db->query("select * from ".SURVEY_TABLE_APP_PAYMENT_ORDER_DETAIL." where payment_order_id ='{$payment_order_id}' AND split_order_id='{$id}' order by id desc limit 1")->row_array();
        if($order_detail['status'] == 2){//支付成功
           return true;
        }
        if($order_detail['status'] == 1){//正在处理中，不能重复操作
            if ($order_detail['back_info']) {
                //查询是否支付成功
                $arr_back_info = json_decode($order_detail['back_info'], true);
                if ($arr_back_info['code'] == 10000) {//支付成功
                    $update_order_data = [
                        "status" => 2,
                        'end_time' => time()
                    ];
                    $update_order_detail_data = [
                        "status" => 2,
                        "order_id" => $arr_back_info['order_id'],
                    ];
                    //更新订单表
                    $this->db->update($split_order_table, $update_order_data, ["id" => $id]);
                    //更新订单明细表
                    $this->db->update("app_payment_order_detail_new", $update_order_detail_data, ["id" => $order_detail['id']]);
                    return true;
                }
                return false;
            }
        }
        // 生成订单明细，返回新增明细id
        $order_detail_id = savePaymentOrderDetailNew($payment_order_detail_data);
        if ($order_detail_id > 0) {
            ####    订单备注信息
            $alipay_remark = payment_remark($payment_order);
            $exchange_amount = number_format($payment_order['exchange_amount'], 2, '.', '');//保留两位小数
            $order_data = [
                "out_biz_no" => $out_biz_no,//必填，商户端的唯一订单号
                "trans_amount" => $exchange_amount,//必填，金额
                "product_code" => "TRANS_ACCOUNT_NO_PWD",//必填，业务产品码，单笔无密转账到支付宝账户
                "payee_info" => [
                    "identity" => $payment_order['payment_account'],//必填，参与方的唯一标识
                    "identity_type" => "ALIPAY_LOGON_ID",//必填，参与方的标识类型,【ALIPAY_USER_ID 支付宝的会员ID】，【ALIPAY_LOGON_ID：支付宝登录号，支持邮箱和手机号格式】
                    "name" => $payment_order['payment_name'],//必填，参与方真实姓名
                ],//必填，收款方信息
                "remark" => $alipay_remark ? $alipay_remark : $out_biz_no,
                "biz_scene" => "DIRECT_TRANSFER",
            ];
//            file_put_contents("./tmp/project_exchange_n.txt", json_encode($order_data, true).PHP_EOL, FILE_APPEND | LOCK_EX);
            $res = alipay_exchange_uni($order_detail_id, $order_data);
            //通过order_detail_id获取返回详情
            $order_detail = $this->db->where(['id' => $order_detail_id])->get(SURVEY_TABLE_APP_PAYMENT_ORDER_DETAIL)->row_array();
            if (!$order_detail['back_info']) {//更新失败，订单不能继续下去
                return false;
            }
            $back_info = json_decode($order_detail['back_info'], true);
            if ($res === "success") {//支付成功
                $payment_no = $back_info['order_id'];
                $update_split_order_data = [
                    "status" => 2,
                    'end_time' => time()
                ];
                $update_order_detail_data = [
                    "status" => 2,
                    "order_id" => $payment_no,
                ];
            } else {//支付失败
                $error_code = $back_info['sub_code'];
                $error_msg = $back_info['sub_msg'];
                // 支付宝支付失败，更新错误信息表
                $this->update_payment_bk_error($order_detail_id, $id, $payment_order['payment_type']);
                $update_split_order_data = [
                    "status" => 3,
                    'end_time' => time(),
                    "error_code" => $error_code ? $error_code : "",
                    "error_msg" => $error_msg ? $error_msg : "",
                ];
                $update_order_detail_data = [
                    "status" => 3,
                    "error_code" => $error_code ? $error_code : "",
                ];
            }

            //更新订单表
            $this->db->update($split_order_table, $update_split_order_data, ["id" => $id]);
            //更新订单明细表
            $this->db->update("app_payment_order_detail_new", $update_order_detail_data, ["id" => $order_detail_id]);
            //更新支付账号统计表
            $this->update_payment_amount_report(EXCHANGE_ALIPAY);
            if ($res === "success") {
                return true;
            } else {
                return false;
            }
        }
    }

    private function wx_to_pay($scene_info, $order_info)
    {
        if (!$scene_info || !$order_info) {
           return false;
        }
        $v_info = $order_info;
        //微信很多错误都要求原订单提交，因此订单号不能变
        //文档说明：https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=14_2
        $pid = $v_info['pid'];
        $member_uid = $v_info['uid'];
        $openid = $v_info['payment_account'];
        $payment_name = $v_info['payment_name'];
        $exchange_amount = $v_info['exchange_amount'];
        $split_order_id = $v_info['id'];
        $order_id = $v_info['payment_order_id'];
        $payment_from = $v_info['payment_from'];//需要哪个渠道支付 1、上医说微信小程序 2、网医微信小程序
        $payment_batch = $v_info['payment_batch'];//批次号

        //106 项目-赛小欧-推荐费
        if($v_info['log_code'] == ORDER_SOURCE_STATUS_PROSXO_REFEREE){
            $scene_sign = "sxo_invite_";
        } else if ($v_info['log_code'] == ORDER_SOURCE_STATUS_PROJECT && $pid == 0) {//剩余积分提现
            $scene_sign = "syjf_";
        } else if ($v_info['log_code'] == ORDER_SOURCE_STATUS_ELSAY) {//医来说
            $scene_sign = "els_";
        } else if ($v_info['log_code'] == ORDER_SOURCE_STATUS_PROSXO) {//赛小欧
            $scene_sign = "sxo_";
        } else if ($v_info['log_code'] == ORDER_SOURCE_STATUS_TO_PAY) {//www.to-pay.cn公共礼金支付平台
            $scene_sign = "tp_";
        } else {//正常项目
            $scene_sign = "project_";
        }
        $scene = $scene_sign.$v_info['id'];
        if (!$v_info['id']) {
            return false;
        }

        //商户订单号
        $payment_qz = payment_out_biz_no($v_info);//前缀
        $payment_qz = $payment_qz ? $payment_qz : "HCPE";//没有返回前缀信息
        $out_biz_no = "DSW".$payment_qz.$order_id.substr(md5(time().rand(1,99)), 0, 16);

        $payment_order_detail_data = array(
            "uid" => $member_uid,
            "payment_order_id" => $order_id,
            "split_order_id" => $split_order_id,
            "out_biz_no" => $out_biz_no,
            "add_time" => time(),
            "pay_date" => time(),
            "payment_batch" => $payment_batch,
        );
        $order_detail_id = savePaymentOrderDetailNew($payment_order_detail_data);
        $flag = false;
        if (!$order_detail_id) {
            return false;
        } else {
            $alipay_remark = payment_remark($v_info);
            //开始提现操作，微信小程序打款
            $order_info = [
                "partner_trade_no" => $out_biz_no,//订单号
                "openid" => $openid,//openid
                "re_user_name" => $payment_name,//用户真实姓名
                "amount" => $exchange_amount,//兑换金额
                "desc" => $alipay_remark ? $alipay_remark : $out_biz_no,//描述信息
                "payment_order_id" => $order_id,//上医说订单编号
                "order_detail_id" => $order_detail_id,//订单明细表
                "split_order_id" => $split_order_id,//订单明细表
                "payment_from" => $payment_from,//需要哪个渠道支付 1、上医说微信小程序 2、网医微信小程序
            ];
            $res = $this->wx_payment($order_info);
            if ($res === true) {//兑换失败
                $flag = true;
                //更新支付账号统计表
                $this->update_payment_amount_report(EXCHANGE_WEBCHAT_AUTO);
            }
        }
        if (!$flag) {
            return false;
        } else {
            return true;
        }
    }

    //微信小程序接口打款
    private function wx_payment($order_info)
    {
        if (!$order_info) {
            return ["msg" => "提现失败，提现参数未提交！"];
        }
        $partner_trade_no = $order_info['partner_trade_no'];
        $openid = $order_info['openid'];
        $re_user_name = $order_info['re_user_name'];
        $amount = $order_info['amount'] * 100;//按分传输
        $desc = $order_info['desc'];//按分传输
        $payment_order_id = $order_info['payment_order_id'];//上医说订单表
        $split_order_id = $order_info['split_order_id'];//拆单明细编号
        $order_detail_id = $order_info['order_detail_id'];//上医说订单明细表
        $payment_from = $order_info['payment_from'];//需要哪个渠道支付 1、上医说微信小程序 2、网医微信小程序
        $payment_from = $payment_from ? $payment_from : 1;//默认是上医说微信小程序支付
        if (!in_array($payment_from, [1,2])) {
            return ["msg" => "提现失败，支付渠道有误！"];
        }
        if ($payment_from == 1) {//上医说小程序
            $config = [
                //上医说兑换接口配置
                // 必要配置
                'app_id'             => DRSAY_WECHAT_APPLET_APP_ID, //小程序对应的appid
                'mch_id'             => WECHAT_JKT_BJ_PAY_MCH_ID, //支付商户的信息
                'key'                => WECHAT_JKT_BJ_PAY_API_KEY,
                'cert_path'          => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_CERT_PATH,
                'key_path'           => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_KEY_PATH,
                // 将上面得到的公钥存放路径填写在这里
                'rsa_public_key_path' => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PUBLIC_PAY_KEY_PATH,
                'notify_url'         => WECHAT_JKT_BJ_PAY_NOTIFY_URL,
            ];
        } else {//网医微信小程序
            $config = [
                //上医说兑换接口配置
                // 必要配置
                'app_id'             => IDR_WECHAT_APPLET_APP_ID, //小程序对应的appid
                'mch_id'             => WECHAT_JKT_BJ_PAY_MCH_ID, //支付商户的信息
                'key'                => WECHAT_JKT_BJ_PAY_API_KEY,
                'cert_path'          => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_CERT_PATH,
                'key_path'           => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_KEY_PATH,
                // 将上面得到的公钥存放路径填写在这里
                'rsa_public_key_path' => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PUBLIC_PAY_KEY_PATH,
                'notify_url'         => WECHAT_JKT_BJ_PAY_NOTIFY_URL,
            ];
        }

        $app = Factory::payment($config);
        $params = [
            'partner_trade_no' => $partner_trade_no, //订单号
            'openid'           => $openid, //小程序的openid
            'check_name'       => 'FORCE_CHECK', //校验真实姓名验  2020-11-26启动实名认证流程
            're_user_name'     => $re_user_name,//真实姓名
            'amount'           => $amount, //分为计量单位
            'desc'             => $desc, // 企业付款操作说明信息。必填
        ];
        $query_result = $app->transfer->queryBalanceOrder($params['partner_trade_no']);
        $res = false;
        $payment_no = $error_msg = $err_code = "";
        //检测订单是否成功
        if ($query_result['return_code'] === 'SUCCESS' && $query_result['result_code'] === 'SUCCESS') {//先检测订单号是否已经支付成功
            $res = true;
            $result = $query_result;
            $payment_no = $query_result['detail_id'];
        } else {
            $result = $app->transfer->toBalance($params);
            //提交订单结果
            if (isset($result['result_code']) && $result['result_code'] === 'SUCCESS') {
                $res = true;
                $payment_no = $result['payment_no'];
            } else {
                $query_result = $app->transfer->queryBalanceOrder($params['partner_trade_no']);
                //检测订单是否成功
                if ($query_result['return_code'] === 'SUCCESS' && $query_result['result_code'] === 'SUCCESS') {
                    $res = true;
                    $payment_no = $query_result['detail_id'];
                } else {
                    $error_msg = $result['err_code_des'];
                    $err_code = $result['err_code'];
                }
            }
        }

        if ($res) {//支付成功
            $update_order_data = [
                "status" => 2,
                'end_time' => time(),
            ];
            $update_order_detail_data = [
                "status" => 2,
                "order_id" => $payment_no,
                "back_info" => $result ? json_encode($result, JSON_UNESCAPED_UNICODE) : "",
            ];
        } else {//支付失败
            //查询支付失败
            $error_type = 1;//默认是付款账户的问题
            if ($err_code) {
                $error_info = $this->db->query("SELECT * FROM app_payment_bk_error WHERE error_code=? AND pay_way=?", [$err_code, EXCHANGE_WEBCHAT_AUTO])->row_array();
                if ($error_info) {//责任方
                    $error_type = $error_info['responsible_party'] == "收款账户" ? 2 : 1;
                }
            }

            $update_order_data = [
                "status" => 3,
                'end_time' => time(),
                "error_code" => $err_code ? $err_code : "",
                "error_msg" => $error_msg ? $error_msg : "",
            ];
            $update_order_detail_data = [
                "status" => 3,
                "error_code" => $err_code ? $err_code : "",
                "back_info" => $result ? json_encode($result, JSON_UNESCAPED_UNICODE) : "",
            ];
        }

        //更新订单表
        $this->db->update("app_payment_order_new_split", $update_order_data, ["id" => $split_order_id]);
        //更新订单明细表
        $this->db->update("app_payment_order_detail_new", $update_order_detail_data, ["id" => $order_detail_id]);
        // 支付宝支付失败，更新错误信息表
        $this->update_payment_bk_error($order_detail_id, $payment_order_id, EXCHANGE_WEBCHAT_AUTO);
        return $res ? true : ['msg' => $error_msg];
    }

    //验证参数有效性
    function check_fail_order_scene($scene, $is_ajax = true)
    {
        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "payment_err", "", "", "", false);
        if (!$scene) {
            if ($is_ajax) {
                _back_msg('error', '参数有误！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $arr_scene = explode("_", $scene);
        $order_id = $arr_scene[0];
        $encrypted_data = $arr_scene[1];
        if (!$order_id || !$encrypted_data) {//参数有误
            if ($is_ajax) {
                _back_msg('error', '参数有误！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $decrypt_scene = "order_{$order_id}";
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=? AND order_status=1", [$order_id])->row_array();
        if (!$order_info || ($order_info && $order_info['order_status'] != 1)) {//不是有效订单
            if ($is_ajax) {
                _back_msg('error', '订单不存在！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }

        return $order_info;
    }

    ####    公共方法
    private function update_payment_amount_report($payment_type){
        if (!$payment_type) {return false;}
        $payment_order = getDataByCondition(SURVEY_TABLE_APP_PAYMENT_ORDER,"and status =2 AND payment_type='{$payment_type}'", "sum(exchange_amount) as exchange_amount", "*", true);
        $this->db->where(['payment_type' => $payment_type])->update('app_payment_note',array('pay_money'=>$payment_order['exchange_amount']));
    }

    private function update_payment_bk_error($order_detail_id, $id, $payment_type){
        if (!$order_detail_id || !$id || !$payment_type) {return false;}
        //查找订单明细
        $order_detail = $this->db->where(['id' => $order_detail_id, 'payment_order_id' => $id])->get(SURVEY_TABLE_APP_PAYMENT_ORDER_DETAIL)->row_array();
        if ($order_detail) {
            $back_info = json_decode($order_detail['back_info'], true);
            if ($payment_type == EXCHANGE_ALIPAY) {
                $error_msg_info = $back_info['sub_msg'];
                $where = array(
                    'error_code' => $back_info['sub_code'],
                    'error_info' => $back_info['sub_msg'],
                    'pay_way' => $payment_type,
                );
            } else if($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信自动打款
                $error_msg_info = $back_info['err_code_des'];
                $where = array(
                    'error_code' => $back_info['err_code'],
                    'error_info' => $back_info['err_code_des'],
                    'pay_way' => $payment_type,
                );
            }else{
                $error_msg_info = $back_info['sub_msg'];
                $where = array(
                    'error_info' => $back_info['sub_msg'],
                    'pay_way' => $payment_type,
                );
            }
            //是否存在在错误信息表中
            $error_info = $this->db->where($where)->get('app_payment_bk_error')->row_array();
            if (empty($error_info)) {
                //新增一条信息
                $add_data = array(
                    'pay_way' => $payment_type,
                    'error_info' => $error_msg_info,
                );
                if ($payment_type == EXCHANGE_ALIPAY) {
                    $add_data['error_code'] = $back_info['sub_code'];
                }
                if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {
                    $add_data['error_code'] = $back_info['err_code'];
                    //默认是付款账户的问题
                    $add_data['responsible_party'] = "付款账户";
                }
                if (in_array($payment_type, [EXCHANGE_WEBCHAT, EXCHANGE_BANK])) {
                    $add_data['responsible_party'] = RESPONSIBLE_PARTY_COLLECTION_ACCOUNT;
                }
                $res = $this->db->insert('app_payment_bk_error',$add_data);
                if ($res) {
                    $error_insert = $this->db->where(['id' => $this->db->insert_id()])->get('app_payment_bk_error')->row_array();
                    return $error_insert;
                }else{
                    return false;
                }
            }else{
                // 若为收款账户信息问题，前台修改个人信息
                return $error_info;
            }
        }
    }

    //提现账号默认设置
    private function payment_account_setting($member_uid, $payment_info, $order_id)
    {
        try {
            if (!$member_uid) {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "1、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            $payment_type = $payment_info['payment_type'] ?? "";
            $payment_name = $payment_info['payment_name'] ?? "";
            $payment_account = $payment_info['payment_account'] ?? "";
            $payment_from = $payment_info['payment_from'] ?? "";//主要用于微信支付标记（openid来源于哪个小程序）：1、上医说小程序支付 2、网医小程序支付
            if (!$payment_type || !in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO]) || !$payment_name || !$payment_account) {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "2、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            //事务开始
            $this->db->trans_start();
            //SQL数据处理
            //检测提现账号是否正确
            $check_account = getDataByConditionCi("app_ex_payment_account", " AND uid=? AND payment_type=? ORDER BY id DESC","*", true, [$member_uid, $payment_type]);
            $local_data = [];
            $new_data = [
                "payment_type" => $payment_type,
                "payment_name" => $payment_name ? $payment_name : "",
                "payment_account" => $payment_account,
                "payment_from" => $payment_from,
            ];
            if (!$check_account) {
                $insert_account_data = [
                    "uid" => $member_uid,
                    "payment_type" => $payment_type,
                    "payment_name" => $payment_name ? $payment_name : "",//支付账号表不存在支付名称时，用明细表的姓名填充
                    "payment_account" => $payment_account,
                    "payment_from" => $payment_from,
                    "add_time" => time(),
                ];
                $res_account = $this->db->insert("app_ex_payment_account", $insert_account_data);
                if (!$res_account) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "3、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }
                $payment_account_id = $this->db->insert_id();
            } else {
                //更新账号
                $res_update_account_info = $this->db->update("app_ex_payment_account", $new_data, ["id" => $check_account['id']]);
                if (!$res_update_account_info) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "4、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }
                $local_data = [
                    "payment_type" => $check_account['payment_type'],
                    "payment_name" => $check_account['payment_name'],
                    "payment_account" => $check_account['payment_account'],
                    "payment_from" => $check_account['payment_from'],
                ];
                $payment_account_id = $check_account['id'];
            }
            $n_data = array_merge($new_data, ["order_id" => $order_id]);
            $insert_account_log = [
                "payment_account_id" => $payment_account_id,
                "uid" => $member_uid,
                "local_data" => $local_data ? json_encode($local_data, JSON_UNESCAPED_UNICODE) : "",
                "new_data" => $new_data ? json_encode($n_data, JSON_UNESCAPED_UNICODE) : "",
                "add_time" => time(),
                "change_type" => ACCOUNT_LOG_PAYMENT_ORDER,
            ];
            $res_account_log = $this->db->insert("app_ex_payment_account_log", $insert_account_log);
            if (!$res_account_log) {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "5、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            //设置默认账号
            if ($payment_account_id) {
                //再把最新的记录变更成默认账号
                $res_update_account = $this->db->update("app_ex_payment_account", ["is_default" => 0], ['uid' => $member_uid]);
                if (!$res_update_account) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "6、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }
                //再把最新的记录变更成默认账号
                $res_update_default_account = $this->db->update("app_ex_payment_account", ["is_default" => 1], ["id" => $payment_account_id]);
                if (!$res_update_default_account) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "7、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }

            }
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "8、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    //微信提现二维码生成【AMY 2022-12-30,使用网医支付流程，先放在支付失败流程里使用】
    private function wx_payment_qrcode($order_id)
    {
        if (!$order_id) {return false;}
        //临时二维码，由于小程序二维码参数最多只能传32位，因此要控制参数长度
        $scene = "order_".$order_id;
        $scene = $scene."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
        //检测订单信息
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=?", [$order_id])->row_array();
        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "payment_err", "", "", "", false);
        //订单不存在/订单无效/不是支付失败的订单，不能处理
        if (!$order_info || ($order_info && $order_info['order_status'] != 1) || ($order_info && $order_info['status'] != 3)) {
            redirect($redirect_url);
        }
//        if ($order_info['uid'] == 2982098) {
//            $config = [
//                //网医微信小程序
//                'app_id' => IDR_WECHAT_APPLET_APP_ID,
//                'secret' => IDR_WECHAT_APPLET_APP_SECRET,
//                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
//                'response_type' => 'array',
//            ];
//            $page = "my/drsayExchange/drsayExchange";
//        } else {
//            $config = [
//                //上医说
//                'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
//                'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
//                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
//                'response_type' => 'array',
//            ];
//            $page = "pages/drsayExchange/drsayExchange";
//        }
        $config = [
            //网医微信小程序
            'app_id' => IDR_WECHAT_APPLET_APP_ID,
            'secret' => IDR_WECHAT_APPLET_APP_SECRET,
            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];
        $page = "my/drsayExchange/drsayExchange";
        $app = Factory::miniProgram($config);
        $response = $app->app_code->getUnlimit($scene, [
            //上医说页面
            'page'  => $page,
            'width' => 50,
        ]);
        // 或
        if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
            if (!is_dir("./uploads/wechat/project_exchange/")) {
                mk_dir("./uploads/wechat/project_exchange/", 0777, true);
            }
            $filename = $response->saveAs("./uploads/wechat/project_exchange/", $scene.'.png');
        } else {
            return false;
        }
        return $filename;
    }

    //商家明细单号生成
    private function get_out_detail_no()
    {
        //批次号
//        $random_bytes = openssl_random_pseudo_bytes(16);
//        $unique_id = bin2hex($random_bytes);
//        $payment_batch = $payment_type.$unique_id;
        $unique_id = md5(uniqid().rand(99999, 999999));
        $out_detail_no = $unique_id;//微信支付要求最大32位，不能超过，因此去掉增加类型的想法
        //查询该批次号是否已经存在，存在继续生成
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new_split WHERE out_detail_no=? LIMIT 1", [$out_detail_no])->row_array();
        if ($order_info) {
            return $this->get_out_detail_no();
        } else {
            return $out_detail_no;
        }
    }
}
