<?php
/**
 * Created by PhpStorm.
 * User: Amy
 * Date: 2018/05/09
 */
// http://www.drsay.cn/fixed_survey?pid=54&uid={uid}
class Fixed_survey extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    //回答问卷这进入甄别问卷
    public function index()
    {
        
        $uid = trim($this->input->get('uid'));
        $pid = trim($this->input->get('pid'));

        if($uid=="" || $pid=="") redirect("/");
        $error_link = "/bk/s/46_3079c167ffedae0c?uid=".$uid;
        // check链接中的会员并获取会员ID
        $member = $this->checkUser($pid, $uid);
        if(empty($member)) redirect($error_link);

        $user_info = $this->getUser($member['member_uid']);
        if(!$user_info) redirect($error_link);
        $data_ins = array(
            'uid' => $member['member_uid'], 
            'pid' => $pid, 
            'name' => $user_info['name'], 
            'department' => $user_info['department'], 
            'department_name' => $user_info['department_name'], 
            'unit_id' => $user_info['unit_id'], 
            'unit_name' => $user_info['unit_name'], 
            'add_time' => time(), 
            'survey_uid' => $uid
        );
        //判断配额
        $quota = $this->checkQuota($user_info['unit_id'], "k");
        if($quota >= 1){
            $data_ins['status'] = "Q";
            $ins = $this->db->insert('survey_newborn',$data_ins);
            redirect("/bk/s/46_50403e549c95cd42?uid=".$uid);
        } 
        //有两条数据表明已经有a和b
        $quota = $this->checkQuota($user_info['unit_id'], "m");
        if($quota >= 2) {
            $data_ins['status'] = "Q";
            $ins = $this->db->insert('survey_newborn',$data_ins);
            redirect("/bk/s/46_50403e549c95cd42?uid=".$uid);
        }

        $data = array(
            'member_uid' => $member['member_uid'],
            'pid' => $pid,
            'uid' => $uid,
            'user_info' => $user_info,

        );
        $this->load->view('/fixed_survey/index', $data);
    }

    public function submit()
    {
        $post = $this->input->post();
        if(empty($post)) _back_msg("error", "提交失败！");
        $member_uid = $post['member_uid'];
        $pid = trim($post['pid']);
        $uid = trim($post['uid']);
        // check链接中的会员并获取会员ID
        $cnt = $this->getCount($member_uid);
        if($cnt >= 1) _back_msg("error", "重复提交！");

        $user_info = $this->getUser($member_uid);
        if(!$user_info) _back_msg("error", "提交失败！");
        // 科室选择
        $s1 = trim($post['office_radio']);
        if(empty($s1)) _back_msg("error", "请选择科室！");
        // 问卷
        $q1 = trim($post['number']);
        $q2_a = trim($post['number_baby']);
        $q2_b = trim($post['premature']);
        // 登录页面数据
        $data = array(
            'uid' => $member_uid, 
            'pid' => $pid, 
            'name' => $user_info['name'], 
            // 'mobile' => $user_info['mobile'], 
            'department' => $user_info['department'], 
            'department_name' => $user_info['department_name'], 
            'unit_id' => $user_info['unit_id'], 
            'unit_name' => $user_info['unit_name'], 
            's1' => $s1,
            'add_time' => time(), 
            'survey_uid' => $uid
        );
        if($s1 == 'a') {
            $data['q1'] = $q1; 
        } elseif ($s1 == 'b') {
            $data['q2_a'] = $q2_a; 
            $data['q2_b'] = $q2_b; 
        } elseif ($s1 == 'c') {
            $data['q1'] = $q1; 
            $data['q2_a'] = $q2_a; 
            $data['q2_b'] = $q2_b; 
        }
        
        //判断配额
        $quota = $this->checkQuota($user_info['unit_id'], $s1);
        if($quota >= 1) {
            $data['status'] = "Q";
            $ins = $this->db->insert('survey_newborn',$data);
            redirect("/bk/s/46_50403e549c95cd42?uid=".$uid);
        }
        //支付金额
        if($s1 == 'a' || $s1 == 'b') {
            $data['b1'] = 20; 
        }elseif($s1 == 'c'){
            $data['b1'] = 30; 
        }
        $data['status'] = "C";

        $ins = $this->db->insert('survey_newborn',$data);
        if($ins){
            _back_msg("success", "提交成功！","/bk/s/46_27c68f245bd96cd5?uid=".$uid);
        } else {
            _back_msg("error", "提交失败！");
        }
    }

    private function getUser($member_uid) {
        $sql = "SELECT M.name,M.mobile,M.unit_id,M.department,
                    (SELECT U.name FROM app_unit U WHERE U.id=M.unit_id) AS unit_name,
                    (SELECT D.department FROM info_department D WHERE D.id=M.department) AS department_name
                FROM app_member M WHERE M.id={$member_uid}";
        $user_info = $this->db->query($sql)->row_array();
        return $user_info;
    }
    private function checkUser($pid, $uid) {
        $res_data = array();
        // 获取项目执行表名称
        $table_name = $this->db->select("p_table")->where("id",$pid)->get("app_project")->row()->p_table;
        if(!$table_name) return "";
        $res_data['table_name'] = trim($table_name);
        // 判断表里存在default_newborn_num字段
        // $column = $this->db->query("Describe ".$res_data['table_name']." default_newborn_num")->result_array();
        // if(empty($column)) return "";

        // 获取会员ID（受访者）
        $member = $this->db->select("member_uid")->where("survey_uid",$uid)->get($table_name)->row();
        if(empty($member)) return "";
        $res_data['member_uid'] = trim($member->member_uid);
        // $res_data['default_newborn_num'] = trim($member->default_newborn_num);

        if(empty($res_data['member_uid'])) return "";
        // 是不是会员
        $user_id = $this->db->select("id")->where("id",$res_data['member_uid'])->get("app_member")->row()->id;
        if(!$user_id) return "";
        // 是否已提交
        $cnt = $this->getCount($res_data['member_uid']);
        if($cnt >= 1) {
            return "";
        }
        return $res_data;
    }
    private function getCount($member_uid) {
        $cnt = $this->db->select("count(*) AS cnt")->where("uid",$member_uid)->from("survey_newborn")->get()->row()->cnt;
        return $cnt;
    }

    private function checkQuota($unit_id, $s1) {
        $sql = "SELECT count(*) AS cnt FROM survey_newborn WHERE unit_id={$unit_id} ";
        if($s1 == 'a'){
            $sql .= " AND (s1= 'c' OR s1= 'a')";
        }else if ($s1 == 'b'){
            $sql .= " AND (s1= 'c' OR s1= 'b')";
        }else if ($s1 =='k'){
            $sql .= " AND s1= 'c'";
        }
        $cnt = $this->db->query($sql)->row()->cnt;
        return $cnt;
    }

}