<?php

/*
 * 入驻妙医佳互联网医院-活动
 * 2020-12-09
 * bryant
 */

class Myj extends MY_Controller {

    function __construct() {
        parent::__construct();
        //活动已结束
        redirect('/');
    }

    /**
     * 项目介绍
     */
    public function intro() {
        $this->load->view("/myj/intro");
    }
    
    /**
     * code 认证
     * @param type $code
     */
    private function check_code($code,$time){
        if(!$code || !$time){
            return false;
        }
        
        //是否在有效时间-10分钟
        if((time()-$time)>600){
            return false;
        }
        
        //检测host
        $origin = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        //字符串是否存在指定地址
        if(strpos($origin,'/myj/invite')===false && strpos($origin,'/myj/sel_city')===false && 
            strpos($origin,'/myj/save_invite')===false && strpos($origin,'/myj/obtain_vercode')===false && 
            strpos($origin,'/myj/check_province_recruit')===false){
            return false;
        }
        
        $code_str = 'myj_'.$time;
        $new_code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
        if($code!==$new_code){
            return false;
        }
        return true;
    }

    private $unit_name = [
        '泰州市'=>['兴化市人民医院','泰州人民医院'],
        '苏州市'=>['苏州大学附属第一医院平江院区'],
        '南通市'=>['江苏省如皋市人民医院','南通市第一人民医院'],
        '南京市'=>['江苏省南京市中国人民解放军第八十一医院','东部战区总医院'],
        '淮安市'=>['淮安市第一人民医院','淮安市第二人民医院'],
        '温州市'=>['平阳县中医院','泰顺县人民医院','温州市中心医院','永嘉县中医医院'],
        '台州市'=>['浙江省台州市立医院'],
        '绍兴市'=>['新昌县人民医院'],
        '金华市'=>['金华市中心医院'],
        '嘉兴市'=>['嘉兴市中医医院','中国人民武装警察部队海警总队医院'],
        '湖州市'=>['浙江省湖州市德清县人民医院','湖州市第一人民医院','浙江中医药大学附属湖州市中医院'],
        '石家庄市'=>['河北省人民医院','河北医科大学第一医院','石家庄市第五医院','河北医科大学第四医院','河北医科大学附属以岭医院'],
        '合肥市'=>['安徽省立儿童医院','安徽医科大学附属巢湖医院'],
        '福州市'=>['福建省妇幼保健院','福建省立医院','福建省肿瘤医院','福州市肺科医院'],
        '深圳市'=>['深圳市人民医院','中国医学科学院肿瘤医院深圳医院'],
        '汕头市'=>['汕头市中心医院','汕头市第二人民医院','汕头大学医学院附属肿瘤医院','汕头大学医学院第二附属医院'],
        '江门市'=>['广东省江门市人民医院','江门市五邑中医院'],
        '哈尔滨市'=>['哈尔滨市第五医院','黑龙江省中医院'],
        '武汉市'=>['湖北省中西医结合医院','武汉市妇女儿童医疗保健中心'],
        '沈阳市'=>['北部战区总医院','中国医科大学附属盛京医院滑翔院区'],
        '大连市'=>['大连医科大学附属第一医院','大连大学附属中山医院','大连大学附属新华医院','大连市第三人民医院'],
        '包头市'=>['包头市中心医院','包头医学院第二附属医院','内蒙古北方重工业集团有限公司医院','内蒙古包钢医院'],
        '青岛市'=>['齐鲁医院青岛分院','平度市人民医院'],
        '上海市'=>['上海市徐汇区中心医院','上海交通大学医学院附属瑞金医院北院','复旦大学附属中山医院','上海第九人民医院黄浦分院'],
    ];
    private $department = ['传染科','放疗科','肝胆外科','肛肠外科','呼吸科','泌尿外科','内分泌科','普内科','普外科',
        '乳腺外科','神经内科','神经外科','肾病科','胃肠外科','消化内科','心内科','心外科','心胸外科','血液科','移植科','中医科',
        '肿瘤科','肿瘤内科','肿瘤外科'];
    /**
     * 邀请信息
     */
    public function invite(){
        $code = $this->input->get('code',true);
        $time = $this->input->get('time',true);
        $check_code = $this->check_code($code, $time);
        if(!$check_code){
            redirect('/myj/intro');
        }
        
        //省份
        $province = $this->db->select('code,name')->where(['level' => 1])->get(TABLE_BASE_AREA)->result_array();
        $province_list = !empty($province) ? array_column($province, 'name', 'code') : [];
        
        $data = ['province'=>$province_list,'unit_name_ary'=> $this->unit_name,'department'=> $this->department];
        $this->load->view("/myj/invite",$data);
    }
    
    public function save_invite(){
        //加密参数认证
        $code = $this->input->post('code',true);
        $time = $this->input->post('time',true);
        $check_code = $this->check_code($code, $time);
        if(!$check_code){
            _back_msg("error", "无效操作，认证参数失败，请刷新！",'ver_code_err');
        }
        $fields = ['province_code','province','city_code','city','unit_name','dr_department','dr_name','dr_mobile','ver_code'];
        $param = $this->input->post($fields,true);
        $data = $this->filter_data($param);
        
        if(empty($data['province_code'])){
            _back_msg("error", "请选择省份！", 'province_code');
        }
        //检测当前省份是否超过招募目标数
        $check_recruit_number = $this->check_recruit_number($data['province']);
        if($check_recruit_number['code']===2){
            _back_msg("error", $check_recruit_number['msg'], 'province_code');
        }
        
        if(empty($data['city_code'])){
            _back_msg("error", "请选择城市！", 'city_code');
        }
        if(empty($data['unit_name'])){
            _back_msg("error", "请选择医院！", 'unit_name');
        }
        $unit_name = isset($this->unit_name[$data['city']])?$this->unit_name[$data['city']]:[];
        if(!in_array($data['unit_name'], $unit_name)){
            _back_msg("error", "非法操作，未选择要求的医院！", 'unit_name');
        }
        if(empty($data['dr_department'])){
            _back_msg("error", "请选择科室！", 'dr_department');
        }
        if(!in_array($data['dr_department'], $this->department)){
            _back_msg("error", "非法操作，未选择要求的科室！", 'dr_department');
        }
        if(empty($data['dr_name'])){
            _back_msg("error", "请输入姓名！", 'dr_name');
        }
        if(!preg_match("/^[\x{4e00}-\x{9fa5}]{2,10}$/u", $data['dr_name'])){
            _back_msg("error", "请输入2-10位的中文名称！", 'dr_name');
        }
        if(empty($data['dr_mobile'])){
            _back_msg("error", "请输入手机号码！", 'dr_mobile');
        }
        if(!check_mobile($data['dr_mobile'])){
            _back_msg("error", "请输入正确手机号码格式！", 'dr_mobile');
        }
        //检测是否已邀请
        $exist = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$data['dr_mobile']])
                ->get('mb_myj_invite_info')->row_array();
        if(!empty($exist)){
            $code_str = $exist['id'];
            $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
            $code_str.='DRSAY'.$code;
            _back_msg("error_vercode", "您已成功报名！", $code_str);
        }
        
        //根据省份、城市、医院、科室、姓名
        $where_more = ['is_del'=>1,'province_code'=>$data['province_code'],'city_code'=>$data['city_code'],
            'unit_name'=>$data['unit_name'],'dr_department'=>$data['dr_department'],'dr_name'=>$data['dr_name']];
        $exist_more = $this->db->select('id')->where($where_more)->get('mb_myj_invite_info')->row_array();
        if(!empty($exist_more)){
            $code_str = $exist_more['id'];
            $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
            $code_str.='DRSAY'.$code;
            _back_msg("error_vercode", "您已成功报名！", $code_str);
        }
        
        if(empty($data['ver_code'])){
            _back_msg("error", "请输入验证码！",'ver_code_err');
        }
        $verify_code = $data['ver_code'];
        unset($data['ver_code']);
        //验证码认证
        $user_verification_code = get_verification_code($data['dr_mobile'], VERIFICATION_CODE_MYJ_INVITE_REG);
        if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
            _back_msg("error", "验证码输入错误，请重新输入！",'ver_code_err');
        }
        
        $data['add_time'] = time();
        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);
        $data['adder_ip'] = $local_ip;
        $data['adder_address'] = $ip_addr;
        
        //开启事物
        $this->db->trans_begin();
        $this->db->insert('mb_myj_invite_info',$data);
        $insert_id = $this->db->insert_id();
        //更新验证码为已使用状态
        if ($user_verification_code) {
            update_verification_code($user_verification_code['id']);
        }
        if ($this->db->trans_status() === FALSE){
            $this->db->trans_rollback();
            _back_msg("error", "操作失败，请稍后重试！",'error_vercode');
        }
        $this->db->trans_commit();
        $code_suc = substr(md5($insert_id.PROJECT_ENCODE_KEY), 8, 6);
        _back_msg("success", "操作成功！",'/myj/register?code='.$insert_id.'DRSAY'.$code_suc);
    }
    
    /**
     * 检测省份是否已完成招募
     * @param type $province
     */
    private function check_recruit_number($province=''){
        //重点区域
        $key_areas_ary = ['江苏省','浙江省','河北省'];
        if(in_array($province, $key_areas_ary)){
            //查询重点区域是否已经招募完成-目标数量（30）
            $count_info = $this->db->where(['is_del'=>1])->where_in('province',$key_areas_ary)->count_all_results('mb_myj_invite_info');
            if($count_info>=303){
                return ['code'=>2,'msg'=>$province.'已达成招募目标，感谢您的支持！'];
            }
        }
        
        //非重点区域
        $nonpoint_areas_ary = ['安徽省','福建省','广东省','黑龙江省','湖北省','辽宁省','内蒙古自治区','山东省','上海市'];
        if(in_array($province, $nonpoint_areas_ary)){
            //查询非重点区域是否已经招募完成-目标数量（20）
            $count_info = $this->db->where(['is_del'=>1])->where_in('province',$nonpoint_areas_ary)->count_all_results('mb_myj_invite_info');
            if($count_info>=27){
                return ['code'=>2,'msg'=>$province.'已达成招募目标，感谢您的支持！'];
            }
        }
        return ['code'=>1];
    }

    /**
     * 异步检测省份是否招募完成
     */
    public function check_province_recruit(){
        //加密参数认证
        $code = $this->input->post('code',true);
        $time = $this->input->post('time',true);
        $check_code = $this->check_code($code, $time);
        if($check_code){
            $province = $this->input->post('province',true);
            //检测当前省份是否超过招募目标数
            $check_recruit_number = $this->check_recruit_number($province);
            if($check_recruit_number['code']===2){
                _back_msg("error", $check_recruit_number['msg'], 'province_code');
            }
            _back_msg('success', '检测成功！','province_code');
        }
        _back_msg("error", "无效操作，认证参数失败，请刷新！",'province_code');
    }
    
    /**
     * 过滤请求数据
     * 去除左右空格
     * @param type $data
     */
    private function filter_data($data) {
        if (!is_array($data))
            return trim($data);

        array_walk($data, function (&$value) {
            $value = trim($value);
        });
        return $data;
    }
    
    //异步城市（base_area 地区统计用区划代码表）
    public function sel_city(){
        //加密参数认证
        $code = $this->input->post('code',true);
        $time = $this->input->post('time',true);
        $check_code = $this->check_code($code, $time);
        $city_info = [];
        if($check_code){
            //省份代码
            $province_code = $this->input->post('province_id',true);
            if($province_code):
                $base_area = $this->db->select('province')->where(['code'=>$province_code,'level'=>1])->get(TABLE_BASE_AREA)->row_array();
                $where = ['level'=>2,'province'=>$base_area['province']];
                $city_info = $this->db->select('name as val_translate,code as sys_dictionary_id')
                    ->where($where)->get(TABLE_BASE_AREA)->result_array();
            endif;
        }
        _back_msg("success", $city_info);
    }
    
    /**
     * 获取手机动态码
     */
    public function obtain_vercode(){
        //加密参数认证
        $code = $this->input->post('code',true);
        $time = $this->input->post('time',true);
        $check_code = $this->check_code($code, $time);
        if($check_code){
            $post_data = $this->input->post(['verify_mobile','province','city','unit_name','dr_department','dr_name'], true);
            $post_data = format_post_data($post_data);
            if(empty($post_data['province'])){
                _back_msg("error", "请选择省份！", 'province_code');
            }
            //检测当前省份是否超过招募目标数
            $check_recruit_number = $this->check_recruit_number($post_data['province']);
            if($check_recruit_number['code']===2){
                _back_msg("error", $check_recruit_number['msg'], 'province_code');
            }
            if(empty($post_data['city'])){
                _back_msg("error", "请选择城市！", 'city_code');
            }
            if(empty($post_data['unit_name'])){
                _back_msg("error", "请选择医院！", 'unit_name');
            }
            $unit_name = isset($this->unit_name[$post_data['city']])?$this->unit_name[$post_data['city']]:[];
            if(!in_array($post_data['unit_name'], $unit_name)){
                _back_msg("error", "非法操作，未选择要求的医院！", 'unit_name');
            }
            if(empty($post_data['dr_department'])){
                _back_msg("error", "请选择科室！", 'dr_department');
            }
            if(!in_array($post_data['dr_department'], $this->department)){
                _back_msg("error", "非法操作，未选择要求的科室！", 'dr_department');
            }
            if(empty($post_data['dr_name'])){
                _back_msg("error", "请输入姓名！", 'dr_name');
            }
            if(!preg_match("/^[\x{4e00}-\x{9fa5}]{2,10}$/u", $post_data['dr_name'])){
                _back_msg("error", "请输入2-10位的中文名称！", 'dr_name');
            }
            
            $verify_mobile = isset($post_data['verify_mobile'])?$post_data['verify_mobile']:'';
            if(empty($verify_mobile)){
                _back_msg("error", "请输入手机号码！",'dr_mobile');
            }
            if(!check_mobile($verify_mobile)){
                _back_msg("error", "请输入正确手机号码格式！",'dr_mobile');
            }
            //检测是否存在邀请中
            $exist = $this->db->select('id')->where(['is_del'=>1,'dr_mobile'=>$verify_mobile])
                    ->get('mb_myj_invite_info')->row_array();
            if(!empty($exist)){
                $code_str = $exist['id'];
                $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                $code_str.='DRSAY'.$code;
                _back_msg("error_vercode", "您已成功报名！",$code_str);
            }
            
            //根据省份、城市、医院、科室、姓名
            $where_more = ['is_del'=>1,'province'=>$post_data['province'],'city'=>$post_data['city'],
                'unit_name'=>$post_data['unit_name'],'dr_department'=>$post_data['dr_department'],'dr_name'=>$post_data['dr_name']];
            $exist_more = $this->db->select('id')->where($where_more)->get('mb_myj_invite_info')->row_array();
            if(!empty($exist_more)){
                $code_str = $exist_more['id'];
                $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                $code_str.='DRSAY'.$code;
                _back_msg("error_vercode", "您已成功报名！",$code_str);
            }

            //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
            $type = VERIFICATION_CODE_MYJ_INVITE_REG;
            //检测验证码是否重复获取
            $exist_ver = $this->db->select('id,mobile,create_time')
                    ->where(['status'=>1,'type'=>"$type",'mobile'=>$verify_mobile])
                    ->order_by('id desc')->get('app_member_verify',1)->row_array();
            if(!empty($exist_ver)){
                //检测重新获取是否大于60秒
                $time_diff = time()-$exist_ver['create_time'];
                if($time_diff<SMS_RESEND_TIME){
                    _back_msg("error", "操作频繁，请等待".(SMS_RESEND_TIME-$time_diff)."秒后重新获取！",'ver_code_err');
                }
            }

            //发送短信验证码
            $vcode = rand(10000, 99999);
            $sms_log_code = SMS_LOG_CODE_SXO_INVITE_REG;
            //创蓝
            if ($_SERVER['HTTP_HOST'] == "dev.drsay.cn") {//本地
                $st = true;
            } else {
                $sms_content = "邀您报名互联网医院活动，您的验证码为:{$vcode}，" . SMS_ACTIVE_TIME . "分钟内有效，请勿泄漏！";
                $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
            }

            if ($st) {//短信发送成功,记录入库
                $verify_data = [
                    'mobile' => $verify_mobile,'vcode' => $vcode,
                    'create_time' => time(),'type' => (string) $type
                ];
                $add_res = $this->db->insert('app_member_verify', $verify_data);
                if ($add_res) {
                    _back_msg('success', '短信发送成功，请查收！');
                }
                _back_msg("error", "短信发送失败，请重试！",'ver_code_err');
            }
            _back_msg("error", "短信发送失败，请重试！",'ver_code_err');
        }
        _back_msg("error", "无效操作，认证参数失败，请刷新！",'ver_code_err');
    }
    
    /**
     * 邀请人匹配二维码
     */
    public function register(){
        //加密参数认证
        $code = $this->input->get('code',true);
        if(!$code){
            redirect('/myj/intro');
        }
        
        $data = [];
        $explode_param = explode('DRSAY',$code);
        
        //解析认证参数
        $info_id = $explode_param[0];
        $old_encryption = $explode_param[1];
        //根据参数组建加密字符
        $new_encryption = substr(md5($info_id . PROJECT_ENCODE_KEY), 8, 6);
        if(count($explode_param)!==2 || !$info_id || $old_encryption!==$new_encryption){
            redirect('/myj/intro');
        }
        //推荐管理(推荐人信息设置)-链接邀请人详情
        $exist_info = $this->db->select('id,city_code')->where(['is_del'=>1,'id'=>$info_id])
                ->get('mb_myj_invite_info',1)->row_array();
        if(empty($exist_info)){
            redirect('/myj/intro');
        }
            
        //是否存在访问员二维码
        $exist_code = $this->db->select('name,code')->where(['city_code'=>$exist_info['city_code']])
                ->get('mb_myj_city_code')->row_array();
        if(empty($exist_code)){
            redirect('/myj/intro');
        }
        $this->load->view("/myj/register",$exist_code);
    }
}
