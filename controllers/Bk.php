<?php
/**
 * Created by PhpStorm.
 * 用途：进入问卷引导页
 * User: Amy
 * Date: 2017/11/07
 */
use EasyWeChat\Factory;
class Bk extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('survey_model');
        //http://www.drsay.cn/bk/sc18/6dfaa6d76aead89d?uid=
    }

    public function index()
    {
        //http://www.drsay.cn/bk/s/18/6dfaa6d76aead89d?uid=
//        $partner_survey_link = 'http://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
//        print_r($partner_survey_link);
        redirect("/");
    }


    //返回问卷
    public function s() {
        $back_url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
        //1、解析返回链接，验证链接有效性
        $bk_code = $this->uri->segment(3);
        $partner_paran = explode('_',$bk_code); //返回参数
        $pid = (int)trim($partner_paran[0]); // 项目id
        $bk_info = $partner_paran[1];

        $jkt_thanks_url = "/bk/rs";
        if ($pid <= 0) {
//            redirect("/go/thanks?st=p");
            $this->survey_model->get_redirect_info($jkt_thanks_url, "p");
        }
        //查询项目表
        $res_project_info = $this->db->query("SELECT * FROM app_project WHERE id='{$pid}' LIMIT 1")->row_array();
        if (!$res_project_info) {
            $this->survey_model->get_redirect_info($jkt_thanks_url, "p");
//            redirect("/go/thanks?st=p");
        }
        //按问卷添加时间去生成日志文件
        $tmp_time_path = "./tmp/bk/".date("Y", $res_project_info['add_time'])."/".date("m-d", $res_project_info['add_time']);
        if(! is_dir( $tmp_time_path ) ) {
            mk_dir($tmp_time_path);
        }
        $tmp_bak_url = $tmp_time_path."/".$pid.".txt";
        file_put_contents($tmp_bak_url, $back_url."**".date("Y-m-d H:i:s").PHP_EOL, FILE_APPEND | LOCK_EX);
//        file_put_contents("./tmp/bk.txt", $back_url);

        //问卷状态
        $st = $this->survey_status($pid, $bk_info);
        $p_table = "app_project_implement_" . $pid;

        if (!$st) {
            $this->survey_model->get_redirect_info($jkt_thanks_url, "p");
//            redirect("/go/thanks?st=p");
        }
        $survey_uid = $this->input->get("uid");//问卷链接uid

        if (!$survey_uid) {
            $this->survey_model->get_redirect_info($jkt_thanks_url, "p");
//            redirect("/go/thanks?st=p");
        }

        //加锁处理 AMY 2020-06-25
        if(Redis_lock::getInstance()->lockNoWait("bk_s_".$survey_uid, 120) !== true) {
            //错误提示
//            show_json_msg('error', 'Please try again later！');
            set_sys_warning($pid, "", "", "", "重复点击返回".$survey_uid);
            $this->survey_model->get_redirect_info($jkt_thanks_url, "repeat");
        }
        ####   AMY 2021-12-15 为5516项目提示信息调整做的标识
        $this->session->set_userdata(["bk_project_id" => $pid]);
        ####   AMY 2021-12-15 为5516项目提示信息调整做的标识

        ### 检测survey_uid是否只有一个 AMY 2020-06-18
        $check_survey_uid = $this->db->query("SELECT count(*) as num,my,invalid_mobile_status FROM {$p_table} WHERE survey_uid='{$survey_uid}' ")->row_array();
        if ($check_survey_uid['num'] != 1) {//survey_uid不存在或者存在多个时，不给记录返回信息
            set_sys_warning($pid, "", "", "", "survey_uid个数：{$check_survey_uid['num']}"."**survey_uid编号：".$survey_uid);
            $this->survey_model->get_redirect_info($jkt_thanks_url, "l_err");
        }
        ### 检测survey_uid是否只有一个 AMY 2020-06-18

        //查询survey_uid是否存在
        $get_pro_s_link = $this->db->query("SELECT * FROM app_project_s_link WHERE pid='{$pid}' AND survey_uid='{$survey_uid}' LIMIT 1")->row_array();

        //如果没有参与过调查问卷
        if (!$get_pro_s_link) {
            set_sys_warning($pid, "", "", "", "抱歉，你还没有参与本次调查，谢谢！".$survey_uid);
            $this->survey_model->get_redirect_info($jkt_thanks_url, "l_err");
//            redirect("/go/thanks?st=l_err");
        }

        ######  AMY 2019-02-13 通过域名配置，获取跳转地址    ######
//        $redirect_url_info = "/go/thanks";
        $redirect_url_info = $jkt_thanks_url;
        //通过$get_pro_s_link获取partner_uid
        $partner_uid = $get_pro_s_link['partner_uid'];
        $bespeak_info = getDataByCondition("app_project_bespeak", " AND pid={$pid} AND partner_uid='{$partner_uid}'", "*", true);
        if ($bespeak_info) {//存在来源地址
            $sys_id = $bespeak_info['sys_id'] ? $bespeak_info['sys_id'] : 1;//默认是上医说官网地址
            $site_domain_info = getDataByCondition("app_sys_survey_domain", " AND sys_id={$sys_id}", "*", true);
            $redirect_url_info = $site_domain_info ? $site_domain_info['redirect_link'] : $redirect_url_info;
        }
        ######  AMY 2019-02-13 通过域名配置，获取跳转地址    ######

        ### rafael 查询明细表 2017-11-23
        //如果已经参与，且在项目明细表中有状态记录，有则已经记录不能重复记录
//        $project_implement_quota = $this->db->query("select a.quta_id,b.now_c_mount,b.now_s_mount,b.now_q_mount,b.now_t_mount,b.click_mount,a.finish_status,a.partner_uid from {$p_table} a INNER join app_project_quta b on a.quta_id = b.id where a.survey_uid ='{$survey_uid}'  ")->row_array();
        $project_implement_quota = $this->db->query("select a.*,b.now_c_mount,b.now_s_mount,b.now_q_mount,b.now_t_mount,b.click_mount from {$p_table} a left join app_project_quta b on a.quta_id = b.id where a.survey_uid ='{$survey_uid}'  ")->row_array();
        $sv_st = $project_implement_quota['finish_status']; //完成状态
        $partner_uid = $project_implement_quota['partner_uid'];
        $mid_id = $project_implement_quota['id'];
        $my = $project_implement_quota['my'];
        if($sv_st != ''){
//            set_sys_warning($pid, "", "", "", "不能重复操作，谢谢！".$survey_uid);
            set_sys_warning($pid, "", "", "", "会员重复进入问卷结束页！"."_survey_uid:".$survey_uid);
//            $this->survey_model->get_redirect_info($redirect_url_info, "l_err");
            //跳到状态重复请求页 AMY 2022-03-29
            $this->survey_model->get_redirect_info($redirect_url_info, "s_repeat");
//            redirect("{$redirect_url_info}?st=l_err");
        }
        //配额表相关信息
        $now_c_mount = $project_implement_quota['now_c_mount'] ? $project_implement_quota['now_c_mount'] : 0;
        $now_q_mount = $project_implement_quota['now_q_mount'] ? $project_implement_quota['now_q_mount'] : 0;
        $now_s_mount = $project_implement_quota['now_s_mount'] ? $project_implement_quota['now_s_mount'] : 0;
        $now_t_mount = $project_implement_quota['now_t_mount'] ? $project_implement_quota['now_t_mount'] : 0;
        $click_mount = $project_implement_quota['click_mount'] ? $project_implement_quota['click_mount'] : 0;
        $quota_id = $project_implement_quota['quta_id'];
        ### rafael 查询明细表 2017-11-23

        $partner_id = $get_pro_s_link['partner_id'];
        $group_no = $get_pro_s_link['link_group'];
        // 获取外包国家相关信息，通过国家获取默认语言
        $country_default_lang = $this->survey_model->get_country_default_lang($pid, $partner_id, $group_no);
        $lang = $country_default_lang['lang'];
        $lang_ary = get_lang($lang);
//        if ($survey_uid == "4260730587") {
//            echo $partner_uid."<br />";
//            die;
//        }
        //查看项目是否存在
//        $check_table_info = $this->db->query("SHOW TABLES LIKE '%".$p_table."%';")->row_array();
        $check_table_info = $this->db->query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE  TABLE_NAME = '{$p_table}'")->row_array();
        if (!$check_table_info) {
            set_sys_warning($pid, $partner_id, $group_no, $survey_uid, $lang_ary[LABEL_PROJECT_IMPLEMENT_NOT_EXIST]."_BACK");
            $this->survey_model->get_redirect_info($redirect_url_info, "p");
//            redirect("{$redirect_url_info}?st=p");
        }
        //货币与积分的转换关系
        $res_point_info = $this->price_to_point($pid, $survey_uid, $st);
        if (!$res_point_info) {
            set_sys_warning($pid, "", "", "", $lang_ary[LABEL_SURVEY_POINT_BACK_ERROR]);
            $this->survey_model->get_redirect_info($redirect_url_info, "l_err");
//            redirect("{$redirect_url_info}?st=l_err");
        }
        $uid = $res_point_info['member_uid'];
        $point_info = $res_point_info['point_info'];
        $partner_id = $res_point_info['partner_id'];
        $open_c = $res_point_info['openc'];
        $open_q = $res_point_info['openq'];
        $open_s = $res_point_info['opens'];
        $partner_country = $res_point_info['country'];//外包国家
        $partner_money_rate = $res_point_info['money_rate'];//外包汇率
        $pid_id = $res_point_info['pid_id'];//项目执行表唯一编号

        //记录会员点击日志
        user_log(LOG_CODE_SURVEY_BACK, $pid, $uid, "", $survey_uid);

        //AMY 2020-06-05 跳转到gooddr官网的code组装
        $to_gooddr_code = $this->survey_model->get_gooddr_log_info($uid, $pid);
        $to_gooddr_code = $to_gooddr_code ? $to_gooddr_code : "";
        $to_gooddr = $to_gooddr_code ? "&gooddr_code={$to_gooddr_code}" : "";

        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);
        if($st == 'c'){
            $screening_status = 0;
        }
        if($st == 'q'){
            $screening_status = 7;
        }
        if($st == 's'){
            $screening_status = 6;
        }

        if ($st == 's') {
            //记录题号信息
            $question_id = $this->input->get("gdqid");
            if (!empty($question_id)) {

                $sql = "
                INSERT INTO app_project_implement_question (
                    pid,
                    pid_id,
                    question_id,
                    created_at
                    ) VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    question_id = VALUES(question_id),
                    created_at = VALUES(created_at
                )";

                $this->db->query($sql, [
                    $pid,
                    $pid_id,
                    htmlspecialchars($question_id),
                    time()
                ]);
            }
        }

        $data_implement = array(
            'bk_ip' => $ip,
            'bk_ip_addr' => $ip_addr,
            'finish_status' => $st,
            'original_finish_status' => $st,
            'finish_time' => time(),
            'screening_status' => $screening_status,
            'survey_answer_status' => 2,
            'get_point' => $point_info > 0 ? $point_info : 0,//记录完成问卷获得的积分
        );
//        file_put_contents("./tmp/bk_info.txt", json_encode($data_implement, JSON_UNESCAPED_UNICODE)."**".$p_table.PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
        //1:更新项目明细表
        $where_implement = array("survey_uid" => $survey_uid);
        // 更新相关内容到项目明细表
        $res_update_implement = $this->survey_model->update_surface($p_table, $data_implement, $where_implement);
        if ($res_update_implement) {//更新项目执行表成功
            //2:继续更新项目链接表
            $res_update_s_link = $this->db->query("UPDATE app_project_s_link SET finish_status='{$st}' WHERE pid='{$pid}' AND survey_uid='{$survey_uid}' LIMIT 1");

            ### rafael 更新外包成本及完成量 2017-11-23
            //查询项目外包
            $app_project_partner = $this->db->query(" select * from app_project_partner where  project_id={$pid} and partner_id={$partner_id} and groupno={$group_no} ")->row_array();

            //项目基础成本与完成量及外包基础成本与完成量记录
            $app_project_detail = $this->get_project_award($pid, $st, $app_project_partner);
            ### rafael 更新外包成本及完成量 2017-11-23

            if (!$res_update_s_link) {//更新项目链接表失败
                set_sys_warning($pid, "", "", "", $lang_ary[LABEL_SURVEY_UPDATE_LINK_FIELD]);
                $this->survey_model->get_redirect_info($redirect_url_info, "p", "", "", "gooddr_code={$to_gooddr_code}");
//                redirect("{$redirect_url_info}?st=p{$to_gooddr}");
            }
        } else {//更新项目执行表失败
            set_sys_warning($pid, "", "", "", $lang_ary[LABEL_SURVEY_UPDATE_IMPLEMENT_FIELD]);
            $this->survey_model->get_redirect_info($redirect_url_info, "p", "", "", "gooddr_code={$to_gooddr_code}");
//            redirect("{$redirect_url_info}?st=p{$to_gooddr}");
        }

        //更新配额表的完成量
        if(($st == 'c' || $st == 's' || $st == 'q' || $st == 't') &&
                ((isset($check_survey_uid['my']) && $check_survey_uid['my']==0) ||
                (isset($check_survey_uid['invalid_mobile_status']) && $check_survey_uid['invalid_mobile_status']==0))){
            //该配额已收集的对应状态的总数量
            $get_now_mount = "now_{$st}_mount";
            $data_quota = array(
                $get_now_mount => $$get_now_mount + 1,
                "click_mount" => $click_mount + 1,
            );
            //3:更新配额表
            $where_quota_cond = array('pid' => $pid,'id' => $quota_id);
            $this->survey_model->update_surface("app_project_quta", $data_quota, $where_quota_cond);
            //查询配额是否已满，已满就关闭配额         2019-10-14 Rainnie要求放开，进入完成c，配额关闭导致会员没有生成积分明细表
            $res_pro_quota = $this->survey_model->close_project_quota($quota_id,VERIFICATION_QUOTA);
            if ($res_pro_quota) {//配额已满
                set_sys_warning($pid, $partner_id, $group_no, $partner_uid, "问卷返回，配额已满，关闭配额");
            }
        }

        //查询外包信息
        $partner_info = $this->db->query("SELECT * FROM app_vendor WHERE id='{$partner_id}' LIMIT 1")->row_array();
        if ($partner_info['property'] == VENDOR_INTERNAL) {// 内部资源，做加分处理
            //查询项目是否是即时加分
            $point_fetch = $res_project_info['point_fetch'];
            //奖励方式
            $reward_type = $res_project_info['reward_type'];
           //如果调查奖励是礼品
            if ($st == "c") {// 完成状态

                //记录用户参与次数
                $project_implement_finish_status = $this->db->where(array('partner_uid'=>$partner_uid))->get($p_table)->row_array();
                $this->db->query("update app_member set finish_c_count =finish_c_count+1  WHERE `id` = {$project_implement_finish_status['member_uid']}");


                if ($reward_type == PROJECT_REWARD_TYPE_GIFT) {//礼品

                    //完成时，需要跳转到收集地址页去收集快递信息
                    $survey_uid_md5 = md5($survey_uid.PROJECT_ENCODE_KEY);
                    $survey_uid_str = $survey_uid."_".$survey_uid_md5;
                    $gift_address_url = "/bk/get_address/".$bk_code."/".$survey_uid_str;
                    redirect($gift_address_url);
                }
                if ($reward_type == PROJECT_REWARD_TYPE_POINT) {//积分
                    $point_detail_info["point_fetch"] = $point_fetch;//1：及时加分 2：后续加分
                    if ($point_fetch == PROJECT_POINT_FETCH_TIMELY) {//及时加分
                        $point_detail_status = POINT_AUDITING_END;

                        ######## AMY 2019-12-12 start   ########
                        //及时加分时，把用户的确认状态，确认积分字段标记好
                        $point_detail_info["cash_status"] = 1;//【老流程】确认积分发放
                        $point_detail_info["clent_confirm_status"] = PRO_IMP_CLIENT_CONFIRM_STATUS_PROCESSED;//【新流程】确认状态
                        $point_detail_info["clent_confirm_status_time"] = time();//【新流程】确认状态时间
                        $point_detail_info["point_status"] = PRO_IMP_POINT_STATUS_PROCESSED;//【新流程】积分发放
                        $point_detail_info["point_status_time"] = time();//【新流程】积分发放时间
                        $point_detail_info["change_operator"] = "system";//【新流程】积分变动操作者
                        $point_detail_info["mid_id"] = $mid_id ? $mid_id : "";//【新流程】记录项目执行表编号，用于后台提现操作

                        // 预支付使用总积分
                        $prepay_info = $this->db->select('sum(prepay_point) as sum_prepay_point')->where(array('pid' => $pid, 'project_detail_id' => $pid_id, 'member_uid' => $uid))->get('app_project_prepay')->row_array();
                        $sum_prepay_point = 0;
                        if ($prepay_info) {
                            $sum_prepay_point = $prepay_info['sum_prepay_point'];
                        }
                        // 获得积分 = 外包配置积分 - 预支付总积分
                        $point_info = $point_info - $sum_prepay_point;
                        $point_info = $point_info < 0 ? 0 : $point_info;

                        // 更新相关内容到项目明细表【补充更新到项目执行表】
                        $supplement_data = [
                            "clent_confirm_status" => PRO_IMP_CLIENT_CONFIRM_STATUS_PROCESSED,
                            "point_status" => PRO_IMP_POINT_STATUS_PROCESSED,
                        ];

                        $this->survey_model->update_surface($p_table, $supplement_data, $where_implement);
                        ######## AMY 2019-12-12 end   ########
                    } else {//后续加分
                        $point_detail_status = POINT_AUDITING_START;
                    }
                }
            } else {// 别的状态，直接给奖励积分
                $point_detail_status = POINT_AUDITING_END;
            }
            //3、处理会员表与会员相关积分明细信息
//            $point_detail_arr = array("status" => $point_detail_status);
            //6:更新会员积分表
            $point_detail_info["finish_status"] = $st;//完成状态
            $point_detail_info["country"] = $partner_country;//外包国家
            $point_detail_info["money_rate"] = $partner_money_rate;//外包汇率
            $reward_log = APP_PROJECT_RETURN_REWARD_DEFAULT;//默认奖励
            $sample_id = 0;
            ####    AMY 2021-02-07 查询外部邀请流程，记录是否从此流程来，如果是，奖励金额调取填写的金额
            if ($st == 'c') {//完成项目信息，只调取通过的记录
                $invite_info = $this->db->query("SELECT * FROM app_patient_fwy_add WHERE pid=? AND btjr_member_id=? AND pm_status=1 LIMIT 1", [$pid, $uid])->row_array();
                if ($invite_info) {//邀请信息
                    $invite_price = $invite_info['patient_reward'];
                    $point_info = $invite_price * 100;//金额转换成积分
                    $reward_log = APP_PROJECT_RETURN_REWARD_INVITE;//邀请管理奖励
                }
            }
            ####    ANNE 2022-05-29 问卷返回，确认奖励来源，奖励对应数据
            $secret = $this->input->get("reward", true);//问卷密钥
            $secret = $secret ? $secret : '';
            //去配置：这个老师有没有在配置，在配置以配置奖励为主
            $res_sample = $this->db->query("select * from app_project_setting_sample where pid = '{$pid}' and is_delete = 0 AND (member_uid=? OR member_uid=0)",[$uid])->result_array();
            if ($res_sample) {
                $res_member_uid = $res_no_member_uid = $res_sample_id_arr = [];
                foreach ($res_sample as $v_sample) {
                    $res_sample_id_arr[$v_sample['id']] = $v_sample['id'];
                    if($v_sample['member_uid']){
                        $res_member_uid[$v_sample['member_uid']] = [
                            "price" => $v_sample['price'],
                            "id" => $v_sample['id'],
                        ];
                    }else{
                        $res_no_member_uid[$v_sample['secret']] = [
                            "price" => $v_sample['price'],
                            "id" => $v_sample['id'],
                        ];
                    }
                }
                if (isset($res_member_uid[$uid])) {//【医师UID】奖励配置，取奖励
                    $point_info = $res_member_uid[$uid]['price']*100;
                    $reward_log = APP_PROJECT_RETURN_REWARD_UID;//多金额配置【医师UID】奖励
                    $sample_id = $res_member_uid[$uid]['id'];
                } else {//没有配置，查询是否有奖励密钥参数
                    if ($secret && isset($res_no_member_uid[$secret])) {//存在，检测密钥格式是否正确，正确，取值
                        $point_info = $res_no_member_uid[$secret]['price']*100;
                        $sample_id = $res_no_member_uid[$secret]['id'];
                        $reward_log = APP_PROJECT_RETURN_REWARD_SECRET;//多金额配置【密钥】奖励
                    }
                }
            }
            $secret_price = $secret && isset($res_no_member_uid[$secret]['price']) ? $res_no_member_uid[$secret]['price'] : 0;
            //奖励日志
            $return_data = [
                'pid' => $pid,
                'log_code' => $reward_log,
                'survey_uid' => $survey_uid,
                'member_uid' => $uid,
                'secret' => $secret,
                'secret_price' => $secret_price,
                'real_price' => $point_info/100,
                'sample_id' => $sample_id,
                'add_time' => time(),
            ];
            $this->db->insert('app_project_return_reward_log',$return_data);
            ####    ANNE 2022-05-29 问卷返回，确认奖励来源，奖励对应数据
            ####    AMY 2021-02-07 查询外部邀请流程，记录是否从此流程来，如果是，奖励金额调取填写的金额

            add_member_point_info($uid, $point_info, POINT_CHANGE_CODE_SURVEY, $pid, $res_project_info['pro_type'], $point_detail_info, $point_detail_status, "survey_point");

            //i_survey调查记录状态更新
            $this->update_i_survey_finish_status($st, $pid, $uid, $point_fetch);
        }
        //记录完成状态
        $this->get_back_status($st, $point_info, $pid, $partner_uid);

        if ($partner_info['property'] == VENDOR_INTERNAL && $res_project_info['point_fetch'] == PROJECT_POINT_FETCH_TIMELY) {//内部资源，而且是及时加分
            if ($st == 'c') {
                if ($my == 1) {//集团账号进来的记录，不做支付
                    redirect("http://www.drsay.cn/bk/rs?code=cDRSAYDRSAYDRSAYf578bd256bbb4540");
                } else {//完成时，需要跳转到收集地址页去收集快递信息
                    $survey_uid_md5 = md5($survey_uid.PROJECT_ENCODE_KEY);
                    $survey_uid_str = $survey_uid."_".$survey_uid_md5;
                    $gift_address_url = "/bk/rs/".$bk_code."/".$survey_uid_str;
                    redirect($gift_address_url);
                }
            } else {
                if (in_array($st, ['q', 's'])) {
                    $this->survey_model->get_redirect_info("/bk/rs", $st);
                } else {
                    $this->survey_model->get_redirect_info("/bk/rs", "l_err");
                }
            }
//            if ($my == 1 && $st == 'c') {//集团账号进来的记录，不做支付
//                redirect("http://www.drsay.cn/bk/rs?code=cDRSAYDRSAYDRSAYf578bd256bbb4540");
//            } else {
//                //完成时，需要跳转到收集地址页去收集快递信息
//                $survey_uid_md5 = md5($survey_uid.PROJECT_ENCODE_KEY);
//                $survey_uid_str = $survey_uid."_".$survey_uid_md5;
//                $gift_address_url = "/bk/rs/".$bk_code."/".$survey_uid_str;
//                redirect($gift_address_url);
//            }
        } else {
            // 获取对应状态的返回链接，重定向跳转
            $redirect_url = "open_".$st;
            $url_partner = $this->url_partner($partner_uid, $$redirect_url);
            //检查链接是否存在参数
            $check_url_param = $this->survey_model->redirect_is_param($url_partner);
            if (!$check_url_param) {//不存在参数
                $to_gooddr = $to_gooddr_code ? "?gooddr_code={$to_gooddr_code}" : "";
            }
            redirect($url_partner.$to_gooddr);
        }


//        // 获取对应状态的返回链接，重定向跳转
//        $redirect_url = "open_".$st;
//        $url_partner = $this->url_partner($partner_uid,$$redirect_url);
//        redirect($url_partner);
//        redirect($$redirect_url);
    }

    //检测用户是否已经兑换成功
    public function check_order_info()
    {
        $scene = $this->input->post("scene", true);
        if (!$scene) {
            _back_msg("error", "无效");
        }
        $arr_scene = explode("_", $scene);
        $pid = $arr_scene[0];
        $member_uid = $arr_scene[1];
        $encrypted_data = $arr_scene[2];
        if (!$pid || !$member_uid || !$encrypted_data) {//参数有误
            _back_msg("error", "参数有误");
        }
        $decrypt_scene = $pid."_".$member_uid;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            _back_msg("error", "参数有误");
        }
        //通过member_uid查询账号表里记录信息,查询微信唯一码是否存在
        $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type=? LIMIT 1", [$member_uid, EXCHANGE_WEBCHAT_AUTO])->row_array();
        if ($account_info) {
            if ($account_info['payment_from'] != 2) {//引导到网医小程序中授权
                _back_msg("error", "未绑定");
            }
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }

    private function check_order_detail($scene, $is_ajax = true)
    {
        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
        if (!$scene) {
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $arr_scene = explode("_", $scene);
        $pid = $arr_scene[0];
        $member_uid = $arr_scene[1];
        $encrypted_data = $arr_scene[2];
        if (!$pid || !$member_uid || !$encrypted_data) {//参数有误
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $decrypt_scene = $pid."_".$member_uid;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        //检测问卷是否已经打款成功
        $check_detail = getDataByConditionCi("app_member_point_detail", " AND log_code in(".POINT_CHANGE_CODE_SURVEY.",".POINT_REISSUE_INTEGRAL.") AND finish_status='c' AND param=? AND uid=?","*", true, [$pid, $member_uid]);
        if (!$check_detail) {
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        if ($check_detail['apply_status'] == 1) {//已提交过支付申请
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "cash_suc", "", "", "", false);
            if ($is_ajax) {
                _back_msg("success", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
    }

    //公共提现页面
    public function comm_sv()
    {
        $post_data = $this->input->post();
        if ($post_data) {
            $s_u = trim($post_data['s_u']);
            $s_c = trim($post_data['s_c']);
            $arr_survey_code = explode("_", $s_c);
            $pid = (int)trim($arr_survey_code[0]);
            //检测参数是否正确
            $this->check_comm_sv($s_u, $s_c);
            $finish_status = trim($post_data['finish_status']);
            if (!$s_u || !$s_c) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            }
            if (!in_array($finish_status, ['c', 's'])) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            }
            //跳转页面
            //通过问卷状态获取跳转地址
            $url_link = DRSAY_WEB.'bk/s/' . link_encode($pid, $finish_status) . '?uid='.$s_u;
            //测试
//            $url_link = 'http://local.drsay.cn/bk/s/' . link_encode($pid, $finish_status) . '?uid='.$s_u;
            redirect($url_link);
        } else {
            $survey_uid = $this->uri->segment(3);
            $survey_code = $this->uri->segment(4);
            $this->check_comm_sv($survey_uid, $survey_code);
            $data = array(
                "s_u" => $survey_uid,
                "s_c" => $survey_code,
            );
            $this->load->view("/bk/comm_sv",$data);
        }
    }

    //公共提现页面(微信支付接口申请)
    public function wx_pay_test()
    {
        $post_data = $this->input->post();
        if ($post_data) {
            $s_u = trim($post_data['s_u']);
            $s_c = trim($post_data['s_c']);
            $arr_survey_code = explode("_", $s_c);
            $pid = (int)trim($arr_survey_code[0]);
            //检测参数是否正确
            $this->check_comm_sv($s_u, $s_c);
            $finish_status = trim($post_data['finish_status']);
            if (!$s_u || !$s_c) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            }
            if (!in_array($finish_status, ['c', 's'])) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            }
            //跳转页面
            //通过问卷状态获取跳转地址
            $url_link = DRSAY_WEB.'bk/s/' . link_encode($pid, $finish_status) . '?uid='.$s_u;
//            echo $url_link;die;
            redirect($url_link);
        } else {
            $survey_uid = $this->uri->segment(3);
            $survey_code = $this->uri->segment(4);
            $this->check_comm_sv($survey_uid, $survey_code);
            $data = array(
                "s_u" => $survey_uid,
                "s_c" => $survey_code,
            );
            $this->load->view("/bk/wx_pay_test",$data);
        }
    }

    private function check_comm_sv($survey_uid, $survey_code)
    {
        if (!$survey_uid || !$survey_code) {//参数错误
            $this->survey_model->get_redirect_info("/bk/rs", "l_err");
        }
        $arr_survey_code = explode("_", $survey_code);
        $pid = (int)trim($arr_survey_code[0]);
        if (!$pid) {
            $this->survey_model->get_redirect_info("/bk/rs", "l_err");
        }
        $code = $arr_survey_code[1];
        $local_code = substr(md5($pid."_".$survey_uid."_".PROJECT_ENCODE_KEY), 8, 16);
        if ($code != $local_code) {//参数有误
            $this->survey_model->get_redirect_info("/bk/rs", "l_err");
        }
        //检测项目是否已经存在状态
        $pro_imp_info = $this->db->query("SELECT * FROM app_project_implement_{$pid} WHERE survey_uid=?", [$survey_uid])->row_array();
        if (!$pro_imp_info) {
            $this->survey_model->get_redirect_info("/bk/rs", "l_err");
        }
        if ($pro_imp_info['finish_status'] != '') {//项目已经有状态
            $this->survey_model->get_redirect_info("/bk/rs", $pro_imp_info['finish_status']);
        }
    }

    //验证参数有效性
    private function check_fail_order_scene($scene, $is_ajax = true)
    {
        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "payment_err", "", "", "", false);
        if (!$scene) {
            if ($is_ajax) {
                _back_msg('error', '参数有误！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $arr_scene = explode("_", $scene);
        $order_id = $arr_scene[0];
        $encrypted_data = $arr_scene[1];
        if (!$order_id || !$encrypted_data) {//参数有误
            if ($is_ajax) {
                _back_msg('error', '参数有误！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $decrypt_scene = "order_{$order_id}";
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=? AND order_status=1", [$order_id])->row_array();
        if (!$order_info || ($order_info && $order_info['order_status'] != 1)) {//不是有效订单
            if ($is_ajax) {
                _back_msg('error', '订单不存在！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }

        return $order_info;
    }

    //及时加分，跳转到微信二维码页面
    public function rs()
    {

//        $this->survey_model->get_redirect_info("/bk/to_wx", "c");
        $scene = $this->input->get("scene");
        $code = $this->input->get("code");
        $is_submit_account = $is_phone_type = $is_phone_val = false;
        $is_follow_up = $is_show_payment = false;
        $fail_scene = $gooddr_code = $lock_account = $wy_reg_link = $is_to_wy = $reg_link_info = "";
        $project_info = $exist_sxo_url = $user_payment_info = $fail_order_info = [];
        if ($code) {
            $fail_scene = $scene;
            if ($fail_scene) {
                $arr_fail_scene = explode("_", $fail_scene);
                $fail_order_id = (int)trim($arr_fail_scene[0]);
                if ($fail_order_id > 0) {
                    $fail_order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=?", [$fail_order_id])->row_array();
                }
            }
            $lang = $this->session->userdata["survey_lang"];
            $lang = $lang ? $lang : 140;
            //获取语言包
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
            if (!$lang_ary) {//不存在语言包缓存
                //生成语言包
                setting_lang($this->lang_version);
                $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
            }
            $arr_code = explode("DRSAY", $code);
            if (count($arr_code) != 4) {
                redirect("/");
            }
            $encode_str = $arr_code[3];
            $st = $arr_code[0];
            $member_uid = isset($arr_code[1]) ? $arr_code[1] : "";
            $pid = isset($arr_code[2]) ? $arr_code[2] : "";

            $local_encode_str = substr(md5($st. "DRSAY" .$member_uid. "DRSAY" .$pid. "DRSAY". PROJECT_ENCODE_KEY), 8, 16);
            if ($encode_str != $local_encode_str) {
                redirect("/");
            }

            $point = 0;
            if ($member_uid && $pid) {
                $check_detail = getDataByConditionCi("app_member_point_detail", " AND log_code in(".POINT_CHANGE_CODE_SURVEY.",".POINT_REISSUE_INTEGRAL.") AND finish_status='{$st}' AND param=? AND uid=?","*", true, [$pid, $member_uid]);
                $point = $check_detail['point'];
            }
            if ($st == "c") {
                $is_follow_up = true;
            }
            if ($fail_order_info) {
                $pid = $fail_order_info['pid'];
            }
            if ($st == 'p_s' && $scene) {//问卷完成
                ####    验证账号有效性
                $is_to_wy = $this->input->get("to_wy");
                $order_info = $this->check_fail_order_scene($scene, false);
                $imp_pid = $order_info['pid'];
                $imp_pid_id = $order_info['pid_id'];
                $member_uid = $order_info['uid'];
                //查询该用户是否都在这个名单内
                $wy_doctors  = $this->db->query("SELECT * FROM api_wy_doctors_message WHERE dr_id=? LIMIT 1", [$member_uid])->row_array();
                $app_member  = $this->db->query("SELECT * FROM app_member WHERE id=? AND occupation_type=? LIMIT 1", [$member_uid, RELATION_TYPE_DOCTOR])->row_array();
                if (!$wy_doctors && $app_member) {//不是网医用户并且是医生类型的用户，跳至注册页，非医师类型的用户不跳注册页
                    if ($is_to_wy != 1) {
                        $reg_link_info = "/bk/rs?scene=".$scene."&code=".$code."&to_wy=1";
                    } else {
                        //网医默认注册页面
                        $wy_reg_link = "https://api.idr.cn/web/wangyi/register.html";
                        if ($imp_pid > 0 && $imp_pid_id > 0) {
                            //获取访问员编号
                            $imp_table = $this->db->query("select * from information_schema.COLUMNS where table_name = 'app_project_implement_{$imp_pid}'")->row_array();
                            if ($imp_table) {//表存在
                                $imp_info = $this->db->query("SELECT * FROM app_project_implement_{$imp_pid} WHERE id=?", [$imp_pid_id])->row_array();
                                $fwy_uid = 0;
                                if ($imp_info) {//存在执行表记录，查询访问员信息
                                    $fwy_uid = (int)$imp_info['uid'];
                                }
                                if ($fwy_uid > 0) {//存在访问员绑定信息
                                    //获取网医访问员接口地址
                                    $wy_chat_list = curl_post_data("https://api.idr.cn/dkt/other/sys_chat", []);
                                    if ($wy_chat_list) {
                                        $arr_wy_chat_list = json_decode($wy_chat_list, true);
                                        if (isset($arr_wy_chat_list['code']) && $arr_wy_chat_list['code']== 200) {
                                            if (isset($arr_wy_chat_list['data'][$fwy_uid])) {//带有访问员信息的注册地址
                                                $wy_reg_link = $arr_wy_chat_list['data'][$fwy_uid];
                                            }
                                        }
                                    }
                                }

                            }
                        }
                        file_put_contents('./tmp/wy_reg_link.txt', $member_uid."**".$wy_reg_link."**".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL,FILE_APPEND | LOCK_EX);
                    }
                }
                ####    验证账号有效性
                // AMY 2025-05-22 根据bella要求，把网医引流的地址置空，不走网医提示流程
                $wy_reg_link = $reg_link_info = '';
            }
        } else {
            $bk_code = $this->uri->segment(3);
            $survey_uid_code = $this->uri->segment(4);
            $sys_id = (int)trim($this->uri->segment(5));
            $sys_id = $sys_id > 0 ? $sys_id : 1;

//            $this->session->unset_userdata('project_sys_id');
            if (in_array($sys_id, [7,8,9])) {//是网医
                $this->session->set_userdata(["project_sys_id" => $sys_id]);
            }

            $partner_paran = explode('_',$bk_code);
            $pid = $partner_paran[0];
            $bk_info = $partner_paran[1];
            //问卷状态
            $st = $this->survey_status($pid, $bk_info);
            if (!$st) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            } else {
                if ($st != 'c') {
                    if (!in_array($st, ['q', 's'])) {
                        $this->survey_model->get_redirect_info("/bk/rs", "l_err");
                    } else {
                        $this->survey_model->get_redirect_info("/bk/rs", $st);
                    }
                }
            }

            //验证账号是否有误
            $get_code_param = $this->account_validity($bk_code, $survey_uid_code);
            if (!$get_code_param) {
                set_sys_warning(0, "", "", "", "rs->account_validity：链接检测无效{$bk_code}**{$survey_uid_code}");
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
//                redirect("/go/thanks?st=l_err");
            }
            $pid = $get_code_param['pid'];
            $survey_uid = $get_code_param['survey_uid'];
            $member_uid = $get_code_param['member_uid'];
            $m_id = $get_code_param['m_id'];
            $point = $get_code_param['point'];//获取积分
            $st = $get_code_param['st'];
            $lang_ary = $get_code_param['lang_ary'];
            $country = $get_code_param['country'];
            $imp_name = $get_code_param['imp_name'];


//            if (!isset($this->session->userdata["bk_exchange_{$pid}_{$m_id}"]) || $this->session->userdata["bk_exchange_{$pid}_{$m_id}"] != $member_uid) {
//                file_put_contents('./tmp/project_exchange.txt',$survey_uid.'_'.$pid.'_'.$m_id."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
//                redirect("/bk/exchange_sms_sub/{$bk_code}/{$survey_uid_code}");
//            }

            $session_bk_exchange = $this->session->userdata("bk_exchange_{$pid}_{$m_id}");
            if (!$session_bk_exchange || $session_bk_exchange != $member_uid) {
                file_put_contents('./tmp/project_exchange.txt',$survey_uid.'_'.$pid.'_'.$m_id."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                redirect("/bk/exchange_sms_sub/{$bk_code}/{$survey_uid_code}/{$sys_id}");
            }

//            if ($member_uid == 2982098) {
//                if (!isset($this->session->userdata["bk_exchange_{$pid}_{$m_id}"]) || $this->session->userdata["bk_exchange_{$pid}_{$m_id}"] != $member_uid) {
//                    redirect("/bk/exchange_sms_sub/{$bk_code}/{$survey_uid_code}");
//                }
//            }

//            if ($member_uid == 2982098) {//用网医小程序测试收款
//                $config = [
//                    //网医小程序
//                    'app_id' => IDR_WECHAT_APPLET_APP_ID,
//                    'secret' => IDR_WECHAT_APPLET_APP_SECRET,
//
//                    // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
//                    'response_type' => 'array',
//                ];
//                $page = "my/drsayExchange/drsayExchange";
//            } else {
//                $config = [
//                    //ipanel
////            'app_id' => 'wx33d03b0f9b327925',
////            'secret' => '50e4d154ed30ed59d8fdd00c58c2026f',
////            //医map
////            'app_id' => 'wx286118feb7a3f12e',
////            'secret' => '227cc3f4adbe82c978b4e99f03e39605',
//
////            上医说
//                    'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
//                    'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
//
//                    // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
//                    'response_type' => 'array',
//                ];
//                $page = "pages/drsayExchange/drsayExchange";
//            }

            //AMY 2022-12-31 正式上线使用网医微信小程序扫码
            $config = [
                //网医小程序
                'app_id' => IDR_WECHAT_APPLET_APP_ID,
                'secret' => IDR_WECHAT_APPLET_APP_SECRET,

                // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
                'response_type' => 'array',
            ];
            $page = "my/drsayExchange/drsayExchange";


            $app = Factory::miniProgram($config);
            //临时二维码，由于小程序二维码参数最多只能传32位，因此要控制参数长度
            $scene = $pid."_".$member_uid;
            $scene = $scene."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
            //检测订单状态，如果已经兑换成功，直接跳走
            $this->check_order_detail($scene, false);
            //通过pid，member_uid，finish_status查询用户实际需要支付的积分
            $check_detail = getDataByConditionCi("app_member_point_detail", " AND log_code in(".POINT_CHANGE_CODE_SURVEY.",".POINT_REISSUE_INTEGRAL.") AND finish_status='c' AND param=? AND uid=?","*", true, [$pid, $member_uid]);
            if ($check_detail) {
                $point = $check_detail['point'];
            }
            $error_code_info = array(
                "45009" => "调用分钟频率受限(目前5000次/分钟，会调整)，如需大量小程序码，建议预生成。",
                "41030" => "所传page页面不存在，或者小程序没有发布",
            );
            $filename = "";
            if ($point >= 100) {
                $response = $app->app_code->getUnlimit($scene, [
                    //医map页面
//                'page'  => 'pages/queryScene/queryScene',
                    //上医说页面
                    'page'  => $page,
                    'width' => 50,
                ]);
                // 或
                if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
                    if (!is_dir("./uploads/wechat/{$pid}/")) {
                        mk_dir("./uploads/wechat/{$pid}/", 0777, true);
                    }
//                    $filename = $response->saveAs("./uploads/wechat/{$pid}/", $pid."_".$member_uid.'.png');
                    $filename = $response->saveAs("./uploads/wechat/{$pid}/", $scene.'_idr.png');//网医扫码二维码图片
                } else {
//                $error_info = isset($error_code_info[$response['errcode']]) ? $error_code_info[$response['errcode']] : "";
                    echo "error_code:{$response['errcode']}";
                    die;
                }
            }


            //通过用户编号，获取用户的，手机充值，支付宝，微信自动打款账号
            $account_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type in('".EXCHANGE_MOBILE."','".EXCHANGE_ALIPAY."','".EXCHANGE_WEBCHAT_AUTO."') ORDER BY id ASC", [$member_uid])->result_array();
            $payment_type = [];
            if ($account_info) {
                foreach($account_info as $v_account){
                    $payment_type[$v_account['payment_type']] = $v_account['payment_type'];
                }
            }
            $st = "c";
            $is_submit_account = true;

            //检测手机号码是否符合提现操作，不拆单
            $exchange_amount = $point / 100;
            $exchange_amount = $exchange_amount;
            if (in_array($exchange_amount, [20,30,50,100,200,300,500])) {//不存在符合条件的支付方式
                $is_phone_type = true;
            }
            if (isset($payment_type[EXCHANGE_MOBILE]) && $payment_type[EXCHANGE_MOBILE]) {//存在手机充值支付账号
                $is_phone_val = true;
            }
            //健康通官网code组装
            $gooddr_code = $member_uid."_".$pid."_".substr(md5($member_uid. "_" . $pid . "_".API_ACCOUNT_APP_ID_GOODDR."_".API_ACCOUNT_APP_KEY_GOODDR), 8, 16);
            //通过项目编号，获取项目信息
            $project_info = $this->db->query("SELECT * FROM app_project WHERE id=?", [$pid])->row_array();
            $is_show_payment = true;

            if($member_uid){
                /**
                 * 检测当前医师是否存在赛小欧项目中
                 * mb_sxo sxo_status-招募状态、has_sent=3-有效、wechat_number-微信号为空、participation-非参与
                 */
                $exist_sxo = $this->db->select('id')->where(['has_sent'=>3,'sxo_status'=>0,
                    'wechat_number'=>'','participation'=>3,'dr_id'=>$member_uid])->get('mb_sxo',1)->row_array();
                if(!empty($exist_sxo)){
                    //存在赛小欧项目中，显示活动页地址
                    $sxo_activity = $this->db->select('url')->where(['status'=>3,'sxo_id'=>$exist_sxo['id']])->get('mb_sxo_activity',1)->row_array();
                    if(!empty($sxo_activity['url'])){
                        $exist_sxo_url['url'] = $sxo_activity['url'];
                    }
                }

                //通过会员编号，查询用户的支付宝账号及微信账号信息
                //查询用户的支付账号
                $payment_info = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid=? AND payment_type in(".EXCHANGE_ALIPAY.", ".EXCHANGE_WEBCHAT_AUTO.", ".EXCHANGE_MOBILE.") ORDER BY id ASC", [$member_uid])->result_array();
                if ($payment_info) {
                    foreach ($payment_info as $v_payment) {
                        $user_payment_info[$v_payment['payment_type']] = $v_payment;
                        if ($v_payment['is_default'] == 1) {
                            $lock_account = $v_payment['payment_type'];//默认支付宝
                        }
                    }
                }
                ####    AMY 2022-07-06 支付账号存在，查询该账号是否绑定在别的账号名下，如果是，需要用户重置
                if ($user_payment_info) {
                    foreach ($user_payment_info as $k_p_t => $v_p_t) {
                        $check_p_a = $this->db->query("SELECT * FROM app_ex_payment_account WHERE uid != ? AND payment_type=? AND payment_account=?", [$member_uid, $k_p_t,$v_p_t['payment_account']])->row_array();
                        if ($check_p_a) {//支付账号已存在
                            file_put_contents('./tmp/payment_account_exist.txt', $member_uid."_".$k_p_t."_".$v_p_t['payment_account']."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                            unset($user_payment_info[$k_p_t]);
                            if ($lock_account == $k_p_t) {
                                $lock_account = "";
                            }
                        }
                    }
                }
                ####    AMY 2022-07-06 支付账号存在，查询该账号是否绑定在别的账号名下，如果是，需要用户重置

                //AMY 2022-12-31 正式上线使用网医微信小程序扫码
//                if ($lock_account == EXCHANGE_WEBCHAT_AUTO) {
                    if (isset($user_payment_info[EXCHANGE_WEBCHAT_AUTO]) && $user_payment_info[EXCHANGE_WEBCHAT_AUTO]['payment_from'] != 2) {
                        //需要扫网医小程序码的用户
                        file_put_contents('./tmp/idr_wx_qrcode.txt', $member_uid."_".$scene."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                        unset($user_payment_info[EXCHANGE_WEBCHAT_AUTO]);//需要重新授权网医小程序
                    }
//                }
            }
        }

        switch($st) {
            case 'c': //返回状态为c的成功样本
                $session_bk_project_id = (int)$this->session->userdata("bk_project_id");
                if ($session_bk_project_id == 5516 || $session_bk_project_id == 5713 || $session_bk_project_id == 6636) {
                    $st_msg_info = '恭喜老师，您已完成本次调研问卷！';
                } else {
                    $st_msg_info = $is_follow_up ? $lang_ary[LABEL_SURVEY_COMPLETE_FOLLOW_UP] : $lang_ary[LABEL_SURVEY_COMPLETE];
                }
                // AMY 2025-03-13 按负一要求调整的文案
                $st_msg_info = '恭喜您完成本次调研，后续我们会进行数据审核，如有疑问可能还需您的帮助，感谢您为医疗事业做出的贡献！';
                $st_msg = '<p>' . $st_msg_info . '</p>
            <img src="/theme/go/image/01.png" alt="">';
                break;
            case 's': //返回状态为s的成功样本
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_SCREEN_OUT] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'q': //返回状态为q的成功样本
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_QUOTA_FULL] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'p': //问卷链接在项目链接表中不存在
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_LINK_ERROR] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'npros'://回答问卷前品控未通过
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_NOT_CONFORM_TO] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'nquta'://项目的配额不存在
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_PROJECT_READY] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'qutasuc'://项目测试完毕返回状态
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_PROJECT_TEST_FINISH] . '</p>
            <img src="/theme/go/image/01.png" alt="">';
                break;
            case 'l_err': //项目不存在
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_LINK_OVERDUE] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'ne': //问卷链接不足，提醒表中记录
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_PROJECT_LINK_INSUFFICIENT] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'finish': //项目结束
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_PROJECT_FINISH] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'suspend': //项目暂停，稍后再试
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_PROJECT_SUSPEND] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'bl': //进入问卷的链接中合作伙伴id，不存在，或者该合作伙伴被禁用
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_NOT_AN_INVITATION] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'not_start': //项目未启动
                $st_msg = '<p>' . $lang_ary[LABEL_PROJECT_NOT_START] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'repeat': //重复参与项目，不能进入第二次
                $st_msg = '<p>' . $lang_ary[LABEL_SURVEY_REPEAT] . '</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 's_repeat': //重复进入有状态的流程
                $st_msg = '<p>您的操作太过频繁，请稍后再试！</p>
            <img src="/theme/go/image/04.png" alt="">';
                break;
            case 'cash_suc': //微信提现成功
                $st_msg = '<p>礼金申请成功!</p>
            <img src="/theme/go/image/03.png" alt="">';
                break;
            case 'payment_err': //提现链接有误
                $st_msg = '<p class="tips">链接无效</p><p>支付流程非常严格,请按常规操作!</p>
            <img src="/theme/go/image/03.png" alt="">';
                break;
            case 'p_s': //礼金支付成功【即时支付】
                if ($is_to_wy == 1 && $wy_reg_link) {
                    $st_msg = '<span style="text-align: left !important;padding: 0px 10px 10px;display: inline-block;">我司现研发了一款专门服务于医生的app“网医”。您可以在app上进行有奖问卷调研，病例分享，学术问答等，也可以看最新指南，研究分析，以及其他医生分享的指南解析，学术视频等。</span>';
                } else {
                    $st_msg = '<p class="tips">礼金支付成功</p>';
                    if ($fail_order_info) {
                        $st_msg .= '<p>请查看收款账户进行确认</p>';
                        $st_msg .= '<div style="text-align:left;padding:5px;">';
                        $st_msg .= '<p>支付结果：总共分'.$fail_order_info['payment_num'].'笔</p>';
                        $st_msg .= '<p>成功支付：'.$fail_order_info['success_num'].'笔</p>';
                        $st_msg .= '</div>';
                    } else {
                        $st_msg .= '<p>请查看收款账户进行确认</p>';
                    }
                    $st_msg .= '<img src="/theme/go/image/01.png" alt="">';
                }
                break;
            case 'p_s_p': //礼金支付成功【部分成功、部分失败】
                $st_msg = '<p class="tips">礼金支付情况</p>';
                if ($fail_order_info) {
                    $st_msg .= '<div style="text-align:left;padding:5px;">';
                    $st_msg .= '<p>支付结果：总共分'.$fail_order_info['payment_num'].'笔</p>';
                    $st_msg .= '<p>成功支付：'.$fail_order_info['success_num'].'笔</p>';
                    $st_msg .= '<p>失败支付：'.($fail_order_info['payment_num'] - $fail_order_info['success_num']).'笔</p>';
                    $st_msg .= '<p>【支付异常，后续人工核对后会继续处理】</p>';
                    $st_msg .= '</div>';
                } else {
                    $st_msg .= '<p>请查看收款账户进行确认</p>';
                }
                $st_msg .= '<img src="/theme/go/image/01.png" alt="">';
                break;
            case 'p_s_a': //礼金支付中【ACCEPTED：支付已受理】
                $st_msg = '<p class="tips">微信支付已受理</p>';
                if ($fail_order_info) {
                    $st_msg .= '<div style="text-align:left;padding:5px;">';
                    $st_msg .= '<p>支付笔数：总共分'.$fail_order_info['payment_num'].'笔</p>';
                    $st_msg .= '<p>【微信支付已受理，请【10~15分钟后】查看收款账户进行确认】</p>';
                    $st_msg .= '</div>';
                } else {
                    $st_msg .= '<p>请查看收款账户进行确认</p>';
                }
                $st_msg .= '<img src="/theme/go/image/01.png" alt="">';
                break;
            case 'p_s_b': //申请礼金成功【银行】
                $st_msg = '<p class="tips">申请礼金成功</p>
                <p>选择银行收款7个工作日到账</p>
                <img src="/theme/go/image/01.png" alt="">';
                break;
            case 'p_d': //操作过于频繁
                $st_msg = '<p class="tips">操作过于频繁</p>
                <p>请耐心等待,2分钟后重新操作!</p>
                <img src="/theme/go/image/03.png" alt="">';
                break;
            case 'p_f': //付款失败
                $failure_cause_msg = "非本人认证账户需重新更换账户";
                if ($fail_order_info && $fail_order_info['failure_cause']) {
//                    if (strpos($fail_order_info['failure_cause'], "余额不足") === false) {
//                        $failure_cause_msg = $fail_order_info['failure_cause'];
//                    }
                    if (strpos($fail_order_info['failure_cause'], "真实姓名不一致") !== false || strpos($fail_order_info['failure_cause'], "收款账号不存在或姓名有误") !== false) {
                        $failure_cause_msg = $fail_order_info['failure_cause'];
                    }
                }
                $st_msg = '<p class="tips">付款失败</p>
                <!--<p>收款账户信息有误</p>-->
                <p style="color:red;">'.$failure_cause_msg.'</p>
                <p>请点击按钮重新设置</p>
                <img src="/theme/go/image/02.png" alt="">';
                break;
            case 'p_s_don_be_polite': //不要礼金
                $st_msg = '<p class="tips">问卷完成</p>
                <p>健康通感谢您的支持！</p>
                <img src="/theme/go/image/01.png" alt="">';
                break;
            case 'zb': //甄别提示：用于自定义问卷返回
                $st_msg = '<p>非常感谢您的时间与回答，很抱歉您未能符合本次研究的条件，希望下次能再次得到您的支持与配合！！</p>
                <img src="/theme/go/image/02.png" alt="">';
                break;
            case 'fail_order_suc': //失败订单账号信息提交成功
                $doctor_name = $this->input->get("doctor_name", true);
                $st_msg = '<p class="status" style="font-size:16px;padding: 10px 10px; text-align: left;">
        尊敬的' . $doctor_name . '：
          <span style="display:block;padding-top: 30px;">
            感谢您的参与和支持，您的收款账号设置成功，礼金会尽快打入您账号中，支付成功时，将会有短信提醒，如有任何问题，请在线联系客服。
        </span>
         <span style="display:block;padding-top: 30px;"> 健康通再次感谢您的支持！</span>

        </p>';
                break;
            case 'qw_project_list_fail': // 项目列表链接错误
                $st_msg = '<p>页面错误，请联系客服</p>
                <img src="/theme/go/image/04.png" alt="">';
                break;
            default: //除以上之外的告警
                $st_msg = $lang_ary[LABEL_SURVEY_DEFAULT];
                break;
        }
//        switch($st) {
//            case 'c': //返回状态为c的成功样本
//                $st_msg = $is_follow_up ? $lang_ary[LABEL_SURVEY_COMPLETE_FOLLOW_UP] : $lang_ary[LABEL_SURVEY_COMPLETE];
////                $st_msg = "恭喜您成功完成问卷";
//                break;
//            case 's': //返回状态为s的成功样本
//                $st_msg = $lang_ary[LABEL_SURVEY_SCREEN_OUT];
////                $st_msg = "很遗憾您不符合问卷甄别条件";
//                break;
//            case 'q': //返回状态为q的成功样本
//                $st_msg = $lang_ary[LABEL_SURVEY_QUOTA_FULL];
////                $st_msg = "很遗憾您所在的配额组已满";
//                break;
//            case 'p': //问卷链接在项目链接表中不存在
//                $st_msg = $lang_ary[LABEL_SURVEY_LINK_ERROR];
//                break;
//            case 'npros'://回答问卷前品控未通过
//                $st_msg = $lang_ary[LABEL_SURVEY_NOT_CONFORM_TO];
//                break;
//            case 'nquta'://项目的配额不存在
//                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_READY];//外部资源时，需要甄别问卷，或者项目在准备阶段
//                break;
//            case 'qutasuc'://项目测试完毕返回状态
//                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_TEST_FINISH];//甄别问卷测试结束
//                break;
//            case 'l_err': //项目不存在
//                $st_msg = $lang_ary[LABEL_SURVEY_LINK_OVERDUE];
//                break;
//            case 'ne': //问卷链接不足，提醒表中记录
//                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_LINK_INSUFFICIENT];
//                break;
//            case 'finish': //项目结束
//                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_FINISH];
//                break;
//            case 'suspend': //项目暂停，稍后再试
//                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_SUSPEND];
//                break;
//            case 'bl': //进入问卷的链接中合作伙伴id，不存在，或者该合作伙伴被禁用
//                $st_msg = $lang_ary[LABEL_SURVEY_NOT_AN_INVITATION];
//                break;
//            case 'not_start': //项目未启动
//                $st_msg = $lang_ary[LABEL_PROJECT_NOT_START];
//                break;
//            case 'repeat': //重复参与项目，不能进入第二次
//                $st_msg = $lang_ary[LABEL_SURVEY_REPEAT];
//                break;
//            case 'cash_suc': //微信提现成功
////                $st_msg = $lang_ary[LABEL_SURVEY_REPEAT];
//                $st_msg = "兑换申请成功";
//                break;
//            case 'fail_order_suc': //失败订单账号信息提交成功
//                $doctor_name = $this->input->get("doctor_name", true);
//                $st_msg = '<p class="status" style="font-size:22px;padding: 10px 0; text-align: left;">
//        尊敬的'.$doctor_name.'：
//          <span style="display:block;padding-top: 30px;">
//            感谢您的参与和支持，您的收款账号设置成功，礼金会尽快打入您账号中，支付成功时，将会有短信提醒，如有任何问题，请在线联系客服。
//        </span>
//         <span style="display:block;padding-top: 30px;"> 健康通再次感谢您的支持！</span>
//
//        </p>';
//                break;
//            default: //除以上之外的告警
//                $st_msg = $lang_ary[LABEL_SURVEY_DEFAULT];
//                break;
//        }
        $data = array(
            "st_msg" => $st_msg,
            "scene" => $scene,
            "filename" => $filename,
            "point" => $point,
            "payment_type" => $payment_type,
            "st" => $st,
            "pid" => $pid,
            "is_submit_account" => $is_submit_account,
            "is_phone_type" => $is_phone_type,
            "is_phone_val" => $is_phone_val,
            "gooddr_code" => $gooddr_code,
            "project_info" => $project_info,
            "is_show_payment" => $is_show_payment,

            "bk_code" => $bk_code ?? "",
            "survey_uid_code" => $survey_uid_code ?? "",
            'exist_sxo_url'=>$exist_sxo_url,

            'user_payment_info' => $user_payment_info,
            'lock_account' => $lock_account,
            'fail_scene' => $fail_scene,
            'wy_reg_link' => $wy_reg_link,
            'is_to_wy' => $is_to_wy,
            'reg_link_info' => $reg_link_info,//网医注册说明页
            'imp_name' => $imp_name,//提现名称,不能修改,医师本人
        );
//        if ($member_uid == 2982098) {
//            $this->load->view("/bk/to_wx_n.php",$data);
//        } else {
//            $this->load->view("/bk/to_wx.php",$data);
//        }
//        if ($member_uid == 2982098) {
//            if ($code) {
//                $this->load->view("/bk/to_wx_finish.php",$data);
//            } else {
//                $this->load->view("/bk/to_wx_new.php",$data);
//            }
//        } else {
//            $this->load->view("/bk/to_wx_n.php",$data);
//        }
        //AMY 2021-01-22 15:32 上线支付新流程
        if ($code) {
            $this->load->view("/bk/to_wx_finish.php",$data);
        } else {
            $this->load->view("/bk/to_wx_new.php",$data);
        }
    }

    ####    AMY 2020-10-26 短信验证通过后，才能进入提现页面
    public function pro_send_sms()
    {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $bk_code = $post_data['bk_code'];
            $survey_uid_code = $post_data['survey_uid_code'];
            $verify_mobile = $post_data['verify_mobile'];
            try {
                $country = 68;
                $get_code_param = $this->account_validity($bk_code, $survey_uid_code, true);
                if (!$get_code_param) {
                    throw new Exception("参数有误！");
                }
                $pid = $get_code_param['pid'];
                $survey_uid = $get_code_param['survey_uid'];
                $member_uid = $get_code_param['member_uid'];
                $m_id = $get_code_param['m_id'];

                //member_uid与pid，查询项目执行表中，是否存在此用户信息
                $check_implement = getDataByConditionCi("app_project_implement_{$pid}", " AND survey_uid=? AND finish_status='c'","*", true, [$survey_uid]);
                if (!$check_implement) {
                    throw new Exception("用户信息不存在！");
                }
                //查询是否已经做过提现操作
                $payment_info = getDataByConditionCi("app_payment_order_new", " AND log_code='".ORDER_SOURCE_STATUS_PROJECT."' AND pid=? AND uid=?","*", true, [$pid, $member_uid]);
                if ($payment_info && $payment_info['status'] == 2) {//订单已存在
                    throw new Exception("已存在提现记录，不能重复提现！");
                }
                if (!check_mobile($check_implement['mobile'])) {
                    throw new Exception("手机号有误，请确认后重新输入！[项目]");
                }
                if ($check_implement['mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入");
                }

                //发送短信验证码
                $vcode = rand(10000,99999);
                $sms_log_code = SMS_LOG_CODE_BK_EXCHANGE;
                $content = $lang_ary[LABEL_COMM_MOB_TEMPLATE];//国际短信通道使用
                $err_msg = "";
                if ($country == '68') {//中国短信，使用send_cloud发送
                    //创蓝
                    if ($_SERVER['HTTP_HOST'] == "local.drsay.cn" || (($$verify_mobile == "18350217317") && $pid == 1675)) {//本地
                        //手机号码校验的流程，暂时用于微信支付接口申请使用，申请完以后，要撤掉
                        $vcode = 66666;
                        $st = true;
                    } else {
                        // $sms_content = "上医说验证码:{$vcode}，".SMS_ACTIVE_TIME."分钟内有效，请勿泄漏！";//国际短信通道使用
                        // $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);

                        //todo:接高富帅的统一短信接口
                        $sendData = [
                            'mobile' => $verify_mobile,
                            "template_data" => [$vcode],

                            "other_param" => [
                                "log_code" => $sms_log_code,
                            ],
                        ];

                        $templateId =  '【健康通】上医说验证码:{$var}，5分钟内有效，请勿泄漏！';// 签名要带上，否则会用这个发送账号的默认签名去发，比如这个账号绑定的默认签名是【医师定考】，不带的话，显示的就是【医师定考】上医说验证码:{$var}，5分钟内有效，请勿泄漏！

                        $resSms = $this->wfunctions->sendSms($sendData, $templateId);
                        $resSmsArr = $resSms ? json_decode($resSms, true) : [];
                        if (isset($resSmsArr['code']) && $resSmsArr['code'] == 200) {
                            $st = true;
                        } else {
                            $st = false;
                        }
                    }
                    if ($st === true) {
                        $send_st = true;
                    } else {
                        $send_st = false;
                        $err_msg = $st;
                    }
                }else{//国外短信，使用国际短信通道
                    $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                    $area = getDataByID("app_sys_dictionary", $country);
                    $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                    if ($st == "OK") {
                        $send_st = true;
                    }else{
                        $send_st = false;
                    }
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_EXCHANGE;
                if($send_st) {//短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);
                    if($insert_id_sms_code > 0) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    //提现短信验证
    public function exchange_sms_sub() {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            $bk_code = $post_data['bk_code'];
            $survey_uid_code = $post_data['survey_uid_code'];
            $verify_mobile = $post_data['verify_mobile'];
            //验证数据有效性
            $verify_code = $post_data['verify_code'];

            // 后续问卷简短确认参与意愿
            $is_join = $post_data['is_join'] ?? '';// 参与意愿
            $join_time = $post_data['join_time'] ?? '';// 参与时间
            $join_type = $post_data['join_type'] ?? [];// 参与方式

            try {
                file_put_contents('./tmp/exchange_sms_sub.txt',json_encode($post_data, JSON_UNESCAPED_UNICODE)."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                $get_code_param = $this->account_validity($bk_code, $survey_uid_code, true);
                if (!$get_code_param) {
                    throw new Exception("参数有误！");
                }
                $pid = $get_code_param['pid'];
                $survey_uid = $get_code_param['survey_uid'];
                $member_uid = (int)trim($get_code_param['member_uid']);
                $m_id = $get_code_param['m_id'];
                if ($member_uid <= 0) {
                    throw new Exception("用户不存在，请联系管理员处理！");
                }

                //member_uid与pid，查询项目执行表中，是否存在此用户信息
                $check_implement = getDataByConditionCi("app_project_implement_{$pid}", " AND survey_uid=? AND finish_status='c'","*", true, [$survey_uid]);
                if (!$check_implement) {
                    throw new Exception("用户信息不存在！");
                }

                if (!check_mobile($check_implement['mobile'])) {
                    throw new Exception("手机号有误，请确认后重新输入！[项目]");
                }
                if ($check_implement['mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $member_info = $this->db->query("SELECT * FROM app_project_payment_confirm_user_info WHERE member_uid=? LIMIT 1", [$member_uid])->row_array();
                // $is_agree = $post_data['is_agree'];//【您是否愿意授权健康通公开您的真实姓名以满足健康通的终端向健康通发起的审计需求？】
                // $is_agree = $is_agree ? $is_agree : 0;
                // if ($is_agree <= 0) {
                //     throw new Exception("请选择【您是否愿意授权健康通公开您的真实姓名以满足健康通的终端向健康通发起的审计需求？】！");
                // }

                $project_evaluation = $post_data['project_evaluation'];//项目评价
                $project_evaluation = $project_evaluation ? $project_evaluation : "";
                if (!$member_info) {//不存在记录，添加入库
                    $province = $post_data['province'];
                    $city = $post_data['city'];
                    $dr_name = $post_data['dr_name'];
                    $unit_name = $post_data['unit_name'];
                    $indentity_code = $post_data['indentity_code'];
                    $department = $post_data['department'];
                    if ($province <= 0) {
                        _back_msg("error", "请选择省份！");
                    }
                    $area_id = $province;
                    if ($city <= 0) {
                        _back_msg("error", "请选择城市！");
                    }
                    $area_id = $area_id."->".$city;
                    //通过省份城市区域获取对应的文字信息
                    $area_id = DIC_COUNTRY_CHINA_ID."->".$area_id;
                    $area_info = $this->db->query("SELECT * FROM app_sys_dictionary WHERE big_class_id='".APP_SYS_BIG_CLASS_AREA."' AND area_parent_id=?", [$area_id])->row_array();
                    if (!$area_info) {
                        _back_msg("error", "地区信息选择有误，请重新选择！");
                    }
                    $area_info_content = $area_info['area_parent_name'];
                    $arr_area_info = explode("->", $area_info_content);
                    if (!$dr_name) {
                        _back_msg("error", "请输入姓名！");
                    }
                    if (!$unit_name) {
                        _back_msg("error", "请输入医院！");
                    }
                    if (!$indentity_code) {
                        _back_msg("error", "请输入身份证号！");
                    }
                    if (strlen($indentity_code) != 18 && strlen($indentity_code) != 15) {
                        _back_msg("error", "身份证号格式错误,请重新输入！");
                    }
                    if (!$department) {
                        _back_msg("error", "请输入科室！");
                    }

                    if (!$verify_code) {
                        _back_msg("error", "请输入验证码！");
                    }
                    $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_EXCHANGE);
                    if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                        _back_msg("error", $lang_ary[LABEL_EXCHANGE_VERIFICATION_CODE_ERROR]);
                    }

                    //身份证+姓名有效性验证
                    if ($_SERVER['HTTP_HOST'] != "local.drsay.cn") {//非本地
                        $check_name_id_card = $this->check_name_id_card($dr_name, $indentity_code);
                        if ($check_name_id_card !== SYS_TWO_ELEMENTS_RESULT_AGREEMENT) {
                            file_put_contents('./tmp/exchange_sms_sub_name_id_card_fail.txt',json_encode($post_data, JSON_UNESCAPED_UNICODE)."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                            _back_msg("error", "身份证与姓名不匹配，请确认后重新输入！");
                        }
                    }

                    //根据身份证号码查询医师表是否存在记录
                    $insert_data = [
                        "province" => isset($arr_area_info[1]) && $arr_area_info[1] ? $arr_area_info[1] : "",
                        "city" => isset($arr_area_info[2]) && $arr_area_info[2] ? $arr_area_info[2] : "",
                        "member_uid" => $member_uid,
                        "name" => $dr_name,
                        "unit_name" => $unit_name,
                        "id_card" => $indentity_code,
                        "mobile" => $verify_mobile,
                        "department" => $department,
                        "add_time" => time(),

                        "is_join" => $is_join,// 页面回显以它为主
                        "join_time" => $join_time,// 页面回显以它为主
                        "join_type" => $join_type ? implode(",", $join_type) : "",// 页面回显以它为主
                    ];
                    $res_member = $this->db->insert("app_project_payment_confirm_user_info", $insert_data);
                    if (!$res_member) {
                        throw new Exception("提交失败[u]！");
                    }

                    $local_ip = getip();
                    $ip = ip2long($local_ip);
                    $ip_addr = ip2location($ip);
                    $insert_data_log = [
                        "pid" => $pid ? $pid : "",
                        "pid_id" => $m_id ? $m_id : 0,
                        "province" => isset($arr_area_info[1]) && $arr_area_info[1] ? $arr_area_info[1] : "",
                        "city" => isset($arr_area_info[2]) && $arr_area_info[2] ? $arr_area_info[2] : "",
                        "member_uid" => $member_uid,
                        "survey_uid" => $survey_uid,
                        "name" => $dr_name,
                        "unit_name" => $unit_name,
                        "id_card" => $indentity_code,
                        "mobile" => $verify_mobile,
                        "department" => $department,
                        "add_time" => time(),
                        "ip" => $local_ip,
                        "ip_address" => $ip_addr,
                        "project_evaluation" => $project_evaluation,
                        // "is_agree" => $is_agree,

                        "is_join" => $is_join,// 页面回显以它为主
                        "join_time" => $join_time,// 页面回显以它为主
                        "join_type" => $join_type ? implode(",", $join_type) : "",// 页面回显以它为主
                    ];

                    $res_data = $this->db->insert("app_project_payment_confirm_log", $insert_data_log);
                    if (!$res_data) {
                        throw new Exception("提交失败[a_l]！");
                    }
                } else {
                    if (!$verify_code) {
                        _back_msg("error", "请输入验证码！");
                    }
                    $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_EXCHANGE);
                    if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                        _back_msg("error", $lang_ary[LABEL_EXCHANGE_VERIFICATION_CODE_ERROR]);
                    }
                    $isSame = ([] === array_diff($join_type, $member_info['join_type'])) && ([] === array_diff($member_info['join_type'], $join_type));
                    if (!empty($is_join) || !empty($join_time) || !empty($join_type)) {
                        if (strcmp($is_join, $member_info['is_join']) != 0 || strcmp($join_time, $member_info['join_time']) != 0 || !$isSame) {
                            $this->db->update("app_project_payment_confirm_user_info", [
                                "is_join" => $is_join,// 页面回显以它为主
                                "join_time" => $join_time,// 页面回显以它为主
                                "join_type" => $join_type ? implode(",", $join_type) : "",// 页面回显以它为主
                            ], ["id" => $member_info['id']]);
                        }
                    }
                    $project_payment_log = $this->db->query("SELECT * FROM app_project_payment_confirm_log WHERE member_uid=? AND pid>0 AND pid=? LIMIT 1", [$member_uid, $pid])->row_array();
                    if (!$project_payment_log) {
                        $local_ip = getip();
                        $ip = ip2long($local_ip);
                        $ip_addr = ip2location($ip);
                        $doctor_province = $member_info['province'];
                        $doctor_city = $member_info['city'];
                        $doctor_name = $member_info['name'];
                        $doctor_unit_name = $member_info['unit_name'];
                        $doctor_id_card = $member_info['id_card'];
                        $doctor_department = $member_info['department'];
                        $insert_data_log = [
                            "pid" => $pid ? $pid : "",
                            "pid_id" => $m_id ? $m_id : 0,
                            "province" => $doctor_province ? $doctor_province : "",
                            "city" => $doctor_city ? $doctor_city : "",
                            "member_uid" => $member_uid,
                            "survey_uid" => $survey_uid,
                            "name" => $doctor_name ? $doctor_name : "",
                            "unit_name" => $doctor_unit_name ? $doctor_unit_name : "",
                            "id_card" => $doctor_id_card ? $doctor_id_card : "",
                            "mobile" => $verify_mobile,
                            "department" => $doctor_department ? $doctor_department : "",
                            "add_time" => time(),
                            "ip" => $local_ip,
                            "ip_address" => $ip_addr,
                            "project_evaluation" => $project_evaluation,
                            // "is_agree" => $is_agree,

                            "is_join" => $is_join,// 页面回显以它为主
                            "join_time" => $join_time,// 页面回显以它为主
                            "join_type" => $join_type ? implode(",", $join_type) : "",// 页面回显以它为主
                        ];

                        $res_data = $this->db->insert("app_project_payment_confirm_log", $insert_data_log);
                        if (!$res_data) {
                            file_put_contents('./tmp/exchange_sms_sub_new.txt',$this->db->last_query()."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                            throw new Exception("提交失败[e_l]！");
                        }
                    } else {
                        $this->db->update("app_project_payment_confirm_log", [
                            "project_evaluation" => $project_evaluation,
                            // "is_agree" => $is_agree,

                            "is_join" => $is_join,// 只作为日志存储痕迹，不作为业务逻辑
                            "join_time" => $join_time,// 只作为日志存储痕迹，不作为业务逻辑
                            "join_type" => $join_type ? implode(",", $join_type) : "",// 只作为日志存储痕迹，不作为业务逻辑
                        ], ["id" => $project_payment_log['id']]);
                    }
                }

                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                //查询订单状态
                $redirect_url = "/";
                $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE log_code=? AND pid=? AND pid_id=? AND order_status=1 LIMIT 1", [ORDER_SOURCE_STATUS_PROJECT, $pid, $m_id])->row_array();
                if ($order_info) {//已经存在订单
                    if ($order_info['order_status'] != 1) {//不是有效订单,跳到链接无效
                        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "payment_err", "", "", "", false);
                    }
                    if ($order_info['status'] == 2) {//已完成
                        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "p_s", "", "", "", false);
                    } else if ($order_info['status'] == 5) {//直接跳转到支付流程
                        //跳到即时支付流程
                        $this->session->set_userdata(["auto_payment_order_id" => $order_info['id']]);
                        $decrypt_scene = "order_{$order_info['id']}";
                        $auto_payment_scene = $order_info['id']."_".substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
                        $redirect_url = "/project_exchange/auto_payment?scene=".$auto_payment_scene;
                    } else if ($order_info['status'] == 6) {//直接跳转到支付流程
                        //跳到即时支付流程
                        $this->session->set_userdata(["auto_payment_order_id" => $order_info['id']]);
                        $decrypt_scene = "order_{$order_info['id']}";
                        $auto_payment_scene = $order_info['id']."_".substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
                        $redirect_url = "/project_exchange_n/auto_payment?scene=".$auto_payment_scene;
                    } else if ($order_info['status'] == 3) {//支付失败，直接跳转到支付修改流程
                        $this->session->set_userdata(["modify_payment_order_id" => $order_info['id']]);
                        $decrypt_scene = "order_{$order_info['id']}";
                        $modify_payment_scene = $order_info['id']."_".substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
//                        $redirect_url = "/project_exchange/modify_payment/?scene={$modify_payment_scene}";
                        $redirect_url = "/project_exchange_n/modify_payment/?scene={$modify_payment_scene}";
                    }
                } else {
                    //存session，方便从短信或者问卷链接进入时，直接跳过验证
                    $this->session->set_userdata(["bk_exchange_{$pid}_{$m_id}" => $member_uid]);
                    $sys_id = (int)trim($this->session->userdata('project_sys_id'));
                    $sys_id = $sys_id ? $sys_id : 1;
                    $redirect_url = "/bk/rs/{$bk_code}/{$survey_uid_code}/{$sys_id}";
                }
                _back_msg("success", "验证通过", $redirect_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $bk_code = $this->uri->segment(3);
            $survey_uid_code = $this->uri->segment(4);

            $sys_id = (int)trim($this->uri->segment(5));
            $sys_id = $sys_id > 0 ? $sys_id : 1;

//            $this->session->unset_userdata('project_sys_id');
            if (in_array($sys_id, [7,8,9])) {//是网医
                $this->session->set_userdata(["project_sys_id" => $sys_id]);
            }

            //验证账号是否有误
            $get_code_param = $this->account_validity($bk_code, $survey_uid_code);
            if (!$get_code_param) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            }
            $pid = $get_code_param['pid'];
            $member_uid = $get_code_param['member_uid'];
            $m_id = $get_code_param['m_id'];
            $imp_mobile = $get_code_param['imp_mobile'];//执行表里的医师手机号码
            $imp_name = $get_code_param['imp_name'];//执行表里的医师姓名
            //通过pid及uid查询是否已经做过提现操作
            $check_detail = getDataByConditionCi("app_member_point_detail", " AND log_code in(".POINT_CHANGE_CODE_SURVEY.",".POINT_REISSUE_INTEGRAL.") AND finish_status='c' AND param=? AND uid=?","*", true, [$pid, $member_uid]);
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if (!$check_detail) {//信息不存在
                redirect($redirect_url);
            }
            if ($check_detail['m_pay_type'] == DON_BE_POLITE) {//不要礼金
                $this->survey_model->get_redirect_info("/bk/rs", "p_s_don_be_polite");
            }

            $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE log_code=? AND pid=? AND pid_id=? LIMIT 1", [ORDER_SOURCE_STATUS_PROJECT, $pid, $m_id])->row_array();
            if ($order_info) {//已经存在订单
                if ($order_info['status'] == 2) {//已完成
                    $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "p_s", "", "", "", false);
                    redirect($redirect_url);
                }
            }

//            if ($check_detail['apply_status'] == 1) {//已提交过支付申请
//                $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "cash_suc", "", "", "", false);
//                redirect($redirect_url);
//            }

            $session_bk_exchange = $this->session->userdata("bk_exchange_{$pid}_{$m_id}");
            if ($session_bk_exchange && $session_bk_exchange == $member_uid) {
                redirect("/bk/rs/{$bk_code}/{$survey_uid_code}/{$sys_id}");
            }

            $member_info = $this->db->query("SELECT * FROM app_project_payment_confirm_user_info WHERE member_uid=? LIMIT 1", [$member_uid])->row_array();

            //获取省份信息
            $province_list = $this->db->query("SELECT * FROM app_sys_dictionary WHERE big_class_id='".APP_SYS_BIG_CLASS_AREA."' AND `level`='".AREA_LEVEL_PROVINCE."' AND area_parent_id LIKE '68_%'")->result_array();

            $project_payment_log = $this->db->query("SELECT * FROM app_project_payment_confirm_log WHERE member_uid=? AND pid>0 AND pid=? LIMIT 1", [$member_uid, $pid])->row_array();
            $res_member_info = $this->db->query("SELECT a.id,b.name FROM app_member a left join app_unit b on a.unit_id=b.id WHERE a.id=? AND a.unit_id>0", [$member_uid])->row_array();
            $data = array(
                "province_list" => array_column($province_list, "val", "id"),
                "bk_code" => $bk_code,
                "survey_uid_code" => $survey_uid_code,
                "member_info" => $member_info,
                "join_type" => $member_info && !empty($member_info['join_type']) ? explode(",", $member_info['join_type']) : [],
                "imp_mobile" => $imp_mobile,
                "imp_name" => $imp_name,
                "imp_unit_name" => $res_member_info && $res_member_info['name'] ? $res_member_info['name'] : "",
                "project_payment_log" => $project_payment_log,
                "pid" => $pid,
            );

//            //AMY 2021-01-22 15:35上线
//            $this->load->view("/bk/exchange_send_sms_n",$data);

            //AMY 2022-08-03 样式调整，增加单选题【您是否愿意授权健康通公开您的真实姓名以满足健康通的终端向健康通发起的审计需求？】
            $this->load->view("/bk/exchange_send_sms",$data);
        }
    }

    //获取省份城市区域联动信息
    public function get_city_district()
    {
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_data($post_data);
            $parent_id = (int)$post_data['parent_id'];
            if ($parent_id <= 0) {
                _back_msg("error", "参数错误！");
            }
            $area_info = $this->db->query("SELECT id,val FROM app_sys_dictionary WHERE big_class_id='".APP_SYS_BIG_CLASS_AREA."' AND pid={$parent_id}")->result_array();
            if (!$area_info) {
                _back_msg("error", "地区信息错误！");
            }
            _back_msg("success", $area_info);
        }
    }

    //身份证+姓名，二要素信息有效性验证
    protected function check_name_id_card($name, $id_card, $source = SYS_TWO_ELEMENTS_SOURCE_DRSAY)
    {
        $this->load->file(BASEPATH . 'libraries/Third_party/253_drsay_invite/Two_elements_api.php');
        $two_elements = new Two_elements_api();
        $result = $two_elements->two_elements($name, $id_card, $source);
        return $result;
    }

    // 保存验证码数据
    private function set_vcode($data)
    {
        if (!$data) {return false;}
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }
    ####    AMY 2020-10-26 短信验证通过后，才能进入提现页面


    //授权提现
    public function privacy_policy_exchange()
    {
        $this->load->view("/bk/privacy_policy_exchange", []);
    }


    //邀请
    public function invitation()
    {
        $data = array();
        $this->load->view("/bk/invitation.php",$data);
    }

    //邀请
    public function invite_qrcode()
    {
        $data = array();
        $this->load->view("/bk/invite_qrcode.php",$data);
    }

    /**
     * @param $partner_uid
     * @param $redirect_url
     * @return string
     */
    public function url_partner($partner_uid,$redirect_url){
        if(!$partner_uid || !$redirect_url){
            set_sys_warning("", "", "", $partner_uid, $redirect_url);
            return  '/';
        }
        if($partner_uid){
            $res_sub = substr($partner_uid,0,1);
            $res_redirect_url = explode('?',$redirect_url);
            $res_redirect_url = array_filter($res_redirect_url);
            if($res_sub == '&'){
                if(count($res_redirect_url) > 1){
                    $redirect_url .= $partner_uid;
                }
            } else {
                if(count($res_redirect_url) > 1){
                    $redirect_url .= '&'.$partner_uid;
                } else {
                    $redirect_url .= "?".$partner_uid;
                }
            }
        }
        return $redirect_url;
    }

    //项目以礼品进行兑换，项目完成并且获得c的，需要收集用户的地址等相关信息
    public function get_address()
    {
        //http://www.jq22.com/demo/jquery-gd-************/
        $bk_code = $this->uri->segment(3);
        $survey_uid_code = $this->uri->segment(4);
        //验证账号是否有误
        $get_code_param = $this->account_validity($bk_code, $survey_uid_code, true);
        if (!$get_code_param) {redirect("/go/thanks?st=l_err");}
        $pid = $get_code_param['pid'];
        $survey_uid = $get_code_param['survey_uid'];
        $st = $get_code_param['st'];
        $lang_ary = $get_code_param['lang_ary'];
        $country = $get_code_param['country'];
        //通过国家获取国家对应的省市区
        if ($country) {
//            if ($country == DIC_COUNTRY_CHINA_ID) {//中国，获取省份
//
//            }
            $province_info = get_translate_by_condition(" big_class_id='".APP_SYS_BIG_CLASS_AREA."' AND pid='".DIC_COUNTRY_CHINA_ID."'");
        }

        //状态是c,把对应积分的礼品扫描出来
        //查询是否已经记录过地址，，直接跳转到问卷，否则就跳转
        $get_pro_prizes_log = $this->db->query("SELECT * FROM app_project_prizes_log WHERE pid='{$pid}' AND survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        $res_point_info = $this->price_to_point($pid, $survey_uid, $st, false);
        $point_info = $res_point_info['point_info'];
        $open_c = $res_point_info['openc'];
        if (!$res_point_info) {
            set_sys_warning($pid, "", "", "", $lang_ary[LABEL_SURVEY_PRIZES_POINT_CURRENCY_ERROR].$survey_uid);
            redirect("/go/thanks?st=l_err");
        }
        if ($get_pro_prizes_log) {//如果已经记录过，跳转到外包设置的相关地址
            header("Location:".$open_c);
//            redirect($open_c);
        } else {// 如果没有记录过，显示选择礼品页
            $get_prizes_info = $this->db->query("SELECT * FROM app_prizes WHERE prize_type = '2' AND  status='1' AND prize_point<='{$point_info}' AND prize_photo!='' LIMIT 10")->result_array();
            //通过
            $data = array(
                'title' => $lang_ary[LABEL_EXCHANGE_SIGN],
                'get_prizes_info' => $get_prizes_info,
                'bk_code' => $bk_code,
                'survey_uid_code' => $survey_uid_code,
                'lang_ary' => $lang_ary,
                'province_info' => myselect($province_info, "province", "", false, ' onchange="get_area_info(this.value, \'city\')"', $lang_ary[LABEL_PROVINCE]),
            );
            $this->load->view('/bk/get_address', $data);
        }
    }

    //获取地区信息
    public function get_area_info()
    {
        $post_data = $this->input->post();
        //过滤空值
        $rs_post_data = format_post_data($post_data);
        $pid = $rs_post_data['pid'];
        $div_sign = $rs_post_data['div_sign'];
        $bk_code = isset($rs_post_data['bk_code']) ? $rs_post_data['bk_code'] : "";
        $survey_uid_code = isset($rs_post_data['survey_uid_code']) ? $rs_post_data['survey_uid_code'] : "";
        //验证账号是否有误
        $get_code_param = $this->account_validity($bk_code, $survey_uid_code, true);
        if (!$get_code_param) {
            _back_msg("error", "", "/go/thanks?st=l_err");
        }
        $lang_ary = $get_code_param['lang_ary'];
        if (!$pid || !$div_sign || !in_array($div_sign, array('city', 'district'))) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_AREA_ERROR]);
        } else {
            $area_info = get_translate_by_condition(" big_class_id='".APP_SYS_BIG_CLASS_AREA."' AND pid='".$pid."'");
            $next_change = "";
            $default_select = "";
            if ($div_sign == "city") {//城市
                $default_select = $lang_ary[LABEL_CITY];
                $next_change = ' onchange="get_area_info(this.value, \'district\')"';
            }
            if ($div_sign == "district") {//区域
                $default_select = $lang_ary[LABEL_DISTRICT];
            }
            $info = myselect($area_info, $div_sign, "", false, $next_change, $default_select);
            _back_msg("success", $info);
        }

    }

    public function act_get_address()
    {
        $post_data = $this->input->post();
        //过滤空值
        $res_post_data = format_post_data($post_data);
        $rs_post_data = $res_post_data;
        $bk_code = isset($res_post_data['bk_code']) ? $res_post_data['bk_code'] : "";
        $survey_uid_code = isset($res_post_data['survey_uid_code']) ? $res_post_data['survey_uid_code'] : "";
        $prize_id = isset($res_post_data['prize_id']) ? $res_post_data['prize_id'] : "";
        $consignee = isset($res_post_data['consignee']) ? $res_post_data['consignee'] : "";
        $receiving_address = isset($res_post_data['receiving_address']) ? $res_post_data['receiving_address'] : "";
        $mobile = isset($res_post_data['mobile']) ? $res_post_data['mobile'] : "";
        $id_number = isset($res_post_data['id_number']) ? $res_post_data['id_number'] : "";
        $province = isset($res_post_data['province']) ? $res_post_data['province'] : "";
        $city = isset($res_post_data['city']) ? $res_post_data['city'] : "";
        $district = isset($res_post_data['district']) ? $res_post_data['district'] : "";
        //验证账号是否有误
        $get_code_param = $this->account_validity($bk_code, $survey_uid_code, true);
        if (!$get_code_param) {
            _back_msg("error", "", "/go/thanks?st=l_err");
        }
        $pid = $get_code_param['pid'];
        $survey_uid = $get_code_param['survey_uid'];
        $member_uid = $get_code_param['member_uid'];
        $lang_ary = $get_code_param['lang_ary'];
        //查询礼品是否已经领过
        $res_pro_prizes_log = $this->db->query("SELECT * FROM app_project_prizes_log WHERE pid='{$pid}' AND survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        if ($res_pro_prizes_log) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_IS_RECEIVE]);
        }

        if (!$prize_id) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_NOT_SELECT]);
        }
        //查询礼品是否存在
        $res_prizes = $this->db->query("SELECT * FROM app_prizes WHERE id='{$prize_id}' LIMIT 1")->row_array();
        if (!$res_prizes) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_NOT_EXIST]);
        }

        //查询此项目的积分是否达到领取该礼品的标准
        $res_point_info = $this->price_to_point($pid, $survey_uid, 'c', false);
        if (!$res_point_info) {
            _back_msg("error", $lang_ary[LABEL_SUBMIRT_FAILED]);//参数有误
        }
        if ($res_point_info['point_info'] < $res_prizes['prize_point']) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_EXCHANGE_ERROR]);
        }
        if (!$consignee) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_CONSIGNEE_EMPTY]);
        }
        if (!$province) {
            _back_msg("error", $lang_ary[LABEL_PROVINCE_EMPTY]);
        }
        if (!$city) {
            _back_msg("error", $lang_ary[LABEL_CITY_EMPTY]);
        }
        if (!$district) {
            _back_msg("error", $lang_ary[LABEL_DISTRICT_EMPTY]);
        }
        if (!$receiving_address) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_RECEIVING_ADDRESS_EMPTY]);
        }
        if (!$mobile) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_MOBILE_EMPTY]);
        }
        if (!check_mobile($mobile)) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_FORMAT_ERROR]);
        }
        if (!$id_number) {
            _back_msg("error", $lang_ary[LABEL_SURVEY_PRIZES_ID_NUMBER_EMPTY]);
        }
        //通过pid获取项目信息
        $project_info = $this->survey_model->getProjectInfoById($pid);
        if (!$project_info) {
            _back_msg("error", $lang_ary[LABEL_PROJECT_NON_EXISTENT]);
        }
        //奖励执行方式
        if ($project_info['point_fetch'] == PROJECT_POINT_FETCH_TIMELY) {//及时加分
            $point_detail_status = POINT_AUDITING_END;
        } else {//后续加分
            $point_detail_status = POINT_AUDITING_START;
        }
        //奖励方式
        $reward_type = $project_info['reward_type'];
        //所有条件均合法，把相关信息入库
        unset($rs_post_data['bk_code']);
        unset($rs_post_data['survey_uid_code']);
        $rs_post_data['pid'] = $pid;
        $rs_post_data['survey_uid'] = $survey_uid;
        $rs_post_data['member_uid'] = $member_uid;
        $rs_post_data['point_c'] = $res_prizes['prize_point'];
        $rs_post_data['add_time'] = time();
        $rs_post_data['fee'] = $res_prizes['fee'];
        //事务开始
        $this->db->trans_start();
        $res = $this->db->insert('app_project_prizes_log', $rs_post_data);
        if ($res) {
            //记录完成状态
            $point_info = $res_point_info['point_info'];
            $partner_uid = $res_point_info['partner_uid'];
            $this->get_back_status('c', $point_info, $pid, $partner_uid, $res_prizes['prize_point']);
            $other_param = array(
                "finish_status" => 'c',
                "reward_type" => $reward_type,
                "prize_id" => $prize_id,
            );
            //记录积分日志奖励
            add_member_point_info($member_uid, $res_prizes['prize_point'], POINT_CHANGE_CODE_SURVEY_PRIZE, $pid, $project_info['pro_type'], $other_param, $point_detail_status, "survey_prize_point", $res_prizes['fee']);

            //推送信息
            $title = "恭喜你，完成调查！";
            $message = "您获取礼品：【".$res_prizes['prize_name']."】奖励";
            jPushLog($title, $message, PUSH_MESSAGE_LOG_CODE_SURVEY_POINT, "", $member_uid, $member_uid);

            //事务结束
            $this->db->trans_complete();
            //下一步骤就是跳转到问卷列表
            _back_msg("success", $lang_ary[LABEL_SUBMIT_SUCCESS], "/bk/get_address/".$bk_code."/".$survey_uid_code);
        }else{
            _back_msg("error", $lang_ary[LABEL_SUBMIRT_FAILED]);
        }

    }

    //账号合法性验证
    private function account_validity($bk_code, $survey_uid_code, $is_ajax = false)
    {
        if (!$bk_code){return false;}
        $partner_paran = explode('_',$bk_code);
        $pid = $partner_paran[0];
        $bk_info = $partner_paran[1];
        //问卷状态
        $st = $this->survey_status($pid, $bk_info);
        if (!$st) {
            set_sys_warning($pid, "", "", "", "account_validity：状态无效".$survey_uid_code);
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "p", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        if (!$bk_code || !$survey_uid_code) {
            set_sys_warning($pid, "", "", "", "account_validity：bk_code[{$bk_code}]或survey_uid_code[{$survey_uid_code}]不存在");
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $arr_survey_uid = explode("_", $survey_uid_code);
        $survey_uid = $arr_survey_uid[0];
        $survey_uid_encryption = $arr_survey_uid[1];
        $local_survey_uid = md5($survey_uid.PROJECT_ENCODE_KEY);
        if (!$survey_uid || $local_survey_uid != $survey_uid_encryption) {
            set_sys_warning($pid, "", "", "", "account_validity：链接解析无效{$survey_uid}**{$local_survey_uid}");
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }

        $res = $this->db->query("SELECT * FROM app_project_s_link WHERE pid = '{$pid}' AND survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        if (!$res) {//查询记录不存在
            set_sys_warning($pid, "", "", "", "account_validity：链接不存在{$survey_uid}");
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }

        $partner_id = $res['partner_id'];
        $group_no = $res['link_group'];
        //语言包
        $country_default_lang = $this->survey_model->get_country_default_lang($pid, $partner_id, $group_no);
        $lang = $country_default_lang['lang'];
        $country = $country_default_lang['country'];
        $lang_ary = get_lang($lang);
        //通过survey_uid查询对应的项目执行表的member_uid
        $res_project_info = $this->db->query("SELECT * FROM app_project WHERE id='{$pid}' LIMIT 1")->row_array();
        $p_table = $res_project_info['p_table'];
        $res_p_table_info = $this->db->query("SELECT * FROM {$p_table} WHERE survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        if (!$res_p_table_info) {
            set_sys_warning($pid, "", "", "", "account_validity：表不存在");
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }

        ### 补加积分或者后续支付都没有完成状态，因此此验证去掉 AMY 2021-07-15
//        if ($res_project_info['point_fetch'] == 1) {//AMY 2021-05-06 即时加分项目才需要校验link表里的状态
//            if ($res['finish_status'] != 'c') {//查询记录存在,但是状态不是c,则跳转到对应状态的提示信息
//                $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", $res['finish_status'], $res_p_table_info['member_uid'], $pid, "", false);
//                if ($is_ajax) {
//                    _back_msg("error", "", $redirect_url);
//                } else {
//                    redirect($redirect_url);
//                }
//            }
//        }
        ### 补加积分或者后续支付都没有完成状态，因此此验证去掉 AMY 2021-07-15

        $res = array(
            "pid" => $pid,
            "survey_uid" => $survey_uid,
            "member_uid" => $res_p_table_info['member_uid'],
            "m_id" => $res_p_table_info['id'],
            "imp_mobile" => $res_p_table_info['mobile'],
            "imp_name" => $res_p_table_info['name'],
            "st" => $st,
            "lang_ary" => $lang_ary,
            "country" => $country,
            "groupno" => $group_no,
            "partner_id" => $partner_id,
        );
        return $res ? $res : false;
    }

    //问卷状态解析
    private function survey_status($pid, $bk_info){
        if (!$pid) {return false;}
        $c_info = $pid ."_c";
        $q_info = $pid ."_q";
        $s_info = $pid ."_s";
        $res_c_info = str_md5_code($c_info.PROJECT_ENCODE_KEY);
        $res_q_info = str_md5_code($q_info.PROJECT_ENCODE_KEY);
        $res_s_info = str_md5_code($s_info.PROJECT_ENCODE_KEY);
        if (!$pid) {
            $this->survey_model->get_redirect_info("/bk/rs", "p");
//            redirect("/go/thanks?st=p");
        }
        $st = "";
        switch ($bk_info){
            case $res_c_info:
                $st = 'c';
                break;
            case $res_q_info:
                $st = 'q';
                break;
            case $res_s_info:
                $st = 's';
                break;
        }
        return $st ? $st : false;
    }

    //货币与积分的转换关系
    private function price_to_point($pid, $survey_uid, $st, $is_survey_index = true)
    {
        if (!$pid || !$survey_uid || !$st) {return false;}
        //查询项目执行表中，相关记录是否存在
        $p_table = "app_project_implement_" . $pid;
        $get_pro_implement_info = $this->db->query("SELECT * FROM {$p_table} WHERE survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        $uid = $get_pro_implement_info['member_uid'];
        $finish_status = $get_pro_implement_info['finish_status'];
        $partner_id = $get_pro_implement_info['partner_id'];
        $groupno = $get_pro_implement_info['groupno'];
        $partner_uid = $get_pro_implement_info['partner_uid'];
        if (!$get_pro_implement_info) {
            $this->survey_model->get_redirect_info("/bk/rs", "l_err");
//            redirect("/go/thanks?st=l_err");
        }
        //已经有问卷状态，直接跳转到对应的状态提示页
        if ($is_survey_index) {
            if ($finish_status) {
                $this->survey_model->get_redirect_info("/bk/rs", $st, $uid, $pid);
//                redirect("/go/thanks?st=".$st);
            }
        }
        //获取积分奖励比例
        $res_pro_partner_info = $this->db->query("SELECT * FROM app_project_partner WHERE project_id='{$pid}' AND partner_id='{$partner_id}' AND groupno='{$groupno}'")->row_array();
        $price_info = $res_pro_partner_info["price_".$st];//奖励价格【RMB】
        file_put_contents('./tmp/price_to_point.txt',$survey_uid.'_'.$pid.'_'.$partner_id.'_'.$groupno.'_'.$st.'_'.$price_info."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
        $pro_partner_country = $res_pro_partner_info['sample_country'];//外包国家
        $open_c = $res_pro_partner_info['openc'];
        $open_q = $res_pro_partner_info['openq'];
        $open_s = $res_pro_partner_info['opens'];

        //通过国家获取对应的人民币与对应国家的比例
        $res_survey_point = $this->db->query("SELECT * FROM app_survey_point WHERE country_id='{$pro_partner_country}' LIMIT 1")->row_array();
        //获取人民币与别的币种的转换比率
        $cny_to_other_money_type = $res_survey_point['country_point'] / $res_survey_point['china_money'];

        $rate = $res_survey_point['reward_'.$st]/100;
//        $point_info = round($price_info * $cny_to_other_money_type * $rate);
        $point_info = round($price_info * $cny_to_other_money_type);
        if ($st != 'c') {
            $point_info = 0;
        }

        $res = array(
            "openc" => $open_c,
            "openq" => $open_q,
            "opens" => $open_s,
            "point_info" => $point_info,
            "member_uid" => $uid,
            "partner_id" => $partner_id,
            "partner_uid" => $partner_uid,
            "country" => $pro_partner_country,//外包国家
            "money_rate" => $res_pro_partner_info['money_rate'],//外包汇率
            "pid_id" => $get_pro_implement_info['id'],//项目执行表编号
        );
        return $res ? $res : false;
    }

    //通话返回
    public function get_ly_project_group()
    {
        $get_post_data = file_get_contents("php://input");
        if ($get_post_data) {
            file_put_contents("./tmp/hb_phone.txt", $get_post_data."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);
            $base_return_info = base64_decode($get_post_data);
            $this->db->query("INSERT INTO app_huibo_return_phone_log(return_info,base_return_info,add_time)VALUES('{$get_post_data}','{$base_return_info}','".time()."')");
            $insert_id = $this->db->insert_id();
            $res = $this->xml_to_json($base_return_info);
            $res_info = json_decode($res, true);
            $res_return_info = $res_info['body']['record']['@attributes'];
            $code = $res_return_info['code'];
            $callType = $res_return_info['callType'];
            $callerNum = $res_return_info['callerNum'];
            $calledNum = $res_return_info['calledNum'];
            $startTime = $res_return_info['startTime'];
            $timeLength = $res_return_info['timeLength'];
            $recordURL = $res_return_info['recordURL'];
            $communicationNO = $res_return_info['communicationNO'];
            $followId = $res_return_info['followId'];
            $pid = 0;
            $pid_id = 0;
            $interviewer = 0;
            if ($followId) {
                $arr_follow_id = explode("_", $followId);
                $pid = $arr_follow_id[0];
                $pid_id = $arr_follow_id[1];
                $interviewer = $arr_follow_id[2];
            }
            if ($recordURL) {
                $recordURL = str_replace("&amp;", "&", $recordURL);
                $this->db->query("INSERT INTO app_project_cdr_huibo(`pid`,`pid_id`,`interviewer`,`code`,`callType`,`callerNum`,`calledNum`,`startTime`,`timeLength`,`recordURL`,`communicationNO`,`followId`)VALUES('{$pid}','{$pid_id}','{$interviewer}','{$code}','{$callType}','{$callerNum}','{$calledNum}','{$startTime}','{$timeLength}','{$recordURL}','{$communicationNO}','{$followId}')");
            }

            $this->db->query("UPDATE app_huibo_return_phone_log SET status=2 WHERE id='{$insert_id}' LIMIT 1");
        }
        echo base64_encode("<xml><result>1</result><body></body></xml>");
    }

    /**
     * rafael 更新项目总表，各个状态数量及成本
     */
    public function get_project_award($pid, $sv_st, $app_project_partner){
        if (!$pid) {return false;}
        //查询项目总表
        $app_project_detail = $this->db->query("select * from app_project where id={$pid}")->row_array();
        $data_project = array(
            'pro_price' => $app_project_detail['pro_price'],          //项目样本单价
            'pro_sample_num' => $app_project_detail['pro_sample_num'],     //样本需求数量
            'base_fee' => $app_project_detail['base_fee'],           //项目最小收费(货币类型随报价)
            'money_type' => $app_project_detail['money_type'],         //货币类型
            'money_rate' => $app_project_detail['money_rate'],         //报价汇率(相对于RMB)
            'c_num' => $app_project_detail['c_num'],                  //项目完成样本量
            'profits' => $app_project_detail['profits'],            //毛利（与报价货币一致）
            'last_profits' => $app_project_detail['last_profits'],  //纯利
            'per_profits' => $app_project_detail['per_profits'],    //毛利率
            'interviewer_award' => $app_project_detail['interviewer_award'],     //访问员奖励
            'sales_total' => $app_project_detail['sales_total'],        //项目总金额
            'pj_fixed_cost' => $app_project_detail['pj_fixed_cost'],      //项目成本
            'pm_reward' => $app_project_detail['pm_reward'],          //pm奖励
            'sales_reward' => $app_project_detail['sales_reward'],       //销售奖励
            'pj_cost' => $app_project_detail['pj_cost'],       //销售额
        );

        if($sv_st == 'c'|| $sv_st == 's' || $sv_st == 'q' || $sv_st == 't'){
            //查询奖励规则表 rule_sales_reward->销售提成 ,rule_pm_reward->项目经理提成,rule_inventer_reward->访问员提成,rule_tax->税
            $app_award_rules = $this->db->query("select * from app_award_rules where `key` in('rule_sales_reward','rule_pm_reward','rule_inventer_reward','rule_tax')")->result_array();

            $arr_award_rules = array();
            foreach ($app_award_rules as $k_rules){
                $arr_award_rules[$k_rules['key']] =  $k_rules['val'];
            }

            //从项目总表和项目外包表中获得对应的数据
            //第一步从外包表中获得对应的数据
            $project_partner_id = $app_project_partner['pp_id'] ;      //外包编号
            $price_c = $app_project_partner['price_c'] ;               //c项目样本外包单价
            $price_s = $app_project_partner['price_s'] ;               //s项目样本外包单价
            $price_q = $app_project_partner['price_q'] ;               //q项目样本外包单价
            $price_t = $app_project_partner['price_t'] ;               //t项目样本外包单价
            $client_price_c = $app_project_partner['client_price_c'] ; //项目外包单价
            $property = $app_project_partner['property'] ;              //外包类型1.内部资源 2.外部资源
            $total_pay = $app_project_partner['total_pay']; // 总支出
            //外包完成量
            $complete_num_c = $app_project_partner['complete_num_c'];
            $complete_num_s = $app_project_partner['complete_num_s'];
            $complete_num_q = $app_project_partner['complete_num_q'];
            $complete_num_t = $app_project_partner['complete_num_t'];
            //各状态总支出
            $pay_num_c = $app_project_partner['pay_num_c'];
            $pay_num_s = $app_project_partner['pay_num_s'];
            $pay_num_q = $app_project_partner['pay_num_q'];
            $pay_num_t = $app_project_partner['pay_num_t'];

            //从项目总表中获得对应的数据
            $pro_price = $data_project['pro_price'] ;                   //项目销售样单价
            $pj_fixed_cost = $data_project['pj_fixed_cost'] ;           //项目成本
            $pm_reward = $data_project['pm_reward'] ;                  //pm奖励
            $interviewer_award = $data_project['interviewer_award'] ;  //访问员奖励
            $sales_reward = $data_project['sales_reward'] ;            //销售奖励

            $c_num = isset($data_project['c_num']) ? $data_project['c_num'] : 0;                           //c项目完成样本量
            $s_num = isset($data_project['s_num']) ? $data_project['s_num'] : 0;                           //s项目完成样本量
            $q_num = isset($data_project['q_num']) ? $data_project['q_num'] : 0;                           //q项目完成样本量
            $t_num = isset($data_project['t_num']) ? $data_project['t_num'] : 0;                           //t项目完成样本量

            $profits = $data_project['profits'];                       //毛利润（与报价货币一致）
            $last_profits = $data_project['last_profits'];             //纯利
            $per_profits = $data_project['per_profits'];               //毛利率
            $pj_cost = $data_project['pj_cost'];                       //销售额

            //外包单价不用更新
            $c_price_c = $price_c;

            //需要更新的字段参数初始化
            ##########  第一部分更新--完成状态数量 start  ##########
            // 任何状态只要有状态就更新对应的状态数量
            $complete_num = $sv_st."_num";
            ##########  第一部分更新--完成状态数量 end ##########

            ##########  第二部分更新--项目成本 start ##########
                //项目成本
    //            $pro_price //项目销售单价-固定不变
    //            $client_price_c // 项目外包单价-固定不变
                if($property == 1){ // 内部资源
                    //当前完成状态的项目样本外包单价
                    $now_price_complete = "price_".$sv_st;
                    $pj_fixed_cost += $$now_price_complete;
                } else { //外部资源，成本+外包单价
//                    if($sv_st =='c'){ $pj_fixed_cost += $client_price_c; }
                    //不管内部资源或外部资源，项目c单价都是price_c字段信息
                    if($sv_st =='c'){ $pj_fixed_cost += $price_c; }
                }
            ##########  第二部分更新--项目成本 end  ##########

            ##########  第三部分更新--项目税金成本 start  ##########
            $pj_c_tax_fee = 0;
            if($sv_st == 'c'){//税金 不要更新
                $pj_c_tax_fee = $pro_price * $arr_award_rules['rule_tax_all'];
            }
            ##########  第三部分更新--项目税金成本 end  ##########

            ##########  第四部分求项目基本执行成本 start  ##########
            $pj_fixed_cost = $pj_fixed_cost + $pj_c_tax_fee;
            $data_project_up = array(
                $complete_num => $$complete_num + 1,
                'pj_fixed_cost' => $pj_fixed_cost,
            );
            $this->db->where(array('id'=>$pid))->update('app_project',$data_project_up);
            ##########  第四部分求项目基本执行成本 end  ##########

            ##########  第五部分求外包基本执行成本 start  ##########
            $res_partner_price = "price_".$sv_st;//单价
            $res_partner_complete = "complete_num_".$sv_st;//状态完成量
            $res_pay_num = "pay_num_".$sv_st;//总支出
            $pay_n = $$res_partner_price;
            $data_partner = array(
                'total_pay' => $total_pay + $pay_n,
                $res_partner_complete => $$res_partner_complete + 1,
                $res_pay_num => $$res_pay_num + $pay_n,
            );
            //更新项目外包成本及完成量表
            $where_partner_cond = array('project_id'=>$pid, 'pp_id'=>$project_partner_id);
            $this->survey_model->update_surface("app_project_partner", $data_partner, $where_partner_cond);
            ##########  第五部分求外包基本执行成本 end  ##########

            //pm奖金

            //访问员奖金

            //销售提成

            // 毛利

            // 纯利

            //不同状态下项目总表中，对应项目的计数更新

            //以下计算一个c的财务信息
            //项目基本固定参数
            //外包单价不用更新

        }
    }

    //记录完成状态
    public function get_back_status($st, $point_info, $pid, $partner_uid, $prize_point = 0)
    {
//        if (!$st || !$point_info || !$pid || !$partner_uid) {return false;}
        //没有积分也要标记问卷状态
        if (!$st || !$pid || !$partner_uid) {return false;}
        //记录完成状态
        $update_project_status = array(
            "finish_status" => $st,
            "price" => $point_info,
            "prize_price" => $prize_point,
        );
        $where_project_status = array(
            "pid" => $pid,
            "partner_uid" => $partner_uid,
        );
//        //amy 2020-03-02 增加项目最后完成时间
//        if ($st == 'c') {//完成状态
//            $this->db->query("UPDATE app_project SET last_c_time='".time()."' WHERE id='{$pid}' LIMIT 1");
//        }

        ####    查询partner_uid是否有点击记录，有的时候，不更新项目扩展表的点击状态 amy 2020-03-04
        if ($st == 'c') {//完成状态
            $get_click_num = $this->db->query("SELECT * FROM app_project_response_lct WHERE project_id='{$pid}' LIMIT 1")->row_array();
            $lct_now_time = time();
            if (!$get_click_num) {//不存在
                $this->db->query("INSERT INTO app_project_response_lct(`project_id`,`lct`,`response_num`,`c_num`,update_t)VALUES('{$pid}','{$lct_now_time}',1,1,'{$lct_now_time}')");
            } else {
                $this->db->query("UPDATE app_project_response_lct SET lct='".time()."',`c_num`=`c_num`+1,update_t='{$lct_now_time}' WHERE project_id='{$pid}' LIMIT 1");
            }

            //完成问卷的医生，待审状态时，直接变成审核通过 amy 2020-03-27
            if (strpos($partner_uid, "uid=".$pid."_") === false) {//不是上医说会员
                $uid = 0;
            } else {//是上医说会员
                $uid = str_replace("uid=".$pid."_", "", $partner_uid);
                $uid = (int)$uid;
            }
            if ($uid > 0) {//存在会员编号
                $member_info = $this->db->query("SELECT * FROM app_member WHERE id=?", [$uid])->row_array();
                if ($member_info && $member_info['status'] == 1) {//待审
                    $update_member = $this->db->query("UPDATE app_member SET `status`=2 WHERE id=? LIMIT 1", [$uid]);
                    if ($update_member) {
                        $update_member_data = [
                            "member_uid" => $uid,
                            "data_from" => $member_info['data_from'],
                            "edit_time" => time(),
                            "old_data" => json_encode(["status" => $member_info['status']], JSON_UNESCAPED_UNICODE),
                            "new_data" => json_encode(["status" => 2], JSON_UNESCAPED_UNICODE),
                            "log_code" => MEMBER_INFO_CHANGE_FROM_WHERE_AUDIT_PASS,
                        ];
                        $this->db->insert("app_member_log", $update_member_data);
                    }
                }
            }
        }
        ####    查询partner_uid是否有点击记录，有的时候，不更新项目扩展表的点击状态 amy 2020-03-04


        $this->survey_model->update_surface("app_project_bespeak", $update_project_status, $where_project_status);
        return true;
    }

    /**
     * i_survey账号执行时，需要把问卷状态同步到i_survey_project_bespeak表中
     * @param $st
     * @param $pid
     * @param $member_uid
     * @param $point_fetch  加分规则【加分规则：1:即时加分 2:后续加分
     * @return bool
     */
    private function update_i_survey_finish_status($st, $pid, $member_uid, $point_fetch){
        if (!$st || !$pid || !$member_uid) {return false;}
        $bespeak_info = getDataByConditionCi('i_survey_project_bespeak', " and pid = ? AND member_uid = ? LIMIT 1", "*", true, [$pid, $member_uid]);
        if ($bespeak_info) {//存在
            $local_ip = getip();
            $ip = ip2long($local_ip);
            $ip_addr = ip2location($ip);
            //记录完成状态
            $update_project_status = array(
                "original_finish_status" => $st,
                "finish_status" => $st,
                "finish_time" => time(),
                "bk_ip" => $local_ip,
                "bk_ip_address" => $ip_addr,
            );
            $where_project_status = array(
                "pid" => $pid,
                "member_uid" => $member_uid,
            );
            //bryant 2020-07-31 如果是及时加分并且问卷完成状态,追加更新审核状态
            if ($point_fetch == PROJECT_POINT_FETCH_TIMELY && $st == 'c') {
                $update_project_status['qc_status'] = 1;    //审核状态  1：有效
                $update_project_status['qc_time'] = time();    //qc时间
            }
            $this->survey_model->update_surface("i_survey_project_bespeak", $update_project_status, $where_project_status);

            if ($st == 'c') {//完成状态是c的
                //把完成量累加到i_survey_dc_hand_project表中
                $local_estimated_revenue = $bespeak_info['cost'];
                $local_estimated_revenue = $local_estimated_revenue ? $local_estimated_revenue : 0;
                $this->db->query("UPDATE i_survey_dc_hand_project SET complete_c=complete_c+1,estimated_revenue=estimated_revenue+{$local_estimated_revenue} WHERE pid='{$pid}' AND i_survey_id='{$bespeak_info['i_survey_id']}' LIMIT 1");

            }
        }

        return true;
    }

}
