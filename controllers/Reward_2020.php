<?php

class Reward_2020 extends CI_Controller
{
    private $username = "jkt";
    private $password = "12345678";
    function __construct()
    {
        parent::__construct();
        if (!isset($_SERVER['PHP_AUTH_USER'])) {
            header('WWW-Authenticate: Basic realm="抽奖"');
            header('HTTP/1.0 401 Unauthorized');
            echo '请输入验证信息';
            exit;
        } else {
            if(strcmp($_SERVER['PHP_AUTH_USER'], $this->username) != 0 || md5($_SERVER['PHP_AUTH_PW']) != md5($this->password)) {
                header('WWW-Authenticate: Basic realm="抽奖"');
                header('HTTP/1.0 401 Unauthorized');
                echo '请输入正确的验证信息';
                exit;
            }
        }
    }

    public function index(){
        $this->load->view("/reward_2020/index.php");
    }

    public function lottery(){
        $info = $this->db->limit(1)->select('name')->where(['is_win' => 0])->order_by('id', 'RANDOM')->get('reward_2020')->row_array();
        if ($info) {
            $wind_id = $info['name'];
        }else{
            _back_msg("error", "抽奖完毕");
        }

        $update = $this->db->where(['id' => $wind_id, 'is_win' => 0])->update('reward_2020', ['is_win'=>1]);
        if (!$update) {
            _back_msg("error", "重新抽奖");
        }
        $res = '000';
        if (strlen($wind_id) == 1) {
            $res = '00' . $wind_id;
        }elseif (strlen($wind_id) == 2) {
            $res = '0' . $wind_id;
        }else{
            $res = $wind_id;
        }
        _back_msg("success", $res);
    }
}
