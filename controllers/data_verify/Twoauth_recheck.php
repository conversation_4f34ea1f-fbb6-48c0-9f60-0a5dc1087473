<?php

class Twoauth_recheck extends CI_Controller
{
    public $statusArr      = ['1' => '认证一致(收费)', '2' => '认证不一致(收费)', '3' => '认证不确定（收费)', '4' => '认证失败(不收费)'];
    public $twosourceArr   = [1 => '客户数据', 2 => '认证管理', 3 => '客户API-创蓝', 4 => '客户API-贵州数据宝'];
    public $threesourceArr = [1 => '赛小欧自动认证', 2 => '赛小欧人工认证', 3 => '项目--进入--邀请管理', 4 => '批量执行数据', 5 => 'idcode_three_211115', 6 => '认证管理'];
    public $stArr          = ['0' => '等待复核', '1' => '复核完成 ', '2' => '等待运营商进一步复核', '3' => '复核不一致'];
    public $check_status_ary = [0 => '未验证', 1 => '认证一致 ', 2 => '认证不一致'];

    public function __construct()
    {
        parent::__construct();
        $this->load->model('data_verify/twoauth_elements_model');
        //三要素认证
        $this->load->model('data_verify/three_elements_model');
        //创蓝运营商二要素（姓名+手机号）日志表
        $this->load->model('data_verify/twoauth_elements_log_model');
        //复核表
        $this->load->model('data_verify/twoauth_elements_recheck_model');
        //复核表
        $this->load->model('data_verify/data_vf_model');
        $this->load->helper('url');
        $this->load->helper('cookie');
    }

    /**
     * 登陆页面
     * User: wy
     */
    public function login()
    {
        $title = '登陆';
        $data  = ['title' => $title];
        $this->load->view('/data_verify/twoauth_recheck/login', $data);
    }

    /**
     * 登陆
     * User: wy
     * @param string username
     * @param string password
     */
    public function land()
    {
        $post_data = $this->input->post();
        $rec_act   = trim($post_data['username']); //用户名
        //$rec_act   = 'x207MvNrOz';
        $rec_pwd   = trim($post_data['password']); //密码
        //$rec_pwd   = 'a2f24As3';

        $error_data = json_encode([
            "code" => 201,
            "msg" => "登陆失败，账号或者密码错误！"
        ], JSON_UNESCAPED_UNICODE);

        if ($rec_act == '') {
            die($error_data);
        }
        if ($rec_pwd == '') {
            die($error_data);
        }

        $vf_datas = $this->data_vf_model->get_data_vf([
            'rec_act'   => $rec_act,
            'rec_pwd'   => $rec_pwd,
            'status'    => 1,
            'is_delete' => 1
        ]);

        if (!empty($vf_datas)) {
            $vf_data = current($vf_datas);
            //登录成功，记录SESSION
            $data = [
                'id'      => $vf_data['id'],
                'app_id'  => $vf_data['app_id'],
                'app_key' => $vf_data['app_key'],
                'rec_act' => $vf_data['rec_act'],
                'rec_pwd' => $vf_data['rec_pwd'],
            ];
            $this->session->set_userdata($data);
            die(json_encode(['code' => 200], JSON_UNESCAPED_UNICODE));
        } else {
            die($error_data);
        }
    }

    /**
     * 退出
     * User: wy
     */
    public function out()
    {
        $this->session->unset_userdata('id');
        $this->session->unset_userdata('app_id');
        $this->session->unset_userdata('app_key');
        $this->session->unset_userdata('rec_act');
        $this->session->unset_userdata('rec_pwd');
        delete_cookie('rec_act');
        delete_cookie('rec_pwd');
        redirect('/data_verify/twoauth_recheck/login');
    }

    /**
     * 统一校验登陆状态
     * User: wy
     */
    private function action()
    {
        $id      = $this->session->userdata('id');
        $app_id  = $this->session->userdata('app_id');
        $app_key = $this->session->userdata('app_key');
        $rec_act = $this->session->userdata('rec_act');
        $rec_pwd = $this->session->userdata('rec_pwd');
        if (!isset($id) || !isset($app_id) || !isset($app_key) || !isset($rec_act) || !isset($rec_pwd)) {
            redirect('/data_verify/twoauth_recheck/login');
        }
    }


    /**
     * 二要素复核添加页面
     * User: wy
     *
     */
    public function add()
    {
        $this->action();
        $title = '数据检测';
        $data  = ['title' => $title];

        $this->load->view('/templates/header.php', $data);
        $this->load->view('/data_verify/twoauth_recheck/add', $data);
        $this->load->view('/templates/footer.php', $data);
    }

    /**
     * 二要素复核添加
     * User: wy
     * @param array add
     * @param file files
     *
     */
    public function insert()
    {
        $this->action();
        $post_data = $this->input->post();
        $data      = $post_data['add'];
        $files     = $_FILES['files'];

        // print_r('<pre>');
        // print_r($files);
        $mobiles = [];
        foreach ($data as $key => $val) {
            if ($val['name'] == '' && $val['mobile'] == '' && !isset($files['name'][$key])) {
                unset($data[$key]); //空的去掉
            } else {
                if ($val['name'] == '') {
                    _back_msg("error", "存在姓名为空");
                }
                if ($val['mobile'] == '') {
                    _back_msg("error", "存在手机号为空");
                }
                $vname = str_replace("·", '', $val['name']);
                if (!preg_match('/^([\xe4-\xe9][\x80-\xbf]{2}){2,11}$/', trim($vname))) {
                    _back_msg("error", "存在姓名不为中文名称");
                }

                if (preg_match("/^(13|14|15|16|17|18|19)[0-9]{9}$/", trim($val['mobile'])) == 0) {
                    _back_msg("error", "存在手机号格式错误");
                }

                if (!$files['name'][$key] || !$files['type'][$key] || !$files['tmp_name'][$key] || !$files['size'][$key]) {
                    _back_msg("error", "所有图片都必选");
                }
                $mobiles[$val['mobile']] = $val['name'];
            }
        }
        $exit_data = $this->twoauth_elements_recheck_model->check_exit(array_keys($mobiles), $this->session->userdata('app_id'));
        if (!empty($exit_data)) {
            foreach ($exit_data as $value) {
                if ($value['name'] == $mobiles[$value['mobile']]) {
                    _back_msg("error", "部分数据已经提交过，请勿重复添加。");
                }
            }
        }
        // print('<pre>');
        // print_r($files);exit;
        if (empty($files)) {
            _back_msg("error", "请选择图片上传");
        }




        try {
            //事务开始
            $this->db->trans_start();

            $new_logs    = [];
            $insert_data = [];
            foreach ($data as $key => $val) {
                $file = [];
                $file['name'] = $files['name'][$key];
                $file['type'] = $files['type'][$key];
                $file['tmp_name'] = $files['tmp_name'][$key];
                $file['error'] = $files['error'][$key];
                $file['size'] = $files['size'][$key];
                $path = $this->upload($file);
                $insert_data[$key]['img'] = $path;
                $insert_data[$key]['name'] = trim($val['name']);
                $insert_data[$key]['mobile'] = trim($val['mobile']);
                $insert_data[$key]['appid'] = $this->session->userdata('app_id');

                $insert_data[$key]['check_status'] = 0;
                $insert_data[$key]['sys_twoauth_elements_log_id'] = 0;
                $insert_data[$key]['old_check_status'] = isset($log['result']['respond']['data']['result']) ? $log['result']['respond']['data']['result'] : 0;
                if ($log['result']['respond']['data']['result'] == '01') {
                    $insert_data[$key]['st'] = 1;
                } else {
                    $insert_data[$key]['st'] = 0;
                }
                $insert_data[$key]['add_time'] = time();
            }

            if ($insert_data) {
                //分组提交
                $chunk_data = array_chunk($insert_data, 20);
                foreach ($chunk_data as $in_data) {
                    $res_add = $this->twoauth_elements_recheck_model->insert_batch($in_data);
                    if (!$res_add) {
                        throw new Exception("提交失败！[添加]");
                    }
                }
            }
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === false) {
                throw new Exception("复核失败,请重新提交1！");
            }
            $this->db->query("update sys_twoauth_elements_recheck rt, sys_twoauth_elements te set rt.old_check_status=case te.`status` when '01' then 1 when '02' then 2 when '03' then 2 when '04' then 2 end where rt.st=0 and rt.old_check_status=0 and rt.mobile=te.mobile and rt.`name`=te.`name`");
            $this->db->query("update sys_twoauth_elements_recheck rt, sys_three_elements te set rt.old_check_status = case te.`status` when '01' then 1 when '02' then 2 when '03' then 2 when '04' then 2 end where rt.st=0 and rt.old_check_status=0 and rt.mobile=te.mobile and rt.`name`=te.`name`");

            $aids = $this->db->query('select * from sys_data_vf where app_id="' . $this->session->userdata('app_id') . '"')->row_array();
            $app_key = $aids['app_key'];
            $app_id = $aids['app_id'];
            $rlist = $this->db->query('select * from sys_twoauth_elements_recheck where st=0 and appid="' . $app_id . '" limit 40')->result_array();
            $this->load->library('tools');

            $check_rows = $uplist = $app_ids = $erids = [];
            if ($rlist) {
                foreach ($rlist as $row) {
                    $check_rows[] = $row;
                    $uplist[] = $row['name'] . $row['mobile'];
                    $app_ids[] = $row['appid'];
                    $erids[] = $row['id'];
                }
                $this->db->query('update sys_twoauth_elements set name=CONCAT(name,id) where `status`!="01" and CONCAT(name, mobile) in ("' . implode('","', $uplist) . '")');
                $this->db->query('update sys_three_elements set name=CONCAT(name,id) where `status`!="01" and CONCAT(name, mobile) in ("' . implode('","', $uplist) . '")');

                foreach ($check_rows as $row) {
                    $app_key            = $aidkey[$row['appid']];
                    $app_id             = $row['appid'];
                    $prarm['timestamp'] = time();
                    $prarm['signature'] = md5($app_key . $prarm['timestamp'], false);
                    $prarm['app_id']    = $app_id;
                    $prarm['name']      = $row['name'];
                    $prarm['mobile']    = $row['mobile'];
                    $url                = 'https://api.drsay.cn/twoauth/opera';
                    $res = $this->tools->curl_post($url, $prarm);
                }
                $this->db->query('update sys_twoauth_elements_recheck rc, sys_twoauth_elements te set up_time=UNIX_TIMESTAMP(), up_uid=0, rc.check_status=case te.status when "01" then 1 when "02" then 2 end, rc.st=1 where rc.name=te.name and rc.mobile=te.mobile and rc.st=0');
                $this->db->query('update sys_twoauth_elements_recheck set st=2 where check_status=2 and id in ("' . implode('","', $erids) . '")');
            }

            _back_msg("success", "提交成功！", "/data_verify/twoauth_recheck/index");
            // foreach($insert_data as $k => $value) {
            //     $list = $this->twoauth_elements_recheck_model->get_twoauth_elements_recheck(['name' => $value['name'] , 'mobile' => $value['mobile']]);
            //     $recheck = current($list);
            //     $res = $this->check($recheck['id'] , $recheck['name'] , $recheck['mobile'] );
            //     if(!$res) {
            //         throw new Exception("接口返回错误！");
            //     }

            // }
            // //事务结束
            // $this->db->trans_complete();
            // if ($this->db->trans_status() === FALSE)
            // {
            //     throw new Exception("复核失败,请重新提交1！");
            // }

            // _back_msg("success", "提交成功！", "/data_verify/twoauth_recheck/index");
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }

    /**
     * 二要素复核列表
     * User: wy
     * @param  string search_name
     * @param  string search_mobile
     *
     */
    public function index()
    {
        $this->action();

        $date          = trim($this->input->get('date', true));
        $flag          = trim($this->input->get('flag', true));
        $search_name   = trim($this->input->get('search_name', true));
        $search_mobile = trim($this->input->get('search_mobile', true));
        $search_status = trim($this->input->get('search_status', true));

        $offset = $this->input->get('per_page', true);
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset) - 1) * PAGE_NUM : 0;


        $prarm = [];
        if (isset($search_name) && !empty($search_name)) {
            $prarm['name']   = $search_name;
        }
        if (isset($search_mobile) && !empty($search_mobile)) {
            $prarm['mobile'] = $search_mobile;
        }
        if (isset($search_status) && $search_status != '') {
            $prarm['check_status'] = $search_status;
        }
        if (isset($date) && !empty($date)) {
            $prarm['data_start_time'] = strtotime($date);
            $prarm['data_end_time'] = strtotime($date) + 24 * 60 * 60;
        }

        $prarm['appid'] = $this->session->userdata('app_id'); //查询自动带appid

        $new_logs = [];
        $total    = 0;
        $list     = [];
        $dates    = [];

        $total = $this->twoauth_elements_recheck_model->get_count_twoauth_elements_recheck($prarm);

        if ($total > 0) {
            $list  = $this->twoauth_elements_recheck_model->get_twoauth_elements_recheck($prarm, $offset);

            $dates = $this->twoauth_elements_recheck_model->get_recheck_group($prarm['appid']);
        }

        if ($flag  == '1' &&  (date('Y-m-d', strtotime($date)) == $date)) {
            $pary = [];
            foreach ($list as $value) {
                $pary[] = [
                    $value['id'],
                    isset($value['add_time']) ? date('Y-m-d', $value['add_time']) : '',
                    $value['name'],
                    $value['mobile'],
                    // isset($this->stArr[$value['st']]) ? $this->stArr[$value['st']] : '',
                    isset($this->check_status_ary[$value['check_status']]) ? $this->check_status_ary[$value['check_status']] : ''
                ];
            }

            $filename = '复核列表(' . $date . ')';
            $title = array('序号', '创建时间', '姓名', '手机号', '复核结果');


            //return $this->exCSV($title, $pary, $filename);
            return $this->exportExcel($pary, $title, $filename);
        }



        //分页
        $s_url = '/data_verify/twoauth_recheck/index?search_name=' . $search_name . '&search_value' . $search_value;
        $print_page = getPagination($total, $s_url, PAGE_NUM);

        $data = [
            'title'         => $this->title,
            'page_title'    => '二要素复核',
            'total'         => $total,
            'list'          => $list,
            'dates'          => $dates,
            'date_time'          => $date,
            'new_logs'      => $new_logs,
            'offset'        => $offset,
            'search_name'   => $search_name,
            'search_mobile' => $search_mobile,
            'search_status' => $search_status,
            'check_status_ary' => $this->check_status_ary,
            'status'        => $this->statusArr,
            'st_arr'        => $this->stArr,
            'source'        => $flag == 0 ? $this->twosourceArr : $this->threesourceArr,
            'source_table'  => $flag == 0 ? '二要素' : '三要素',
            'print_page'    => $print_page,

        ];

        $this->load->view('/templates/header.php', $data);
        $this->load->view('/data_verify/twoauth_recheck/index', $data);
        $this->load->view('/templates/footer.php', $data);
    }

    public function exCSV($table_list, $datas, $filename)
    {
        $this->action();

        $title = iconv("utf-8", "gbk//TRANSLIT", implode(",", array_column($table_list, 'Comment'))) . PHP_EOL;
        header("Cache-control: private");
        header("Pragma: public");
        header('Content-type: application/x-csv');
        if (strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE 5')) {
            header("Content-Disposition: inline; filename=" . $filename);
        } else {
            header("Content-Disposition: attachment; filename=" . $filename);
        }
        echo $title;
        foreach ($datas as $d) {
            echo iconv("utf-8", "gbk//TRANSLIT", implode(",", $d)) . PHP_EOL;
        }
        exit;
    }


    public function excel()
    {
        $this->action();
        $offset = $this->input->get('per_page', true);
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset) - 1) * PAGE_NUM : 0;


        $prarm = [];

        $appid = $this->session->userdata('app_id'); //查询自动带appid

        $new_logs = [];
        $total = 0;
        $list = [];
        $list = $this->twoauth_elements_recheck_model->get_recheck_group($appid);

        $result = [];
        //导出
        foreach ($list as $key => $vo) {
            $result[$key]['id'] = $vo['id'];
            $result[$key]['add_time'] = date('Y-m-d H:i:s', $vo['add_time']);
            $result[$key]['name'] = $vo['name'];
            $result[$key]['mobile'] = $vo['mobile'];
            $result[$key]['st'] = isset($st_arr[$row['st']]) ? $st_arr[$row['st']] : '';
        }

        $filename = '复核列表';
        $title = array('序号', '创建时间', '姓名', '手机号', '复核结果');
        return $this->exportExcel($result, $title, $filename);
    }



    /**
     * 导出数据为excel表格
     *
     * @param $data 一个二维数组,结构如同从数据库查出来的数组
     * @param $title excel的第一行标题,一个数组,如果为空则没有标题
     * @param $filename 下载的文件名
     */
    public function exportExcel($data = array(), $title = array(), $filename = '统计数据')
    {
        $filename = iconv("UTF-8", "GB2312", $filename);
        header("Content-type:application/octet-stream");
        header("Accept-Ranges:bytes");
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:attachment;filename=" . $filename . ".xls");
        header("Pragma: no-cache");
        header("Expires: 0");
        // 导出xls 开始
        if (!empty($title)) {
            foreach ($title as $k => $v) {
                $title[$k] = iconv("UTF-8", "GB2312", $v);
            }
            $title = implode("\t", $title);
            echo "{$title}\n";
        }
        if (!empty($data)) {
            foreach ($data as $k1 => $v1) {
                foreach ($v1 as $k2 => $v2) {
                    $data[$k1][$k2] = iconv("UTF-8", "gbk", $v2);
                }
                $data[$k1] = implode("\t", $data[$k1]);
            }
            echo implode("\n", $data);
        }
        exit();
    }

    /**
     * 复核认证查询api
     * User: wy
     * @param int id
     * @param string name
     * @param string mobile
     */
    public function check($id, $name, $mobile)
    {
        $this->action();
        $prarm = [];
        if ($id <= 0) {
            return false;
        }
        if ($name == '') {
            return false;
        }
        if ($mobile == '') {
            return false;
        }

        try {
            $app_key            = 'JKTGRAHU1GTJU5FRUPMZ';
            $app_id             = 'x207MvNrOz';
            $prarm['name']      = $name;
            $prarm['mobile']    = $mobile;
            $prarm['timestamp'] = time();
            $prarm['signature'] = md5($app_key . $prarm['timestamp'], false);
            $prarm['app_id']    = $app_id;

            $this->load->library('tools');
            $url     = 'https://api.drsay.cn/twoauth/opera';
            $api_res = $this->tools->curl_post($url, $prarm);
            $result  = json_decode($api_res, true);
            if ($result['code'] != 20000) {
                return false;
            }

            $st = 0;
            if ($result['status'] == 1) {
                $st = 1;
            } else {
                $st = 2;
            }

            //二要素（姓名+手机号）日志
            $te_logs = $this->twoauth_elements_log_model->get_twoauth_elements_log(['name' => $name, 'mobile' => $mobile]);
            $log = current($te_logs); //最新的一条

            //更新复核验证结果 check_status
            $up_param = [];
            $up_param['check_status'] = $result['status'];
            $up_param['appid']        = $app_id;
            $up_param['st']           = $st;
            $up_param['up_time']      = time();
            $up_param['sys_twoauth_elements_log_id']  = $log['id'];
            $up_res = $this->update_tek($up_param, $id);
            if (!$up_res) {
                return false;
            }

            return true;
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }

    /**
     * 更新
     * User: wy
     * @param int id
     * @param array up_param
     */
    private function update_tek($up_param, $id)
    {
        if (empty($up_param)) {
            return false;
        }
        $up_res = $this->twoauth_elements_recheck_model->update($up_param, $id);
        if (!$up_res) {
            return false;
        }
        return $up_res;
    }

    /**
     * 上传文件
     * User: wy
     * @param int id
     * @param array up_param
     */
    protected function upload($file)
    {
        $this->action();
        $url = '/uploads/web/data_verify/twoauth_recheck/';

        //是否存在上传路径
        if (!is_dir($url)) {
            @mk_dir($url);
        }

        if ($file['size'] > 2 * 1024 * 1024) {
            _back_msg("error", "文件过大！");
            return false;
        } else {
            switch ($file['type']) {
                case 'image/pjpeg':
                    $hz = "jpg";
                    break;
                case 'image/png':
                    $hz = "png";
                    break;
                case 'image/jpg':
                    $hz = "jpg";
                    break;
                case 'image/jpeg':
                    $hz = "jpeg";
                    break;
                case 'image/gif':
                    $hz = "gif";
                    break;
                case 'image/bmp':
                    $hz = "bmp";
                    break;
                case 'image/svg':
                    $hz = "svg";
                    break;
                case 'image/psd':
                    $hz = "psd";
                    break;
                default:
                    $hz = false;
                    break;
            }

            if ($hz) {
                $path = $url . md5(time() . $file['name']) . '.' . $hz;
                if (is_uploaded_file($path)) {
                    //图片存在直接返回
                    return $path;
                } else {
                    if (!move_uploaded_file($file['tmp_name'], '.' . $path)) {
                        _back_msg("error", "文件上传失败！");
                        return false;
                    }
                    return $path;
                }
            } else {
                _back_msg("error", "文件上传失败！");
                return false;
            }
        }
    }
}
