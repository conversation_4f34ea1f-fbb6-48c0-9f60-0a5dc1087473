<?php
/**
 * Created by PhpStorm.
 * User: Amy
 * Date: 2019/11/4
 */
class I_survey extends CI_Controller
{
    function __construct()
    {

        parent::__construct();

    }

    function rp()
    {
        $data =[
            'title'=> "ISURVEY",

        ];
        $this->load->view("/i_survey/rp",$data);
    }

    function tpl_test()
    {
        $data =[
            'title'=> "ISURVEY",

        ];
        $this->load->view("/i_survey/tpl_test",$data);
    }

    public function test_info()
    {
        die;
        $pid = '733';
        $member_uid = '1360453';
        $info = getDataByConditionCi('i_survey_project_bespeak', " and pid = ? AND member_uid = ? LIMIT 1", "*", true, [$pid, $member_uid]);
        print_r($info);
    }

    //我的资料
    public function myinfo(){
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_id = $i_survey_user['i_survey_id'];
        $i_survey_adm_id = $i_survey_user['i_survey_adm_id'];
        $get_isuv_user_info = getDataByConditionCi("i_survey_user", " AND id=?", '*', true, [$i_survey_id]);
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        if ($post_data) {
            $i_survey_info = $post_data['i_survey'];
            $this->db->where("id", $i_survey_id);
            $res = $this->db->update("i_survey_user", $i_survey_info);
            if ($res) {
                //新增变更日志
                $insert_log_data = [
                    "i_survey_id" => $i_survey_id,
                    "old_data" => $get_isuv_user_info ? json_encode($get_isuv_user_info, JSON_UNESCAPED_UNICODE) : "",
                    "new_data" => $i_survey_info ? json_encode($i_survey_info, JSON_UNESCAPED_UNICODE) : "",
                    "add_uid" => $i_survey_adm_id ?? "",
                    "add_time" => time(),
                ];
                $this->db->insert("i_survey_user_log", $insert_log_data);
                _back_msg("success", "提交成功", "/i_survey/myinfo");
            } else {
                _back_msg("error", "提交失败");
            }
        }

        $data =[
            'title'=> "我的资料",
            'get_isuv_user_info'=> $get_isuv_user_info,
        ];
        $this->load->view("/i_survey/header",$data);
        $this->load->view("/i_survey/myinfo",$data);
        $this->load->view("/i_survey/footer",$data);
    }

    //修改密码
    public function password(){
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_id = $i_survey_user['i_survey_id'];
        $i_survey_adm_id = $i_survey_user['i_survey_adm_id'];
        $get_isuv_user_info = getDataByConditionCi("i_survey_user", " AND id=?", '*', true, [$i_survey_id]);
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        if ($post_data) {
            $i_survey_info = $post_data['i_survey'];
            $this->db->where("id", $i_survey_id);
            if (!$i_survey_info['password']) {
                _back_msg("error", "密码必须填写才能提交");
            }
            $res = $this->db->update("i_survey_user", $i_survey_info);
            if ($res) {
                //新增变更日志
                $insert_log_data = [
                    "i_survey_id" => $i_survey_id,
                    "old_data" => $get_isuv_user_info ? json_encode($get_isuv_user_info, JSON_UNESCAPED_UNICODE) : "",
                    "new_data" => $i_survey_info ? json_encode($i_survey_info, JSON_UNESCAPED_UNICODE) : "",
                    "add_uid" => $i_survey_adm_id ?? "",
                    "add_time" => time(),
                ];
                $this->db->insert("i_survey_user_log", $insert_log_data);
                _back_msg("success", "提交成功", "/i_survey/password");
            } else {
                _back_msg("error", "提交失败");
            }
        }
        $data =[
            'title'=> "修改密码",
            'get_isuv_user_info'=> $get_isuv_user_info,
        ];
        $this->load->view("/i_survey/header",$data);
        $this->load->view("/i_survey/password",$data);
        $this->load->view("/i_survey/footer",$data);
    }

    /**
     * 客户登录
     */
    public function login(){
        $val_encode_str = trim($this->input->get('code'));
        if ($val_encode_str) {
            $val_arr_code = explode("||", base64_decode($val_encode_str));
            if (count($val_arr_code) != 5) {
                die("param error!");
            }
            $code = $val_arr_code[0];
            $password = $val_arr_code[1];
            $now_time = $val_arr_code[2];
            $i_survey_adm_id = $val_arr_code[3];
            $check_val_encode_str = $val_arr_code[4];
            $check_local_encode_code = substr(md5("{$code}||{$password}||{$now_time}||{$i_survey_adm_id}||". PROJECT_ENCODE_KEY), 8, 16);
            if ($check_val_encode_str !== $check_local_encode_code) {
                die("验证失败，无法提交!");
            }
            $get_isuv_user_info = getDataByConditionCi("i_survey_user", " AND code=? AND password=?", '*', true, [$code, $password]);
            if(empty($get_isuv_user_info)){
                die("账号或密码错误!");
            }
            if($get_isuv_user_info['status'] != 1){
                die("账号被禁用!");
            }
            //登录成功，记录SESSION
            $data_session = array(
                'i_survey_name' => $get_isuv_user_info['name'],
                'i_survey_account' => $code,
                'i_survey_id' => $get_isuv_user_info['id'],
                'i_survey_adm_id' => $i_survey_adm_id,//后台管理者编辑的信息，从i_survey_adm平台过来的记录
            );

            $this->session->set_userdata($data_session);
            redirect("/i_survey/dc_index");
        }


        $data_post = $this->input->post();
        if(!empty($data_post)){
            if(empty($data_post['account'])) _back_msg('error',"请输入账号！");
            if(empty($data_post['pwd'])) _back_msg('error',"请输入密码！");
            $account = trim($data_post['account']);
            $pwd = trim($data_post['pwd']);
            $get_isuv_user_info = getDataByConditionCi("i_survey_user", " AND code=?", '*', true, [$account]);
            if(empty($get_isuv_user_info) || $pwd != $get_isuv_user_info['password']){
                _back_msg('error',"账号或密码错误！");
            }
            if($get_isuv_user_info['status'] != 1){
                _back_msg('error',"账号被禁用！");
            }

            //登录成功，记录SESSION
            $data_session = array(
                'i_survey_name' => $get_isuv_user_info['name'],
                'i_survey_account' => $account,
                'i_survey_id' => $get_isuv_user_info['id'],
            );

            $this->session->set_userdata($data_session);
            _back_msg('success',"登录成功！", "/i_survey/dc_index");
        } else {
            $i_survey_account = $this->session->userdata('i_survey_account');
            if($i_survey_account){
                redirect('/i_survey/dc_index');
            }
            $this->load->view("/i_survey/login");
        }
    }

    /**
     * 登录退出
     */
    public function out(){
        $this->session->unset_userdata('i_survey_account');
        $this->session->unset_userdata('i_survey_id');
        redirect('/i_survey/login');
    }

    //获取进行的项目列表
    public function index(){
        $client_session = $this->session->userdata('i_survey_account');
        if(empty($client_session)){
            redirect('/i_survey/login');
        }

        //        $page = PAGE_NUM;
        $page = 12;
        $offset = $this->input->get('per_page', true);
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*$page : 0;
        //检测符合展示条件的项目信息
        $pids = $this->start_project();
        $project_list = [];
        $pagination = "";
        if ($pids) {
            $pids_info = implode(",", $pids);
            $project_list = getDataByCondition('app_project a left join app_admin b on a.pm_id=b.id', " and a.id in({$pids_info}) order by a.id desc limit {$offset},{$page}", "a.*,b.name as admin_name");
            $total = getDataNumByCondition('app_project', " and id in(".implode(",", $pids).")");
            //查询分页
            $url = '/i_survey/index';
            $pagination = getPagination($total, $url, $page);
        }


        //项目类型
        $arr_pro_type = $this->config->item("arr_pro_type");


        $data =[
            'title'=> "ISURVEY",
            'pagination'    => $pagination,
            'project_list'    => $project_list,
            'arr_pro_type'    => $arr_pro_type,
            'offset'    => $offset,

        ];
        $this->load->view("/i_survey/index",$data);
    }

    //问卷链接
    public function implement_list(){
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_id = $i_survey_user['i_survey_id'];

        $pid = (int)trim($this->uri->segment(3));
        if ($pid <= 0) {
            redirect('/i_survey/index');
        }
        $project_info = getDataByConditionCi('app_project', " and id=?", "*", true, [$pid]);
        if (!$project_info || $project_info['pro_status'] != 3) {//不是进行状态
            redirect('/i_survey/index');
        }
        $offset = $this->input->get('per_page', true);
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*PAGE_NUM : 0;
        $page = PAGE_NUM;
        $cond = "";
        //允许的科室
        $department = $project_info['allow_department'] ? explode(",", $project_info['allow_department']) : "";
        if ($department) {
            $department_info = "'".implode("','", $department)."'";
            $cond .= " AND department in({$department_info})";
        }
        //允许的职称
        $job_title = $project_info['allow_job_title'] ? explode(",", $project_info['allow_job_title']) : "";
        if ($job_title) {
            $job_title_info = "'".implode("','", $job_title)."'";
            $cond .= " AND job_title in({$job_title_info})";
        }
        //允许的医院等级
        $unit_level = $project_info['allow_unit_level'] ? explode(",", $project_info['allow_unit_level']) : "";
        if ($unit_level) {
            $unit_level_info = "'".implode("','", $unit_level)."'";
            $cond .= " AND unit_level in({$unit_level_info})";
        }
        //允许的医院
        $unit_name = $project_info['allow_unit_name'] ? explode(",", $project_info['allow_unit_name']) : "";
        if ($unit_name) {
            $unit_name_info = "'".implode("','", $unit_name)."'";
            $cond .= " AND unit_name in({$unit_name_info})";
        }

        //获取符合条件的配额
        $quota_ids = $this->get_quota($pid);
        if ($quota_ids) {
            $cond .= " AND quta_id in(".implode(",", $quota_ids).")";
        }

        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);
        $province = $city = "";
        if (strpos($ip_addr, "-") !== false) {
            //根据省份城市筛选结果
            $area_arr = explode("-", $ip_addr);
            $province = $area_arr[1];
            $city = $area_arr[2];
        }

        //获取此项目中，所有的被此用户点击的记录
        $project_bespeak_list = getDataByConditionCi("i_survey_project_bespeak", " and pid=?", "*", false, [$pid]);
        $pid_ids = [];
        $pid_not_ids = [];
        if ($project_bespeak_list) {
            foreach ($project_bespeak_list as $v_bespeak) {
                if ($i_survey_id == $v_bespeak['i_survey_id']) {//是本人点击
                    $pid_ids[$v_bespeak['pid_id']] = $v_bespeak['pid_id'];
                } else {//非本人点击
                    $pid_not_ids[$v_bespeak['pid_id']] = $v_bespeak['pid_id'];
                }
            }
        }
        if ($pid_not_ids) {//存在非本人点击
            $cond .= " AND id not in(".implode(",", $pid_not_ids).")";
        }

        $project_list = getDataByCondition("app_project_implement_{$pid}", " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name ORDER BY RAND() limit {$offset},{$page}");

        $total = getDataNumByCondition('app_project_implement_'.$pid, " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name");
        //查询分页
        $url = '/i_survey/implement_list/'.$pid;
        $pagination = getPagination($total,$url);


        $data =[
            'title'=> "ISURVEY",
            'pagination'    => $pagination,
            'pid'    => $pid,
            'project_info'    => $project_info,
            'project_list'    => $project_list,
            'province'    => $province,
            'city'    => $city,
            'ip_addr'    => $ip_addr,
        ];
        $this->load->view("/i_survey/implement_list",$data);
    }

    //获取进行的项目列表
    public function dc_survey_report(){
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_id = $i_survey_user['i_survey_id'];

        $offset = $this->input->get('per_page', true);

        $page = PAGE_NUM;
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*$page : 0;

        $project_list = getDataByCondition('i_survey_dc_hand_project a left join app_project b on a.pid=b.id', " and a.i_survey_id='{$i_survey_id}' AND a.complete_c>0 order by a.pid desc limit {$offset},{$page}", "a.*,b.pro_name");
        echo "<!--";
        echo $this->db->last_query();
        echo "-->";

        //统计总收入
        $project_total = $this->db->query("SELECT sum(complete_c) as complete_c,sum(finish_c) as finish_c,sum(estimated_revenue) as estimated_revenue,sum(real_income) as real_income FROM i_survey_dc_hand_project WHERE i_survey_id='{$i_survey_id}'")->row_array();

        $total = getDataNumByCondition('i_survey_dc_hand_project', " and i_survey_id='{$i_survey_id}'");
        //查询分页
        $url = '/i_survey/dc_survey_report';
        $pagination = getPagination($total, $url, $page);


        //项目类型
        $arr_pro_type = $this->config->item("arr_pro_type");


        $data =[
            'title'=> "ISURVEY",
            'pagination'    => $pagination,
            'project_list'    => $project_list,
            'arr_pro_type'    => $arr_pro_type,
            'offset'    => $offset,
            'project_total'    => $project_total,

        ];
        $this->load->view("/i_survey/header",$data);
        $this->load->view("/i_survey/dc_survey_report",$data);
        $this->load->view("/i_survey/footer",$data);
    }

    //获取进行的项目列表
    public function dc_index(){
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_id = $i_survey_user['i_survey_id'];
//        $page = PAGE_NUM;
//        $page = 12;
//        $offset = $this->input->get('per_page', true);
//        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*$page : 0;
        //检测符合展示条件的项目信息
        $pids = $this->start_project();
        $project_list = [];
        $pagination = "";
        if ($pids) {
            $pids_info = implode(",", $pids);
//            $project_list = getDataByCondition('app_project a left join app_admin b on a.pm_id=b.id', " and a.id in({$pids_info}) order by a.id desc limit {$offset},{$page}", "a.*,b.name as admin_name");
            $project_list = getDataByCondition('app_project a left join app_admin b on a.pm_id=b.id', " and a.id in({$pids_info}) order by a.id desc", "a.*,b.name as admin_name");

//            $total = getDataNumByCondition('app_project', " and id in(".implode(",", $pids).")");
            //查询分页
//            $url = '/i_survey/dc_index';
//            $pagination = getPagination($total, $url, $page);
        }

        //项目类型
        $arr_pro_type = $this->config->item("arr_pro_type");

        $data =[
            'title'=> "ISURVEY",
//            'pagination'    => $pagination,
            'project_list'    => $project_list,
            'arr_pro_type'    => $arr_pro_type,
//            'offset'    => $offset,

        ];
        $this->load->view("/i_survey/header",$data);
        $this->load->view("/i_survey/dc_index",$data);
        $this->load->view("/i_survey/footer",$data);
    }

    //问卷链接
    public function dc_implement_list(){
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_id = $i_survey_user['i_survey_id'];

        $pid = (int)trim($this->uri->segment(3));
        if ($pid <= 0) {
            redirect('/i_survey/dc_index');
        }
        $project_info = getDataByConditionCi('app_project', " and id=?", "*", true, [$pid]);
        if (!$project_info || $project_info['pro_status'] != 3) {//不是进行状态
            redirect('/i_survey/dc_index');
        }
        $page = 30;//老板要求，改成每次随机出来30个 2020-05-02
        $offset = $this->input->get('per_page', true);
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*$page : 0;
//        $page = PAGE_NUM;
//        $page = 2000;


        $cond = $cond_is_click = "";
        //允许的科室
        $department = $project_info['allow_department'] ? explode(",", $project_info['allow_department']) : "";
        if ($department) {
            $department_info = "'".implode("','", $department)."'";
            $cond .= " AND department in({$department_info})";
        }
        //允许的职称
        $job_title = $project_info['allow_job_title'] ? explode(",", $project_info['allow_job_title']) : "";
        if ($job_title) {
            $job_title_info = "'".implode("','", $job_title)."'";
            $cond .= " AND job_title in({$job_title_info})";
        }
        //允许的医院等级
        $unit_level = $project_info['allow_unit_level'] ? explode(",", $project_info['allow_unit_level']) : "";
        if ($unit_level) {
            $unit_level_info = "'".implode("','", $unit_level)."'";
            $cond .= " AND unit_level in({$unit_level_info})";
        }
        //允许的医院
        $unit_name = $project_info['allow_unit_name'] ? explode(",", $project_info['allow_unit_name']) : "";
        if ($unit_name) {
            $unit_name_info = "'".implode("','", $unit_name)."'";
            $cond .= " AND unit_name in({$unit_name_info})";
        }

        //获取符合条件的配额
        $quota_ids = $this->get_quota($pid);
        $quota_cond = "";
        if ($quota_ids) {
            $cond .= " AND quta_id in(".implode(",", $quota_ids).")";
            $quota_cond = " AND quta_id in(".implode(",", $quota_ids).")";
        }

        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);
        $province = $city = "";
        if (strpos($ip_addr, "-") !== false) {
            //根据省份城市筛选结果
            $area_arr = explode("-", $ip_addr);
            $province = $area_arr[1];
            $city = $area_arr[2];
        }

        //禁止的省份
        $arr_forbid_city = [];
        if ($project_info['forbid_city']) {
            $arr_forbid_city = explode(",", $project_info['forbid_city']);
            $forbid_city = "'".implode("','", $arr_forbid_city)."'";
            $cond .= " AND (province not in({$forbid_city}) and city not in({$forbid_city}) and district not in({$forbid_city}))";
        }

        $get_data = $this->input->get();
        $search_area_info = $search_department = $search_unit_level = $search_job_title = "";
        if ($get_data) {
            $search_area_info = trim($get_data['area_info']);
            if ($search_area_info) {//存在地区
                $cond .= " AND (province like '%{$search_area_info}%' OR city like '%{$search_area_info}%' OR district like '%{$search_area_info}%')";
            }
            $search_department = trim($get_data['department']);
            if ($search_department) {//存在科室
                $cond .= " AND (department like '%{$search_department}%')";
            }
            $search_unit_level = trim($get_data['unit_level']);
            if ($search_unit_level) {//存在医院等级
                $cond .= " AND (unit_level='{$search_unit_level}')";
            }
            $search_job_title = trim($get_data['job_title']);
            if ($search_job_title) {//存在职称
                $cond .= " AND (job_title='{$search_job_title}')";
            }
        }

        //获取此项目中，所有的被此用户点击的记录
        $project_bespeak_list = getDataByConditionCi("i_survey_project_bespeak", " and pid=?", "*", false, [$pid]);
        $pid_ids = [];
        $pid_not_ids = [];
        if ($project_bespeak_list) {
            foreach ($project_bespeak_list as $v_bespeak) {
                if ($i_survey_id == $v_bespeak['i_survey_id']) {//是本人点击
                    $pid_ids[$v_bespeak['pid_id']] = $v_bespeak['pid_id'];
                } else {//非本人点击
                    $pid_not_ids[$v_bespeak['pid_id']] = $v_bespeak['pid_id'];
                }
            }
        }

        if ($pid_ids) {//本人点击
            $cond_is_click .= " AND id in(".implode(",", $pid_ids).")";
            $cond_is_click .= $cond;
        }
        $cond .=  " AND click_time=0";

        $project_list = getDataByCondition("app_project_implement_{$pid}", " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name ORDER BY RAND() limit {$page}");

        //点击过的记录
        $project_list_click = [];
        if ($pid_ids) {
            $project_list_click = getDataByCondition("app_project_implement_{$pid}", " and member_uid > 0 AND finish_status='' AND original_finish_status = '' {$cond_is_click}");
        }



        //统计此项目的，医院等级，职称
        $unit_level = getDataByCondition("app_project_implement_{$pid}", " {$quota_cond} GROUP BY unit_level");

        $job_title = getDataByCondition("app_project_implement_{$pid}", " {$quota_cond} GROUP BY job_title");

        $data =[
            'title'=> "ISURVEY",
//            'pagination'    => $pagination,
            'pid'    => $pid,
            'project_info'    => $project_info,
            'project_list'    => $project_list,
            'province'    => $province,
            'city'    => $city,
            'ip_addr'    => $ip_addr,
            'arr_forbid_city'    => $arr_forbid_city,
            'unit_level'    => $unit_level,
            'job_title'    => $job_title,

            'search_area_info'    => $search_area_info,
            'search_department'    => $search_department,
            'search_unit_level'    => $search_unit_level,
            'search_job_title'    => $search_job_title,
            'project_list_click'    => $project_list_click,
        ];
        $this->load->view("/i_survey/header",$data);
        $this->load->view("/i_survey/dc_implement_list",$data);
        $this->load->view("/i_survey/footer",$data);
    }

//    //问卷链接
//    public function dc_implement_list(){
////                error_reporting(-1);
////                ini_set('display_errors', 1);
//        //登录验证
//        $i_survey_user = $this->check_login();
//        $i_survey_id = $i_survey_user['i_survey_id'];
//
//        $pid = (int)trim($this->uri->segment(3));
//        if ($pid <= 0) {
//            redirect('/i_survey/dc_index');
//        }
//        $project_info = getDataByConditionCi('app_project', " and id=?", "*", true, [$pid]);
//        if (!$project_info || $project_info['pro_status'] != 3) {//不是进行状态
//            redirect('/i_survey/dc_index');
//        }
//        $page = 1;//老板要求，改成每次随机出来1个,如果存在已点击的记录，优先显示已点击未完成的记录 2020-05-02
//        $offset = $this->input->get('per_page', true);
//        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*$page : 0;
////        $page = PAGE_NUM;
////        $page = 2000;
//
//
//        $cond = $cond_is_click = "";
//        //允许的科室
//        $department = $project_info['allow_department'] ? explode(",", $project_info['allow_department']) : "";
//        if ($department) {
//            $department_info = "'".implode("','", $department)."'";
//            $cond .= " AND department in({$department_info})";
//        }
//        //允许的职称
//        $job_title = $project_info['allow_job_title'] ? explode(",", $project_info['allow_job_title']) : "";
//        if ($job_title) {
//            $job_title_info = "'".implode("','", $job_title)."'";
//            $cond .= " AND job_title in({$job_title_info})";
//        }
//        //允许的医院等级
//        $unit_level = $project_info['allow_unit_level'] ? explode(",", $project_info['allow_unit_level']) : "";
//        if ($unit_level) {
//            $unit_level_info = "'".implode("','", $unit_level)."'";
//            $cond .= " AND unit_level in({$unit_level_info})";
//        }
//        //允许的医院
//        $unit_name = $project_info['allow_unit_name'] ? explode(",", $project_info['allow_unit_name']) : "";
//        if ($unit_name) {
//            $unit_name_info = "'".implode("','", $unit_name)."'";
//            $cond .= " AND unit_name in({$unit_name_info})";
//        }
//
//        //获取符合条件的配额
//        $quota_ids = $this->get_quota($pid);
//        $quota_cond = "";
//        if ($quota_ids) {
//            $cond .= " AND quta_id in(".implode(",", $quota_ids).")";
//            $quota_cond = " AND quta_id in(".implode(",", $quota_ids).")";
//        }
//
//        $local_ip = getip();
//        $ip = ip2long($local_ip);
//        $ip_addr = ip2location($ip);
//        $province = $city = "";
//        if (strpos($ip_addr, "-") !== false) {
//            //根据省份城市筛选结果
//            $area_arr = explode("-", $ip_addr);
//            $province = $area_arr[1];
//            $city = $area_arr[2];
//        }
//
//        //禁止的省份
//        $arr_forbid_city = [];
//        if ($project_info['forbid_city']) {
//            $arr_forbid_city = explode(",", $project_info['forbid_city']);
//            $forbid_city = "'".implode("','", $arr_forbid_city)."'";
//            $cond .= " AND (province not in({$forbid_city}) and city not in({$forbid_city}) and district not in({$forbid_city}))";
//        }
//
//        $get_data = $this->input->get();
//        $search_area_info = $search_department = $search_unit_level = $search_job_title = "";
//        if ($get_data) {
//            $search_area_info = trim($get_data['area_info']);
//            if ($search_area_info) {//存在地区
//                $cond .= " AND (province like '%{$search_area_info}%' OR city like '%{$search_area_info}%' OR district like '%{$search_area_info}%')";
//            }
//            $search_department = trim($get_data['department']);
//            if ($search_department) {//存在科室
//                $cond .= " AND (department like '%{$search_department}%')";
//            }
//            $search_unit_level = trim($get_data['unit_level']);
//            if ($search_unit_level) {//存在医院等级
//                $cond .= " AND (unit_level='{$search_unit_level}')";
//            }
//            $search_job_title = trim($get_data['job_title']);
//            if ($search_job_title) {//存在职称
//                $cond .= " AND (job_title='{$search_job_title}')";
//            }
//        }
//
//        //获取此项目中，所有的被此用户点击的记录
//        $project_bespeak_list = getDataByConditionCi("i_survey_project_bespeak", " and pid=?", "*", false, [$pid]);
//        $pid_ids = [];
//        $pid_not_ids = [];
//        if ($project_bespeak_list) {
//            foreach ($project_bespeak_list as $v_bespeak) {
//                if ($i_survey_id == $v_bespeak['i_survey_id']) {//是本人点击
//                    $pid_ids[$v_bespeak['pid_id']] = $v_bespeak['pid_id'];
//                } else {//非本人点击
//                    $pid_not_ids[$v_bespeak['pid_id']] = $v_bespeak['pid_id'];
//                }
//            }
//        }
//
////        if ($this->input->get("act") == "amy") {
//////            $cond_is_click = $cond." AND id in(".implode(",", $pid_ids).")";
//////            $cond .=  " AND click_time=0";
////            if ($pid_ids) {//本人点击
////                $cond_is_click .= " AND id in(".implode(",", $pid_ids).")";
////            }
////            $cond_is_click .= $cond;
////            $cond .=  " AND click_time=0";
////        } else {
////            if ($pid_not_ids) {//存在非本人点击
////                $cond .= " AND (id not in(".implode(",", $pid_not_ids).") OR click_time=0)";
////            } else {
////                if($pid_ids) {
////                    $cond .=  " AND (id in(".implode(",", $pid_ids).") OR click_time=0)";
////                } else {
////                    $cond .=  " AND click_time=0";
////                }
////            }
////        }
//
//        if ($pid_ids) {//本人点击
//            $cond_is_click .= " AND id in(".implode(",", $pid_ids).")";
//            $cond_is_click .= $cond;
//        }
//        $cond .=  " AND click_time=0";
//
//
////        echo "<!--";
////        echo " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name ORDER BY RAND() limit {$offset},{$page}";
////        echo "-->";
//        //
//
//        //点击过的记录
//        $project_list_click = $project_list = [];
//        if ($pid_ids) {//每次只出现一条记录
//            $project_list_click = getDataByCondition("app_project_implement_{$pid}", " and member_uid > 0 AND finish_status='' AND original_finish_status = '' {$cond_is_click} LIMIT 1", "*", true);
//        }
//
//        if (!$project_list_click) {//不存在已点击的记录时，才随机分配一条未点击的记录
////        $project_list = getDataByCondition("app_project_implement_{$pid}", " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name ORDER BY RAND() limit {$offset},{$page}");
//            $project_list_click = getDataByCondition("app_project_implement_{$pid}", " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name ORDER BY RAND() LIMIT 1", "*", true);
//            echo "<!--".$this->db->last_query()."-->";
//        }
//
//
////        $project_list = getDataByCondition("app_project_implement_{$pid}", " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name ORDER BY RAND()");
////        echo "<!--";
////        echo " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name ORDER BY RAND() limit {$offset},{$page}";
////        echo "-->";
//
//        //老板要求，分页去掉 2020-05-02
////        $total = getDataNumByCondition('app_project_implement_'.$pid, " and member_uid > 0 AND finish_status='' AND original_finish_status = '' AND screening_status = 0 {$cond} group by unit_name");
//////        $total = 1;
////        //查询分页
////        $url = '/i_survey/implement_list/'.$pid;
////        $pagination = getPagination($total,$url);
//
//        //统计此项目的，医院等级，职称
//        $unit_level = getDataByCondition("app_project_implement_{$pid}", " {$quota_cond} GROUP BY unit_level");
//
//        $job_title = getDataByCondition("app_project_implement_{$pid}", " {$quota_cond} GROUP BY job_title");
//
//
//        $data =[
//            'title'=> "ISURVEY",
////            'pagination'    => $pagination,
//            'pid'    => $pid,
//            'project_info'    => $project_info,
////            'project_list'    => $project_list,
//            'province'    => $province,
//            'city'    => $city,
//            'ip_addr'    => $ip_addr,
//            'arr_forbid_city'    => $arr_forbid_city,
//            'unit_level'    => $unit_level,
//            'job_title'    => $job_title,
//
//            'search_area_info'    => $search_area_info,
//            'search_department'    => $search_department,
//            'search_unit_level'    => $search_unit_level,
//            'search_job_title'    => $search_job_title,
//            'project_list_click'    => $project_list_click,
//        ];
//        $this->load->view("/i_survey/dc_implement_list",$data);
//    }

    //项目中转站
    public function project_redirect()
    {
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_id = $i_survey_user['i_survey_id'];

        $link_info = trim($this->uri->segment(3));
        $link_info_arr = explode("_", $link_info);
        $pid = $link_info_arr[0];
        $pid_id = $link_info_arr[1];
        $decrypt_info = $link_info_arr[2];//加密结果
        $res_pc_info = str_md5_code($pid."_".$pid_id.PROJECT_ENCODE_KEY);
        if ($decrypt_info != $res_pc_info || !$pid || !$pid_id) {//加密结果不匹配
            redirect("/i_survey/dc_index");
        }
        //检测表是否存在
        $check_table = check_table_exist("app_project_implement_".$pid);
        if (!$check_table) {//表不存在
            redirect("/i_survey/dc_index");
        }
        $project_info = getDataByConditionCi('app_project', " and id=?", "*", true, [$pid]);
        if (!$project_info || $project_info['pro_status'] != 3) {//不是进行状态
            redirect('/i_survey/dc_index');
        }

        //通过项目及项目编号查询问卷链接信息
        $implement_info = getDataByConditionCi("app_project_implement_{$pid}", " AND id = ? LIMIT 1", "*", true, [$pid_id]);
        if (!$implement_info || !$implement_info['partner_uid']) {//记录是否存在
            redirect("/i_survey/dc_index");
        }

        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);

        $province = $city = "";
        if (strpos($ip_addr, "-") !== false) {
            //根据省份城市筛选结果
            $area_arr = explode("-", $ip_addr);
            $province = $area_arr[1];
            $city = $area_arr[2];
        }

        if ($project_info['is_ip_astrict'] == 1) {//需要检测ip
            $is_show_link = true;
            if ($implement_info['province'] && $implement_info['province'] != $province){
                $is_show_link = false;
            }
            if ($implement_info['city'] && $implement_info['city'] != $city){
                $is_show_link = false;
            }
            if (!$is_show_link) {
                redirect("/i_survey/implement_list/".$pid);
            }
        }

        //点击记录录入
        $res = $this->project_click_info($project_info, $implement_info, $pid, $pid_id, $local_ip, $ip_addr);
        if (!$res) {
            echo "Click information record failed";die;
        }

        $uid_str = project_link_encode($pid, $implement_info['partner_id'], $implement_info['groupno']);
        $partner_link = DRSAY_WEB . 'go/s/' . $uid_str . '?';
        $res_partner_link = !empty($implement_info['partner_uid']) ? $partner_link.$implement_info['partner_uid'] : '';
        if ($res_partner_link) {//跳转到问卷中
            redirect($res_partner_link);
        }
    }

    ############  公共方法  ############
    //点击记录
    private function project_click_info($project_info, $implement_info, $pid, $pid_id, $local_ip, $ip_addr)
    {
        if (!$project_info || !$implement_info || !$pid || !$pid_id || !$local_ip || !$ip_addr) {return false;}
        $i_survey_id = $this->session->userdata('i_survey_id');
        //记录点击信息
        $bespeak_info = getDataByConditionCi('i_survey_project_bespeak', " and pid = ? AND pid_id = ? LIMIT 1", "*", true, [$pid, $pid_id]);
        if ($bespeak_info && $bespeak_info['i_survey_id'] != $i_survey_id) {//此记录已经被人点击过，跳到问卷项目列表
            redirect("/i_survey/dc_implement_list/".$pid);
        }
        $hand_project_info = getDataByConditionCi('i_survey_dc_hand_project', " and pid = ? AND i_survey_id = ? LIMIT 1", "*", true, [$pid, $i_survey_id]);
        if (!$hand_project_info) {//已经存在，更新信息
            $insert_data_hand = [
                "pid" => $pid,
                "i_survey_id" => $this->session->userdata('i_survey_id'),
                "cost" => $project_info['recommend_money'] + $project_info['answer_question_money'],//礼金
            ];
            $this->db->insert("i_survey_dc_hand_project", $insert_data_hand);
        }
        //通过项目编号，获取项目信息
        $pro_info = $this->db->query("SELECT * FROM app_project WHERE id=?", [$pid])->row_array();

        if ($bespeak_info) {//已经存在，更新信息
            $update_data = [
                "update_time" => time(),
                "i_survey_id" => $this->session->userdata('i_survey_id'),
                "in_ip" => $local_ip,
                "in_ip_address" => $ip_addr,
            ];
            $this->db->where("id", $bespeak_info['id']);
            $res = $this->db->update("i_survey_project_bespeak", $update_data);
        } else {//添加信息
            $insert_data = [
                "pid" => $pid,
                "pid_id" => $pid_id,
                "add_time" => time(),
                "i_survey_id" => $this->session->userdata('i_survey_id'),
                "in_ip" => $local_ip,
                "in_ip_address" => $ip_addr,
                "partner_uid" => $implement_info['partner_uid'],
                "member_uid" => $implement_info['member_uid'],
                "dr_name" => $implement_info['name'],
                "province" => $implement_info['province'],
                "city" => $implement_info['city'],
                "cost" => $pro_info['answer_question_money'],
            ];
            $res = $this->db->insert("i_survey_project_bespeak", $insert_data);
        }
        return $res ? true : false;
    }

    //获取进行中的项目信息
    private function start_project()
    {
        //进行的项目
        $project_list = getDataByCondition('app_project', " and pro_status = 3 AND pro_type=1 AND pro_sample_num > c_num AND is_request_support=1");
        $pids = $res_pids = [];
        if ($project_list) {//检测内部资源的外包是否是进行状态
            foreach ($project_list as $v) {
                $pids[$v['id']] = $v['id'];
            }
            //项目进行，内部资源，外包进行
            $partner_info = getDataByCondition('app_project_partner', " AND project_id in(".implode(',', $pids).") and property = 1 AND status=1");

            if ($partner_info) {
                foreach ($partner_info as $v_partner) {
                    //检测每个项目外包是否有数据
                    $implement_info = getDataByCondition("app_project_implement_{$v_partner['project_id']}", " AND partner_id={$v_partner['partner_id']} limit 1", "*", true);
                    if ($implement_info) {
                        $res_pids[$v_partner['project_id']] = $v_partner['project_id'];
                    }
                }
            }
        }
        return $res_pids;
    }

    //获取符合条件的配额
    private function get_quota($project_id)
    {
        if (!$project_id) {return false;}
        //获取此项目符合条件的配额
        $quota_list = getDataByConditionCi("app_project_quta", " and pid=? AND is_db=1 AND quta_status = ".QUOTA_STATUS_OPENING." AND mount>now_c_mount", "*", false, [$project_id]);
        $quotas = [];
        if ($quota_list) {
            foreach ($quota_list as $v_quota) {
                $quotas[$v_quota['id']] = $v_quota['id'];
            }
        }
        return $quotas;
    }

    //登录验证
    private function check_login()
    {
        $i_survey_account = $this->session->userdata('i_survey_account');
        $i_survey_id = $this->session->userdata('i_survey_id');
        $i_survey_adm_id = $this->session->userdata('i_survey_adm_id');
        if(empty($i_survey_account)){
            redirect('/i_survey/login');
        }
        return ["i_survey_id" => $i_survey_id,"i_survey_account" => $i_survey_account,"i_survey_adm_id" => $i_survey_adm_id];
    }


}
