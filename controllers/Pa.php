<?php

/*
 * bryant-2020-09-30
 * 赛小欧支付前登录认证
 */

class Pa extends CI_Controller {

    public $lang_ary;
    public $lang_version;
    public $lang_info;

    public function __construct() {
        parent::__construct();
        $this->load->model('survey_model');

        //获取当前最新的语言版本
        $this->lang_version = get_lang_version();
        $this->lang_info = 140; //默认中文简体
        $this->lang_ary = Redis_db::getInstance()->get("web_lang", "web_" . $this->lang_version . "_" . $this->lang_info);
        if (!$this->lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $this->lang_ary = Redis_db::getInstance()->get("web_lang", "web_" . $this->lang_version . "_" . $this->lang_info);
        }
    }

    /**
     * 赛小欧支付登录授权
     */
    public function s_auth() {
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证手机
            $verify_mobile = isset($post_data['verify_mobile']) ? $post_data['verify_mobile'] : '';
            //验证码
            $verify_code = isset($post_data['verify_code']) ? $post_data['verify_code'] : '';
            //认证加密串
            $scene = isset($post_data['scene']) ? $post_data['scene'] : '';
            if (empty($verify_mobile)) {
                _back_msg("error", "请输入手机号码！");
            }
            if (!check_mobile($verify_mobile)) {
                _back_msg("error", "请输入正确手机号码格式！");
            }
            if (empty($verify_code)) {
                _back_msg("error", "请输入验证码！");
            }
            if (empty($scene)) {
                _back_msg("error", "参数错误！");
            }
            try {
                $country = 68;
                $arr_scene = explode("_", $scene);
                $pid = $arr_scene[0];
                $member_uid = $arr_scene[1];
                $sxo_id = $arr_scene[2];
                $encrypted_data = $arr_scene[3];
                if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                    throw new Exception("缺少参数，请重试！");
                }
                $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
                $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
                if ($encrypted_data !== $decrypt_data) {//解密值不等
                    throw new Exception("参数错误！");
                }
                //根据sxo-id查询是否存在记录信息，且医师手机是否等于获取验证码手机
                $sxo_info = $this->db->select('id,dr_mobile')->where(['id' => $sxo_id, 'pay_status' => 2])->get('mb_sxo', 1)->row_array();
                if (!$sxo_info) {
                    throw new Exception("支付记录不存在！");
                }
                if ($sxo_info['dr_mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_LOGIN);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                    throw new Exception($this->lang_ary[LABEL_ACCOUNT_VERIFICATION_CODE_ERROR]);
                }
                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                
                //设置登录成功session
                $this->session->set_userdata('sxo_payment_authentication','1');
                if($this->session->has_userdata('sxo_payment_authentication')){
                    $redirect_url = DRSAY_WEB . 'sxo/paysxo/' . $scene;
                    _back_msg("success", "验证通过", $redirect_url);
                }else{
                    throw new Exception("未验证通过，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $uid_code = $this->uri->segment(3);
            if (!$uid_code) {
                $this->survey_model->get_redirect_info("/sxo/paysxo", "l_errDRSAY".$uid_code);
            }
            //验证账号是否有误
            $arr_scene = explode("_", $uid_code);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                $this->survey_model->get_redirect_info("/sxo/paysxo", "l_errDRSAY".$uid_code);
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                $this->survey_model->get_redirect_info("/sxo/paysxo", "l_errDRSAY".$uid_code);
            }

            $data = array("scene" => $uid_code);
            $this->load->view("/payment_auth/s_auth", $data);
        }
    }

    /**
     * 获取短信验证码
     * 支付授权验证码获取（赛小欧）
     * @throws Exception
     */
    public function obtain_sms() {

        $post_data = $this->input->post(['scene', 'verify_mobile'], true);
        $post_data = format_post_data($post_data);
        //验证码
        $scene = $post_data['scene'];
        $verify_mobile = $post_data['verify_mobile'];
        if (empty($verify_mobile)) {
            _back_msg("error", "请输入手机号码！");
        }
        if (!check_mobile($verify_mobile)) {
            _back_msg("error", "请输入正确手机号码格式！");
        }
        if (empty($scene)) {
            _back_msg("error", "参数错误！");
        }
        try {
            $country = 68;
            $arr_scene = explode("_", $scene);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                throw new Exception("缺少参数，请重试！");
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                throw new Exception("参数错误！");
            }
            //根据sxo-id查询是否存在记录信息，且医师手机是否等于获取验证码手机
            $sxo_info = $this->db->select('id,dr_mobile')->where(['id' => $sxo_id, 'pay_status' => 2])->get('mb_sxo', 1)->row_array();
            if (!$sxo_info) {
                throw new Exception("未存在正确的支付信息，已支付或未申请！");
            }
            if ($sxo_info['dr_mobile'] != $verify_mobile) {//验证手机号码
                throw new Exception("手机号有误，请确认后重新输入！");
            }

            //发送短信验证码
            $vcode = rand(10000, 99999);
            $sms_log_code = SMS_LOG_CODE_SXO_PAY_AUTH;
            $content = $this->lang_ary[LABEL_COMM_MOB_TEMPLATE]; //国际短信通道使用
            $err_msg = "";
            if ($country == '68') {//中国短信，使用send_cloud发送
                //创蓝
                if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") {//本地
                    $st = true;
                } else {
                    $sms_content = "上医说验证码:{$vcode}，" . SMS_ACTIVE_TIME . "分钟内有效，请勿泄漏！"; //国际短信通道使用
                    $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                }
                if ($st === true) {
                    $send_st = true;
                } else {
                    $send_st = false;
                    $err_msg = $st;
                }
            } else {//国外短信，使用国际短信通道
                $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                $area = getDataByID("app_sys_dictionary", $country);
                $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                if ($st == "OK") {
                    $send_st = true;
                } else {
                    $send_st = false;
                }
            }

            //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
            $type = VERIFICATION_CODE_LOGIN;
            if ($send_st) {//短信发送成功,记录入库
                $verify_data = array(
                    'mobile' => $verify_mobile,
                    'vcode' => $vcode,
                    'create_time' => time(),
                    'type' => (string) $type
                );
                $insert_id_sms_code = $this->set_vcode($verify_data);
                if ($insert_id_sms_code > 0) {
                    _back_msg('success', '短信发送成功，请查收！');
                } else {
                    throw new Exception("短信发送失败，请重试！");
                }
            } else {
                throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
            }
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }

    // 保存验证码数据
    private function set_vcode($data) {
        if (!$data) {
            return false;
        }
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }

    
    /**
     * 赛小欧推荐支付登录授权
     * @throws Exception
     */
    public function s_ref_auth(){
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证手机
            $verify_mobile = isset($post_data['verify_mobile']) ? $post_data['verify_mobile'] : '';
            //验证码
            $verify_code = isset($post_data['verify_code']) ? $post_data['verify_code'] : '';
            //认证加密串
            $scene = isset($post_data['scene']) ? $post_data['scene'] : '';
            if (empty($verify_mobile)) {
                _back_msg("error", "请输入手机号码！");
            }
            if (!check_mobile($verify_mobile)) {
                _back_msg("error", "请输入正确手机号码格式！");
            }
            if (empty($verify_code)) {
                _back_msg("error", "请输入验证码！");
            }
            if (empty($scene)) {
                _back_msg("error", "参数错误！");
            }
            try {
                $country = 68;
                $arr_scene = explode("_", $scene);
                $pid = $arr_scene[0];
                $member_uid = $arr_scene[1];
                $ref_id = $arr_scene[2];
                $encrypted_data = $arr_scene[3];
                if (!$pid || !$ref_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                    throw new Exception("缺少参数，请重试！");
                }
                $decrypt_scene = $pid . '_' . $member_uid . '_' . $ref_id;
                $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
                if ($encrypted_data !== $decrypt_data) {//解密值不等
                    throw new Exception("参数错误！");
                }
                //根据sxo-id查询是否存在记录信息，且医师手机是否等于获取验证码手机
                $sxo_info = $this->db->select('id,dr_mobile')
                    ->where(['id' => $ref_id, 'dr_id'=>$member_uid, 'pay_status' => 1,'send_sms_status'=>2])
                    ->get('mb_sxo_payment_referee', 1)->row_array();
                if (!$sxo_info) {
                    throw new Exception("支付记录不存在！");
                }
                if ($sxo_info['dr_mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_LOGIN);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                    throw new Exception($this->lang_ary[LABEL_ACCOUNT_VERIFICATION_CODE_ERROR]);
                }
                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                
                //设置登录成功session
                $this->session->set_userdata('sxo_payment_referee_authentication','1');
                if($this->session->has_userdata('sxo_payment_referee_authentication')){
                    $redirect_url = DRSAY_WEB . 'sxo/paysxoref/' . $scene;
                    _back_msg("success", "验证通过", $redirect_url);
                }else{
                    throw new Exception("未验证通过，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $uid_code = $this->uri->segment(3);
            if (!$uid_code) {
                $this->survey_model->get_redirect_info("/sxo/paysxoref", "l_errDRSAY".$uid_code);
            }
            //验证账号是否有误
            $arr_scene = explode("_", $uid_code);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $ref_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$ref_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                $this->survey_model->get_redirect_info("/sxo/paysxo", "l_errDRSAY".$uid_code);
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $ref_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                $this->survey_model->get_redirect_info("/sxo/paysxoref", "l_errDRSAY".$uid_code);
            }

            $data = array("scene" => $uid_code);
            $this->load->view("/payment_auth/s_ref_auth", $data);
        }
    }
    
    /**
     * 获取短信验证码
     * 支付授权验证码获取（赛小欧-推荐费提取）
     * @throws Exception
     */
    public function obtain_sms_ref() {

        $post_data = $this->input->post(['scene', 'verify_mobile'], true);
        $post_data = format_post_data($post_data);
        //验证码
        $scene = $post_data['scene'];
        $verify_mobile = $post_data['verify_mobile'];
        if (empty($verify_mobile)) {
            _back_msg("error", "请输入手机号码！");
        }
        if (!check_mobile($verify_mobile)) {
            _back_msg("error", "请输入正确手机号码格式！");
        }
        if (empty($scene)) {
            _back_msg("error", "参数错误！");
        }
        try {
            $country = 68;
            $arr_scene = explode("_", $scene);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $ref_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$ref_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                throw new Exception("缺少参数，请重试！");
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $ref_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                throw new Exception("参数错误！");
            }
            //根据ref_id查询是否存在记录信息，且医师手机是否等于获取验证码手机
            $sxo_info = $this->db->select('id,dr_mobile')
                    ->where(['id' => $ref_id, 'dr_id'=>$member_uid, 'pay_status' => 1,'send_sms_status'=>2])
                    ->get('mb_sxo_payment_referee', 1)->row_array();
            if (!$sxo_info) {
                throw new Exception("未存在正确的支付信息，已支付或未申请！");
            }
            if ($sxo_info['dr_mobile'] != $verify_mobile) {//验证手机号码
                throw new Exception("手机号有误，请确认后重新输入【与领取人手机号码不一致】！");
            }

            //发送短信验证码
            $vcode = rand(10000, 99999);
            $sms_log_code = SMS_LOG_CODE_SXO_PAY_AUTH;
            $content = $this->lang_ary[LABEL_COMM_MOB_TEMPLATE]; //国际短信通道使用
            $err_msg = "";
            if ($country == '68') {//中国短信，使用send_cloud发送
                //创蓝
                if ($_SERVER['HTTP_HOST'] == "dev.drsay.cn") {//本地
                    $st = true;
                } else {
                    $sms_content = "上医说验证码:{$vcode}，" . SMS_ACTIVE_TIME . "分钟内有效，请勿泄漏！"; //国际短信通道使用
                    $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                }
                if ($st === true) {
                    $send_st = true;
                } else {
                    $send_st = false;
                    $err_msg = $st;
                }
            } else {//国外短信，使用国际短信通道
                $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                $area = getDataByID("app_sys_dictionary", $country);
                $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                if ($st == "OK") {
                    $send_st = true;
                } else {
                    $send_st = false;
                }
            }

            //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
            $type = VERIFICATION_CODE_LOGIN;
            if ($send_st) {//短信发送成功,记录入库
                $verify_data = array(
                    'mobile' => $verify_mobile,
                    'vcode' => $vcode,
                    'create_time' => time(),
                    'type' => (string) $type
                );
                $insert_id_sms_code = $this->set_vcode($verify_data);
                if ($insert_id_sms_code > 0) {
                    _back_msg('success', '短信发送成功，请查收！');
                } else {
                    throw new Exception("短信发送失败，请重试！");
                }
            } else {
                throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
            }
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }
    
    
    /**
     * 赛小欧核验支付登录授权
     */
    public function verif_auth() {
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证手机
            $verify_mobile = isset($post_data['verify_mobile']) ? $post_data['verify_mobile'] : '';
            //验证码
            $verify_code = isset($post_data['verify_code']) ? $post_data['verify_code'] : '';
            //认证加密串
            $scene = isset($post_data['scene']) ? $post_data['scene'] : '';
            if (empty($verify_mobile)) {
                _back_msg("error", "请输入手机号码！");
            }
            if (!check_mobile($verify_mobile)) {
                _back_msg("error", "请输入正确手机号码格式！");
            }
            if (empty($verify_code)) {
                _back_msg("error", "请输入验证码！");
            }
            if (empty($scene)) {
                _back_msg("error", "参数错误！");
            }
            try {
                $country = 68;
                $arr_scene = explode("_", $scene);
                $pid = $arr_scene[0];
                $member_uid = $arr_scene[1];
                $sxo_id = $arr_scene[2];
                $encrypted_data = $arr_scene[3];
                if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                    throw new Exception("缺少参数，请重试！");
                }
                $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
                $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
                if ($encrypted_data !== $decrypt_data) {//解密值不等
                    throw new Exception("参数错误！");
                }
                //根据sxo-id查询是否存在记录信息，且医师手机是否等于获取验证码手机
                $sxo_info = $this->db->select('id,dr_mobile')->where(['sxo_id' => $sxo_id, 'status' => 1])
                        ->get('mb_sxo_verification_payment', 1)->row_array();
                if (!$sxo_info) {
                    throw new Exception("支付记录不存在！");
                }
                if ($sxo_info['dr_mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_LOGIN);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                    throw new Exception($this->lang_ary[LABEL_ACCOUNT_VERIFICATION_CODE_ERROR]);
                }
                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                
                //设置登录成功session
                $this->session->set_userdata('sxo_verifpay_authentication','1');
                if($this->session->has_userdata('sxo_verifpay_authentication')){
                    $redirect_url = DRSAY_WEB . 'sxo/verifpay/' . $scene;
                    _back_msg("success", "验证通过", $redirect_url);
                }else{
                    throw new Exception("未验证通过，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $uid_code = $this->uri->segment(3);
            if (!$uid_code) {
                $this->survey_model->get_redirect_info("/sxo/verifpay", "l_errDRSAY".$uid_code);
            }
            //验证账号是否有误
            $arr_scene = explode("_", $uid_code);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                $this->survey_model->get_redirect_info("/sxo/verifpay", "l_errDRSAY".$uid_code);
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                $this->survey_model->get_redirect_info("/sxo/verifpay", "l_errDRSAY".$uid_code);
            }

            $data = array("scene" => $uid_code);
            $this->load->view("/payment_auth/verif_auth", $data);
        }
    }
    
    /**
     * 获取短信验证码
     * 支付授权验证码获取（赛小欧-核验支付提取）
     * @throws Exception
     */
    public function verif_obtain_sms() {

        $post_data = $this->input->post(['scene', 'verify_mobile'], true);
        $post_data = format_post_data($post_data);
        //验证码
        $scene = $post_data['scene'];
        $verify_mobile = $post_data['verify_mobile'];
        if (empty($verify_mobile)) {
            _back_msg("error", "请输入手机号码！");
        }
        if (!check_mobile($verify_mobile)) {
            _back_msg("error", "请输入正确手机号码格式！");
        }
        if (empty($scene)) {
            _back_msg("error", "参数错误！");
        }
        try {
            $country = 68;
            $arr_scene = explode("_", $scene);
            $pid = $arr_scene[0];
            $member_uid = $arr_scene[1];
            $sxo_id = $arr_scene[2];
            $encrypted_data = $arr_scene[3];
            if (!$pid || !$sxo_id || !$member_uid || !$encrypted_data || count($arr_scene) != 4) {//参数有误
                throw new Exception("缺少参数，请重试！");
            }
            $decrypt_scene = $pid . '_' . $member_uid . '_' . $sxo_id;
            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 16);
            if ($encrypted_data !== $decrypt_data) {//解密值不等
                throw new Exception("参数错误！");
            }
            //根据sxo_id查询是否存在记录信息，且医师手机是否等于获取验证码手机
            $sxo_info = $this->db->select('id,dr_mobile')->where(['sxo_id' => $sxo_id, 'dr_id'=>$member_uid, 'status' => 1])
                    ->get('mb_sxo_verification_payment', 1)->row_array();
            if (!$sxo_info) {
                throw new Exception("未存在正确的支付信息，已支付或未申请！");
            }
            if ($sxo_info['dr_mobile'] != $verify_mobile) {//验证手机号码
                throw new Exception("手机号有误，请确认后重新输入【与领取人手机号码不一致】！");
            }

            //发送短信验证码
            $vcode = rand(10000, 99999);
            $sms_log_code = SMS_LOG_CODE_SXO_PAY_AUTH;
            $content = $this->lang_ary[LABEL_COMM_MOB_TEMPLATE]; //国际短信通道使用
            $err_msg = "";
            if ($country == '68') {//中国短信，使用send_cloud发送
                //创蓝
                if ($_SERVER['HTTP_HOST'] == "dev.drsay.cn") {//本地
                    $st = true;
                } else {
                    $sms_content = "上医说验证码:{$vcode}，" . SMS_ACTIVE_TIME . "分钟内有效，请勿泄漏！"; //国际短信通道使用
                    $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                }
                if ($st === true) {
                    $send_st = true;
                } else {
                    $send_st = false;
                    $err_msg = $st;
                }
            } else {//国外短信，使用国际短信通道
                $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                $area = getDataByID("app_sys_dictionary", $country);
                $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                if ($st == "OK") {
                    $send_st = true;
                } else {
                    $send_st = false;
                }
            }

            //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
            $type = VERIFICATION_CODE_LOGIN;
            if ($send_st) {//短信发送成功,记录入库
                $verify_data = array(
                    'mobile' => $verify_mobile,
                    'vcode' => $vcode,
                    'create_time' => time(),
                    'type' => (string) $type
                );
                $insert_id_sms_code = $this->set_vcode($verify_data);
                if ($insert_id_sms_code > 0) {
                    _back_msg('success', '短信发送成功，请查收！');
                } else {
                    throw new Exception("短信发送失败，请重试！");
                }
            } else {
                throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
            }
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }
}
