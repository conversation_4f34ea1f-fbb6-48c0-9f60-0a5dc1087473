<?php
/**
 * 6636项目问卷
 * Created by PhpStorm.
 * User: AMY
 * Date: 2022-06-01
 * Time: 15:37
 */

class S extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    //问卷开始【情感】
    public function emotion()
    {
        //记录点击时间
        $s = trim($this->input->get("s", true));
        if (!$s) {
            redirect("/");
        }
        $res_log = $this->db->insert("app_project_6636_log", [
            "s" => $s,
            "start_time" => time(),
            "type" => 1,//1、情感 2、学术
            "info" => json_encode($_SERVER, JSON_UNESCAPED_UNICODE),
        ]);
        $last_click_id = $this->db->insert_id();
        if ($res_log) {
            $info = $this->db->query("SELECT * FROM app_project_6636 WHERE s=? AND `type`=1", [$s])->row_array();
            if ($info) {
                $this->db->where("id", $info['id']);
                $this->db->update("app_project_6636", [
                    "last_click_id" => $last_click_id,
                ]);
            } else {
                $this->db->insert("app_project_6636", [
                    "s" => $s,
                    "add_time" => time(),
                    "last_click_id" => $last_click_id,
                    "type" => 1,//1、情感 2、学术
                ]);
            }
        }
        $data = [
            "title" => "情感",
            "last_click_id" => $last_click_id,
        ];
        $this->load->view('/markting/emotion', $data);
    }

    //问卷开始【学术】
    public function learning()
    {
        //记录点击时间
        $s = trim($this->input->get("s", true));
        if (!$s) {
            redirect("/");
        }
        $res_log = $this->db->insert("app_project_6636_log", [
            "s" => $s,
            "start_time" => time(),
            "type" => 2,//1、情感 2、学术
            "info" => json_encode($_SERVER, JSON_UNESCAPED_UNICODE),
        ]);
        $last_click_id = $this->db->insert_id();
        if ($res_log) {
            $info = $this->db->query("SELECT * FROM app_project_6636 WHERE s=? AND `type`=2", [$s])->row_array();
            if ($info) {
                $this->db->where("id", $info['id']);
                $this->db->update("app_project_6636", [
                    "last_click_id" => $last_click_id,
                ]);
            } else {
                $this->db->insert("app_project_6636", [
                    "s" => $s,
                    "add_time" => time(),
                    "last_click_id" => $last_click_id,
                    "type" => 2,//1、情感 2、学术
                ]);
            }
        }
        $data = [
            "title" => "学术",
            "last_click_id" => $last_click_id,
        ];
        $this->load->view('/markting/learning', $data);
    }

    //问卷【6636项目问卷】
    public function survey()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        if ($post_data) {
            file_put_contents("./tmp/survey_6636.txt", json_encode($post_data, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s").PHP_EOL, FILE_APPEND | LOCK_EX);
            $edit_data = $post_data['edit'];
            if (!$edit_data) {
                _back_msg("error", "请完善信息再提交");
            }
            $last_click_id = $post_data['last_click_id'];
            if ($last_click_id <= 0) {
                _back_msg("error", "参数错误！");
            }
            $info_log = $this->db->query("SELECT * FROM app_project_6636_log WHERE id=?", [$last_click_id])->row_array();
            $info = $this->db->query("SELECT * FROM app_project_6636 WHERE s=?", [$info_log['s']])->row_array();

            $q1 = $edit_data['q1'];
            $q2 = $edit_data['q2'];
            $q3 = $edit_data['q3'];
            $q3_other = $edit_data['q3_other'];
            $q4 = $edit_data['q4'];
            $q5 = $edit_data['q5'];
            $q6 = $edit_data['q6'];
            if (!in_array($q1, ['A','B','C','D','E'])) {
                _back_msg("error", "请选择您所在的城市!");
            }
            if (!in_array($q2, [1,2,3,4,5,6,7,8,9,10])) {
                _back_msg("error", "请选择【10个脓毒症休克患者中，有几个您会进行液体反应性评估】");
            }
            if (!in_array($q3, ['A','B','C','D','E','F'])) {
                _back_msg("error", "请选择【对于液体反应性评估，您最常使用哪种方法】");
            }
            if ($q3 == 'F' && !$q3_other) {
                _back_msg("error", "【对于液体反应性评估，您最常使用哪种方法】您选择了【其它】，请输入其它内容!");
            }
            if (!in_array($q4, ['A','B','C','D'])) {
                _back_msg("error", "请选择【对于监测手段的选择，您最看重哪个因素】");
            }
            if (!in_array($q5, ['A','B'])) {
                _back_msg("error", "请选择【无创生物电抗技术Starling内置的PLR/Bolus模块提示试验的操作步骤及操作时间，试验结束呈现ΔSVI及Frank-Starling曲线，您是否认为这是液体反应性评估的便捷方案】");
            }
            if (!in_array($q6, ['A','B'])) {
                _back_msg("error", "请选择【无创生物电抗技术Starling安全便捷、连续监测，与所有主要技术都进行过对比研究（例如与PAC的验证研究表明其敏感性和特异性均为93%），您是否愿意试用】");
            }

            try {
                //事务开始
                $this->db->trans_start();
                //SQL数据处理
                //有提交时间，按提交时间记录
                $now_time = time();
                $this->db->where("id", $last_click_id);
                $res_log = $this->db->update("app_project_6636_log", [
                    "answer_result" => json_encode($edit_data, JSON_UNESCAPED_UNICODE),
                    "end_time" => $now_time,
                ]);
                if (!$res_log) {
                    throw new Exception("提交失败，请重试1！");
                }
                $jg_time = $now_time - $info_log['start_time'];
                $edit_data['total_second'] = $info['total_second'] + $jg_time;
                $edit_data['submit_time'] = time();
                $this->db->where("id", $info['id']);
                $res = $this->db->update("app_project_6636", $edit_data);
                if (!$res) {
                    throw new Exception("提交失败，请重试2！");
                }
                //事务结束
                $this->db->trans_complete();
                if ($this->db->trans_status() === FALSE)
                {
                    throw new Exception("提交失败，请重试3！");
                    // generate an error... or use the log_message() function to log your error
                }
                $this->session->set_userdata(["bk_project_id" => "6636"]);
                $this->load->model('survey_model');
                $back_url = $this->survey_model->get_redirect_info("/bk/rs", "c", "", "", "", false);
                _back_msg("success", "提交成功！", $back_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        }
        //记录点击时间
        $s = trim($this->input->get("s", true));
        if (!$s) {
            redirect("/");
        }
        $res_log = $this->db->insert("app_project_6636_log", [
            "s" => $s,
            "start_time" => time(),
            "type" => 3,//1、情感 2、学术 3、第二期，增加问卷题
            "info" => json_encode($_SERVER, JSON_UNESCAPED_UNICODE),
        ]);
        $last_click_id = $this->db->insert_id();
        if ($res_log) {
            $info = $this->db->query("SELECT * FROM app_project_6636 WHERE s=? AND `type`=3", [$s])->row_array();
            if ($info) {
                $this->db->where("id", $info['id']);
                $this->db->update("app_project_6636", [
                    "last_click_id" => $last_click_id,
                ]);
            } else {
                $this->db->insert("app_project_6636", [
                    "s" => $s,
                    "add_time" => time(),
                    "last_click_id" => $last_click_id,
                    "type" => 3,//1、情感 2、学术 3、第二期问卷
                ]);
            }
        }

        $data = [
            "title" => "问卷",
            "last_click_id" => $last_click_id,
        ];
        $this->load->view('/markting/survey', $data);
    }

    //关闭浏览器时，记录返回时间
    public function act()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        $last_click_id = (int)$post_data['last_click_id'];
        if ($last_click_id <= 0) {
            _back_msg("error", "记录错误！");
        }
        $now_time = time();
        $info_log = $this->db->query("SELECT * FROM app_project_6636_log WHERE id=?", [$last_click_id])->row_array();
        $info = $this->db->query("SELECT * FROM app_project_6636 WHERE s=?", [$info_log['s']])->row_array();
        $this->db->where("id", $last_click_id);
        $jg_time = 5;//间隔请求时间
        $res_log = $this->db->update("app_project_6636_log", [
//            "end_time" => $now_time,
//            "end_time" => $info_log['start_time'] + $jg_time,//定时请求，5秒请求一次
            "end_time" => ($info_log['end_time'] ? $info_log['end_time'] : $info_log['start_time']) + $jg_time,//定时请求，5秒请求一次
        ]);
        //扣除上一次结束时间，把隐藏页面时间去掉【手机操作】
        $end_time = $now_time - $info_log['start_time'];
        if ($res_log) {
            $this->db->where("id", $info['id']);
            $res = $this->db->update("app_project_6636", [
//                "total_second" => $info['total_second'] + $end_time,
                "total_second" => $info['total_second'] + $jg_time,
            ]);
            if ($res) {
                _back_msg("success", "记录成功！");
            }
        }
        _back_msg("error", "记录失败！");
    }

    //问卷调度
    private function q_jump($s)
    {
        //查询问卷完成情况
        $info = $this->db->query("SELECT * FROM app_project_7778 WHERE s=?", [$s])->row_array();
        if ($info) {//记录已存在
            if ($info['step_page'] == 3) {
                $this->session->set_userdata(["bk_project_id" => "6636"]);
                $this->survey_model->get_redirect_info("/bk/rs", "c");
            } else {
                if ($info['is_informed_consent'] == 2) {//选了否的，直接跳至感谢页
                    $this->survey_model->get_redirect_info("/bk/rs", "zb");
                } else {//选了是，检测是否是被甄别记录
                    if ($info['step_page'] == 2) {//进入甄别页
                        $screen_out = $info['screen_out'] ? explode(",", $info['screen_out']) : [];
                        if (in_array("E", $screen_out)) {//选了被甄别的选项
                            $this->survey_model->get_redirect_info("/bk/rs", "zb");
                        } else {
                            header("location:/markting/s/survey2?s=".$s."&f=".$info['from_where']);
                        }
                    } else {//第一步骤完成，进入第二页
                        header("location:/markting/s/survey2_zb?s=".$s."&f=".$info['from_where']);
                    }
                }
            }
        }
    }

    //问卷【7778项目问卷-知情同意书】
    public function survey2_info()
    {
        //记录点击时间
        $s = trim($this->input->get("s", true));
        if (!$s) {
            redirect("/");
        }
        $this->load->model('survey_model');
        $zb_link = $this->survey_model->get_redirect_info("/bk/rs", "zb", "", "", "", false);
        //问卷调度
        $this->q_jump($s);
        $data = [
            "s" => $s,
            "zb_link" => $zb_link,
        ];
        $this->load->view('/markting/survey2_info', $data);
    }

    //问卷【7778项目问卷-甄别题】
    public function survey2_zb()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        $this->load->model('survey_model');
        $zb_link = $this->survey_model->get_redirect_info("/bk/rs", "zb", "", "", "", false);
        if ($post_data) {
            $s = $post_data['s'];
            $f = $post_data['f'];
            $edit_data = $post_data['edit'];
            $screen_out = $edit_data['screen_out'];
            $is_screen_out = false;
            if (in_array("E", $screen_out)) {
                $is_screen_out = true;
            }
            if (!$s || !$f) {
                _back_msg("error", "参数错误！");
            }
            if (!$screen_out) {
                _back_msg("error", "请选择【请问您接诊的眼底疾病患者包含以下哪些类型？】");
            }
            $insert_data = [
                "s" => $s,
                "answer_result" => json_encode($screen_out, JSON_UNESCAPED_UNICODE),
                "add_time" => time(),
                "type" => 1,//第一期
                "from_where" => $f,//来源
            ];
            $info = $this->db->query("SELECT * FROM app_project_7778 WHERE s=?", [$s])->row_array();
            try {
                //事务开始
                $this->db->trans_start();
                //SQL数据处理
                $res_zb = $this->db->insert("app_project_7778_zb", $insert_data);
                if (!$res_zb) {
                    throw new Exception("提交失败！");
                }
                if ($info) {
                    $res = $this->db->update("app_project_7778", [
                        "screen_out" => $screen_out ? implode(",", $screen_out) : "",
                        "step_page" => 2,
                    ], ["s" => $s, "type" => 1]);
                } else {
                    $res = $this->db->insert("app_project_7778", [
                        "s" => $s,
                        "screen_out" => $screen_out ? implode(",", $screen_out) : "",
                        "from_where" => $f,
                        "step_page" => 2,
                    ]);
                }
                if (!$res) {
                    throw new Exception("提交失败！");
                }
                if ($is_screen_out) {
                    $back_url = $zb_link;
                } else {
                    $back_url = "/markting/s/survey2?s={$s}&f={$f}";
                }
                //事务结束
                $this->db->trans_complete();
                if ($this->db->trans_status() === FALSE)
                {
                    throw new Exception("提交失败！");
                }
                _back_msg("success", "提交成功！", $back_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }

        }
        //记录点击时间
        $s = trim($this->input->get("s", true));
        $f = trim($this->input->get("f", true));
        if (!$s) {
            redirect("/");
        }
        if (!in_array($f, [1,2])) {
            redirect("/");
        }
        $data = [
            "s" => $s,
            "f" => $f,
            "zb_link" => $zb_link,
        ];
        $this->load->view('/markting/survey2_zb', $data);
    }

    //问卷【7778项目问卷】
    public function survey2()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        if ($post_data) {
//            print_r($post_data);
//            _back_msg("error", "正在开发中");
            file_put_contents("./tmp/survey_7778.txt", json_encode($post_data, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s").PHP_EOL, FILE_APPEND | LOCK_EX);
            $edit_data = $post_data['edit'];
            if (!$edit_data) {
                _back_msg("error", "请完善信息再提交");
            }
            $last_click_id = $post_data['last_click_id'];
            if ($last_click_id <= 0) {
                _back_msg("error", "参数错误！");
            }
            $info_log = $this->db->query("SELECT * FROM app_project_7778_log WHERE id=?", [$last_click_id])->row_array();
            $info = $this->db->query("SELECT * FROM app_project_7778 WHERE s=?", [$info_log['s']])->row_array();

            $q1 = $edit_data['q1'];
            $q2 = $edit_data['q2'];
            $q3 = $edit_data['q3'];
            if (count($q1) != 2) {
                _back_msg("error", "请选择【真实世界Anti-VEGF治疗中，您最关注的是？】,请选择2项!");
            }
            if (in_array("E", $q1) && !$edit_data['q1_other']) {
                _back_msg("error", "请选择【真实世界Anti-VEGF治疗中，您最关注的是？】,您选择了【其他】选项，请补充!");
            }
            $arr_q2 = explode(",", $q2);
            if (!$q2) {
                _back_msg("error", "请排序【在Faricimab的DME全球三期临床试验YOSEMITE/RHINE 2年最新结果中，对您最具吸引力的疗效数据是？】");
            }
            if (count($arr_q2) != 4) {
                _back_msg("error", "请正确排序【在Faricimab的DME全球三期临床试验YOSEMITE/RHINE 2年最新结果中，对您最具吸引力的疗效数据是？】");
            }
            if (!$q3) {
                _back_msg("error", "请选择【Faricimab作为全球首个用于眼内注射的抗Ang-2/VEGF-A双特异性抗体， 研究表明，抑制Ang-2可以显著增加血管稳定性，您会关注Faricimab带来的哪些患者获益？】");
            }

            try {
                //事务开始
                $this->db->trans_start();
                $edit_data['q1'] = implode(",", $edit_data['q1']);
                $edit_data['q3'] = implode(",", $edit_data['q3']);
                //SQL数据处理
                //有提交时间，按提交时间记录
                $now_time = time();
                $this->db->where("id", $last_click_id);
                $res_log = $this->db->update("app_project_7778_log", [
                    "answer_result" => json_encode($edit_data, JSON_UNESCAPED_UNICODE),
                    "end_time" => $now_time,
                ]);
                if (!$res_log) {
                    throw new Exception("提交失败，请重试1！");
                }
//                //查询最新一条甄别记录，更新至表中
//                $zb_info = $this->db->query("SELECT * FROM app_project_7778_zb WHERE s=? ORDER BY id DESC", [$info_log['s']])->row_array();
//                $zb_info_answer_result = $zb_info && $zb_info['answer_result'] ? json_decode($zb_info['answer_result'], true) : [];
//                $edit_data['screen_out'] = $zb_info_answer_result ? implode(",", $zb_info_answer_result) : "";
//                //查最新一条知情同意书结果

                $jg_time = $now_time - $info_log['start_time'];
                $edit_data['total_second'] = $info['total_second'] + $jg_time;
                $edit_data['submit_time'] = time();
                $edit_data['step_page'] = 3;
                $this->db->where("id", $info['id']);
                $res = $this->db->update("app_project_7778", $edit_data);
                if (!$res) {
                    throw new Exception("提交失败，请重试2！");
                }
                //事务结束
                $this->db->trans_complete();
                if ($this->db->trans_status() === FALSE)
                {
                    throw new Exception("提交失败，请重试3！");
                }
                $this->session->set_userdata(["bk_project_id" => "6636"]);
                $this->load->model('survey_model');
                $back_url = $this->survey_model->get_redirect_info("/bk/rs", "c", "", "", "", false);
                _back_msg("success", "提交成功！", $back_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        }
        //记录点击时间
        $s = trim($this->input->get("s", true));
        $f = trim($this->input->get("f", true));
        if (!$s) {
            redirect("/");
        }
        if (!in_array($f, [1,2])) {
            redirect("/");
        }
        $res_log = $this->db->insert("app_project_7778_log", [
            "s" => $s,
            "start_time" => time(),
            "type" => 1,//第一期问卷
            "info" => json_encode($_SERVER, JSON_UNESCAPED_UNICODE),
            "is_informed_consent" => 1,
            "from_where" => $f,
        ]);
        $last_click_id = $this->db->insert_id();
        if ($res_log) {
            $info = $this->db->query("SELECT * FROM app_project_7778 WHERE s=? AND `type`=1", [$s])->row_array();
            if ($info) {
                $this->db->where("id", $info['id']);
                $this->db->update("app_project_7778", [
                    "last_click_id" => $last_click_id,
                ]);
            } else {
                $this->db->insert("app_project_7778", [
                    "s" => $s,
                    "add_time" => time(),
                    "last_click_id" => $last_click_id,
                    "type" => 1,//1、第一期问卷
                ]);
            }
        }

        $data = [
            "title" => "问卷",
            "last_click_id" => $last_click_id,
        ];
        if ($this->input->get("is_old")) {
            $this->load->view('/markting/survey2', $data);
        } else {
            $this->load->view('/markting/survey2_new', $data);
        }
    }

    //关闭浏览器时，记录返回时间
    public function act2()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        $last_click_id = (int)$post_data['last_click_id'];
        if ($last_click_id <= 0) {
            _back_msg("error", "记录错误！");
        }
        $info_log = $this->db->query("SELECT * FROM app_project_7778_log WHERE id=?", [$last_click_id])->row_array();
        $info = $this->db->query("SELECT * FROM app_project_7778 WHERE s=?", [$info_log['s']])->row_array();
        $this->db->where("id", $last_click_id);
        $jg_time = 5;//间隔请求时间
        $res_log = $this->db->update("app_project_7778_log", [
            "end_time" => ($info_log['end_time'] ? $info_log['end_time'] : $info_log['start_time']) + $jg_time,//定时请求，5秒请求一次
        ]);
        //扣除上一次结束时间，把隐藏页面时间去掉【手机操作】
        if ($res_log) {
            $this->db->where("id", $info['id']);
            $res = $this->db->update("app_project_7778", [
                "total_second" => $info['total_second'] + $jg_time,
            ]);
            if ($res) {
                _back_msg("success", "记录成功！");
            }
        }
        _back_msg("error", "记录失败！");
    }

    //知情同意书记录
    public function link_res()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        $s = $post_data['s'];
        $type = $post_data['type'];
        $f = (int)trim($post_data['f']);
        if (!$s || !$type) {
            _back_msg("error", "参数错误");
        }
        $this->load->model('survey_model');
        $zb_link = $this->survey_model->get_redirect_info("/bk/rs", "zb", "", "", "", false);
        if ($type == "refuse") {//拒绝
            $is_informed_consent = 2;
            $back_url = $zb_link;
        } else {//同意
            if (!$f) {
                _back_msg("error", "参数错误");
            }
            $is_informed_consent = 1;
            $back_url = "/markting/s/survey2_zb?s={$s}&f={$f}";
        }
        //查询记录是否存在
        $info = $this->db->query("SELECT * FROM app_project_7778 WHERE s=?", [$s])->row_array();
        try {
            //事务开始
            $this->db->trans_start();
            //SQL数据处理
            if ($info) {
                $res = $this->db->update("app_project_7778", [
                    "is_informed_consent" => $is_informed_consent,
                    "from_where" => $f,
                    "step_page" => 1,
                ], ["s" => $s, "type" => 1]);
            } else {
                $res = $this->db->insert("app_project_7778", [
                    "add_time" => time(),
                    "type" => 1,
                    "s" => $s,
                    "is_informed_consent" => $is_informed_consent,
                    "from_where" => $f,
                    "step_page" => 1,
                ]);
            }
            if (!$res) {
                throw new Exception("记录存储有误！");
            }
            $res_log = $this->db->insert("app_project_7778_log", [
                "start_time" => time(),
                "end_time" => time(),
                "s" => $s,
                "type" => 1,
                "is_informed_consent" => 2,
            ]);
            if (!$res_log) {
                throw new Exception("记录存储有误！");
            }
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                throw new Exception("用户不存在，记录有误！");
            }
            _back_msg("success", "记录成功！", $back_url);
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }

    }

    //问卷【7823项目问卷】
    public function survey3()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        if ($post_data) {
            $last_click_id = (int)trim($post_data['last_click_id']);
            $page = (int)trim($post_data['page']);
            $s = $post_data['s'];
            if ($last_click_id <= 0 || $page <= 0  || !$s) {
                _back_msg("error", "参数错误！");
            }

            if ($page == 1) {
                $this->session->set_userdata(['session_survey_page_num' => 2]);
                _back_msg("success", "提交成功", "/markting/s/survey3?s=".$s);
            }
            if ($page == 2) {
                $edit_data = $post_data['edit'];
                if (!$edit_data) {
                    _back_msg("error", "请完善信息再提交");
                }
                $info_log = $this->db->query("SELECT * FROM app_project_7823_log WHERE id=?", [$last_click_id])->row_array();
                $info = $this->db->query("SELECT * FROM app_project_7823 WHERE s=?", [$info_log['s']])->row_array();
                $q1 = $edit_data['q1'];
                $q2 = $edit_data['q2'];
                $q3 = $edit_data['q3'];
                if (!$q1) {
                    _back_msg("error", "请选择【Q1：您是否计划（或正在）开展基础医学科研项目？】!");
                }
//                if (!$q2) {
//                    _back_msg("error", "请输入【Q2：您的研究方向或需要的支持？】!");
//                }
                if (!$q3) {
                    _back_msg("error", "请选择【Q3：您是否需要申领小睿助手科研互助金？】");
                }
                try {
                    //事务开始
                    $this->db->trans_start();
                    //SQL数据处理
                    //有提交时间，按提交时间记录
                    $now_time = time();
                    $this->db->where("id", $last_click_id);
                    $res_log = $this->db->update("app_project_7823_log", [
                        "answer_result" => json_encode($edit_data, JSON_UNESCAPED_UNICODE),
                        "end_time" => $now_time,
                    ]);
                    if (!$res_log) {
                        throw new Exception("提交失败，请重试1！");
                    }
                    $jg_time = $now_time - $info_log['start_time'];
                    $edit_data['total_second'] = $info['total_second'] + $jg_time;
                    $edit_data['submit_time'] = time();
                    $this->db->where("id", $info['id']);
                    $res = $this->db->update("app_project_7823", $edit_data);
                    if (!$res) {
                        throw new Exception("提交失败，请重试2！");
                    }
                    //事务结束
                    $this->db->trans_complete();
                    if ($this->db->trans_status() === FALSE)
                    {
                        throw new Exception("提交失败，请重试3！");
                    }
                    $this->session->set_userdata(['session_survey_page_num' => 3]);
                    _back_msg("success", "提交成功", "/markting/s/survey3?s=".$s);
                } catch (Exception $e) {
                    _back_msg("error", $e->getMessage());
                }
            }
            if ($page == 3) {
//                $this->session->unset_userdata('session_survey_page_num');
//                $this->session->unset_userdata('session_survey_last_click_id');
//                $this->session->set_userdata(["bk_project_id" => "6636"]);
//                $this->load->model('survey_model');
//                $back_url = $this->survey_model->get_redirect_info("/bk/rs", "c", "", "", "", false);
//                _back_msg("success", "提交成功！", $back_url);
                try {
                    //事务开始
                    $this->db->trans_start();
                    //SQL数据处理
                    //有提交时间，按提交时间记录
                    $info_log = $this->db->query("SELECT * FROM app_project_7823_log WHERE id=?", [$last_click_id])->row_array();
                    $info = $this->db->query("SELECT * FROM app_project_7823 WHERE s=?", [$info_log['s']])->row_array();
                    $now_time = time();
                    $this->db->where("id", $last_click_id);
                    $res_log = $this->db->update("app_project_7823_log", [
                        "end_time" => $now_time,
                    ]);
                    if (!$res_log) {
                        throw new Exception("提交失败，请重试1！");
                    }
                    $jg_time = $now_time - $info_log['start_time'];
                    $edit_data['total_second'] = $info['total_second'] + $jg_time;
                    $edit_data['submit_time'] = time();
                    $this->db->where("id", $info['id']);
                    $res = $this->db->update("app_project_7823", $edit_data);
                    if (!$res) {
                        throw new Exception("提交失败，请重试2！");
                    }
                    //事务结束
                    $this->db->trans_complete();
                    if ($this->db->trans_status() === FALSE)
                    {
                        throw new Exception("提交失败，请重试3！");
                    }
                    $this->load->model('survey_model');
                    $back_url = $this->survey_model->get_redirect_info("/bk/rs", "c", "", "", "", false);
                    _back_msg("success", "提交成功！", $back_url);
                    } catch (Exception $e) {
                    _back_msg("error", $e->getMessage());
                }
            }
        }
        //第二期，不需要session
        $this->session->unset_userdata('session_survey_page_num');
        $this->session->unset_userdata('session_survey_last_click_id');

        $session_survey_page_num = (int)$this->session->userdata("session_survey_page_num");
        $last_click_id = (int)$this->session->userdata("session_survey_last_click_id");
//        $page = 1;
        $page = 3;
        if ($session_survey_page_num > 0) {//页数
            $page = $session_survey_page_num;
        } else {
//            $this->session->set_userdata(['session_survey_page_num' => 1]);
            $this->session->set_userdata(['session_survey_page_num' => 3]);
        }
//        $page = $page ? $page : 1;
        $page = $page ? $page : 3;

        $q_type = 2;//1、第一期问卷 2、第二期问卷

        //记录点击时间
        $s = trim($this->input->get("s", true));
        if (!$s) {
            redirect("/");
        }
        if ($last_click_id <= 0) {
            $this->db->insert("app_project_7823_log", [
                "s" => $s,
                "start_time" => time(),
                "type" => $q_type,
                "info" => json_encode($_SERVER, JSON_UNESCAPED_UNICODE),
            ]);
            $last_click_id = $this->db->insert_id();
            $this->session->set_userdata(['session_survey_last_click_id' => $last_click_id]);
        }
        if ($last_click_id > 0) {
            $info = $this->db->query("SELECT * FROM app_project_7823 WHERE s=? AND `type`={$q_type}", [$s])->row_array();
            if ($info) {
                $this->db->where("id", $info['id']);
                $this->db->update("app_project_7823", [
                    "last_click_id" => $last_click_id,
                ]);
            } else {
                $this->db->insert("app_project_7823", [
                    "s" => $s,
                    "add_time" => time(),
                    "last_click_id" => $last_click_id,
                    "type" => $q_type,
                ]);
            }
        }

        $data = [
            "title" => "问卷",
            "page" => $page,
            "last_click_id" => $last_click_id,
            "s" => $s,
        ];
        $this->load->view('/markting/survey3_new', $data);
//        if ($this->input->get("is_new")) {
//            $this->load->view('/markting/survey3_new', $data);
//        }  else {
//            $this->load->view('/markting/survey3', $data);
//        }
    }

    //关闭浏览器时，记录返回时间
    public function act3()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        $last_click_id = (int)$post_data['last_click_id'];
        if ($last_click_id <= 0) {
            _back_msg("error", "记录错误！");
        }
        $info_log = $this->db->query("SELECT * FROM app_project_7823_log WHERE id=?", [$last_click_id])->row_array();
        $info = $this->db->query("SELECT * FROM app_project_7823 WHERE s=?", [$info_log['s']])->row_array();
        $this->db->where("id", $last_click_id);
        $jg_time = 5;//间隔请求时间
        $res_log = $this->db->update("app_project_7823_log", [
            "end_time" => ($info_log['end_time'] ? $info_log['end_time'] : $info_log['start_time']) + $jg_time,//定时请求，5秒请求一次
        ]);
        //扣除上一次结束时间，把隐藏页面时间去掉【手机操作】
        if ($res_log) {
            $this->db->where("id", $info['id']);
            $res = $this->db->update("app_project_7823", [
                "total_second" => $info['total_second'] + $jg_time,
            ]);
            if ($res) {
                _back_msg("success", "记录成功！");
            }
        }
        _back_msg("error", "记录失败！");
    }




}