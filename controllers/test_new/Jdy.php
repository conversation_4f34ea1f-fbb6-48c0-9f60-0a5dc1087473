<?php

class Jdy extends MY_Controller {

    function __construct() {
        parent::__construct();
    }

    public function index() {
        $data = file_get_contents('php://input');
        $arr = [
            'errcode' => 200,
            'description' => 'aaa',
            'data' => [
                'status' => 0,
                'msg' => 'abc',
                'type' => 'app_authorize'
            ]
        ];

        $arr = json_encode($arr);
        file_put_contents('./jdy_log.txt', $arr);
    }

}
