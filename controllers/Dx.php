<?php
/**
 * 定性定量受访页面
 * Created by PhpStorm.
 * User: Amy
 * Date: 2022/07/26
 */
class Dx extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    function index()
    {
        $data = [];
        $this->load->view("/dx/index", $data);
    }

    public function recruit_channel()
    {
        $recruit_channel_name = trim($this->input->post("recruit_channel_name", true));
        if (!$recruit_channel_name) {
            _back_msg("error", "请提交内容");
        }
        //招募途径
        $default_reg = [
            "1" => "IQVIA内部资源（包括微信平台或FW团队）",
            "2" => "IQVIA研究团队自行招募（如PI，MC等）",
            "3" => "北京奥力博市场咨询有限公司",
            "4" => "北京博华智信信息咨询有限公司",
            "5" => "北京缔五度管理咨询有限公司",
            "6" => "北京恒申博源信息咨询有限公司",
            "7" => "北京捷峰联合市场咨询有限公司",
            "8" => "北京美利德科技有限公司",
            "9" => "北京仁盈通智咨询有限公司",
            "10" => "北京医脉互通科技有限公司",
            "11" => "北跃（天津）企业管理咨询有限公司",
            "12" => "成都励精企业管理咨询有限公司",
            "13" => "成都智信迈市场调研有限公司",
            "14" => "成都卓讯市场调查有限公司",
            "15" => "福州奥通营销咨询服务有限公司",
            "16" => "福州千询市场调查有限公司",
            "17" => "上海超正科技",
            "18" => "甘肃意通市场研究有限公司",
            "19" => "广州成凯市场调研有限公司",
            "20" => "广州勤越信息咨询有限公司",
            "21" => "广州市晶协信息咨询有限公司",
            "22" => "广州协博市场信息咨询有限公司",
            "23" => "哈尔滨远星信息咨询有限公司",
            "24" => "海鄞信息咨询(上海)有限公司",
            "25" => "杭州邦略信息技术有限公司",
            "26" => "杭州佳盟商务咨询有限公司",
            "27" => "杭州朗顿商务咨询有限公司",
            "28" => "吉林省广深市场调查顾问有限责任公司",
            "29" => "济南瑞恒市场调查有限公司",
            "30" => "济南万达信息咨询有限公司",
            "31" => "健康通（北京）网络科技有限公司",
            "32" => "久远谦长（上海）企业管理咨询有限公司",
            "33" => "昆明希杰信息咨询有限公司",
            "34" => "六晟信息科技（杭州）有限公司",
            "35" => "南京国创市场信息咨询有限公司",
            "36" => "南京鹤儒市场研究有限公司",
            "37" => "南京科佰伊市场研究有限公司",
            "38" => "南京欧迈市场研究有限公司",
            "39" => "太原市齐天恒",
            "40" => "青岛爱斯特市场研究有限公司",
            "41" => "青岛瑞格大公企业管理咨询有限公司",
            "42" => "厦门玮施信息咨询有限公司",
            "43" => "山西同达信息咨询服务中心",
            "44" => "上海铂嘉医疗管理有限公司",
            "45" => "上海博度市场营销咨询有限公司",
            "46" => "上海精微市场信息咨询有限公司",
            "47" => "上海掘瀚企业管理有限公司",
            "48" => "上海库润信息技术有限公司",
            "49" => "上海遴闻商务信息咨询有限公司",
            "50" => "上海钤津咨询管理有限公司",
            "51" => "上海商霖华通投资咨询有限公司",
            "52" => "上海叙岑商务咨询有限公司",
            "53" => "上海勋康市场营销策划事务所",
            "54" => "上海真睿商务信息咨询有限公司",
            "55" => "上海智谱信息科技有限公司",
            "56" => "深圳市海航市场营销策划有限公司",
            "57" => "深圳市依玛思创管理咨询有限公司",
            "58" => "沈阳艾思博市场研究有限公司",
            "59" => "沈阳明志致远市场研究有限公司",
            "60" => "沈阳中宇行销顾问有限公司",
            "61" => "石家庄知汇企业管理咨询有限公司",
            "62" => "天津行天市场信息咨询服务有限公司",
            "63" => "武汉大视野市场研究咨询有限公司",
            "64" => "武汉新景市场信息咨询有限公司",
            "65" => "西安佳进商务信息咨询有限公司",
            "66" => "西安睿思商务信息咨询有限公司",
            "67" => "西安源信市场研究有限责任公司",
            "68" => "新疆瑞孚森信息咨询有限责任公司",
            "69" => "壹信咨询（南京）有限公司",
            "70" => "易贸信息科技（上海）有限公司",
            "71" => "郑州汉邦商务信息服务有限公司",
            "72" => "郑州卓亚企业管理咨询有限公司",
            "73" => "重庆品则鑫企业管理咨询有限公司",
            "74" => "北京金鹏天宇",
            "75" => "灼识人才咨询管理（上海）有限公司",
            "76" => "其他，请注明",
        ];
        $res = [];
        $is_other = false;
        foreach ($default_reg as $k => $v) {
            if (strpos($v, $recruit_channel_name) !== false) {
                $res[] = [
                    "id" => $k,
                    "text" => $v,
                ];
                if ($k == 76) {
                    $is_other = true;
                }
            }
        }
        if (!$is_other) {
            $res[] = [
                "id" => 76,
                "text" => "其他，请注明",
            ];
        }
        _back_msg("success", $res);
    }

    //提交记录
    function save()
    {
        $post_data = $this->input->post();
        $post_data = format_data($post_data);
        $edit_data = $post_data['edit'];
        if (!$edit_data) {
            _back_msg("error", "请完善信息再提交");
        }
        if (!$edit_data['province']) {
            _back_msg("error", "请输入所在省份");
        }
        if (!$edit_data['city']) {
            _back_msg("error", "请输入所在城市");
        }
        if (!$edit_data['disease']) {
            _back_msg("error", "请输入疾病领域");
        }
        if (!$edit_data['interviewee_type']) {
            _back_msg("error", "请选择受访者类型");
        }
        if ($edit_data['interviewee_type'] == "其他，请注明" && !$edit_data['interviewee_type_other']) {
            _back_msg("error", "请输入其他受访者类型");
        }
        if (!$edit_data['fw_type']) {
            _back_msg("error", "请选择FW执行方法");
        }
        if ($edit_data['fw_type'] == "其他，请注明" && !$edit_data['fw_type_other']) {
            _back_msg("error", "请输入其他FW执行方法");
        }
        if (!$edit_data['interviewee_unit']) {
            _back_msg("error", "请输入受访者单位名称");
        }
        if (!$edit_data['unit_level']) {
            _back_msg("error", "请选择医院级别");
        }
        if (!$edit_data['interviewee_department']) {
            _back_msg("error", "请输入受访者科室/岗位");
        }
        if (!$edit_data['interviewee_name']) {
            _back_msg("error", "请输入受访者姓名");
        }
        if (!$edit_data['interviewee_job_title']) {
            _back_msg("error", "请选择技术职称");
        }
        if (!$edit_data['interviewee_position']) {
            _back_msg("error", "请选择行政职务");
        }
        if (!$edit_data['interviewee_landline']) {
            _back_msg("error", "请输入受访者座机");
        }
        if (!$edit_data['interviewee_phone']) {
            _back_msg("error", "请输入受访者手机");
        }
        if (!$edit_data['visit_date']) {
            _back_msg("error", "请输入访问日期");
        }
        if (!$edit_data['visit_time']) {
            _back_msg("error", "请输入访问时间");
        }
        if (!$edit_data['visit_location']) {
            _back_msg("error", "请输入访问地点");
        }
        if (!$edit_data['payer']) {
            _back_msg("error", "请选择礼金支付方");
        }
        if (!$edit_data['cash_amount']) {
            _back_msg("error", "请输入礼金支付数额(元)");
        }
        if (!$edit_data['payment_type']) {
            _back_msg("error", "请选择礼金支付方式");
        }
        if ($edit_data['interviewee_type'] == "其他，请注明" && !$edit_data['payment_type_other']) {
            _back_msg("error", "请输入其他礼金支付方式");
        }
        if (!$edit_data['payment_account']) {
            _back_msg("error", "请输入支付宝/微信账号");
        }
        if (!$edit_data['recruit_channel_id']) {
            _back_msg("error", "请选择招募途径");
        }
        if ($edit_data['recruit_channel_id'] == 76 && !$edit_data['recruit_channel_name_other']) {
            _back_msg("error", "请输入其他招募途径");
        }
        if (!$edit_data['s1']) {
            _back_msg("error", "请输入S1");
        }
        if (!$edit_data['s2']) {
            _back_msg("error", "请输入S2");
        }
        if (!$edit_data['s3']) {
            _back_msg("error", "请输入S3");
        }
        if (!$edit_data['s4']) {
            _back_msg("error", "请输入S4");
        }
        if (!$edit_data['s5']) {
            _back_msg("error", "请输入S5");
        }
        if (!$edit_data['iqvia_amount']) {
            _back_msg("error", "请输入IQVIA微信平台支付金额");
        }
        if (!$edit_data['recruit_fee']) {
            _back_msg("error", "请输入招募费");
        }
        if (!$edit_data['sample_amount']) {
            _back_msg("error", "请输入样本总金额");
        }
        if (!$edit_data['interview_duration']) {
            _back_msg("error", "请输入访谈时长");
        }
        $now_time = time();
        $y = date("Y", $now_time);
        $table_name = "iqvia_dx_data_{$y}";
        //查询表是否存在，不存在时，新增表再添加
        $check_table = $this->db->query("select * from information_schema.COLUMNS where table_name = '{$table_name}'")->row_array();
        if (!$check_table) {//表不存在，创建表
            $this->db->query("CREATE TABLE `{$table_name}` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '流水号',
  `province` varchar(100) NOT NULL DEFAULT '' COMMENT '省份',
  `city` varchar(100) NOT NULL DEFAULT '' COMMENT '城市',
  `disease` varchar(150) NOT NULL DEFAULT '' COMMENT '疾病领域',
  `interviewee_type` varchar(100) NOT NULL DEFAULT '' COMMENT '受访者类型',
  `interviewee_type_other` varchar(100) NOT NULL DEFAULT '' COMMENT '其他受访者类型',
  `fw_type` varchar(100) NOT NULL DEFAULT '' COMMENT 'FW执行方法',
  `fw_type_other` varchar(150) NOT NULL DEFAULT '' COMMENT '其他FW执行方法',
  `interviewee_unit` varchar(150) NOT NULL DEFAULT '' COMMENT '受访者单位名称',
  `unit_level` varchar(50) NOT NULL DEFAULT '' COMMENT '医院级别',
  `interviewee_department` varchar(100) NOT NULL DEFAULT '' COMMENT '受访者科室/岗位',
  `interviewee_name` varchar(100) NOT NULL DEFAULT '' COMMENT '受访者姓名',
  `interviewee_job_title` varchar(100) NOT NULL DEFAULT '' COMMENT '技术职称',
  `interviewee_position` varchar(100) NOT NULL DEFAULT '' COMMENT '行政职务',
  `interviewee_landline` varchar(50) NOT NULL DEFAULT '' COMMENT '受访者座机',
  `interviewee_phone` varchar(50) NOT NULL DEFAULT '' COMMENT '受访者手机',
  `visit_date` varchar(50) NOT NULL DEFAULT '' COMMENT '访问日期',
  `visit_time` varchar(50) NOT NULL DEFAULT '' COMMENT '访问时间',
  `visit_location` varchar(50) NOT NULL DEFAULT '' COMMENT '访问地点',
  `payer` varchar(100) NOT NULL DEFAULT '' COMMENT '礼金支付方',
  `cash_amount` float(12,4) NOT NULL DEFAULT '0.0000' COMMENT '礼金支付数额(元)',
  `payment_type` varchar(100) NOT NULL DEFAULT '' COMMENT '礼金支付方式',
  `payment_type_other` varchar(100) NOT NULL DEFAULT '' COMMENT '其他礼金支付方式',
  `payment_account` varchar(100) NOT NULL DEFAULT '' COMMENT '支付宝/微信账号',
  `recruit_channel_id` smallint(6) NOT NULL DEFAULT '0' COMMENT '招募途径',
  `recruit_channel_other` varchar(150) NOT NULL DEFAULT '' COMMENT '其他招募途径',
  `s1` varchar(100) NOT NULL DEFAULT '' COMMENT 'S1',
  `s2` varchar(100) NOT NULL DEFAULT '' COMMENT 'S2',
  `s3` varchar(100) NOT NULL DEFAULT '' COMMENT 'S3',
  `s4` varchar(100) NOT NULL DEFAULT '' COMMENT 'S4',
  `s5` varchar(100) NOT NULL DEFAULT '' COMMENT 'S5',
  `iqvia_amount` float(12,4) NOT NULL DEFAULT '0.0000' COMMENT 'IQVIA微信平台支付金额',
  `recruit_fee` float(12,4) NOT NULL DEFAULT '0.0000' COMMENT '招募费',
  `sample_amount` float(12,4) NOT NULL DEFAULT '0.0000' COMMENT '样本总金额',
  `interview_duration` varchar(50) NOT NULL DEFAULT '' COMMENT '访谈时长',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `y_m_d` varchar(50) NOT NULL DEFAULT '' COMMENT '年月日',
  PRIMARY KEY (`id`),
  KEY `y_m_d` (`y_m_d`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定性定量受访信息表'");
            $res_table = $this->db->query("select * from information_schema.COLUMNS where table_name = '{$table_name}'")->row_array();
//            $res_table = $this->db->affected_rows();
            if (!$res_table) {
                _back_msg("error", "提交失败");
            }
        }
        $edit_data['add_time'] = time();
        $edit_data['y_m_d'] = date("Y-m-d");
        $res = $this->db->insert($table_name, $edit_data);
        if ($res) {
            _back_msg("success", "提交成功", "/dx/finished");
        } else {
            _back_msg("error", "提交失败".$this->db->last_query());
        }
    }

    function finished()
    {
//        $this->load->view('/dx/finished');
        $this->load->view('/dx/finished_new');
    }

}
