<?php
/**
 * Created by PhpStorm.
 * 用途：上医说官网
 * User: Amy
 * Date: 2017/11/21
 */
class Home extends MY_Controller
{
    public $client_keywords;
    public $lang_ary;
    function __construct()
    {
        parent::__construct();
        $this->client_keywords = array('nokia','sony','ericsson','mot','samsung','htc','sgh','lg','sharp','sie-','philips','panasonic','alcatel','lenovo','iphone','ipod','blackberry','meizu','android','netfront','symbian','ucweb','windowsce','palm','operamini','operamobi','openwave','nexusone','cldc','midp','wap','mobile');
        ############### 获取语言包   ###############
        $lang = 140;
        $this->lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$this->lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $this->lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############
    }

    public function index()
    {
        $data = array();
        $this->load->view('/home/<USER>', $data);
        //IP地址
//        $ip = getip();
//        $ip_address = ip2location($ip);
//        echo $ip_address;
//        腾讯云
//        $sig = linuxSignature('17301849323');
//        echo $sig;

        //批量发送短信短信
//        $send_ary[] = array(
//            'phone' => 17301849323,
//            'vars' => array(
//                '%UCODE%' => "1234",
//                '%NAME%' => "巫桂娥",
//                '%AWARDS%' => 100
//            )
//        );
//
//        $send_st = sms_send_cloud(SMS_INVITE_TEMPLETE, $send_ary);
//        var_dump($send_st);

//        //单个发送短信短信
//        $vars = array(
//            '%VCODE%' => "1234",
//            '%VTIME%' => SMS_ACTIVE_TIME
//        );
//
//        $send_st = send_single_sms(SMS_LOG_CODE_REG, SMS_REG_TEMPLATE, 17301849323, $vars);
//        var_dump($send_st);
    }

    //pdf生成
    public function test_pdf()
    {
        $html = "test";
        $file_name = "1_1";
        create_pdf_info($html, $file_name);
    }

    //下载app地址
    public function download_app()
    {
        echo "正在开发中……";
        print_r($_SERVER['HTTP_USER_AGENT']);
        $http_user_agent = $_SERVER['HTTP_USER_AGENT'];
        // 从HTTP_USER_AGENT中查找手机浏览器的关键字
        if (preg_match("/(" . implode('|', $this->client_keywords) . ")/i", strtolower($http_user_agent)))
        {//是手机
            if (stripos($http_user_agent, "iPhone")!==false) {//IOS
                redirect("https://itunes.apple.com/us/app/id1271662199");
            } else {//安卓
                redirect("http://a.app.qq.com/o/simple.jsp?pkgname=com.ipanelonline.drsay");
            }
        } else {//是PC端
            redirect("/");
        }

        //安卓下载地址
        //http://a.app.qq.com/o/simple.jsp?pkgname=com.ipanelonline.drsay

        //IOS下载地址
        //https://itunes.apple.com/us/app/id1210332875
    }

    public function drsay_meeting()
    {
       redirect("/theme/drsay_meeting.pdf");
    }
}