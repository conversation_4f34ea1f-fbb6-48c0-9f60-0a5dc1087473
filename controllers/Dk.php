<?php
/**
 * 单病种
 * Created by PhpStorm.
 * User: Amy
 * Date: 2020/09/23
 */
class Dk extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    function index()
    {
        $this->load->view("/dk/index");
    }

    //提交记录
    function save()
    {
        $post_data = $this->input->post();
//        print_r($post_data);
//        die;
        file_put_contents("./tmp/drgs_operation_data.txt", json_encode($post_data, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
        $post_data = format_data($post_data);
        $dr_id = (int)$post_data['dr_id'];
//        $unit_name = $post_data['unit_name'];
        $unit_name = $post_data['hospital'];
        $department = $post_data['department'];
//        $job_title = $post_data['job_title'];
        $job_title = $post_data['title'];
        $disease_id = $post_data['disease_id'];//疾病ID
        $disease_name = $post_data['disease_name'];//疾病名称
        $disease_num = $post_data['disease_num'];//诊疗量
        $operation_id = $post_data['operation_id'];//手术ID
        $operation_name = $post_data['operation_name'];//手术名称
        $operation_num = $post_data['operation_num'];//手术量
        if (!$unit_name) {
            _back_msg("error", "请输入【医院】");
        }
        if (!$department) {
            _back_msg("error", "请输入【科室】");
        }
        if (!$job_title) {
            _back_msg("error", "请输入【职称】");
        }
        $disease_id = array_filter($disease_id);
        $operation_id = array_filter($operation_id);
        if (count($disease_name) < 3) {
            _back_msg("error", "请至少填写3个疾病名称及对应诊疗量");
        }
//        foreach ($disease_name as $k_disease_name => $v_disease_name) {
//            if ($v_disease_name && !$disease_num[$k_disease_name] <= 0) {
//                _back_msg("error", "您输入了疾病【{$v_disease_name}】,请输入对应的【诊疗量】");
//            }
//        }
        foreach ($disease_id as $k_disease_name => $v_disease_name) {
            if (!$disease_num[$k_disease_name]) {
                _back_msg("error", "您输入了疾病【{$disease_name[$k_disease_name]}】,请输入对应的【诊疗量】");
            }
        }
        foreach ($disease_num as $k_disease_num => $v_disease_num) {
            if ($v_disease_num && !$disease_id[$k_disease_num]) {
                _back_msg("error", "第【{$k_disease_num}】行，您输入了【诊疗量】,请输入对应的【诊疗疾病名称】");
            }
        }
        $disease_info = [];
        foreach ($disease_name as $k_disease => $v_disease) {
            $disease_info[] = [
                "disease_id" => $disease_id[$k_disease],
                "disease_name" => $v_disease,
                "outpatient_size" => $disease_num[$k_disease],
            ];
        }
//        foreach ($operation_name as $k_operation_name => $v_operation_name) {
//            if ($v_operation_name && !$operation_num[$k_operation_name] <= 0) {
//                _back_msg("error", "您输入了手术【{$v_operation_name}】,请输入对应的【手术量】");
//            }
//        }
        foreach ($operation_id as $k_operation_name => $v_operation_name) {
            if (!$operation_num[$k_operation_name]) {
                _back_msg("error", "您输入了手术【{$operation_name[$k_operation_name]}】,请输入对应的【手术量】");
            }
        }
        foreach ($operation_num as $k_operation_num => $v_operation_num) {
            if ($v_operation_num && !$operation_id[$k_operation_num]) {
                _back_msg("error", "第【{$k_operation_num}】行，您输入了【手术量】,请输入对应的【手术名称】");
            }
        }
        $operation_info = [];
        foreach ($operation_name as $k_operation => $v_operation) {
            $operation_info[] = [
                "operation_id" => $operation_id[$k_operation],
                "operation_name" => $v_operation,
                "operation_size" => $operation_num[$k_operation],
            ];
        }
        $now_time = time();
        $y = date("Y", $now_time);
        $table_name = "drgs_operation_data_{$y}";
        //查询表是否存在，不存在时，新增表再添加
        $check_table = $this->db->query("select * from information_schema.COLUMNS where table_name = '{$table_name}'")->row_array();
        if (!$check_table) {//表不存在，创建表
            $this->db->query("CREATE TABLE `{$table_name}` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dr_id` int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
  `unit_name` varchar(200) NOT NULL DEFAULT '' COMMENT '机构名称',
  `department` varchar(100) NOT NULL DEFAULT '' COMMENT '科室',
  `work_years` varchar(50) NOT NULL DEFAULT '' COMMENT '工作年限',
  `diagnosis_data` text NOT NULL COMMENT '诊疗数据',
  `operation_data` text NOT NULL COMMENT '手术数据',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `y_m_d` varchar(50) NOT NULL DEFAULT '' COMMENT '年月日',
  PRIMARY KEY (`id`),
  KEY `dr_id` (`dr_id`),
  KEY `y_m_d` (`y_m_d`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单病种诊疗数据'");
            $res_table = $this->db->affected_rows();
            if (!$res_table) {
                _back_msg("error", "提交失败");
            }
        }
        $insert_data = [
            "dr_id" => $dr_id,
            "unit_name" => $unit_name,
            "department" => $department,
            "diagnosis_data" => $disease_info ? json_encode($disease_info, JSON_UNESCAPED_UNICODE) : "",
            "operation_data" => $operation_info ? json_encode($operation_info, JSON_UNESCAPED_UNICODE) : "",
            "add_time" => $now_time,
            "y_m_d" => date("Y-m-d", $now_time),
        ];
        $res = $this->db->insert("drgs_operation_data_{$y}", $insert_data);
        if ($res) {
            _back_msg("success", "提交成功", "/dk/finished");
        } else {
            _back_msg("error", "提交失败");
        }
    }

    function finished()
    {
        $this->load->view('/dk/finished');
    }

    //单病种诊疗
    function diagnosis_code(){
        $diagnosis_name = trim($this->input->post("diagnosis_name", true));
        file_put_contents("./tmp/diagnosis_code.txt", $diagnosis_name."**".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
        if (!$diagnosis_name) {
            _back_msg("error", "请提交内容");
        }
        $diagnosis_name = $this->db->escape_str($diagnosis_name);
        $info = $this->db->query("SELECT * FROM mb_offer_diagnosis_code WHERE diagnosis_name LIKE '%{$diagnosis_name}%' LIMIT 20")->result_array();
        $res = [];
        if ($info) {
            foreach ($info as $v) {
                $res[] = [
                    "id" => $v['id'],
                    "text" => "[".$v['diagnosis_code']."]".$v['diagnosis_name'],
                ];
            }
        }
        _back_msg("success", $res);
    }

    //单病种诊疗
    function drgs_operation(){
        $operation_name = trim($this->input->post("operation_name", true));
        file_put_contents("./tmp/drgs_operation.txt", $operation_name."**".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
        if (!$operation_name) {
            _back_msg("error", "请提交内容");
        }
        $operation_name = $this->db->escape_str($operation_name);
        $info = $this->db->query("SELECT * FROM mb_offer_drgs_operation WHERE operation_name LIKE '%{$operation_name}%' LIMIT 20")->result_array();
        $res = [];
        if ($info) {
            foreach ($info as $v) {
                $res[] = [
                    "id" => $v['id'],
                    "text" => "[".$v['main_code']."]".$v['operation_name'],
                ];
            }
        }
        _back_msg("success", $res);
    }
}
