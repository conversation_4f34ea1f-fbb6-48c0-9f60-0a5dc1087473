<?php

class Project extends MY_Controller
{
        public function __construct()
    {
        parent::__construct();

        $this->load->database();
        $this->load->helper('url');

        // 为Project模块设置3天的session过期时间
        $this->config->set_item('sess_expiration', 3 * 24 * 60 * 60);

        $this->load->library('session'); // 重新加载 session 库
        $this->load->model('survey_model');
    }

    public function error_page()
    {
        $this->survey_model->get_redirect_info("/bk/rs", "qw_project_list_fail");
    }

    /**
     * 登录页面
     */
    public function login()
    {
        $token = $this->uri->segment(4);
        $params = decode_param($token);
        if (!$params) {
            return $this->error_page();
        }

        $arr_params = explode('/', $params);
        $member_id = $arr_params[1] ?? '';
        $member_info = $this->db->query("select mobile from app_member where id = '{$member_id}'")->row_array();
        $mobile = $member_info['mobile'] ?? '';
        //手机号只显示后三位，前边的9位都用 * 代替
        $mobile = substr_replace($mobile, '*********', 0, strlen($mobile) - 3);

        $data = [
            'scene' => '',
            'mobile' => $mobile,
            'token' => $token
        ];

        $this->load->view('/project/login', $data);
    }

    /**
     * 发送验证码
     */
    public function send_code()
    {
        $vcode = rand(10000, 99999);
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            $token = $post_data['token'];
            $params = decode_param($token);
            if (!$params) {
                return $this->error_page();
            }

            $arr_params = explode('/', $params);
            $member_id = $arr_params[1];
            $member_info = $this->db->query("select status,mobile from app_member where id = '{$member_id}'")->row_array();
            $verify_mobile = $member_info['mobile'];
            $status = $member_info['status'];

            // if ($status != 2) {
            //     _back_msg("error", "用户状态异常！");
            // }

            try {
                // 检查60秒内是否已经发送过验证码
                $sms_limit_key = "sms_limit_" . $verify_mobile;
                $last_send_time = Redis_db::getInstance()->get("sms_limit", $sms_limit_key);

                if ($last_send_time) {
                    $time_diff = time() - $last_send_time;
                    if ($time_diff < 60) {
                        $remaining_time = 60 - $time_diff;
                        _back_msg("error", "验证码发送过于频繁，请等待{$remaining_time}秒后再试！");
                    }
                }

                // 生成scene参数用于验证码发送 - 使用手机号和验证码
                $scene = $verify_mobile . "_" . $vcode;
                $encrypted_data = substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
                $scene = $scene . "_" . $encrypted_data;

                //发送短信验证码
                $err_msg = "";
                //创蓝
                if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") { //本地
                    $st = true;
                } else {
                    // $sms_content = "上医说验证码:{$vcode}，".SMS_ACTIVE_TIME."分钟内有效，请勿泄漏！";//国际短信通道使用
                    // $st = chuanglan_single_sms(SMS_LOG_CODE_PRO_PAY_MONEY, $verify_mobile, $sms_content);

                    //todo:接高富帅的统一短信接口
                    $sendData = [
                        'mobile' => $verify_mobile,
                        "template_data" => [$vcode],

                        "other_param" => [
                            "log_code" => SMS_LOG_CODE_INVITE_PROJECT_LOGIN_CODE,
                        ],
                    ];

                    $templateId =  '【健康通】上医说验证码:{$var}，5分钟内有效，请勿泄漏！'; // 签名要带上，否则会用这个发送账号的默认签名去发，比如这个账号绑定的默认签名是【医师定考】，不带的话，显示的就是【医师定考】上医说验证码:{$var}，5分钟内有效，请勿泄漏！

                    $resSms = $this->wfunctions->sendSms($sendData, $templateId);
                    $resSmsArr = $resSms ? json_decode($resSms, true) : [];
                    if (isset($resSmsArr['code']) && $resSmsArr['code'] == 200) {
                        $st = true;
                    } else {
                        $st = false;
                    }
                }

                if ($st === true) {
                    $send_st = true;
                } else {
                    $send_st = false;
                    $err_msg = $st;
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_LOGIN;
                if ($send_st) { //短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);

                    if ($insert_id_sms_code > 0) {
                        // 记录发送时间到Redis，设置60秒过期时间
                        Redis_db::getInstance()->set("sms_limit", $sms_limit_key, time(), 60);

                        // 返回scene参数给前端
                        _back_msg('success', '短信发送成功，请查收！', $scene);
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    /**
     * 登录
     */
    public function code_login()
    {
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            $token = $post_data['token'];
            $params = decode_param($token);
            if (!$params) {
                return $this->error_page();
            }

            $arr_params = explode('/', $params);
            $member_id = $arr_params[1];
            $member_info = $this->db->query("select status,mobile from app_member where id = '{$member_id}'")->row_array();
            $verify_mobile = $member_info['mobile'];
            $status = $member_info['status'];

            // if ($status != 2) {
            //     _back_msg("error", "用户状态异常！");
            // }

            //验证数据有效性
            $verify_code = $post_data['verify_code'];
            if (empty($verify_code)) {
                _back_msg("error", "请输入手机验证码！");
            }

            try {
                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_LOGIN);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) { //检测两个字符串是否相等
                    throw new Exception("验证码错误！");
                }

                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }

                $user_info = $this->db->query("select id,mobile from app_member where mobile = '{$verify_mobile}'")->row_array();
                if (empty($user_info)) {
                    throw new Exception("用户不存在！");
                }

                //验证通过，跳转到邀请项目列表页
                $redirect_url = "/project/project/list/" . $token;

                                                                //存session，使用用户ID作为标识
                // $user_id = $user_info['id'];
                $user_id = $member_id;
                $this->session->set_userdata(["invite_project_user_{$user_id}" => 1]);

                // 手动设置session cookie的过期时间为3天
                $cookie_name = $this->config->item('sess_cookie_name');
                $cookie_expire = time() + (3 * 24 * 60 * 60); // 3天后过期
                $cookie_path = '/';
                $cookie_domain = $this->config->item('cookie_domain');
                $cookie_secure = $this->config->item('cookie_secure');
                $cookie_httponly = $this->config->item('cookie_httponly');

                setcookie(
                    $cookie_name,
                    session_id(),
                    $cookie_expire,
                    $cookie_path,
                    $cookie_domain,
                    $cookie_secure,
                    $cookie_httponly
                );

                _back_msg("success", "验证通过", $redirect_url);
                // _back_msg("success", "验证通过");
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        }
    }

    //项目列表
    public function list()
    {
        // error_reporting(-1);
        // ini_set('display_errors', 1);
        // $token = 'a61b37835d46a73e849d0aed9493bb85';
        $token = $this->uri->segment(4);
        if (empty($token)) {
            return $this->error_page();
        }

        // 解码token
        $params = decode_param($token);
        if (empty($params)) {
            return $this->error_page();
        }

        $arr_params = explode('/', $params);
        $send_project_id = $arr_params[0] ?? '';
        $member_id = $arr_params[1] ?? '';

        if (empty($member_id)) {
            return $this->error_page();
        }

        $res['send_project_id'] = $send_project_id;

        // 检查用户是否已登录 - 根据member_id获取用户信息，然后检查session
        $member_info = $this->db->query("select id from app_member where id = '{$member_id}'")->row_array();
        if (empty($member_info)) {
            // 用户不存在，跳转到登录页面
            $login_url = "/project/project/login/" . $token;
            redirect($login_url);
            exit;
        }

        $list = [];
        $type_list = $this->db->query("select id from app_project_type_list where big_type_id in (1,4)")->result_array();
        if (!empty($type_list)) {
            $project_type_list_id = implode(',', array_column($type_list, 'id'));
            //todo 上线后放开
            $app_project = $this->db->query("
                select
                    id,pro_name,min_time_limit,pro_type,open_type
                from
                    app_project
                where
                    pro_status = 3
                and
                    open_type = 1
                and
                    project_type_list_id in ({$project_type_list_id})
                order by id desc
            ")->result_array();

            // $app_project = $this->db->query("
            //     select
            //         id,pro_name,min_time_limit,pro_type,open_type
            //     from
            //         app_project
            //     where
            //         pro_status = 3
            //     and
            //         project_type_list_id in ({$project_type_list_id})
            //     order by id desc
            // ")->result_array();

            $project_partner_map = [];
            $app_project_implement_map = [];
            $send_project = $normal_projects = [];
            // 检查用户参与的项目
            foreach ($app_project as $item) {
                $table = 'app_project_implement_' . $item['id'];
                // 查询用户参与的项目且状态为未处理的项目
                $in_project = $this->db->query("select id,partner_id,groupno,finish_status,screening_status from {$table} where member_uid = '{$member_id}' ")->row_array();
                if (!empty($in_project)) {
                    $project_partner_map[$item['id']] = $in_project;
                    $app_project_implement_map[$item['id']] = $in_project['id'];
                    if ($in_project['finish_status'] == '' && !in_array($in_project['screening_status'], [6, 7])) {
                        //未处理的项目
                        if ($item['id'] == $send_project_id) {
                            $send_project[] = $item;
                        } else {
                            $normal_projects[] = $item;
                        }
                    }
                }
            }

            //合并项目列表 推送项目排在最前面
            $list = array_merge($send_project, $normal_projects);
        }

        //获取项目礼金
        if (!empty($list)) {
            $min_money = $max_money = 0;

            // 提取所有项目ID
            $project_ids = array_column($list, 'id');
            $project_ids_str = implode(',', $project_ids);

            // 批量查询所有相关数据
            // 1. 查询专属医师礼金设置
            $specific_settings = [];
            if (!empty($project_ids)) {
                $specific_query = $this->db->query("
                    SELECT pid, price
                    FROM app_project_setting_sample
                    WHERE pid IN ({$project_ids_str})
                    AND member_uid = {$member_id}
                    AND is_delete = 0
                ")->result_array();

                foreach ($specific_query as $row) {
                    $specific_settings[$row['pid']] = $row['price'];
                }
            }

            // 2. 查询通用礼金设置
            $general_settings = [];
            if (!empty($project_ids)) {
                $general_query = $this->db->query("
                    SELECT pid, MAX(price) as max_price, MIN(price) as min_price
                    FROM app_project_setting_sample
                    WHERE pid IN ({$project_ids_str})
                    AND member_uid = 0
                    AND is_delete = 0
                    GROUP BY pid
                ")->result_array();

                foreach ($general_query as $row) {
                    $general_settings[$row['pid']] = [
                        'min_price' => $row['min_price'],
                        'max_price' => $row['max_price']
                    ];
                }
            }

            // 3. 查询邀请管理奖励
            $patient_rewards = [];
            if (!empty($project_ids)) {
                $patient_query = $this->db->query("
                    SELECT pid, patient_reward
                    FROM app_patient_fwy_add
                    WHERE pid IN ({$project_ids_str})
                    AND btjr_member_id = {$member_id}
                ")->result_array();

                foreach ($patient_query as $row) {
                    $patient_rewards[$row['pid']] = $row['patient_reward'];
                }
            }

            // 4. 批量查询分配对接奖励
            $partner_rewards = [];
            if (!empty($project_partner_map) && !empty($project_ids)) {
                $partner_conditions = [];
                $valid_partner_map = [];
                $added_combinations = []; // 用于记录已添加的组合，避免重复

                foreach ($project_partner_map as $pid => $partner) {
                    if (in_array($pid, $project_ids)) {
                        $partner_key = $pid . '_' . $partner['partner_id'] . '_' . $partner['groupno'];

                        // 检查这个组合是否已经添加过
                        if (!isset($added_combinations[$partner_key])) {
                            $partner_conditions[] = "(project_id = {$pid} AND partner_id = {$partner['partner_id']} AND groupno = {$partner['groupno']})";
                            $added_combinations[$partner_key] = true;
                        }

                        $valid_partner_map[$partner_key] = $pid;
                    }
                }

                if (!empty($partner_conditions)) {
                    $partner_conditions_str = implode(' OR ', $partner_conditions);
                    // $partner_query = $this->db->query("
                    //     SELECT partner_id, groupno, price_c
                    //     FROM app_project_partner
                    //     WHERE project_id IN ({$project_ids_str}) AND ({$partner_conditions_str})
                    // ")->result_array();


                    $partner_query = $this->db->query("
                        SELECT project_id, partner_id, groupno, price_c
                        FROM app_project_partner
                        WHERE ({$partner_conditions_str})
                    ")->result_array();

                    foreach ($partner_query as $row) {
                        $partner_key = $row['project_id'] . '_' . $row['partner_id'] . '_' . $row['groupno'];
                        $partner_rewards[$partner_key] = $row['price_c'];
                        // 确保只存储属于当前项目列表的奖励
                        // if (isset($valid_partner_map[$partner_key])) {
                        //     $partner_rewards[$partner_key] = $row['pirce_c'];
                        // }
                    }
                }
            }

            // 处理每个项目的礼金
            // $arr_pro_type = $this->config->item('arr_pro_type');
            $arr_pro_type = [
                '1' => '问卷',
                '2' => '访谈',
                '3' => 'TDI深访',
                '4' => '座谈',
                '5' => '短信',
                '6' => '邮件',
                '7' => '开发',
                '8' => 'Re-contact',
                '9' => '患者',
                '10' => '专家',
                '11' => '纸质问卷',
                '12' => 'IR-Check',
            ];

            $min_money = $max_money = 0;
            foreach ($list as $key => $item) {
                $pid = $item['id'];
                $money = 0;
                $money_display = 0;
                $pro_type = $item['pro_type'];
                $pro_type_name = $arr_pro_type[$pro_type] ?? '';
                $list[$key]['pro_type_name'] = $pro_type_name;

                //生成问卷连接
                $app_project_implement_id = $app_project_implement_map[$pid] ?? null;
                // $partner_link = '';
                // if ($app_project_implement_id) {
                //     $go_c_param = $app_project_implement_id . "_" . $pid;
                //     $uid_str = encrypt_param($go_c_param);
                //     $partner_link = DRSAY_WEB . 'go_c/c/' . $uid_str;
                // }

                $active_token = encrypt_param($app_project_implement_id . '/' . $pid);
                $partner_link = "/project/project/active_project/" . $active_token;

                $list[$key]['partner_link'] = $partner_link;

                // 按优先级设置礼金
                if (isset($specific_settings[$pid])) {
                    // 第一优先级：专属医师礼金
                    $money = $specific_settings[$pid];
                    $money_display = number_format($money, 2);
                    $min_money = bcadd($money, $min_money, 2);
                    $max_money = bcadd($money, $max_money, 2);
                } elseif (isset($general_settings[$pid])) {
                    // 第二优先级：通用礼金设置
                    $setting = $general_settings[$pid];
                    $min_money = bcadd($setting['min_price'], $min_money, 2);
                    $max_money = bcadd($setting['max_price'], $max_money, 2);
                    if ($min_money == $max_money) {
                        $money_display = number_format($min_money, 2);
                    } else {
                        $money_display = number_format($setting['min_price'], 2) . ' ~ ' . number_format($setting['max_price'], 2);
                    }
                } elseif (isset($patient_rewards[$pid])) {
                    // 第三优先级：邀请管理奖励
                    $money = $patient_rewards[$pid];
                    $money_display = number_format($money, 2);
                    $min_money = bcadd($money, $min_money, 2);
                    $max_money = bcadd($money, $max_money, 2);
                } else {
                    // 第四优先级：分配对接奖励
                    $partner = $project_partner_map[$pid] ?? [];
                    if (!empty($partner)) {
                        $partner_key = $pid . '_' . $partner['partner_id'] . '_' . $partner['groupno'];
                        if (isset($partner_rewards[$partner_key])) {
                            $money = $partner_rewards[$partner_key];
                            $money_display = number_format($money, 2);
                            $min_money = bcadd($money, $min_money, 2);
                            $max_money = bcadd($money, $max_money, 2);
                        }
                    }
                }

                $list[$key]['money'] = $money_display;
            }
        }

        $res['list'] = $list;
        $res['total'] = count($list);
        $res['total_money'] = number_format($max_money, 2);
        // if ($min_money == $max_money) {
        //     $res['total_money'] = number_format($min_money, 2);
        // } else {
        //     $res['total_money'] = number_format($min_money, 2) . ' ~ ' . number_format($max_money, 2);
        // }

        $this->load->view('/project/list', $res);

        // echo json_encode(['err_msg' => 'success', 'result' => $res]);exit;
    }

    // 保存验证码数据
    private function set_vcode($data)
    {
        if (!$data) {
            return false;
        }
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }

    //参加项目
    public function active_project()
    {
        $token = $this->uri->segment(4);
        $token_info = decode_param($token);
        if (!$token_info) {
            echo '参数错误';
            exit;
        }

        $token_arr = explode('/', $token_info);
        $app_project_implement_id = $token_arr[0] ?? '';
        $pid = $token_arr[1] ?? '';
        if (empty($app_project_implement_id) || empty($pid)) {
            echo '参数错误';
            exit;
        }

        // 根据app_project_implement_id获取用户信息
        $table = 'app_project_implement_' . $pid;
        $implement_info = $this->db->query("select member_uid from {$table} where id = '{$app_project_implement_id}'")->row_array();

        if (empty($implement_info)) {
            echo '项目信息不存在';
            exit;
        }

        $member_id = $implement_info['member_uid'];

        // 获取用户信息
        $member_info = $this->db->query("select id from app_member where id = '{$member_id}'")->row_array();
        if (empty($member_info)) {
            echo '用户不在项目中';
            exit;
        }

        // 检查用户是否已登录
        $user_id = $member_info['id'];
        $session_key = "invite_project_user_{$user_id}";
        $is_logged_in = $this->session->userdata($session_key);

        if (!$is_logged_in) {
            // 未登录，跳转到登录页面
            $token = $pid . "/" . $member_id;
            $encrypted_token = encrypt_param($token);
            $login_url = "/project/project/login/" . $encrypted_token;
            redirect($login_url);
            exit;
        }

        // 已登录，生成问卷链接并跳转
        $go_c_param = $app_project_implement_id . "_" . $pid;
        $uid_str = encrypt_param($go_c_param);
        $partner_link = DRSAY_WEB . 'go_c/c/' . $uid_str;
        redirect($partner_link);
    }

    public function privacy_policy()
    {
        $this->load->view('/project/privacy_policy');
    }
}
