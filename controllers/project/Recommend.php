<?php

class Recommend extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->database();
        $this->load->helper('url');

                        // 为Project模块设置2分钟的session过期时间（测试用）
        $this->config->set_item('sess_expiration', 2 * 60); // 服务器端session 2分钟过期

        $this->load->library('session'); // 重新加载 session 库
    }

    public function error_page()
    {
        $this->load->view('/project/recommend/404');
    }

    /**
     * 登录页面
     */
    public function login()
    {
        // 推荐项目编号、被推荐项目编号、推荐人编号
        // $t = encrypt_param('17223/19035/1540124');
        // var_dump($t);
        // exit;

        $token = $this->uri->segment(4);
        $params = decode_param($token);
        if (!$params) {
            return $this->error_page();
        }

        // 检查是否已经登录，如果已登录则直接跳转到对应页面
        $mobile = $this->session->userdata('user_info')['mobile'] ?? '';
        if ($mobile) {
            // 检查登录session是否有效
            $isLoggedIn = false;
            foreach ($_SESSION as $key => $value) {
                if (strpos($key, 'invite_project_user_') === 0 && $value == 1) {
                    $isLoggedIn = true;
                    break;
                }
            }
            
            if ($isLoggedIn) {
                // 已登录，跳转到对应的业务页面
                $redirectUrl = $this->determineRedirectUrl($mobile, $token);
                $this->redirectWithCurrentProtocol($redirectUrl);
                return;
            }
        }

        $data = [
            'token' => $token,
        ];

        $this->load->view('/project/recommend/login', $data);
    }

    /**
     * 发送验证码
     */
    public function sendSms()
    {
        $vcode = rand(10000, 99999);
        $postData = $this->input->post();
        if ($postData) {
            $postData = format_post_data($postData);
            $token = $postData['token'];
            $mobile = $postData['mobile'];
            $params = decode_param($token);
            if (!$params) {
                return $this->error_page();
            }

            try {
                // 检查60秒内是否已经发送过验证码
                $smsLimitKey = "send_sms_limit_" . $mobile;
                $lastSendTime = Redis_db::getInstance()->get("send_sms_limit", $smsLimitKey);

                if ($lastSendTime) {
                    $timeDiff = time() - $lastSendTime;
                    if ($timeDiff < 60) {
                        $remainingTime = 60 - $timeDiff;
                        _back_msg("error", "验证码发送过于频繁，请等待{$remainingTime}秒后再试！");
                    }
                }

                //发送短信验证码
                $errMsg = "";

                //创蓝
                if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") { //本地
                    $st = true;
                } else {
                    //todo:接高富帅的统一短信接口
                    $sendData = [
                        'mobile' => $mobile,
                        "template_data" => [$vcode],

                        "other_param" => [
                            "log_code" => SMS_LOG_CODE_INVITE_PROJECT_PERFECT_INFORMATION_LOGIN_CODE,
                        ],
                    ];

                    $templateId =  '【健康通】上医说验证码:{$var}，5分钟内有效，请勿泄漏！'; // 签名要带上，否则会用这个发送账号的默认签名去发，比如这个账号绑定的默认签名是【医师定考】，不带的话，显示的就是【医师定考】上医说验证码:{$var}，5分钟内有效，请勿泄漏！

                    $resSms = $this->wfunctions->sendSms($sendData, $templateId);
                    $resSmsArr = $resSms ? json_decode($resSms, true) : [];
                    if (isset($resSmsArr['code']) && $resSmsArr['code'] == 200) {
                        $st = true;
                    } else {
                        $st = false;
                    }
                }

                if ($st === true) {
                    $sendSt = true;
                } else {
                    $sendSt = false;
                    $errMsg = $st;
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_LOGIN;
                if ($sendSt) { //短信发送成功,记录入库
                    $verifyData = array(
                        'mobile' => $mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insertIdSmsCode = $this->setVcode($verifyData);

                    if ($insertIdSmsCode > 0) {
                        // 记录发送时间到Redis，设置60秒过期时间
                        Redis_db::getInstance()->set("send_sms_limit", $smsLimitKey, time(), 60);

                        // 返回scene参数给前端
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($errMsg ? $errMsg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    /**
     * 登录
     */
    public function codeLogin()
    {
        $postData = $this->input->post();
        if ($postData) {
            $postData = format_post_data($postData);
            $mobile = $postData['mobile'];
            $token = $postData['token'];
            $params = decode_param($token);
            if (!$params) {
                return $this->error_page();
            }

            $memberInfo = $this->db->query("select id,status,mobile from app_member where mobile = '{$mobile}' and status = 2")->row_array();
            $memberId = $memberInfo['id'];

            //验证数据有效性
            $verifyCode = $postData['verify_code'];
            if (empty($verifyCode)) {
                _back_msg("error", "请输入手机验证码！");
            }

            try {
                $userVerificationCode = get_verification_code($mobile, VERIFICATION_CODE_LOGIN);
                if (strcasecmp($verifyCode, $userVerificationCode['vcode']) !== 0) { //检测两个字符串是否相等
                    throw new Exception("验证码错误！");
                }

                if ($userVerificationCode) {
                    //更新验证码为已使用状态
                    update_verification_code($userVerificationCode['id']);
                }

                // 存session，使用用户ID作为标识和手机号
                $this->session->set_userdata([
                    "invite_project_user_{$memberId}" => 1, 
                    'user_info' => ['mobile' => $mobile]
                ]);

                // 手动设置cookie的过期时间为3分钟（比session长1分钟）
                $cookieName = $this->config->item('sess_cookie_name');
                $cookieExpire = time() + (3 * 60); // 3分钟后过期（比session长1分钟）
                $cookiePath = '/';
                $cookieDomain = $this->config->item('cookie_domain');
                $cookieSecure = $this->config->item('cookie_secure');
                $cookieHttponly = $this->config->item('cookie_httponly');

                setcookie(
                    $cookieName,
                    session_id(),
                    $cookieExpire,
                    $cookiePath,
                    $cookieDomain,
                    $cookieSecure,
                    $cookieHttponly
                );

                // 根据业务流程进行跳转判断
                $redirectUrl = $this->determineRedirectUrl($mobile, $token);
                
                _back_msg("success", "验证通过", $redirectUrl);
                // _back_msg("success", "验证通过");
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        }
    }

    /**
     * 完善信息页
     */
    public function editUser()
    {
        $token = $this->uri->segment(4);
        $params = decode_param($token);
        if (!$params) {
            return $this->error_page();
        }
        
        // 检查登录状态
        $mobile = $this->checkLoginOrRedirect($token);
        if (!$mobile) {
            return;
        }

        // 检查是否强制编辑模式（用户主动要编辑信息，比如从驳回页面跳转过来）
        $forceEdit = $this->input->get('edit') == '1';
        
        if (!$forceEdit) {
            // 非强制编辑模式下，重新判断当前用户应该跳转到哪个页面
            $correctUrl = $this->determineRedirectUrl($mobile, $token);
            $currentUrl = "/project/recommend/editUser/" . $token;
            
            // 如果当前页面不是用户应该访问的页面，则重定向
            if ($correctUrl !== $currentUrl) {
                $this->redirectWithCurrentProtocol($correctUrl);
                return;
            }
        }

        // 获取当前用户信息，用于回显（如果是修改模式）
        $userInfo = [];
        
        if ($mobile) {
            $inviteInfo = $this->db->query("
                SELECT btjr_name, btjr_province, btjr_city, btjr_district, 
                       btjr_unit_id, btjr_department_id, btjr_license_no,
                       btjr_payment_name, btjr_payment_account, 
                       btjr_practice_sort, btjr_job_title, audit_status
                FROM app_member_invite 
                WHERE btjr_mobile = ?
            ", [$mobile])->row_array();
            
            if ($inviteInfo) {
                $userInfo = [
                    'name' => $inviteInfo['btjr_name'],
                    'province_id' => $inviteInfo['btjr_province'],
                    'city_id' => $inviteInfo['btjr_city'],
                    'district_id' => $inviteInfo['btjr_district'],
                    'hospital_id' => $inviteInfo['btjr_unit_id'],
                    'department_id' => $inviteInfo['btjr_department_id'],
                    'practice_sort_id' => $inviteInfo['btjr_practice_sort'],
                    'job_title_id' => $inviteInfo['btjr_job_title'],
                    'license_no' => $inviteInfo['btjr_license_no'],
                    'pay_name' => $inviteInfo['btjr_payment_name'],
                    'pay_count' => $inviteInfo['btjr_payment_account'],
                    'is_edit_mode' => true,
                    'audit_status' => $inviteInfo['audit_status']
                ];
            }
        }

        $data = [
            'token' => $token,
            'user_info' => $userInfo
        ];

        $this->load->view('/project/recommend/edit-user', $data);
    }

    // 保存验证码数据
    private function setVcode($data)
    {
        if (!$data) {
            return false;
        }
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }

    
    /**
     * 检查登录状态，未登录则跳转到登录页
     * @param string $token
     * @return string|null 返回手机号，未登录时会直接跳转
     */
    private function checkLoginOrRedirect($token)
    {
        $mobile = $this->session->userdata('user_info')['mobile'] ?? '';
        if (!$mobile) {
            $this->redirectWithCurrentProtocol("/project/recommend/login/" . $token);
            return null;
        }
        
        // 检查登录session是否有效
        $isLoggedIn = false;
        $sessionKey = '';
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, 'invite_project_user_') === 0 && $value == 1) {
                $isLoggedIn = true;
                $sessionKey = $key;
                break;
            }
        }
        
        if (!$isLoggedIn) {
            $this->redirectWithCurrentProtocol("/project/recommend/login/" . $token);
            return null;
        }
        
        // 用户处于活跃状态，自动续期session
        $this->refreshSession($mobile, $sessionKey);
        
        return $mobile;
    }
    
    /**
     * 刷新用户session，延长有效期
     * @param string $mobile 用户手机号
     * @param string $sessionKey session键名
     */
    private function refreshSession($mobile, $sessionKey = '')
    {
        // 如果没有传入sessionKey，尝试查找
        if (empty($sessionKey)) {
            foreach ($_SESSION as $key => $value) {
                if (strpos($key, 'invite_project_user_') === 0 && $value == 1) {
                    $sessionKey = $key;
                    break;
                }
            }
        }
        
        if (!empty($sessionKey)) {
            // 方法1: 尝试直接修改全局config数组
            global $CFG;
            $originalExpiration = $CFG->config['sess_expiration'] ?? 300;
            $CFG->config['sess_expiration'] = 3 * 60; // 3分钟
            
            // 重新设置session数据
            $this->session->set_userdata([
                $sessionKey => 1, 
                'user_info' => ['mobile' => $mobile],
                'last_activity' => time()
            ]);
            
            // 强制重新生成session cookie
            $this->session->sess_regenerate(false);
            
            // 恢复原配置
            $CFG->config['sess_expiration'] = $originalExpiration;
            
            // 方法2: 如果上面不行，直接设置cookie
            $cookieName = $this->config->item('sess_cookie_name');
            $cookieExpire = time() + (3 * 60);
            
            $result = setcookie(
                $cookieName,
                session_id(),
                $cookieExpire,
                '/',
                $this->config->item('cookie_domain'),
                $this->config->item('cookie_secure'),
                $this->config->item('cookie_httponly')
            );
            
            error_log("refreshSession: Mobile: {$mobile}, 全局配置修改: " . ($CFG->config['sess_expiration'] === 180 ? 'success' : 'failed') . ", 手动cookie设置: " . ($result ? 'success' : 'failed'));
        }
    }
    
    /**
     * 使用当前协议进行跳转，避免协议不一致
     * @param string $url 相对URL路径
     */
    private function redirectWithCurrentProtocol($url)
    {
        // 如果已经是完整URL，直接跳转
        if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
            redirect($url);
            return;
        }
        
        // 构建完整URL，使用当前协议
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $fullUrl = $protocol . '://' . $host . $url;
        redirect($fullUrl);
    }

    /**
     * 根据业务流程确定登录后的跳转URL
     */
    private function determineRedirectUrl($mobile, $token)
    {
        // 解析token获取参数 
        $params = decode_param($token);
        if (!$params) {
            #todo 这里的跳转有问题吧，为什么是跳到完善信息页
            return "/project/recommend/editUser/" . $token;
        }
        
        $arrParams = explode('/', $params);
        $tjPid = (int)$arrParams[0];           // 推荐项目ID
        $btjPid = (int)$arrParams[1];          // 被推荐项目ID  
        $tjrMemberUid = (int)$arrParams[2];    // 推荐人ID
        
        // 1. 先查询会员表，看被推荐人是否已经审核通过
        $memberInfo = $this->db->query("SELECT id, status FROM app_member WHERE mobile = ? AND status = 2", [$mobile])->row_array();
        
        if ($memberInfo) {
            // 被推荐人存在且审核通过，尝试生成问卷链接并直接跳转
            $partnerLink = $this->generatePartnerLink($mobile, $tjPid, $btjPid);
            if ($partnerLink) {
                // 直接跳转到问卷
                $this->redirectWithCurrentProtocol($partnerLink);
                return; // 这里实际上不会执行到，因为上面已经跳转了
            }
            
            // // 无法生成问卷链接，返回成功页
            // return "/project/recommend/subSuccess/" . $token;
        }
        
        // 2. 查询app_member_invite表，看是否有推荐记录
        $inviteInfo = $this->db->query("SELECT audit_status FROM app_member_invite WHERE btjr_mobile = ?", [$mobile])->row_array();
        
        if (!$inviteInfo) {
            // 没有推荐记录，跳到完善信息页
            return "/project/recommend/editUser/" . $token;
        }
        
        // 3. 根据审核状态进行跳转
        switch ($inviteInfo['audit_status']) {
            case 1: // 待审核
                return "/project/recommend/subSuccess/" . $token;
            case 2: // 审核通过，尝试生成问卷链接
                $partnerLink = $this->generatePartnerLink($mobile, $tjPid, $btjPid);
                if ($partnerLink) {
                    // 直接跳转到问卷
                    $this->redirectWithCurrentProtocol($partnerLink);
                    return; // 这里实际上不会执行到，因为上面已经跳转了
                }
                return "/project/recommend/subSuccess/" . $token;
            case 3: // 驳回
                return "/project/recommend/subError/" . $token;
            default:
                return "/project/recommend/editUser/" . $token;
        }
    }
    
    /**
     * 生成被推荐人的问卷链接
     * @param string $mobile 被推荐人手机号
     * @param int $tjPid 推荐项目ID
     * @param int $btjPid 被推荐项目ID
     * @return string|null 返回问卷链接或null
     */
    private function generatePartnerLink($mobile, $tjPid, $btjPid)
    {
        try {
            // 1. 查询app_member_invite表获取invite_uuid
            $inviteInfo = $this->db->query("
                SELECT invite_uuid 
                FROM app_member_invite 
                WHERE btjr_mobile = ? 
                LIMIT 1
            ", [$mobile])->row_array();
            
            if (!$inviteInfo) {
                error_log("生成问卷链接失败 - 未找到推荐记录: {$mobile}");
                return null;
            }
            
            $inviteUuid = $inviteInfo['invite_uuid'];
            
            // 2. 查询app_member_invite_project表
            $projectInfo = $this->db->query("
                SELECT btj_pid 
                FROM app_member_invite_project 
                WHERE tj_pid = ? AND btj_pid = ? AND invite_uuid = ? AND audit_status = 2
                LIMIT 1
            ", [$tjPid, $btjPid, $inviteUuid])->row_array();
            
            if (!$projectInfo) {
                error_log("生成问卷链接失败 - 项目未审核通过或不存在: tj_pid={$tjPid}, btj_pid={$btjPid}, uuid={$inviteUuid}");
                return null;
            }
            
            $finalBtjPid = $projectInfo['btj_pid'];
            
            // 3. 检查项目执行表是否存在
            $implementTable = 'app_project_implement_' . $finalBtjPid;
            $tableExists = $this->db->query("SHOW TABLES LIKE '{$implementTable}'")->row_array();
            
            if (!$tableExists) {
                error_log("生成问卷链接失败 - 项目执行表不存在: {$implementTable}");
                return null;
            }
            
            // 4. 根据被推荐人手机号获取项目执行明细ID
            $implementInfo = $this->db->query("
                SELECT api.id as app_project_implement_id
                FROM {$implementTable} api
                LEFT JOIN app_member am ON api.member_uid = am.id
                WHERE am.mobile = ?
                LIMIT 1
            ", [$mobile])->row_array();
            
            if (!$implementInfo) {
                error_log("生成问卷链接失败 - 被推荐人未参与项目: mobile={$mobile}, table={$implementTable}");
                return null;
            }
            
            $appProjectImplementId = $implementInfo['app_project_implement_id'];
            
            // 5. 生成问卷链接
            $activeToken = encrypt_param($appProjectImplementId . '/' . $finalBtjPid);
            $partnerLink = "/project/project/active_project/" . $activeToken;
            
            error_log("成功生成问卷链接: mobile={$mobile}, link={$partnerLink}");
            
            return $partnerLink;
            
        } catch (Exception $e) {
            error_log("生成问卷链接异常: " . $e->getMessage() . ", mobile={$mobile}");
            return null;
        }
    }

    /**
     * 获取省份列表（仅中国）
     */
    public function getProvinces()
    {
        $provinces = $this->db->query("
            SELECT id, val, f_val 
            FROM app_sys_dictionary 
            WHERE big_class_id = 9 AND level = 1 AND pid = 68 AND status = 1 
            ORDER BY sort_number ASC, id ASC
        ")->result_array();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 200, 'data' => $provinces]);
    }

    /**
     * 根据省份ID获取城市列表
     */
    public function getCities()
    {
        $provinceId = $this->input->get('province_id');
        if (empty($provinceId)) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '省份ID不能为空']);
            return;
        }

        $cities = $this->db->query("
            SELECT id, val, f_val 
            FROM app_sys_dictionary 
            WHERE big_class_id = 9 AND level = 2 AND pid = ? AND status = 1 
            ORDER BY sort_number ASC, id ASC
        ", [$provinceId])->result_array();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 200, 'data' => $cities]);
    }

    /**
     * 根据城市ID获取区县列表
     */
    public function getDistricts()
    {
        $cityId = $this->input->get('city_id');
        if (empty($cityId)) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '城市ID不能为空']);
            return;
        }

        $districts = $this->db->query("
            SELECT id, val, f_val 
            FROM app_sys_dictionary 
            WHERE big_class_id = 9 AND level = 3 AND pid = ? AND status = 1 
            ORDER BY sort_number ASC, id ASC
        ", [$cityId])->result_array();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 200, 'data' => $districts]);
    }

    /**
     * 根据省市区和关键字搜索医院
     */
    public function searchHospitals()
    {
        $provinceId = $this->input->get('province_id');
        $cityId = $this->input->get('city_id');
        $districtId = $this->input->get('district_id');
        $keyword = trim($this->input->get('keyword'));
        $hospitalId = $this->input->get('hospital_id');
        
        // 如果传了hospital_id，直接查询该医院
        if (!empty($hospitalId)) {
            $hospitals = $this->db->query("
                SELECT id, name, full_name, address 
                FROM app_unit 
                WHERE id = ? AND status = 3 AND is_del = 1
            ", [$hospitalId])->result_array();
            
            // 格式化返回数据
            $formattedHospitals = array_map(function($hospital) {
                return [
                    'id' => $hospital['id'],
                    'name' => $hospital['name'],
                    'full_name' => $hospital['full_name'],
                    'display_name' => $hospital['full_name'] ?: $hospital['name'],
                    'address' => $hospital['address']
                ];
            }, $hospitals);
            
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 200, 'data' => $formattedHospitals]);
            return;
        }
        
        if (empty($keyword)) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '搜索关键字不能为空']);
            return;
        }

        if (empty($provinceId) || empty($cityId)) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '请选择省市']);
            return;
        }

        // 构建搜索条件
        $whereConditions = [
            "status = 3", // 有效状态
            "is_del = 1", // 未删除
            "province = ?",
            "city = ?",
            "(name LIKE ? OR full_name LIKE ? OR alias_name LIKE ?)"
        ];
        
        $params = [$provinceId, $cityId];
        $keywordParam = '%' . $keyword . '%';
        $params[] = $keywordParam;
        $params[] = $keywordParam;
        $params[] = $keywordParam;

        // 如果有区县ID，添加区县条件
        if (!empty($districtId)) {
            $whereConditions[] = "district = ?";
            $params[] = $districtId;
        }

        $whereClause = implode(' AND ', $whereConditions);

        $hospitals = $this->db->query("
            SELECT id, name, full_name, address 
            FROM app_unit 
            WHERE {$whereClause}
            ORDER BY name ASC 
            LIMIT 20
        ", $params)->result_array();

        // echo $this->db->last_query();
        // exit;
        
        // 格式化返回数据
        $formattedHospitals = array_map(function($hospital) {
            return [
                'id' => $hospital['id'],
                'name' => $hospital['name'],
                'full_name' => $hospital['full_name'],
                'display_name' => $hospital['full_name'] ?: $hospital['name'],
                'address' => $hospital['address']
            ];
        }, $hospitals);
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 200, 'data' => $formattedHospitals]);
    }

    /**
     * 根据关键字搜索科室
     */
    public function searchDepartments()
    {
        $keyword = trim($this->input->get('keyword'));
        $departmentId = $this->input->get('department_id');
        
        // 如果传了department_id，直接查询该科室
        if (!empty($departmentId)) {
            $departments = $this->db->query("
                SELECT id, department as name 
                FROM info_department 
                WHERE is_standard = 1 AND id = ?
            ", [$departmentId])->result_array();
            
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 200, 'data' => $departments]);
            return;
        }
        
        if (empty($keyword)) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '搜索关键字不能为空']);
            return;
        }

        $departments = $this->db->query("
            SELECT id, department as name 
            FROM info_department 
            WHERE is_standard = 1 AND department LIKE ?
            ORDER BY department ASC 
            LIMIT 20
        ", ['%' . $keyword . '%'])->result_array();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 200, 'data' => $departments]);
    }

    /**
     * 获取执业类别列表
     */
    public function getPracticeSorts()
    {
        $practiceSorts = $this->db->query("
            SELECT id, val as text, id as value 
            FROM app_sys_dictionary 
            WHERE big_class_id = 2 AND pid = 3872 AND status = 1 
            ORDER BY sort_number ASC, id ASC
        ")->result_array();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 200, 'data' => $practiceSorts]);
    }

    /**
     * 根据执业类别ID获取职称列表
     */
    public function getJobTitles()
    {
        $practiceSortId = $this->input->get('practice_sort_id');
        if (empty($practiceSortId)) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '执业类别ID不能为空']);
            return;
        }

        $jobTitles = $this->db->query("
            SELECT id, val as text, id as value 
            FROM app_sys_dictionary 
            WHERE big_class_id = 6 AND pid = ? AND status = 1 
            ORDER BY sort_number ASC, id ASC
        ", [$practiceSortId])->result_array();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 200, 'data' => $jobTitles]);
    }

    /**
     * 提交用户信息
     */
    public function submitUserInfo()
    {
        $postData = $this->input->post();
        if (!$postData) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '请求数据不能为空']);
            return;
        }

        $postData = format_post_data($postData);
        
        // 获取token
        $token = $postData['token'] ?? '';
        if (empty($token)) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => 'Token参数不能为空']);
            return;
        }
        
        // 验证必填字段
        $requiredFields = [
            'name' => '姓名',
            'province_id' => '省份',
            'city_id' => '城市',
            'district_id' => '区县',
            'hospital_id' => '医院',
            'department_id' => '科室',
            'practice_sort_id' => '执业类别',
            'job_title_id' => '职称',
            'pay_name' => '支付宝姓名',
            'pay_count' => '支付宝账号'
        ];

        foreach ($requiredFields as $field => $label) {
            if (empty($postData[$field])) {
                header('Content-Type: application/json; charset=utf-8');
                // 根据字段类型调整提示文案
                $action = in_array($field, ['name', 'pay_name', 'pay_count', 'license_no']) ? '输入' : '选择';
                echo json_encode(['code' => 400, 'msg' => "请{$action}{$label}"]);
                return;
            }
        }
        
        // 数据格式和长度验证
        if (!preg_match('/^[\x{4e00}-\x{9fa5}a-zA-Z\s]{2,20}$/u', $postData['name'])) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '姓名格式不正确，请输入2-20位中文或英文']);
            return;
        }
        
        if (!preg_match('/^[\x{4e00}-\x{9fa5}a-zA-Z\s]{2,20}$/u', $postData['pay_name'])) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '支付宝姓名格式不正确，请输入2-20位中文或英文']);
            return;
        }
        
        
        // 验证数字类型字段
        $numericFields = ['province_id', 'city_id', 'district_id', 'hospital_id', 'department_id', 'practice_sort_id', 'job_title_id'];
        foreach ($numericFields as $field) {
            if (!is_numeric($postData[$field]) || $postData[$field] <= 0) {
                header('Content-Type: application/json; charset=utf-8');
                echo json_encode(['code' => 400, 'msg' => '参数格式错误']);
                return;
            }
        }
        
        // 1. 验证医师执业证号（可选字段，但填写了就要校验格式）
        if (!empty($postData['license_no'])) {
            if (strlen($postData['license_no']) > 50) {
                header('Content-Type: application/json; charset=utf-8');
                echo json_encode(['code' => 400, 'msg' => '执业证书编号长度不能超过50位']);
                return;
            }
            
            if (!check_credentials_no($postData['license_no'])) {
                header('Content-Type: application/json; charset=utf-8');
                echo json_encode(['code' => 400, 'msg' => '医师执业证号格式不正确']);
                return;
            }
        }
        
        // 2. 验证支付宝账号格式（只允许手机号或邮箱格式）
        $alipayAccount = $postData['pay_count'];
        $isValidPhone = preg_match('/^1[3-9]\d{9}$/', $alipayAccount);
        $isValidEmail = filter_var($alipayAccount, FILTER_VALIDATE_EMAIL);
        
        if (!$isValidPhone && !$isValidEmail) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '支付宝账号只能是手机号或邮箱格式']);
            return;
        }
        
        // 3. 验证支付宝姓名与用户姓名一致性
        if ($postData['pay_name'] !== $postData['name']) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 400, 'msg' => '支付宝姓名必须与姓名一致']);
            return;
        }

        try {
            // 解析token获取推荐参数
            $params = decode_param($token);
            if (!$params) {
                header('Content-Type: application/json; charset=utf-8');
                echo json_encode(['code' => 400, 'msg' => 'Token解析失败']);
                return;
            }
            
            $arrParams = explode('/', $params);
            if (count($arrParams) < 3) {
                header('Content-Type: application/json; charset=utf-8');
                echo json_encode(['code' => 400, 'msg' => 'Token参数格式错误']);
                return;
            }
            
            // 从token中解析参数：推荐项目编号/被推荐项目编号/推荐人编号
            $tjPid = (int)$arrParams[0];           // 首次推荐项目id
            $btjPid = (int)$arrParams[1];          // 首次被推荐项目id  
            $tjrMemberUid = (int)$arrParams[2];    // 首次推荐人唯一编号
            
            // 生成唯一的推荐编号
            $inviteUuid = substr(md5(uniqid(mt_rand(), true)), 0, 16);
            
            // 从session获取当前用户信息
            $currentUserMobile = $this->session->userdata('user_info')['mobile'] ?? '';
            
            if (!$currentUserMobile) {
                header('Content-Type: application/json; charset=utf-8');
                echo json_encode(['code' => 401, 'msg' => '用户未登录或登录已过期']);
                return;
            }
            
            // 4. 验证支付宝账号唯一性
            $memberInfo = $this->db->query("SELECT id FROM app_member WHERE mobile = ?", [$currentUserMobile])->row_array();
            
            if (!$memberInfo) {
                // 用户不存在，检查支付宝账号是否被其他人使用
                $existingPayment = $this->db->query(
                    "SELECT id FROM app_ex_payment_account WHERE payment_type = 206 AND payment_account = ?",
                    [$alipayAccount]
                )->row_array();
                
                if ($existingPayment) {
                    header('Content-Type: application/json; charset=utf-8');
                    echo json_encode(['code' => 400, 'msg' => '该支付宝账号已被使用，请更换其他支付宝账号']);
                    return;
                }
            } else {
                // 用户存在，检查支付宝账号是否被其他人使用（排除自己）
                $existingPayment = $this->db->query(
                    "SELECT id FROM app_ex_payment_account WHERE payment_type = 206 AND payment_account = ? AND uid != ?",
                    [$alipayAccount, $memberInfo['id']]
                )->row_array();
                
                if ($existingPayment) {
                    header('Content-Type: application/json; charset=utf-8');
                    echo json_encode(['code' => 400, 'msg' => '该支付宝账号已被其他用户使用，请更换其他支付宝账号']);
                    return;
                }
            }
            
            // 5. 验证不能推荐自己
            if ($tjrMemberUid > 0) {
                $recommenderInfo = $this->db->query("SELECT mobile FROM app_member WHERE id = ?", [$tjrMemberUid])->row_array();
                if ($recommenderInfo && $recommenderInfo['mobile'] === $currentUserMobile) {
                    header('Content-Type: application/json; charset=utf-8');
                    echo json_encode(['code' => 400, 'msg' => '不能推荐自己参与项目']);
                    return;
                }
            }
            
            
            // 检查是否已有记录（用于判断是新增还是更新）
            $existingRecord = $this->db->query("SELECT id, audit_status, invite_uuid FROM app_member_invite WHERE btjr_mobile = ?", [$currentUserMobile])->row_array();
            
            // 准备插入数据，映射到app_member_invite表结构
            $insertData = [
                // 基础信息
                'invite_uuid' => $inviteUuid,
                'tj_pid' => $tjPid, // 首次推荐项目id
                'btj_pid' => $btjPid, // 首次被推荐项目id
                'tjr_member_uid' => $tjrMemberUid, // 首次推荐人唯一编号
                'btjr_member_uid' => 0, // 被推荐人用户唯一编号，暂时为0（可能还没注册）
                
                // 被推荐人基本信息（添加长度控制）
                'btjr_mobile' => $currentUserMobile,
                'btjr_name' => substr($postData['name'], 0, 50), // 限制长度为50
                'btjr_gender' => 0, // 性别暂时为0，表单中没有收集
                
                // 地区信息（确保为整型）
                'btjr_province' => (int)$postData['province_id'],
                'btjr_city' => (int)$postData['city_id'],
                'btjr_district' => (int)$postData['district_id'],
                
                // 工作信息（确保为整型和长度控制）
                'btjr_unit_id' => (int)$postData['hospital_id'],
                'btjr_department_id' => (int)$postData['department_id'],
                'btjr_license_no' => substr($postData['license_no'] ?? '', 0, 50), // 限制长度为50
                
                // 支付信息（添加长度控制）
                'btjr_payment_name' => substr($postData['pay_name'] ?? '', 0, 50), // 限制长度为50
                'btjr_payment_account' => substr($postData['pay_count'] ?? '', 0, 50), // 限制长度为50
                
                // 职业信息（确保为整型）
                'btjr_practice_sort' => (int)$postData['practice_sort_id'],
                'btjr_job_title' => (int)$postData['job_title_id'],
                
                // 审核信息
                'audit_uid' => 0,
                'audit_status' => 1, // 1、待审核
                'audit_time' => 0,
                'reject_reason' => '',
                
                // 时间信息
                'created_at' => time()
            ];

            // 开始事务
            $this->db->trans_start();
            
            // 获取推荐人的支付宝账号信息
            $recommenderPaymentInfo = $this->db->query("
                SELECT payment_name, payment_account 
                FROM app_ex_payment_account 
                WHERE uid = ? AND payment_type = 206 AND status = 1 
                LIMIT 1
            ", [$tjrMemberUid])->row_array();
            
            $tjrPaymentName = $recommenderPaymentInfo['payment_name'] ?? '';
            $tjrPaymentAccount = $recommenderPaymentInfo['payment_account'] ?? '';
            
            // 计算推荐人奖励金额
            $tjrReward = $this->calculateRecommenderReward($tjrMemberUid, $tjPid);
            
            // 获取被推荐项目的配额ID
            $btjQuotaId = $this->getProjectQuotaId($btjPid, $postData);
            error_log("获取配额ID结果: 被推荐项目ID={$btjPid}, 配额ID={$btjQuotaId}");
            
            if ($existingRecord) {
                // 更新现有记录
                unset($insertData['invite_uuid']); // 移除UUID，不更新
                unset($insertData['created_at']); // 移除创建时间，不更新
                $insertData['audit_status'] = 1; // 重新设为待审核
                $insertData['audit_time'] = 0; // 重置审核时间
                $insertData['reject_reason'] = ''; // 清空驳回原因
                
                $this->db->where('btjr_mobile', $currentUserMobile);
                $this->db->update('app_member_invite', $insertData);
                
                if ($this->db->affected_rows() >= 0) { // 更新时affected_rows可能为0
                    error_log("成功更新推荐信息，手机号: {$currentUserMobile}");
                    $operationType = '更新';
                    $finalInviteUuid = $existingRecord['invite_uuid']; // 使用已存在的UUID
                } else {
                    throw new Exception('更新数据失败');
                }
            } else {
                // 插入新记录
                error_log("准备插入推荐信息，手机号: {$currentUserMobile}");
                
                $this->db->insert('app_member_invite', $insertData);
                
                if ($this->db->affected_rows() > 0) {
                    $insertId = $this->db->insert_id();
                    error_log("成功插入推荐信息，ID: {$insertId}, UUID: {$inviteUuid}");
                    $operationType = '提交';
                    $finalInviteUuid = $inviteUuid; // 使用新生成的UUID
                } else {
                    // 记录SQL错误
                    $dbError = $this->db->error();
                    error_log("插入数据失败: " . json_encode($dbError));
                    error_log("SQL查询: " . $this->db->last_query());
                    throw new Exception('插入数据失败，请检查数据完整性');
                }
            }

            // 插入或更新 app_member_invite_project 表
            $projectInviteData = [
                'tjr_member_uid' => $tjrMemberUid,
                'tjr_payment_name' => $tjrPaymentName,
                'tjr_payment_account' => $tjrPaymentAccount,
                'invite_uuid' => $finalInviteUuid,
                'tj_pid' => $tjPid,
                'btj_pid' => $btjPid,
                'btj_quota_id' => $btjQuotaId, // 配额ID
                'tjr_reward' => $tjrReward, // 推荐人奖励金额
                'btjr_reward' => 0, // 暂时不处理
                'audit_uid' => 0, // 暂时不处理
                'audit_status' => 1, // 待审核
                'audit_time' => 0, // 暂时不处理
                'created_at' => time()
            ];

            // 检查是否已存在记录
            $existingProjectRecord = $this->db->query("
                SELECT id FROM app_member_invite_project 
                WHERE invite_uuid = ? AND btj_pid = ?
            ", [$finalInviteUuid, $btjPid])->row_array();

            if ($existingProjectRecord) {
                // 更新现有记录
                unset($projectInviteData['created_at']); // 移除创建时间，不更新
                
                $this->db->where('invite_uuid', $finalInviteUuid);
                $this->db->where('btj_pid', $btjPid);
                $this->db->update('app_member_invite_project', $projectInviteData);
                
                if ($this->db->affected_rows() >= 0) {
                    error_log("成功更新项目推荐记录，UUID: {$finalInviteUuid}, 被推荐项目ID: {$btjPid}");
                } else {
                    throw new Exception('更新项目推荐记录失败');
                }
            } else {
                // 插入新记录
                $this->db->insert('app_member_invite_project', $projectInviteData);
                
                if ($this->db->affected_rows() > 0) {
                    $projectInsertId = $this->db->insert_id();
                    error_log("成功插入项目推荐记录，ID: {$projectInsertId}, UUID: {$finalInviteUuid}");
                } else {
                    throw new Exception('插入项目推荐记录失败');
                }
            }

            // 提交事务
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                throw new Exception('事务执行失败');
            }

            header('Content-Type: application/json; charset=utf-8');
            echo json_encode([
                'code' => 200, 
                'msg' => $operationType . '成功',
                'data' => [
                    'redirect_url' => '/project/recommend/subSuccess/' . $token
                ]
            ]);

        } catch (Exception $e) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['code' => 500, 'msg' => '提交失败，请稍后重试']);
        }
    }

    /**
     * 提交成功页面
     */
    public function subSuccess()
    {
        $token = $this->uri->segment(4);
        $params = decode_param($token);
        if (!$params) {
            return $this->error_page();
        }
        
        // 检查登录状态
        error_log("subSuccess: 开始检查登录状态 - Token: " . substr($token, 0, 8) . "...");
        $mobile = $this->checkLoginOrRedirect($token);
        if (!$mobile) {
            error_log("subSuccess: 登录检查失败，跳转到登录页");
            return;
        }
        error_log("subSuccess: 登录检查通过 - Mobile: {$mobile}");

        // 重新判断当前用户应该跳转到哪个页面
        $correctUrl = $this->determineRedirectUrl($mobile, $token);
        $currentUrl = "/project/recommend/subSuccess/" . $token;
        
        // 如果当前页面不是用户应该访问的页面，则重定向
        if ($correctUrl !== $currentUrl) {
            $this->redirectWithCurrentProtocol($correctUrl);
            return;
        }

        $data = [
            'token' => $token,
        ];

        $this->load->view('/project/recommend/sub-success', $data);
    }

    /**
     * 驳回提示页面
     */
    public function subError()
    {
        $token = $this->uri->segment(4);
        $params = decode_param($token);
        if (!$params) {
            return $this->error_page();
        }
        
        // 检查登录状态
        $mobile = $this->checkLoginOrRedirect($token);
        if (!$mobile) {
            return;
        }

        // 重新判断当前用户应该跳转到哪个页面
        $correctUrl = $this->determineRedirectUrl($mobile, $token);
        $currentUrl = "/project/recommend/subError/" . $token;
        
        // 如果当前页面不是用户应该访问的页面，则重定向
        if ($correctUrl !== $currentUrl) {
            $this->redirectWithCurrentProtocol($correctUrl);
            return;
        }

        // 如果确实应该显示错误页面，获取驳回信息
        $inviteInfo = $this->db->query("SELECT reject_reason FROM app_member_invite WHERE btjr_mobile = ? AND audit_status = 3", [$mobile])->row_array();
        $rejectInfo = $inviteInfo ?: [];

        $data = [
            'token' => $token,
            'reject_reason' => $rejectInfo['reject_reason'] ?? '信息审核未通过，请修改后重新提交'
        ];

        $this->load->view('/project/recommend/sub-error', $data);
    }


    /**
     * 获取被推荐项目的配额ID
     * 
     * @param int $btjPid 被推荐项目ID
     * @param array $userInfo 用户填写的信息
     * @return int 配额ID，未匹配到返回0
     */
    private function getProjectQuotaId($btjPid, $userInfo)
    {
        try {
            // 1. 查询项目的配额设置
            $projectInfo = $this->db->query("
                SELECT quta_setting 
                FROM app_project 
                WHERE id = ? 
                LIMIT 1
            ", [$btjPid])->row_array();
            
            if (!$projectInfo || empty($projectInfo['quta_setting'])) {
                error_log("项目配额设置为空或不存在: 项目ID={$btjPid}");
                return 0;
            }
            
            // 2. 解析JSON格式的配额字段
            $quotaFields = json_decode($projectInfo['quta_setting'], true);
            if (!is_array($quotaFields) || empty($quotaFields)) {
                error_log("项目配额设置解析失败: 项目ID={$btjPid}, 配额设置={$projectInfo['quta_setting']}");
                return 0;
            }
            
            error_log("项目配额字段: 项目ID={$btjPid}, 配额字段=" . json_encode($quotaFields));
            
            // 3. 如果配额字段只有country一个，直接返回匹配的记录
            if (count($quotaFields) == 1 && in_array('country', $quotaFields)) {
                $quotaRecord = $this->db->query("
                    SELECT id 
                    FROM app_project_quta 
                    WHERE pid = ? 
                    LIMIT 1
                ", [$btjPid])->row_array();
                
                if ($quotaRecord) {
                    error_log("单一country配额匹配成功: 项目ID={$btjPid}, 配额ID={$quotaRecord['id']}");
                    return (int)$quotaRecord['id'];
                }
                return 0;
            }
            
            // 4. 批量获取用户相关数据，减少数据库查询
            $userFieldValues = $this->batchGetUserFieldValues($userInfo, $quotaFields);
            if (!$userFieldValues) {
                error_log("批量获取用户字段值失败: 项目ID={$btjPid}");
                return 0;
            }
            
            // 5. 构建查询条件，基于被推荐项目ID
            $whereConditions = ["pid = ?"];
            $queryParams = [$btjPid];
            
            foreach ($quotaFields as $field) {
                if ($field === 'department') {
                    // department字段最后处理，使用模糊匹配
                    continue;
                }
                
                if (isset($userFieldValues[$field]) && $userFieldValues[$field] !== null) {
                    $whereConditions[] = "{$field} = ?";
                    $queryParams[] = $userFieldValues[$field];
                    error_log("添加配额条件: {$field} = {$userFieldValues[$field]}");
                } else {
                    error_log("无法获取字段值: {$field}");
                }
            }
            
            // 释放内存（可选优化）
            unset($userFieldValues);
            
            // 6. 执行初步查询（不包含department）
            $whereClause = implode(' AND ', $whereConditions);
            $quotaRecords = $this->db->query("
                SELECT id, department 
                FROM app_project_quta 
                WHERE {$whereClause}
            ", $queryParams)->result_array();
            
            if (empty($quotaRecords)) {
                error_log("未找到匹配的配额记录: 项目ID={$btjPid}");
                return 0;
            }
            
            error_log("初步筛选结果: " . count($quotaRecords) . " 条记录");
            
            // 7. 如果配额字段包含department，进行精确匹配和关键字匹配
            if (in_array('department', $quotaFields)) {
                $userDepartment = $this->getUserDepartmentName($userInfo['department_id']);
                if (!$userDepartment) {
                    error_log("无法获取用户科室名称: department_id={$userInfo['department_id']}");
                    return 0;
                }
                
                error_log("用户科室: {$userDepartment}");
                
                // 第一步：优先精确匹配
                foreach ($quotaRecords as $record) {
                    $quotaDepartment = trim($record['department']);
                    if (!empty($quotaDepartment) && $userDepartment === $quotaDepartment) {
                        error_log("科室精确匹配成功: 用户科室={$userDepartment}, 配额科室={$quotaDepartment}, 配额ID={$record['id']}");
                        return (int)$record['id'];
                    }
                }
                
                // 第二步：关键字匹配
                $keywordMatches = [];
                foreach ($quotaRecords as $record) {
                    $quotaDepartment = trim($record['department']);
                    if (!empty($quotaDepartment)) {
                        // 检查用户科室是否包含配额设置的科室关键字
                        if (strpos($userDepartment, $quotaDepartment) !== false) {
                            $keywordMatches[] = $record;
                            error_log("科室关键字匹配: 用户科室={$userDepartment}, 配额科室关键字={$quotaDepartment}, 配额ID={$record['id']}");
                        }
                    }
                }
                
                // 判断关键字匹配结果
                if (count($keywordMatches) === 1) {
                    // 只匹配到一个，自动选择
                    $matchedRecord = $keywordMatches[0];
                    error_log("科室关键字唯一匹配成功: 用户科室={$userDepartment}, 配额科室={$matchedRecord['department']}, 配额ID={$matchedRecord['id']}");
                    return (int)$matchedRecord['id'];
                } elseif (count($keywordMatches) > 1) {
                    // 匹配到多个，走人工匹配流程
                    error_log("科室关键字匹配到多个配额，需要人工匹配: 用户科室={$userDepartment}, 匹配数量=" . count($keywordMatches));
                    return 0;
                } else {
                    // 没有匹配到任何配额
                    error_log("科室匹配失败: 用户科室={$userDepartment}");
                    return 0;
                }
            }
            
            // 8. 如果不需要匹配department，返回第一个匹配的记录
            $quotaId = (int)$quotaRecords[0]['id'];
            error_log("配额匹配成功: 项目ID={$btjPid}, 配额ID={$quotaId}");
            return $quotaId;
            
        } catch (Exception $e) {
            error_log("获取配额ID异常: " . $e->getMessage() . ", 项目ID={$btjPid}");
            return 0;
        }
    }
    
    /**
     * 批量获取用户字段值，减少数据库查询次数
     * 
     * @param array $userInfo 用户信息
     * @param array $quotaFields 配额字段列表
     * @return array|null 字段值映射数组
     */
    private function batchGetUserFieldValues($userInfo, $quotaFields)
    {
        try {
            $fieldValues = [];
            
            // 收集需要查询的地区ID
            $regionIds = [];
            $needRegions = array_intersect($quotaFields, ['province', 'city', 'district']);
            
            foreach ($needRegions as $field) {
                $idKey = $field . '_id';
                if (isset($userInfo[$idKey]) && $userInfo[$idKey] > 0) {
                    $regionIds[] = (int)$userInfo[$idKey];
                }
            }
            
            // 批量查询地区信息
            $regionNames = [];
            if (!empty($regionIds)) {
                $regionIdList = implode(',', $regionIds);
                $regions = $this->db->query("
                    SELECT id, val 
                    FROM app_sys_dictionary 
                    WHERE id IN ({$regionIdList}) AND big_class_id = 9 AND status = 1
                ")->result_array();
                
                foreach ($regions as $region) {
                    $regionNames[$region['id']] = $region['val'];
                }
            }
            
            // 查询医院信息（如果需要）
            $hospitalName = null;
            if (in_array('unit_name', $quotaFields) && isset($userInfo['hospital_id']) && $userInfo['hospital_id'] > 0) {
                $hospital = $this->db->query("
                    SELECT name 
                    FROM app_unit 
                    WHERE id = ? AND status = 3 AND is_del = 1 
                    LIMIT 1
                ", [$userInfo['hospital_id']])->row_array();
                
                $hospitalName = $hospital ? $hospital['name'] : null;
            }
            
            // 组装字段值
            foreach ($quotaFields as $field) {
                switch ($field) {
                    case 'country':
                        $fieldValues[$field] = '中国';
                        break;
                    case 'province':
                        $id = (int)$userInfo['province_id'];
                        $fieldValues[$field] = $regionNames[$id] ?? null;
                        break;
                    case 'city':
                        $id = (int)$userInfo['city_id'];
                        $fieldValues[$field] = $regionNames[$id] ?? null;
                        break;
                    case 'district':
                        $id = (int)$userInfo['district_id'];
                        $fieldValues[$field] = $regionNames[$id] ?? null;
                        break;
                    case 'unit_name':
                        $fieldValues[$field] = $hospitalName;
                        break;
                    case 'department':
                        // department字段不在这里处理
                        break;
                    default:
                        error_log("未知的配额字段: {$field}");
                        break;
                }
            }
            
            return $fieldValues;
            
        } catch (Exception $e) {
            error_log("批量获取用户字段值异常: " . $e->getMessage());
            return null;
        } finally {
            // 确保临时变量被释放
            unset($regionNames, $hospitalName, $regions, $hospital);
        }
    }
    
    /**
     * 根据字段名获取用户对应的值
     * 
     * @param string $field 字段名
     * @param array $userInfo 用户信息
     * @return mixed 字段值或null
     */
    private function getUserFieldValue($field, $userInfo)
    {
        switch ($field) {
            case 'country':
                return '中国'; // 固定为中国
            case 'province':
                return $this->getRegionName($userInfo['province_id']);
            case 'city':
                return $this->getRegionName($userInfo['city_id']);
            case 'district':
                return $this->getRegionName($userInfo['district_id']);
            case 'unit_name':
                return $this->getHospitalName($userInfo['hospital_id']);
            default:
                error_log("未知的配额字段: {$field}");
                return null;
        }
    }
    
    /**
     * 根据地区ID获取地区中文名称
     * 
     * @param int $regionId 地区ID
     * @return string|null 地区中文名称
     */
    private function getRegionName($regionId)
    {
        try {
            $region = $this->db->query("
                SELECT val 
                FROM app_sys_dictionary 
                WHERE id = ? AND big_class_id = 9 AND status = 1 
                LIMIT 1
            ", [$regionId])->row_array();
            
            return $region ? $region['val'] : null;
        } catch (Exception $e) {
            error_log("获取地区名称异常: " . $e->getMessage() . ", 地区ID={$regionId}");
            return null;
        }
    }
    
    /**
     * 根据医院ID获取医院名称
     * 
     * @param int $hospitalId 医院ID
     * @return string|null 医院名称
     */
    private function getHospitalName($hospitalId)
    {
        try {
            $hospital = $this->db->query("
                SELECT name 
                FROM app_unit 
                WHERE id = ? AND status = 3 AND is_del = 1 
                LIMIT 1
            ", [$hospitalId])->row_array();
            
            return $hospital ? $hospital['name'] : null;
        } catch (Exception $e) {
            error_log("获取医院名称异常: " . $e->getMessage() . ", 医院ID={$hospitalId}");
            return null;
        }
    }
    
    /**
     * 根据科室ID获取科室名称
     * 
     * @param int $departmentId 科室ID
     * @return string|null 科室名称
     */
    private function getUserDepartmentName($departmentId)
    {
        try {
            $department = $this->db->query("
                SELECT department as name 
                FROM info_department 
                WHERE id = ? AND is_standard = 1 
                LIMIT 1
            ", [$departmentId])->row_array();
            
            return $department ? $department['name'] : null;
        } catch (Exception $e) {
            error_log("获取科室名称异常: " . $e->getMessage() . ", 科室ID={$departmentId}");
            return null;
        }
    }

    /**
     * 计算推荐人奖励金额
     * 基于原代码逻辑，针对单个项目优化：配置【医师UID】->通用奖励配置->邀请管理奖励->【分配对接（外包）】奖励
     * 
     * @param int $tjrMemberUid 推荐人用户ID
     * @param int $tjPid 推荐项目ID
     * @return int 奖励金额
     */
    private function calculateRecommenderReward($tjrMemberUid, $tjPid)
    {
        $money = 0;
        $moneyDisplay = 0;
        
        try {
            // 批量查询所有相关数据
            // 1. 查询专属医师礼金设置
            $specificSettings = [];
            $specificQuery = $this->db->query("
                SELECT pid, price
                FROM app_project_setting_sample
                WHERE pid = ? AND member_uid = ? AND is_delete = 0
            ", [$tjPid, $tjrMemberUid])->result_array();
            
            foreach ($specificQuery as $row) {
                $specificSettings[$row['pid']] = $row['price'];
            }
        
            // 2. 查询邀请管理奖励
            $patientRewards = [];
            $patientQuery = $this->db->query("
                SELECT pid, patient_reward
                FROM app_patient_fwy_add
                WHERE pid = ? AND btjr_member_id = ?
            ", [$tjPid, $tjrMemberUid])->result_array();
            
            foreach ($patientQuery as $row) {
                $patientRewards[$row['pid']] = $row['patient_reward'];
            }
            
            // 3. 查询分配对接奖励（需要先查询推荐人是否在项目执行表中）
            $projectPartnerMap = [];
            $partnerRewards = [];
            
            $implementTable = 'app_project_implement_' . $tjPid;
            
            // 先检查项目执行表是否存在
            $tableExists = $this->db->query("SHOW TABLES LIKE '{$implementTable}'")->row_array();
            
            if ($tableExists) {
                $implementInfo = $this->db->query("
                    SELECT partner_id, groupno
                    FROM {$implementTable}
                    WHERE member_uid = ?
                    LIMIT 1
                ", [$tjrMemberUid])->row_array();
                
                if (!empty($implementInfo)) {
                    $projectPartnerMap[$tjPid] = $implementInfo;
                    
                    $partnerQuery = $this->db->query("
                        SELECT project_id, partner_id, groupno, price_c
                        FROM app_project_partner
                        WHERE project_id = ? AND partner_id = ? AND groupno = ?
                    ", [$tjPid, $implementInfo['partner_id'], $implementInfo['groupno']])->result_array();
                    
                    foreach ($partnerQuery as $row) {
                        $partnerKey = $row['project_id'] . '_' . $row['partner_id'] . '_' . $row['groupno'];
                        $partnerRewards[$partnerKey] = $row['price_c'];
                    }
                }
            } else {
                error_log("项目执行表不存在: {$implementTable}，跳过分配对接奖励查询");
            }
            
            // 按优先级设置礼金（完全遵循原Project.php逻辑）
            $pid = $tjPid;
            
            if (isset($specificSettings[$pid])) {
                // 第一优先级：专属医师礼金
                $money = $specificSettings[$pid];
            } elseif (isset($patientRewards[$pid])) {
                // 第二优先级：邀请管理奖励
                $money = $patientRewards[$pid];
            } else {
                // 第三优先级：分配对接奖励
                $partner = $projectPartnerMap[$pid] ?? [];
                if (!empty($partner)) {
                    $partnerKey = $pid . '_' . $partner['partner_id'] . '_' . $partner['groupno'];
                    if (isset($partnerRewards[$partnerKey])) {
                        $money = $partnerRewards[$partnerKey];
                    }
                }
            }
            
            error_log("推荐人奖励计算 - 项目ID: {$tjPid}, 推荐人ID: {$tjrMemberUid}, 奖励显示: {$moneyDisplay}, 实际金额: {$money}");
            
        } catch (Exception $e) {
            error_log("推荐人奖励计算异常: " . $e->getMessage() . ", 推荐人ID: {$tjrMemberUid}, 项目ID: {$tjPid}");
        }
        
        // 返回整数金额用于数据库存储
        return (int)$money;
    }
}