<?php
/**
 * Created by PhpStorm.
 * User: AMY
 * Date: 2019-12-06
 * Time: 13:39
 */
use EasyWeChat\Factory;
class Test extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        error_reporting(-1);
        ini_set('display_errors', 1);

//        $config = [
////            'app_id' => 'wx33d03b0f9b327925',
////            'secret' => '50e4d154ed30ed59d8fdd00c58c2026f',
//            'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
//            'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
//            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
//            'response_type' => 'array',
//        ];
//        //微信小程序
//        $this->app = Factory::miniProgram($config);
//        $this->load->file(dirname(dirname(dirname(__FILE__))) . '/libraries/253_drsay_invite/Chuanglan_api.php'); //创蓝

        //禁止页面缓存
        header("Cache-control:no-cache,no-store,must-revalidate");
        header("Pragma:no-cache");
        header("Expires:0");
    }

    function sms_test_by_baidu()
    {
        $data = $_SERVER;
        file_put_contents("./tmp/sms_test_by_baidu.txt", json_encode($data, JSON_UNESCAPED_UNICODE)."***".date("Y-m-d H:i:s").PHP_EOL.PHP_EOL, FILE_APPEND | LOCK_EX);
        echo "点击完成！";
    }

    function test_lock()
    {
        if(Redis_lock::getInstance()->lock("amy_test_10", 20) !== true) {
            //错误提示
            echo "加锁失败";
        }
        echo "done";
        sleep(10);
    }

    function test_lock1()
    {
        $time = time();
        if(Redis_lock::getInstance()->lock("amy_test_10", 20) !== true) {
            //错误提示
            echo "加锁失败";
        } else {
            echo "加锁成功";
        }
        echo time() - $time;

    }

    function one_html()
    {
//        if ($this->session->userdata("amy_test_jump")) {
//            echo "跳走";die;
//        }
        echo "<a href='/test/two_html'>提交</a>";
    }

    function two_html()
    {
        $this->session->set_userdata(["amy_test_jump" => "1"]);
        //禁止返回
        echo '<script type="text/javascript">history.go(1);</script>';
        header("location:/test/three_html");
    }

    function three_html()
    {
        //禁止返回
        echo '<script type="text/javascript">history.go(1);</script>';
        echo "第三个页面";
    }

    function test_pro_link()
    {
        $project_sys_id = (int)trim($this->input->get("s_id"));
        if (!in_array($project_sys_id, [7,8,9])) {
            echo "error";die;
        }
        $this->session->set_userdata(["project_sys_id" => $project_sys_id]);
        echo "<a href='/bk/rs?code=sDRSAYDRSAYDRSAY7b0875d36d4d8dec'>点击返回</a>";
    }

    //短信测试
    function test_yzm()
    {
        echo substr(md5("order_121854" . PROJECT_ENCODE_KEY), 8, 6);
        die;
        $res = chuanglan_single_sms(SMS_LOG_CODE_COMM_MOB, 17301849323, '申请支付验证码12345，有效时间是5分钟。');
        var_dump($res);
//        $this->load->library("/Third_party/253_drsay_invite/Chuanglan_api");//文件路径，/system/libraries/Third_party
//        $msg = '申请支付验证码12345，有效时间是5分钟。';
//
//        //单个发送
//        $res = $this->chuanglan_api->send($msg, 17301849323);
//
////        //批量发送
////        $sms_info = [
////            [
////                'mobile' => '17301849323',
////                'vcode' => '12345',
////                'vtime' => '5',
////            ]
////        ];
////        $res = $this->chuanglan_api::send_batch($msg, $sms_info);
//
//        $other_param =array(
//            'back_result' => json_encode($res,JSON_UNESCAPED_UNICODE),
//        );
//        // 加日志
//        sms_log(SMS_LOG_CODE_COMM_MOB, 0, 17301849323, $msg, $other_param);
//        echo "<pre />";
//        print_r($res);
    }

    function test_gooddr_code()
    {
//        $uid = "4448626";
//        $pid = "1462";

        $uid = "2982098";
        $pid = "1675";
        echo $this->get_gooddr_log_info($uid, $pid);
    }

    //准备跳转到健康通官网的数据
    private function get_gooddr_log_info($uid, $pid)
    {
        $code = "";
        if ($uid && $pid) {
            $code = $uid."_".$pid."_".substr(md5($uid. "_" . $pid . "_1002_cW5jK7j7!YS3"), 8, 16);
        }
        return $code;
    }

    function test_hz(){
//        "^[\\u4E00-\\u9FA5][\\u4E00-\\u9FA5|·]*[\\u4E00-\\u9FA5]
        $name = $this->input->get("name");
        if (preg_match("/^[\\u4E00-\\u9FA5][\\u4E00-\\u9FA5|·]*[\\u4E00-\\u9FA5]$/", $name)) {
            echo "正确";
        } else {
            echo "错误";
        }
    }

    //链接加密
    function encode_link()
    {

        $this->load->model("survey_model");
        $url = "/bk/rs";
//        $param = "c";
//        $uid = "2982098";
//        $pid = "1030";

//        $this->session->set_userdata(["project_sys_id" => 7]);
        $st = $this->input->get("st");
        $uid = $this->input->get("uid");
        $pid = $this->input->get("pid");
//        $url_net = "https://www.drsay.cn";
//        $url_net = "http://local.drsay.cn";
        $url_net = '';
//        $other_param = '&act=test';
        $other_param = '';
        if (!$st) {
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'c', $uid, $pid, '', false).$other_param."'>c</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'q', $uid, $pid, '', false).$other_param."'>q</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 's', $uid, $pid, '', false).$other_param."'>s</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'p', $uid, $pid, '', false).$other_param."'>p</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'npros', $uid, $pid, '', false).$other_param."'>npros</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'nquta', $uid, $pid, '', false).$other_param."'>nquta</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'qutasuc', $uid, $pid, '', false).$other_param."'>qutasuc</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'l_err', $uid, $pid, '', false).$other_param."'>l_err</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'ne', $uid, $pid, '', false).$other_param."'>ne</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'finish', $uid, $pid, '', false).$other_param."'>finish</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'suspend', $uid, $pid, '', false).$other_param."'>suspend</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'bl', $uid, $pid, '', false).$other_param."'>bl</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'repeat', $uid, $pid, '', false).$other_param."'>repeat</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'cash_suc', $uid, $pid, '', false).$other_param."'>cash_suc</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'fail_order_suc', $uid, $pid, '', false).$other_param."'>fail_order_suc</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 's_repeat', $uid, $pid, '', false).$other_param."'>s_repeat</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'p_s_a', $uid, $pid, '', false).$other_param."'>p_s_a</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'p_s', $uid, $pid, '', false).$other_param."'>p_s</a><br />";
            echo "<a target='_blank' href='".$url_net.$this->get_redirect_info($url, 'qw_project_list_fail', $uid, $pid, '', false).$other_param."'>qw_project_list_fail</a><br />";
        } else {
            //        $other_param = "gooddr_code=amytest";
            $other_param = "";
            echo $url_net.$this->get_redirect_info($url, $st, $uid, $pid, $other_param, false);
        }
    }

    //问卷完成，对跳转到公共提示页前，链接加密操作处理
    public function get_redirect_info($url, $param, $uid = "", $pid = "", $other_param = "", $is_redirect = true)
    {
        $str = $param. "DRSAY" . $uid. "DRSAY" . $pid;
        $encode_str = substr(md5($str. "DRSAY" .PROJECT_ENCODE_KEY), 8, 16);
        $code = $str."DRSAY".$encode_str;
        $other_param = $other_param ? "&{$other_param}" : "";
        //本地跳转才需要加密code，否则直接跳转
        $pos = strpos($url, "/bk/rs");
        $is_param = $this->redirect_is_param($url);
        if ($is_param) {//存在问号
            $linker = "&";
        } else {//不存在问号
            $linker = "?";
        }
        if ($pos === false) {//不是上医说返回链接]
            $redirect_url = $url."{$linker}st={$param}{$other_param}";
//            redirect($url."?st={$param}&{$other_param}");
        } else {
            $redirect_url = $url."{$linker}code={$code}{$other_param}";
//            redirect($url."?code={$code}{$other_param}");
        }
        if ($is_redirect) {
            redirect($redirect_url);
        } else {
            return $redirect_url;
        }
    }

    //重定向链接是否已经存在参数
    public function redirect_is_param($back_url){
        if ($back_url) {
            if (strpos($back_url, "?") !== false) {//存在问号
                return true;
            } else {//不存在问号
                return false;
            }
        } else {
            return false;
        }
    }

    function to_gooddr_url(){
        $this->load->model("survey_model");
        $this->survey_model->get_gooddr_log_info("/go/thanks?st=c", "40015", "1036");
    }

    function get_server_time()
    {
        echo date("Y-m-d H:i:s");
    }

    function test_info()
    {
//        die;
//        //项目响应时间统计
//        $pro_lct = $this->db->query("SELECT * FROM app_project_response_lct")->result_array();
//        foreach ($pro_lct as $v) {
//            $info = $this->db->query("SELECT count(*) as num FROM app_project_implement_{$v['project_id']} WHERE click_time > 0")->row_array();
//            $this->db->query("UPDATE app_project_response_lct SET response_num='{$info['num']}' WHERE id='{$v['id']}' LIMIT 1");
//        }
//        echo "finish!";
        die;
        $app_id = "1001";
        $app_key = "nX!Z!NX9wxj9";
        $params = [
            "app_id" => $app_id,
            "from_where" => "2",
            "mobile" => "17301849323",
            "openid" => "1",
            "unionid" => "2"
        ];
        ksort($params);
        $sign_str = "";
        foreach ($params as $k => $v){
            if ($v){
                $sign_str .= "{$k}={$v}&";
            }
        }
        if ($sign_str){
            //最后拼接app_key
            $sign_str .= "app_key={$app_key}";
        }
        $sign = md5($sign_str);
        echo $sign;
        die;
        $this->check_signnature($post_data);
        die;
        $mobile = "17301849323";
        $verify_time = "123";
        $validation_info = "123";
        echo md5($mobile."doctor_log_drsay".$verify_time);
        die;
        $lct_now_time = time();
//        $partner_uid = "uid=867_2982098";
//        $pid = 867;
//        $get_imp_info = $this->db->query("SELECT id,click_time FROM app_project_implement_{$pid} WHERE partner_uid='{$partner_uid}' LIMIT 1")->row_array();
//        if ($get_imp_info['click_time'] != 0) {
//            $get_click_num = $this->db->query("SELECT * FROM app_project_response_lct WHERE project_id='{$pid}' LIMIT 1")->row_array();
//            if (!$get_click_num) {//不存在
//                $this->db->query("INSERT INTO app_project_response_lct(`project_id`,`response_num`)VALUES('{$pid}',1)");
//            } else {
//                $this->db->query("UPDATE app_project_response_lct SET response_num=`response_num`+1 WHERE project_id='{$pid}' LIMIT 1");
//            }
//        }




        $st = 'c';
        $pid = 867;
        if ($st == 'c') {//完成状态
            $get_click_num = $this->db->query("SELECT * FROM app_project_response_lct WHERE project_id='{$pid}' LIMIT 1")->row_array();
            if (!$get_click_num) {//不存在
                $this->db->query("INSERT INTO app_project_response_lct(`project_id`,`lct`,`response_num`,`c_num`,update_t)VALUES('{$pid}','{$lct_now_time}',1,1,'{$lct_now_time}')");
            } else {
                $this->db->query("UPDATE app_project_response_lct SET lct='".time()."',`c_num`=`c_num`+1,update_t='{$lct_now_time}' WHERE project_id='{$pid}' LIMIT 1");
            }
        }
        echo "finish!";
    }


    /**
     * 验证签名
     *
     * @access public
     * @param mixed
     * @return
     */
    protected function check_signnature($post_data)
    {
        $app_id = intval($this->input->post("app_id", true));
        $sign = $this->input->post("sign", true);//签名
        if ($app_id < 1) {
            _back_msg("error", "app_id不能为空");
        }
        if (empty($sign)) {
            _back_msg("error", "签名不能为空");
        }
        //查询app_id对应客户信息
        $app_info = $this->db->select("*")->from("api_check_doctor_account")
            ->where(["app_id" => $app_id])->limit(1)->get()->row_array();
        if (empty($app_info)) {
            _back_msg("error", "app_id不存在");
        }
        //判断接口是否启用
        if (intval($app_info["status"]) != 1) {
            _back_msg("error","接口已停用");
        }
        //判断开放接口截止时间
        if ($app_info["open_time"]){
            if (time() > intval($app_info["open_time"])) {
                _back_msg("error", "开放接口日期已截止");
            }
        }
        //判断ip白名单
        $white_ip_arr = $app_info["white_list"];//ip白名单
        if ($white_ip_arr){
            $ip = getip();
            if (!in_array($ip,explode(",",$white_ip_arr))){
                _back_msg("error","ip禁止访问");
            }
        }
        //获取app_key
        $app_key = $app_info["app_key"];
        $post = $_POST;
        unset($post["sign"]);//去除签名
        //按key排序
        ksort($post);
        $sign_str = "";
        foreach ($post as $k => $v) {
            $value = $this->input->post($k, true);
            if (empty($value)) {
                continue;
            }
            $sign_str .= $k . "=" . $value . "&";
        }
        //拼接app_key
        $sign_str .= "app_key={$app_key}";
        if ($sign != md5($sign_str)) {
            _back_msg("error","签名失败");
        }
//        //更新api访问次数
//        $this->db->set("api_num","api_num + 1", false)->where(["id" => $app_info["id"]])->update("api_check_doctor_account");
    }

    function index()
    {
        echo '<div>sdfsdfdsfdsf</div><div><a href="https://www.baidu.com">点击跳转到百度</a></div>';
    }

    //微信小程序打款成功回调地址
    function payment_callback()
    {
        die;
        $data = file_get_contents("php://input");
        file_put_contents("./tmp/payment_callback.txt", $data);
    }

    //微信小程序提现
    function wx_exchange_sub()
    {
        die;
//        $scene = '724_2982098_9026dc';
//        $partner_trade_no = 'DS72429820989026dc';
//        $app = $this->get_payment_jkt_sh_factory();
//        $params = [
//            'partner_trade_no' => $partner_trade_no, //订单号
////            'openid'           => 'oj3fs4hSdwzArwecvpYEVSW_WVvI', //amy 医map小程序的openid
//            'openid'           => 'o_lRt5QOuqroml-3rryEAre1FVi4', //amy 上医说小程序的openid
//            'check_name'       => 'FORCE_CHECK', //不使用的就实名校验
//            're_user_name'     => '巫桂娥',//真实姓名
//            'amount'           => 100, //分为计量单位
//            'desc'             => '上医说项目兑换', // 企业付款操作说明信息。必填
//        ];

        $partner_trade_no = 'JKTAMYTEST003';
        $app = $this->get_payment_jkt_sh_factory();
        $params = [
            'partner_trade_no' => $partner_trade_no, //订单号
//            'openid'           => 'oj3fs4hSdwzArwecvpYEVSW_WVvI', //amy 医map小程序的openid
            'openid'           => 'o_lRt5QOuqroml-3rryEAre1FVi4', //amy 上医说小程序的openid
            'check_name'       => 'FORCE_CHECK', //不使用的就实名校验
            're_user_name'     => '巫桂娥',//真实姓名
            'amount'           => 100, //分为计量单位
            'desc'             => '健康通【amy 测试】', // 企业付款操作说明信息。必填
        ];
        $query_result = $app->transfer->queryBalanceOrder($params['partner_trade_no']);

        $res = false;
        $result = array();
        //检测订单是否成功
        if ($query_result['return_code'] === 'SUCCESS' && $query_result['result_code'] === 'SUCCESS') {
            echo 1;
            $res = true;
        } else {
            $result = $app->transfer->toBalance($params);
            //提交订单结果
            if ($result['result_code'] === 'SUCCESS') {
                echo 2;
                $res = false;
            } else {
                $query_result = $app->transfer->queryBalanceOrder($params['partner_trade_no']);
                //检测订单是否成功
                if ($query_result['return_code'] === 'SUCCESS' && $query_result['result_code'] === 'SUCCESS') {
                    echo 3;
                    $res = true;
                }
            }
        }
        print_r($query_result);
        print_r($result);
        var_dump($res);

    }

    //微信支付配置
    private function get_payment_jkt_sh_factory() {
        die;
        $config = [
//            医map配置
//            // 必要配置
//            'app_id'             => MAP_WECHAT_APPLET_APP_ID, //小程序对应的appid
//            'mch_id'             => WECHAT_JKT_SH_PAY_MCH_ID, //支付商户的信息
//            'key'                => WECHAT_JKT_SH_PAY_API_KEY,
//            'cert_path'          => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_SH_PAY_CERT_PATH,
//            'key_path'           => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_SH_PAY_KEY_PATH,
//            // 将上面得到的公钥存放路径填写在这里
//            'rsa_public_key_path' => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_SH_PUBLIC_PAY_KEY_PATH,
//            'notify_url'         => WECHAT_JKT_SH_PAY_NOTIFY_URL,

//            //上医说兑换接口配置
//            // 必要配置
//            'app_id'             => DRSAY_WECHAT_APPLET_APP_ID, //小程序对应的appid
//            'mch_id'             => WECHAT_JKT_BJ_PAY_MCH_ID, //支付商户的信息
//            'key'                => WECHAT_JKT_BJ_PAY_API_KEY,
//            'cert_path'          => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_CERT_PATH,
//            'key_path'           => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_KEY_PATH,
//            // 将上面得到的公钥存放路径填写在这里
//            'rsa_public_key_path' => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PUBLIC_PAY_KEY_PATH,
//            'notify_url'         => WECHAT_JKT_BJ_PAY_NOTIFY_URL,

            //上医说兑换接口配置
            // 必要配置
            'app_id'             => DRSAY_WECHAT_APPLET_APP_ID, //小程序对应的appid
            'mch_id'             => WECHAT_JKT_BJ_PAY_MCH_ID, //支付商户的信息
            'key'                => WECHAT_JKT_BJ_PAY_API_KEY,
            'cert_path'          => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_CERT_PATH,
            'key_path'           => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PAY_KEY_PATH,
            // 将上面得到的公钥存放路径填写在这里
            'rsa_public_key_path' => BASEPATH.'libraries/Third_party/'.WECHAT_JKT_BJ_PUBLIC_PAY_KEY_PATH,
            'notify_url'         => WECHAT_JKT_BJ_PAY_NOTIFY_URL,
        ];
        $app = Factory::payment($config);
        return $app;
    }




    //微信小程序提交过来的数据,直接提现到零钱
    function wx_exchange()
    {
        die;
        $post_data = $this->input->post();
        print_r($post_data);die;
//        if ($post_data) {
//            $sms_verify = $post_data['sms_verify'];//短信验证码
//            $scene = $post_data['scene'];//二维码参数
//            $arr_scene = explode("_", $scene);
//            $pid = $arr_scene[0];
//            $member_uid = $arr_scene[1];
//            $encrypted_data = $arr_scene[2];
//            if (!$pid || !$member_uid || !$encrypted_data) {//参数有误
//                _back_msg("error", "参数有误！");
//            }
//            $decrypt_scene = $pid."_".$member_uid;
//            $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
//            if ($encrypted_data !== $decrypt_data) {//解密值不等
//                _back_msg("error", "参数有误！");
//            }
//
//            //检测pid与member_uid信息是否有误，是否已经做过兑换
//            //通过查询积分明细表id获取项目流水表信息，检测项目流水表信息
////            $user_point_detail = $this->db->query("select a.*,b.pro_name as p_name from app_member_point_detail a left join app_project b on a.param=b.id where uid='{$uid}' AND a.`cash_status`=1 AND a.m_pay_type!='101' AND a.log_code in(".POINT_CHANGE_CODE_SURVEY.",".POINT_REISSUE_INTEGRAL.") AND finish_status='c' ORDER BY a.id DESC LIMIT ".$offset.",".$num_page)->result_array();
//            $check_detail = getDataByConditionCi("app_member_point_detail", " AND log_code in(".POINT_CHANGE_CODE_SURVEY.",".POINT_REISSUE_INTEGRAL.") AND finish_status='c' AND param_id=?","*", true, [FLOW_SOURCE_PROJECT_POINT_DETAIL,$p_detail_id]);
//
//            //查询流水表，是否已存在流水信息
//            $check_flow = getDataByConditionCi("app_project_flow", " AND log_code=? AND param_id=?","*", true, [FLOW_SOURCE_PROJECT_POINT_DETAIL,$p_detail_id]);
//
//            //查询订单表，查询是否已经有过兑换记录
//            $check_order = getDataByConditionCi(SURVEY_TABLE_APP_PAYMENT_ORDER, " AND pid=? AND uid=?","*", true, [$pid, $member_uid]);
//            if ($check_flow || $check_order) {
//                throw new Exception("您已申请过兑换，不能重复操作！");
//            }
//
//            $user_order = $this->db->query("SELECT * FROM app_payment_order_new WHERE pid='{$pid}' AND uid='{$member_uid}' LIMIT 1")->row_array();
//            if ($user_order) {//订单存在
//                _back_msg("error", "已经提交过审核！");
//            }
//
//
//
//            //获取用户编号
////            $member_uid = 2982098;
//            //查询token是否存在
//            $user_token = $this->db->query("SELECT * FROM app_member_login_token WHERE id='{$member_uid}' LIMIT 1")->row_array();
//            $is_openid = false;
//            if ($user_token && $user_token['openid']) {//存在并且已经关注过的
//                $is_openid = true;
//            }
//            $js_code = $post_data['js_code'];//js_code
//            $get_wx_auth = $this->app->auth->session($js_code);
////            print_r($get_wx_auth);
//            if (isset($get_wx_auth['errcode'])) {//失败
////                _back_msg("error", $get_wx_auth);
//                _back_msg("error", "小程序登录失败！");
//            } else {
//                if (!$is_openid) {//不存在token，需要新增
//                    $insert_user_data = [
//                        "id" => $member_uid,
//                        "session_key" => $get_wx_auth['session_key'],
//                        "openid" => $get_wx_auth['openid'],
//                        "login_token" => md5($member_uid.'drsay'.time())
//                    ];
//                    $this->db->insert("app_member_login_token", $insert_user_data);
//                } else {//更新
//                    //把用户的微信信息存储入token表中
//                    $update_user_data = [
//                        "session_key" => $get_wx_auth['session_key'],
//                        "openid" => $get_wx_auth['openid'],
////                        "login_token" => md5($member_uid.'drsay'.time())
//                    ];
//                    $this->db->where("id", $member_uid);
//                    $this->db->update("app_member_login_token", $update_user_data);
//                }
//                _back_msg("success", "登录成功！");
//            }
//        }
    }

    //微信小程序解密隐私信息(获取手机号)
    function wx_phone()
    {
        die;
        $post_data = $this->input->post();
        $encryptedData = $post_data['encryptedData'];
        $iv = $post_data['iv'];
//        $session_key = 'sXtWdLACXxQNae2BrHEUgA==';
        //获取用户编号
        $member_uid = 2982098;
        //查询token是否存在
        $user_token = $this->db->query("SELECT * FROM app_member_login_token WHERE id='{$member_uid}' LIMIT 1")->row_array();
        $session_key = $user_token['session_key'];
        //解密
        $decryptedData = $this->app->encryptor->decryptData($session_key, $iv, $encryptedData);
        print_r($post_data);
        print_r($decryptedData);
    }

    //微信小程序解密隐私信息（获取用户信息）
    function wx_user_info()
    {
        die;
        $post_data = $this->input->post();
        $encryptedData = $post_data['encryptedData'];
        $iv = $post_data['iv'];
//        $session_key = 'sXtWdLACXxQNae2BrHEUgA==';
        //获取用户编号
        $member_uid = 2982098;
        //查询token是否存在
        $user_token = $this->db->query("SELECT * FROM app_member_login_token WHERE id='{$member_uid}' LIMIT 1")->row_array();
        $session_key = $user_token['session_key'];
        //解密
        $decryptedData = $this->app->encryptor->decryptData($session_key, $iv, $encryptedData);
        print_r($post_data);
        print_r($decryptedData);
    }


    //把选择的地理位置存储到本地
    function f_location()
    {
        die;
        $post_data = $this->input->post();
print_r($post_data);
die;
        $js_code = $post_data['js_code'];//js_code
        $get_wx_auth = $this->app->auth->session($js_code);
        if (isset($get_wx_auth['errcode'])) {//失败
            _back_msg("error", "小程序登录失败！");
        } else {
            $openid = $get_wx_auth['openid'];
            $user_token = $this->db->query("SELECT * FROM f_user WHERE openid='{$openid}' LIMIT 1")->row_array();
            if ($user_token) {//更新
                //把用户的微信信息存储入token表中
                $update_user_data = [
                    "session_key" => $get_wx_auth['session_key'],
                    "login_token" => md5($user_token['id'].time())
                ];
                $this->db->where("openid", $openid);
                $res = $this->db->update("f_user", $update_user_data);
            } else {//添加
                $insert_user_data = [
                    "session_key" => $get_wx_auth['session_key'],
                    "openid" => $get_wx_auth['openid'],
                    "login_token" => md5($get_wx_auth['openid'].time())
                ];
                $res = $this->db->insert("f_user", $insert_user_data);
            }
            if ($res) {//更新或添加信息成功，记录；地理位置

            }
        }

    }
}
