<?php
class Sigin extends <PERSON>Y_<PERSON>
{
    function __construct()
    {
        parent::__construct();
    }

    ####    邮件签名    ####
    function all()
    {
        $data = array(
            'title' => '邮件签名',
        );

        $this->load->view('/sigin/all', $data);
    }

    function bj_cn()
    {
        $data = array(
            'title' => '邮件签名',
        );

        $this->load->view('/sigin/bj_cn', $data);
    }
    function bj_en()
    {
        $data = array(
            'title' => '邮件签名',
        );

        $this->load->view('/sigin/bj_en', $data);
    }
    function sh_cn()
    {
        $data = array(
            'title' => '邮件签名',
        );

        $this->load->view('/sigin/sh_cn', $data);
    }
    function sh_en()
    {
        $data = array(
            'title' => '邮件签名',
        );

        $this->load->view('/sigin/sh_en', $data);
    }
    ####    邮件签名    ####
}
