<?php
/**
 * Created by PhpStorm.
 * 用途：外包
 * User: Amy
 * Date: 2022/06/22
 */
class Project_partner extends CI_Controller
{
    function __construct()
    {

        parent::__construct();
    }

    /**
     * 客户登录
     */
    public function login(){
        $data_post = $this->input->post();
        if(!empty($data_post)){
            if(empty($data_post['pid'])) _back_msg('error',"请输入项目编号！");
            if(empty($data_post['pid_pwd'])) _back_msg('error',"请输入项目访问密码！");
            $pid = trim($data_post['pid']);
            $pro_id = str_replace(array('HCP'),array(''),$pid);
            $pro_id = intval($pro_id);
            $pid_pwd = trim($data_post['pid_pwd']);
            $project = getDataByID('app_project',$pro_id);
            if(empty($project) || $pid != cus_pid($pro_id)){
                _back_msg('error',"项目编号不存在，请确认！");
            }
            if($project['pro_status'] != 3){
                _back_msg('error',"项目已结束访问，登录失败！");
            }
            if($pid_pwd !== $project['client_password']){
                _back_msg('error',"项目访问密码错误，请确认！");
            }
            //登录成功，记录SESSION
            $data_session = array(
                'client_u' => $pid,
                'client_w' => $pid_pwd,
                'client_id' => $pro_id,
            );
            $this->session->set_userdata($data_session);
            _back_msg('success',"登录成功！");
        } else {
            $this->load->view("/project_finish/login");
        }
    }

    //列表
    public function index(){
//        $client_session = $this->session->userdata('client_u');
//        if(empty($client_session)){
//            redirect('/partner/login');
//        }
//        $pid = $this->session->userdata('client_id');
////        $pid = 501;
//        $project = getDataByID('app_project',$pid);
//        $client_reback =[];
//        if(!empty($project['client_reback'])){
//            $client_reback = json_decode($project['client_reback'],true);
//        }
//        $client_list = getDataByID('app_client',$project['client_id']);
//        $offset = $this->input->get('per_page', true);
//        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*PAGE_NUM : 0;
//        $page = PAGE_NUM;
//        $project_finish_report = getDataByCondition('project_finish_report',"and pid = {$pid} order by id desc limit {$offset},{$page}");
//        $total = getDataNumByCondition('project_finish_report',"and pid = {$pid}");
//        //查询分页
//        $url = '/partner/index';
//        $pagination = getPagination($total,$url);
//        //查总数
//        $sum_finish_report = getDataByCondition("project_finish_report","and pid = {$pid}","sum(need_size) as need_size , sum(finish_size) as finish_size");

        $data =[
//            'title'=> $project['pro_name'],
//            'pid'  => $pid,
//            'pagination'    => $pagination,
//            'client_reback' => $client_reback,
//            'client_list'   => $client_list,
//            'project'       => $project,
//            'project_finish_report'=> $project_finish_report,
//            'sum_finish_report'    => !empty($sum_finish_report[0]) ? $sum_finish_report[0] : [],

        ];
        $this->load->view("/partner/index",$data);
    }

    //会员明细
    public function imp_list()
    {
        $data = [];
        $this->load->view("/partner/imp_list",$data);
    }

    /**
     * 留言
     */
    public function send_message_info(){
        $client_session = $this->session->userdata('client_u');
        if(empty($client_session)){
            redirect('/project_finish/login');
        }
        $pid = $this->session->userdata('client_id');
//        $pid = 501;
        $project = getDataByID('app_project',$pid);
        $client_reback =[];
        if(!empty($project['client_reback'])){
            $client_reback = json_decode($project['client_reback'],true);
        }
        $data =[
            'pid' => $pid,
            'client_reback' => $client_reback,
        ];
        $this->load->view("/project_finish/send_message_info",$data);
    }
    /**
     * 客户发送留言数据
     */
    public function send_message(){
        $data_post = $this->input->post();
        if(empty($data_post['message'])){
            _back_msg('error',"请输入内容！");
        }
        $pid = $data_post['pid'];
        $message = $data_post['message'];
        $res_project = getDataByID('app_project',$pid);
        if(empty($res_project)){
            _back_msg('error',"参数错误！");
        }
        $res_info[] =[
            'message' => $message,
            'pid' => $pid,
            'time' => time(),
            'source' => 1, // 来源1:代表客户 2:代表后台人员
        ];
        project_finish_report_log(1, $pid ,$res_project['pro_name'], $res_info ,0);
        if(!empty($res_project['client_reback'])){
            $client_reback = $res_project['client_reback'];
            $client_reback = json_decode($client_reback,true);
            $res_info = array_merge($res_info,$client_reback);
        }
        $json_data = json_encode($res_info,JSON_UNESCAPED_UNICODE);
        $res = upData('app_project',$pid,['client_reback'=>$json_data]);
        if(!empty($res)){
            _back_msg('success','发送成功！');
        }
        _back_msg('error','发送失败！');
    }
    /**
     * 登录退出
     */
    public function out(){
        $this->session->unset_userdata('client_u');
        $this->session->unset_userdata('client_w');
        $this->session->unset_userdata('client_id');
        redirect('/project_finish/login');
    }


}