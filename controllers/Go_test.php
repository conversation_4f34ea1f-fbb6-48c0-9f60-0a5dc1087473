<?php
die;//###############  AMY 2024-08-27 调整用户自己进入的解密规则
/**
 * Created by PhpStorm.
 * 用途：进入问卷引导页
 * User: Amy
 * Date: 2017/11/02
 */
class Go_test extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('survey_model');
    }

    //进入问卷
    public function s() {
        //验证链接有效性
        $go_code = $this->uri->segment(3);
        $partner_uid = $_SERVER["QUERY_STRING"]; // 得到url传送的参数
        $partner_uid = str_replace(["&is_in=1", "?is_in=1"], "", $partner_uid);
        $survey_decrypt = $this->survey_model->survey_decrypt($go_code, $partner_uid, false, false);
        $res_survey_decrypt = json_decode($survey_decrypt,true);
//        print_r($res_survey_decrypt);
//        die;
        $jkt_thanks_url = "/bk/rs";
        if ($res_survey_decrypt['code'] =='error') {
            $this->survey_model->get_redirect_info($jkt_thanks_url, "p");
//            redirect("/go/thanks?st=p");
        }

//        /**
//         * 检测是否是手机端微信内置浏览器，如是显示链接页
//         */
//        if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
//            return $this->load->view("/go/link_tips",['uri'=>DRSAY_WEB . 'go/s/' .$go_code.'?'.$partner_uid]);
//        }

        $res_survey_decrypt['msg']['partner_uid'] = $partner_uid;
        $pid = $res_survey_decrypt['msg']['pid'];

        $partner_id = $res_survey_decrypt['msg']['go_partner_id'];
        $groupno = $res_survey_decrypt['msg']['go_groupno'];
        $from_where = $res_survey_decrypt['msg']['from_where'];
        $app_project_implement = "app_project_implement_".$pid; // 项目执行表
        if (!$partner_uid) {
            $this->survey_model->get_redirect_info($jkt_thanks_url, "p");
//            redirect("/go/thanks?st=p");
        }

        ####    AMY 2023-07-28 进入问卷先提示确认，再进入问卷
        $is_in = $this->input->get("is_in", true);
        //上级链接地址
        $previousPage = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        if ($previousPage && strpos($previousPage, "is_in=1") !== false) {//上一级来源是www.drsay.cn的直接跳转至问卷链接，不在中间跳转了
            $is_in = 1;
//            file_put_contents("./tmp/http_referer.txt", $previousPage."**".date("Y-m-d H:i:s").PHP_EOL, FILE_APPEND | LOCK_EX);
        }
        if ($is_in != 1) {//出来确认页面
            if (strpos($_SERVER['REQUEST_URI'], "?") !== false) {
                $n_link = $_SERVER['REQUEST_URI']."&is_in=1";
            } else {
                $n_link = $_SERVER['REQUEST_URI']."?is_in=1";
            }
//            $n_link = str_replace("go", "go_test", $n_link);
            //根据项目编号获取项目信息
            $pro_info = $this->db->query("SELECT * FROM app_project WHERE id=?", [$pid])->row_array();
            $data = [
                'link' => $n_link,
                'st_msg' => '<p></p><h2>恭喜您获邀参加本次活动</h2><p></p><br><p>为改善提升医疗产品或服务，促进医疗健康事业高质量发展，特邀专业领域的专家参与！</p><br /><p>感谢您的支持</p>',
            ];
            return $this->load->view("/go/is_in.php",$data);
        }
        ####    AMY 2023-07-28 进入问卷先提示确认，再进入问卷

        //加锁处理
        if(Redis_lock::getInstance()->lock("go_s_".$partner_uid, 20) !== true) {
            //错误提示
//            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_REPEAT_CLICK]);
//            show_json_msg('error', $lang_ary[LABEL_SURVEY_REPEAT_CLICK]);
            show_json_msg('error', 'Please try again later！');
        }


        ######  AMY 2019-02-13 通过域名配置，获取跳转地址    ######
//        $redirect_url_info = "/go/thanks";
        $redirect_url_info = $jkt_thanks_url;
        $sys_id = $res_survey_decrypt['msg']['sys_id'];
        $sys_id = $sys_id ? $sys_id : 1;
//        $this->session->unset_userdata('project_sys_id');
        if (in_array($sys_id, [7,8,9])) {//是网医
            $this->session->set_userdata(["project_sys_id" => $sys_id]);
        }
//        $bespeak_info = getDataByCondition("app_project_bespeak", " AND pid={$pid} AND partner_uid='{$partner_uid}'", "*", true);
//        if ($bespeak_info) {//存在来源地址
//            $sys_id = $bespeak_info['sys_id'] ? $bespeak_info['sys_id'] : 1;//默认是上医说官网地址
//            $site_domain_info = getDataByCondition("app_sys_survey_domain", " AND sys_id={$sys_id}", "*", true);
//            if (!in_array($sys_id, [7,8,9])) {//不是网医
//                $redirect_url_info = $site_domain_info ? $site_domain_info['redirect_link'] : $redirect_url_info;
//            } else {//网医用session来控制跳转
//                $this->session->set_userdata(["project_sys_id" => $sys_id]);
//            }
//        }
        ######  AMY 2019-02-13 通过域名配置，获取跳转地址    ######

        $lang = 140;
        if ($groupno && $partner_id) {//存在外包信息
            $country_default_lang = $this->survey_model->get_country_default_lang($pid, $partner_id, $groupno);
            $lang =  $country_default_lang['lang'];
        }
        //存问卷语言
        $this->session->set_userdata(array("survey_lang"=>$lang));

//        //语言包
//        $lang_ary = get_lang($lang);

        ############### 获取语言包   ###############
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        if (!$groupno || !$partner_id) {
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_LINK_ERROR_GROUPNO_PARTNER_EMPTY]);
            $this->survey_model->get_redirect_info($redirect_url_info, "p");
//            redirect("{$redirect_url_info}?st=p");
        }

        //查询表结构是否存在
//        $check_table_info = $this->db->query("SHOW TABLES LIKE '%".$app_project_implement."%';")->row_array();
        $check_table_info = $this->db->query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE  TABLE_NAME = '{$app_project_implement}'")->row_array();
        if (!$check_table_info) {
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_PROJECT_IMPLEMENT_NOT_EXIST]);
            $this->survey_model->get_redirect_info($redirect_url_info, "p");
//            redirect("{$redirect_url_info}?st=p");
        }

        //判断项目状态，暂停/结束，提示项目结束或暂停，谢谢参与
        $app_project = $this->db->where(array('id'=>$pid))->get('app_project')->row_array();
        if(empty($app_project)){
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_PROJECT_NON_EXISTENT]);
            $this->survey_model->get_redirect_info($redirect_url_info, "l_err");
//            redirect("{$redirect_url_info}?st=l_err");
        }
        //项目奖励方式
        $reward_type = $app_project['reward_type'];
        $add_pr_26 = $app_project['add_pr_26'];//附加字段名

        //配额设置
        $quta_setting = $app_project['quta_setting'];
        //品控相关属性
        $repeat_ip = $app_project['repeat_ip'];
        $permit_country = $app_project['permit_country'];
        $permit_province = $app_project['permit_province'];
        $permit_city = $app_project['permit_city'];
        $ip_limit = $app_project['ip_limit'];
        $pay_money = $app_project['pay_money'];

        //获取用户的IP与IP地址
        $local_ip = getip();
        $ip = ip2long($local_ip);
        $ip_addr = ip2location($ip);
        if ($app_project['pro_status'] == PROJECT_TEST){
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_PROJECT_READY]);
            $this->survey_model->get_redirect_info($redirect_url_info, "nquta");
//            redirect("{$redirect_url_info}?st=nquta");
        }
        if ($app_project['pro_status'] == PROJECT_OVER){
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_PROJECT_FINISH]);
            $this->survey_model->get_redirect_info($redirect_url_info, "finish");
//            redirect("{$redirect_url_info}?st=finish");
        }
        if ($app_project['pro_status'] == PROJECT_SUSPEND ){
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_PROJECT_SUSPEND]);
            $this->survey_model->get_redirect_info($redirect_url_info, "suspend");
//            redirect("{$redirect_url_info}?st=suspend");
        }

        //判断外包partner是否存在
        $get_project_partner_data = array(
            'project_id'=>$pid,
            'status'=>PROJECT_PARTNER_STATUS_ENABLE,
            'partner_id'=>$partner_id,
            'groupno'=>$groupno
        );

        $app_project_partner = $this->db->where($get_project_partner_data)->get('app_project_partner')->row_array();
        $partner_country = $app_project_partner['sample_country'];//外包国家
        $partner_money_rate = $app_project_partner['money_rate'];//外包汇率

        if(empty($app_project_partner)){
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_OUTSOURCE_LINK_ERROR]);
            $this->survey_model->get_redirect_info($redirect_url_info, "bl");
//            redirect("{$redirect_url_info}?st=bl");
        }
        if ($app_project_partner['status'] != PROJECT_PARTNER_OPENING) {//外包不是启用状态
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_OUTSOURCE_LINK_ERROR]);
            $this->survey_model->get_redirect_info($redirect_url_info, "not_start");
//            redirect("{$redirect_url_info}?st=not_start");
        }

        $open_c = $app_project_partner['openc'];
        $open_q = $app_project_partner['openq'];
        $open_s = $app_project_partner['opens'];
        if (!$open_c || !$open_q || !$open_s) {
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_OUTSOURCE_BACK_LINK_EMPTY]);
            $this->survey_model->get_redirect_info($redirect_url_info, "p");
//            redirect("{$redirect_url_info}?st=p");
        }

        //检查外包是否已被禁用
        $get_app_vendor_data = array(
            'open_status'=>VENDOR_OPEN_STATUS_ENABLE,
            'id'=>$partner_id,
            'is_del'=>VENDOR_IS_DEL_ENABLE
        );
        $app_vendor = $this->db->where($get_app_vendor_data)->get('app_vendor')->row_array();
        if(empty($app_vendor)){
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_OUTSOURCE_NON_EXISTENT_OR_DISABLE]);
            $this->survey_model->get_redirect_info($redirect_url_info, "bl");
//            redirect("{$redirect_url_info}?st=bl");
        }

        // 判断项目配额是否设置
//        $app_project_quta = $this->db->where(array('pid'=>$pid))->get('app_project_quta')->result_array();//这么写，配额量太大，会导致内存爆
        $app_project_quta = $this->db->query("SELECT * FROM app_project_quta WHERE pid='{$pid}' LIMIT 1")->row_array();
//        if(empty($app_project_quta) || !$quta_setting){
        if(empty($app_project_quta)){
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_PROJECT_QUOTA_NOT_SET]);
            $this->survey_model->get_redirect_info($redirect_url_info, "nquta");
//            redirect("{$redirect_url_info}?st=nquta");
        }



        //判断是否参加过（根据返回状态）
        $project_implement_finish_status = $this->db->where(array('partner_uid'=>$partner_uid))->get($app_project_implement)->row_array();
        $member_uid = $project_implement_finish_status['member_uid'];
        $member_uid = $member_uid ? $member_uid : 0;
        $survey_uid = $project_implement_finish_status['survey_uid'];
        $my = $project_implement_finish_status['my'];
        $pid_id = $project_implement_finish_status['id'];

        //AMY 2020-06-05 跳转到gooddr官网的code组装
        $to_gooddr_code = $this->survey_model->get_gooddr_log_info($member_uid, $pid);
        $to_gooddr_code = $to_gooddr_code ? $to_gooddr_code : "";
        $to_gooddr = $to_gooddr_code ? "&gooddr_code={$to_gooddr_code}" : "";

        //返回状态，c,s,q,如果其中有任何一个状态，弹出一个提示，已经做过问卷不能重复参与，谢谢
        if($project_implement_finish_status['finish_status']){
            //查询用户的状态是否为c状态
            if ($project_implement_finish_status['finish_status'] == "c") {
                if($app_vendor['property'] == VENDOR_INTERNAL) {//内部资源
                    if ($reward_type == PROJECT_REWARD_TYPE_GIFT) {//礼品
                        //查询会员是否已经记录快递信息
                        $this->redirect_get_address($pid, $survey_uid);
                    }

                    //查询是否已经发放奖励
                    if ($member_uid > 0) {
                        $res_project_info = $this->db->query("SELECT * FROM app_member_point_detail WHERE log_code='".POINT_CHANGE_CODE_SURVEY."' AND param='{$pid}' AND uid='{$member_uid}'")->row_array();
                        //查询订单
                        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE log_code=? AND pid=? AND pid_id=? AND order_status=1", [ORDER_SOURCE_STATUS_PROJECT, $pid, $pid_id])->row_array();
                        if ($order_info) {
                            if ($order_info['status'] == 2) {//已支付
                                $this->survey_model->get_redirect_info($redirect_url_info, "p_s");
                            }
                        }
//                        if ($res_project_info['point_fetch'] == PROJECT_POINT_FETCH_TIMELY && $res_project_info['apply_status'] == 0) {//是及时加分且未提现成功时
                        if ($res_project_info['point_fetch'] == PROJECT_POINT_FETCH_TIMELY && ($res_project_info['apply_status'] == 0 || ($res_project_info['apply_status'] == 1 && $order_info && in_array($order_info['status'], [3,5])))) {//是及时加分且未提现成功时
                            if ($my == 1) {//集团账号进来的记录，不做支付
                                redirect("http://www.drsay.cn/bk/rs?code=cDRSAYDRSAYDRSAYf578bd256bbb4540");
                            } else {
                                $this->redirect_to_wx($pid, $survey_uid);
                            }
                        }
                    }
                }
            }
            //已经做过问卷不能重复参与
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_FINISH_STATUS_EXIST]);
            $f_s = strtolower($project_implement_finish_status['finish_status']);
            if (in_array($f_s, ['c','q','s'])) {
                $this->survey_model->get_redirect_info($redirect_url_info, $f_s, "", "", "gooddr_code={$to_gooddr_code}");
            } else {
                $this->survey_model->get_redirect_info($redirect_url_info, "repeat", "", "", "gooddr_code={$to_gooddr_code}");
            }
//            redirect("{$redirect_url_info}?st=repeat{$to_gooddr}");
        }
        //2019-10-24 rainnie要求 项目明细状态 配额已满(结束) 跳转地址
        $screening_status = [3,4,6,7,9,10,12];
        if(in_array($project_implement_finish_status['screening_status'],$screening_status)){
            $this->survey_model->get_redirect_info($redirect_url_info, "q", "", "", "gooddr_code={$to_gooddr_code}");
//            redirect("{$redirect_url_info}?st=q{$to_gooddr}");
        }

        $this->member_active_status($app_vendor['property'],$project_implement_finish_status['member_uid']);
        $is_finish = "";
        if ($project_implement_finish_status['quta_id']) {
            //配额是否已满
            $res_pro_quota = $this->survey_model->close_project_quota($project_implement_finish_status['quta_id']);
            if ($res_pro_quota) {//配额已满
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, "进入问卷，配额已满，关闭配额");
                if($app_vendor['property'] == VENDOR_INTERNAL) {//内部资源
                    $is_finish = "q";
                } else {
                    $this->survey_model->get_redirect_info($redirect_url_info, "s", "", "", "gooddr_code={$to_gooddr_code}");
//                    redirect("{$redirect_url_info}?st=s{$to_gooddr}");
                }
            }
        }

        if (!$project_implement_finish_status) {//不存在此partner_uid
            if ($app_vendor['property'] == VENDOR_INTERNAL) {//内部资源,错误的链接,需要通过甄别问卷去甄别
                ###############  AMY 2018-04-16 流程变更，只要是内部资源链接，已经抽样上来的名单，直接进问卷，否则，走甄别问卷   ###############
//                //查询内部会员的配额
//                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_PARTNER_UID_ERROR].$partner_uid);
//                redirect("/go/thanks?st=p");
                ###############  AMY 2018-04-16 流程变更，只要是内部资源链接，已经抽样上来的名单，直接进问卷，否则，走甄别问卷   ###############
                $member_uid = str_replace("uid=".$pid."_", "", $partner_uid);//自己进入
                $member_uid = (int)(trim($member_uid));
                //查询会员状态是否正常
                if ($member_uid && strpos($partner_uid, "uid=".$pid) !== false) {
                    $res_member_info = $this->db->query("SELECT * FROM app_member WHERE id='{$member_uid}'")->row_array();
                    if ($res_member_info) {//存在用户信息，直接返回结果
                        $res_member_implement = getMemberInfoDic($member_uid, $this->setting_cond_field_doc, "", $lang);
                        $insert_implement_data = array(
                            "partner_uid" => $partner_uid,
                            "data_sources" => 4,
                            "partner_id" => $partner_id,
                            "groupno" => $groupno,
                            "dr_from_where" => $from_where,
                            'member_uid' => $member_uid,
                        );
                        $insert_implement_data = array_merge($insert_implement_data, $res_member_implement);
                        $res_insert_member = $this->db->insert($app_project_implement, $insert_implement_data);
                        if ($res_insert_member) {
                            $project_implement_finish_status = $this->db->where(array('partner_uid'=>$partner_uid))->get($app_project_implement)->row_array();
                        } else {
                            //记录警告信息
                            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, "非抽样会员，入库失败");
                            $this->survey_model->get_redirect_info($redirect_url_info, "bl", "", "", "gooddr_code={$to_gooddr_code}");
//                            redirect("{$redirect_url_info}?st=bl{$to_gooddr}");
                        }
                    } else {
                        //记录警告信息
                        set_sys_warning($pid, $partner_id, $groupno, $partner_uid, "非抽样会员，查询不到会员详细信息");
                        $this->survey_model->get_redirect_info($redirect_url_info, "bl", "", "", "gooddr_code={$to_gooddr_code}");
//                        redirect("{$redirect_url_info}?st=bl{$to_gooddr}");
                    }
                } else{
                    //记录警告信息
                    set_sys_warning($pid, $partner_id, $groupno, $partner_uid, "非抽样会员，通过partner_uid截取不到用户编号");
                    $this->survey_model->get_redirect_info($redirect_url_info, "bl", "", "", "gooddr_code={$to_gooddr_code}");
//                    redirect("{$redirect_url_info}?st=bl{$to_gooddr}");
                }

            } else {//外部资源，把相关链接信息存储到项目执行表中
                $insert_implement_data = array(
                    "ip" => $ip,
                    "ip_addr" => $ip_addr,
                    "click_time" => time(),
                    "partner_uid" => $partner_uid,
                    "mobile" => $partner_uid,
                    "data_sources" => 3,
                    "partner_id" => $partner_id,
                    "groupno" => $groupno,
                    "dr_from_where" => $from_where,
                );
                $this->db->insert($app_project_implement, $insert_implement_data);
            }
        } else if ($member_uid) {
            $res_member_info = $this->db->query("SELECT * FROM app_member WHERE id='{$member_uid}'")->row_array();
        }


        ##############  品控判断   ##############
        //IP地址重复判断
        if ($repeat_ip > 0) {
            $res_repeat_ip = $this->ip_repeat($repeat_ip, $ip, $pid, $partner_uid);
            if (!$res_repeat_ip) {
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_IP_REPEAT]);
                if($app_vendor['property'] == VENDOR_INTERNAL) {//内部资源
                    $is_finish = "q";
                } else {
                    $this->survey_model->get_redirect_info($redirect_url_info, "q", "", "", "gooddr_code={$to_gooddr_code}");
//                    redirect("{$redirect_url_info}?st=q{$to_gooddr}");
                }
            }
        }
        //允许国家 中国-上海-上海
        if ($permit_country) {
            $res_permit = $this->check_country_province_city($permit_country, $ip_addr);
            if(!$res_permit){
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_COUNTRY_PROHIBIT]);
                $this->survey_model->get_redirect_info($redirect_url_info, "npros", "", "", "gooddr_code={$to_gooddr_code}");
//                redirect("{$redirect_url_info}?st=npros{$to_gooddr}");
            }
        }
        //允许省份
        if ($permit_province) {
            $res_permit = $this->check_country_province_city($permit_province, $ip_addr);
            if(!$res_permit){
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_PROVINCE_PROHIBIT]);
                $this->survey_model->get_redirect_info($redirect_url_info, "npros", "", "", "gooddr_code={$to_gooddr_code}");
//                redirect("{$redirect_url_info}?st=npros{$to_gooddr}");
            }
        }
        //允许城市
        if ($permit_city) {
            $res_permit = $this->check_country_province_city($permit_city, $ip_addr);
            if(!$res_permit){
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_CITY_PROHIBIT]);
                $this->survey_model->get_redirect_info($redirect_url_info, "npros", "", "", "gooddr_code={$to_gooddr_code}");
//                redirect("{$redirect_url_info}?st=npros{$to_gooddr}");
            }
        }
        //允许的IP段
        if ($ip_limit) {
            $res_permit = $this->ip_limit($ip_limit,$local_ip);
            if(!$res_permit){
                set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_IP_PROHIBIT]);
                if($app_vendor['property'] == VENDOR_INTERNAL) {//内部资源
                    $is_finish = "q";
                } else {
                    $this->survey_model->get_redirect_info($redirect_url_info, "q", "", "", "gooddr_code={$to_gooddr_code}");
//                    redirect("{$redirect_url_info}?st=q{$to_gooddr}");
                }
            }
        }
        ##############  品控判断   ##############
        //记录会员点击日志
        user_log(LOG_CODE_SURVEY_START, $pid, $member_uid, $partner_uid);

        //判断问卷链接未用的是否还有，没有的时候应该向异常表添加信息，短信提醒管理员
        //分配问卷链接（包含点击过问卷的链接）
        $get_survey_link = $this->survey_model->get_survey_link($pid, $groupno, $partner_uid, $partner_id, $lang_ary);
        if(!$get_survey_link){
            //记录警告信息
            set_sys_warning($pid, $partner_id, $groupno, $partner_uid, $lang_ary[LABEL_SURVEY_LINK_INSUFFICIENT]);
            $this->survey_model->get_redirect_info($redirect_url_info, "ne", "", "", "gooddr_code={$to_gooddr_code}");
//            redirect("{$redirect_url_info}?st=ne{$to_gooddr}");
        }
        //创建甄别问卷链接地址
        $quta_code = base64_encode($partner_uid);
        $quta_code = $quta_code.'_'.md5($quta_code.PROJECT_ENCODE_KEY.$pid);
        //正式问卷链接
        $quta_survey_url = DRSAY_WEB.'/survey/index/'.$go_code.'?'.$quta_code;
        //测试
//        $quta_survey_url = 'http://web.drsay.cn/survey_n/index/'.$go_code.'?'.$quta_code;
        ####### 如果是内部资源时，开始  #######
        if($app_vendor['property'] == VENDOR_INTERNAL){//内部资源

            if ($member_uid > 0) {//设备日志
                $this->load->library('user_agent');
                $platform = $this->agent->platform();
                $browser = $this->agent->browser();
                $browser_version = $this->agent->version();
                $referrer = $this->agent->referrer();
                $agent_data = [
                    "pid" => $pid,
                    "uid" => $member_uid,
                    "platform" => $platform,
                    "browser" => $browser,
                    "browser_version" => $browser_version,
                    "referrer" => $referrer,
                    "mobile" => $this->agent->mobile(),
                    "agent_string" => $this->agent->agent_string(),
                    "add_time" => time(),
                ];
                $this->db->insert('app_project_device_log', $agent_data);
            }


            if($app_project_partner['check_survey'] == CHECK_SURVEY_DISPLAY) {//显示甄别问卷
                //存在抽样条件，说明是系统抽样或者用户第一次进入，已做过甄别问卷，符合条件，那么直接送入问卷
                if ($project_implement_finish_status['quta_id']) {
                    //记录用户进入次数
//                    if($project_implement_finish_status && empty($project_implement_finish_status['click_time'])){
//                        $this->db->query("update app_member set survey_count=survey_count+1  WHERE `id` = {$member_uid}");
//                    }
                } else {//不存在抽样条件，判断用户相关属性是否有符合的甄别条件，如果是待审核状态的用户，需要走甄别问卷，不是待审核状态的用户，直接用会员的属性去检测是否符合
                    if ($res_member_info && $res_member_info['status'] == 1) {//存在会员,但是是待审核状态的，跳转到甄别问卷
                        redirect($quta_survey_url);
                    } else {
                        //检测配额是否符合条件，符合条件后，收集数量是否已满
                        //走到这，说明已经符合条件，如果不符合的话，已经在此步骤跳转，不会继续往下执行了
                        $quota_id = $this->survey_model->get_by_quota_cond_and_member_info($pid, $quta_setting, $project_implement_finish_status, $add_pr_26, $res_survey_decrypt, false, true, $lang_ary);
                        if ($quota_id) {
                            $this->db->query("UPDATE {$app_project_implement} SET quta_id='{$quota_id}' WHERE partner_uid='{$partner_uid}' LIMIT 1");
                        }
                    }
                }
            }
            //随机获取一条未使用的问卷，跳问卷链接
            //分配链接,内部资源的外包，使用的始终是我们自己的会员，因此，如果设置了使用甄别问卷的，如果会员已经审核过了，是正式会员的，用会员基础信息与抽样信息比对，符合直接送入问卷，不符合即被甄别，不是正式会员的，送入甄别问卷，由甄别问卷去甄别
            $data_implement = array(
                'survey_answer_status' => SURVEY_ANSWER_START, // 受访者进入正式问卷的状态：1、答卷中 2、答卷结束
                'survey_uid' => $get_survey_link['survey_uid'],
                'dr_from_where' => $from_where,
                'partner_id' => $partner_id,
            );

            $this->survey_model->update_implement_a_s_link_table(1,$pid, $data_implement, $res_survey_decrypt, true, $get_survey_link['id']);
            //不显示甄别问卷&符合条件的直接进入调查问卷
            if (in_array($is_finish, ['q','s'])) {
                if ($member_uid > 0) {
                    $point_info = $app_project_partner['point_'.$is_finish];
                    if($is_finish == 'q'){
                        $screening_status = 7;
                    }
                    if($is_finish == 's'){
                        $screening_status = 6;
                    }
                    $data_implement = array(
                        'bk_ip' => $ip,
                        'bk_ip_addr' => $ip_addr,
                        'finish_status' => $is_finish,
                        'original_finish_status' => $is_finish,
                        'finish_time' => time(),
                        'screening_status' => $screening_status,
                        'survey_answer_status' => 2,
                        'get_point' => $point_info,//记录完成问卷获得的积分
                    );
                    //1:更新项目明细表
                    $where_implement = array("partner_uid" => $partner_uid);
                    // 更新相关内容到项目明细表
                    $this->survey_model->update_surface($app_project_implement, $data_implement, $where_implement);
                    //外包信息
                    $point_detail_info = [
                        "finish_status" => $is_finish,//完成状态
                        "country" => $partner_country,//外包国家
                        "money_rate" => $partner_money_rate,//外包汇率
                    ];
                    add_member_point_info($member_uid, $point_info, POINT_CHANGE_CODE_SURVEY, $pid, $app_project['pro_type'], $point_detail_info, POINT_AUDITING_END, "survey_point");

                    //记录完成状态
                    $this->get_back_status($is_finish, $point_info, $pid, $partner_uid);

                    //i_survey调查记录状态更新
                    $this->update_i_survey_finish_status($is_finish, $pid, $member_uid);
                }
                $this->survey_model->get_redirect_info($redirect_url_info, $is_finish, $member_uid, $pid, "gooddr_code={$to_gooddr_code}");
//                redirect("{$redirect_url_info}?st=".$is_finish.$to_gooddr);
            }
            //校验survey_link是否有效 2019-07-19
            survey_link_check($pid, $member_uid, $get_survey_link['survey_link']);

            if ($member_uid) {//amy 2020-02-21,记录医师最后点击问卷时间
                $now_time = time();
                if($project_implement_finish_status && empty($project_implement_finish_status['click_time'])){
                    $this->db->query("UPDATE app_member SET survey_count=survey_count+1,last_survey_time='{$now_time}',last_survey_ip='{$ip}',last_survey_ipaddr='{$ip_addr}' WHERE id=?", [$member_uid]);
                } else {
                    $this->db->query("UPDATE app_member SET last_survey_time='{$now_time}',last_survey_ip='{$ip}',last_survey_ipaddr='{$ip_addr}' WHERE id=?", [$member_uid]);
                }
//                $update_click_member_data = array(
//                    "last_survey_time" => time(),
//                    "last_survey_ip" => $ip,
//                    "last_survey_ipaddr" => $ip_addr
//                );
//                $this->survey_model->update_surface("app_member", $update_click_member_data, array("id" => $member_uid));
            }

            if ($pay_money == 1) {//需要短信验证
                $survey_uid = $get_survey_link['survey_uid'];
                $this->redirect_to_verify($pid, $survey_uid);
            }

            redirect($get_survey_link['survey_link']);
        }
        ####### 如果是内部资源时，结束  #######

        ####### 外部资源，开始  #######
        //如果是外部资源，必须设置甄别题，外包必须设置"显示甄别问卷"，否则问卷结束，记录警告
        if ($app_vendor['property'] == VENDOR_EXTERNAL) {//外部资源
            if ($app_project_partner['check_survey'] == CHECK_SURVEY_NO) {//外包未设置启动甄别问卷,记录警告信息
                //直接进入问卷 2018-03-29 rita姐同意把外部资源的链接不走甄别问卷也能过
                // 更新点击情况到项目明细表
                $this->survey_model->update_implement_a_s_link_table(2,$pid, array("dr_from_where" => $from_where,'survey_uid' => $get_survey_link['survey_uid']), $res_survey_decrypt, true, $get_survey_link['id']);
                //校验survey_link是否有效 2019-07-19
                survey_link_check($pid, $member_uid, $get_survey_link['survey_link']);
                redirect($get_survey_link['survey_link']);
            } else {
                // 更新点击情况到项目明细表
                $this->survey_model->update_implement_a_s_link_table(3,$pid, array("dr_from_where" => $from_where), $res_survey_decrypt);
                if ($project_implement_finish_status && $project_implement_finish_status['quta_id']) {//已经做过甄别问卷
                    //校验survey_link是否有效 2019-07-19
                    survey_link_check($pid, $member_uid, $get_survey_link['survey_link']);
                    redirect($get_survey_link['survey_link']);
                } else {//外包未做过甄别问卷
                    redirect($quta_survey_url);
                }
            }
        }
        ####### 外部资源，结束  #######
    }

    //记录完成状态
    public function get_back_status($st, $point_info, $pid, $partner_uid, $prize_point = 0)
    {
//        if (!$st || !$point_info || !$pid || !$partner_uid) {return false;}
        //没有积分也要标记问卷状态
        if (!$st || !$pid || !$partner_uid) {return false;}
        //记录完成状态
        $update_project_status = array(
            "finish_status" => $st,
            "price" => $point_info,
            "prize_price" => $prize_point,
        );
        $where_project_status = array(
            "pid" => $pid,
            "partner_uid" => $partner_uid,
        );
        $this->survey_model->update_surface("app_project_bespeak", $update_project_status, $where_project_status);
        return true;
    }

    /**
     * i_survey账号执行时，需要把问卷状态同步到i_survey_project_bespeak表中
     * @param $st
     * @param $pid
     * @param $member_uid
     * @return bool
     */
    private function update_i_survey_finish_status($st, $pid, $member_uid){
        if (!$st || !$pid || !$member_uid) {return false;}
        $bespeak_info = getDataByConditionCi('i_survey_project_bespeak', " and pid = ? AND member_uid = ? LIMIT 1", "*", true, [$pid, $member_uid]);
        if ($bespeak_info) {//存在记录，才操作
            $local_ip = getip();
            $ip = ip2long($local_ip);
            $ip_addr = ip2location($ip);
            //记录完成状态
            $update_project_status = array(
                "original_finish_status" => $st,
                "finish_status" => $st,
                "finish_time" => time(),
                "bk_ip" => $local_ip,
                "bk_ip_address" => $ip_addr,
            );
            $where_project_status = array(
                "pid" => $pid,
                "member_uid" => $member_uid,
            );
            $this->survey_model->update_surface("i_survey_project_bespeak", $update_project_status, $where_project_status);
        }
        return true;
    }

    //问卷链接无效时，跳转
    public function others()
    {
        $code = $this->input->get('code');
        $partner_param = explode('_',$code);
        $pid = isset($partner_param[0]) ? $partner_param[0] : "";
        $uid = isset($partner_param[1]) ? $partner_param[1] : "";
        $survey_link = isset($partner_param[2]) ? $partner_param[2] : "";
        $survey_link = $survey_link ? base64_decode($survey_link) : "";
        $survey_link = $survey_link ? str_replace("***", "_", $survey_link) : "";
        $code = isset($partner_param[3]) ? $partner_param[3] : "";
        if (!$pid || !$uid || !$code) {//结果出错
            redirect("/");
        }
        $md5_pid = substr(md5($pid."_".$uid."_".$survey_link."_".PROJECT_ENCODE_KEY), 8, 16);
        if ($code != $md5_pid) {
            redirect("/");
        }
        $data = array(
            "pid" => $pid,
            "survey_link" => $survey_link,
            "uid" => $uid,
            "code" => $code,
        );
        $this->load->view("/go/others.php", $data);
    }

    //最终显示的页面
    public function thanks()
    {
//        $lang = 140;
//        $lang_ary = get_lang($lang);
        $lang = $this->session->userdata["survey_lang"];
        $lang = $lang ? $lang : 140;
        //获取语言包
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        $gooddr_code = $this->input->get("gooddr_code", true);
        $now_time = time();
        $md5_from_where = substr(md5("drsay_".$now_time."_".PROJECT_ENCODE_KEY), 8, 16);
        $from_where = "drsay_".$now_time."_".$md5_from_where;

        $st = isset($_REQUEST['st']) ? $_REQUEST['st'] : "";
        switch($st) {
            case 'c': //返回状态为c的成功样本
                $st_msg = $lang_ary[LABEL_SURVEY_COMPLETE];
                break;
            case 's': //返回状态为s的成功样本
                $st_msg = $lang_ary[LABEL_SURVEY_SCREEN_OUT];
                break;
            case 'q': //返回状态为q的成功样本
                $st_msg = $lang_ary[LABEL_SURVEY_QUOTA_FULL];
                break;
            case 'p': //问卷链接在项目链接表中不存在
                $st_msg = $lang_ary[LABEL_SURVEY_LINK_ERROR];
                break;
            case 'npros'://回答问卷前品控未通过
                $st_msg = $lang_ary[LABEL_SURVEY_NOT_CONFORM_TO];
                break;
            case 'nquta'://项目的配额不存在
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_READY];//外部资源时，需要甄别问卷，或者项目在准备阶段
                break;
            case 'qutasuc'://项目测试完毕返回状态
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_TEST_FINISH];//甄别问卷测试结束
                break;
            case 'l_err': //项目不存在
                $st_msg = $lang_ary[LABEL_SURVEY_LINK_OVERDUE];
                break;
            case 'ne': //问卷链接不足，提醒表中记录
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_LINK_INSUFFICIENT];
                break;
            case 'finish': //项目结束
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_FINISH];
                break;
            case 'suspend': //项目暂停，稍后再试
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_SUSPEND];
                break;
            case 'bl': //进入问卷的链接中合作伙伴id，不存在，或者该合作伙伴被禁用
                $st_msg = $lang_ary[LABEL_SURVEY_NOT_AN_INVITATION];
                break;
            case 'not_start': //项目未启动
                $st_msg = $lang_ary[LABEL_PROJECT_NOT_START];
                break;
            case 'repeat': //重复参与项目，不能进入第二次
                $st_msg = $lang_ary[LABEL_SURVEY_REPEAT];
                break;
            case 'wx_cash_suc': //微信提现成功
//                $st_msg = $lang_ary[LABEL_SURVEY_REPEAT];
                $st_msg = "兑换申请成功";
                break;
            default: //除以上之外的告警
                $st_msg = $lang_ary[LABEL_SURVEY_DEFAULT];
                break;
        }
        $data = array(
            "st_msg" => $st_msg,
            "gooddr_url" => $gooddr_code ? "http://www.gooddr.com/index.html?code=".$gooddr_code : "http://www.gooddr.com/index.html?from_where={$from_where}",
        );
        $this->load->view("/go/thanks.php",$data);
    }

    //最终显示的页面
    public function thanks_n()
    {
        $lang = $this->session->userdata["survey_lang"];
        $lang = $lang ? $lang : 140;
        //获取语言包
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        $gooddr_code = $this->input->get("gooddr_code", true);

        $st = isset($_REQUEST['st']) ? $_REQUEST['st'] : "";
        switch($st) {
            case 'c': //返回状态为c的成功样本
                $st_msg = $lang_ary[LABEL_SURVEY_COMPLETE];
                break;
            case 's': //返回状态为s的成功样本
                $st_msg = $lang_ary[LABEL_SURVEY_SCREEN_OUT];
                break;
            case 'q': //返回状态为q的成功样本
                $st_msg = $lang_ary[LABEL_SURVEY_QUOTA_FULL];
                break;
            case 'p': //问卷链接在项目链接表中不存在
                $st_msg = $lang_ary[LABEL_SURVEY_LINK_ERROR];
                break;
            case 'npros'://回答问卷前品控未通过
                $st_msg = $lang_ary[LABEL_SURVEY_NOT_CONFORM_TO];
                break;
            case 'nquta'://项目的配额不存在
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_READY];//外部资源时，需要甄别问卷，或者项目在准备阶段
                break;
            case 'qutasuc'://项目测试完毕返回状态
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_TEST_FINISH];//甄别问卷测试结束
                break;
            case 'l_err': //项目不存在
                $st_msg = $lang_ary[LABEL_SURVEY_LINK_OVERDUE];
                break;
            case 'ne': //问卷链接不足，提醒表中记录
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_LINK_INSUFFICIENT];
                break;
            case 'finish': //项目结束
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_FINISH];
                break;
            case 'suspend': //项目暂停，稍后再试
                $st_msg = $lang_ary[LABEL_SURVEY_PROJECT_SUSPEND];
                break;
            case 'bl': //进入问卷的链接中合作伙伴id，不存在，或者该合作伙伴被禁用
                $st_msg = $lang_ary[LABEL_SURVEY_NOT_AN_INVITATION];
                break;
            case 'not_start': //项目未启动
                $st_msg = $lang_ary[LABEL_PROJECT_NOT_START];
                break;
            case 'repeat': //重复参与项目，不能进入第二次
                $st_msg = $lang_ary[LABEL_SURVEY_REPEAT];
                break;
            case 'wx_cash_suc': //微信提现成功
//                $st_msg = $lang_ary[LABEL_SURVEY_REPEAT];
                $st_msg = "微信提现成功";
                break;
            default: //除以上之外的告警
                $st_msg = $lang_ary[LABEL_SURVEY_DEFAULT];
                break;
        }
        $data = array(
            "st_msg" => $st_msg,
            "gooddr_url" => $gooddr_code ? "http://www.gooddr.com/index.html?code=".$gooddr_code : "http://www.gooddr.com/index.html",
        );
        $this->load->view("/go/thanks.php",$data);
    }

    //点击健康通banner的日志记录
    public function click_gooddr_banner()
    {
        $id = (int)trim($this->input->get("id", true));
        if ($id > 0) {
            file_put_contents("./tmp/gooddr_banner.txt", $id."**".date("Y-m-d H:i:s").PHP_EOL, FILE_APPEND | LOCK_EX);
            redirect("http://www.gooddr.com/index_banner.html?id={$id}");
        } else {
            redirect("/");
        }
    }

    //完成问卷重定向
    public function site_redirect()
    {
        $st = trim($this->input->get("st", true));
        $uid = trim($this->input->get("uid", true));
        $gooddr_code = trim($this->input->get("gooddr_code", true));
        //通过uid查询问卷信息
        $partner_uid = "uid=".$uid;
        $get_info = getDataByConditionCi("app_project_bespeak a left join app_sys_survey_domain b on a.sys_id=b.sys_id", " AND a.partner_uid=?", "a.*,b.redirect_link", true, [$partner_uid]);
//        $to_gooddr = $gooddr_code ? "&gooddr_code={$gooddr_code}" : "";
        $to_gooddr = $gooddr_code ? "gooddr_code={$gooddr_code}" : "";
//        $redirect_url_info = "/bk/rs?st={$st}{$to_gooddr}";
        $redirect_url_info = "/bk/rs";
        if ($get_info && $get_info['redirect_link']) {//存在记录
            $this->survey_model->get_redirect_info($get_info['redirect_link'], $st, "", "", "point={$get_info['price']}");
//            $redirect_url_info = $get_info['redirect_link']."?st={$st}&point={$get_info['price']}";
        }
        $this->survey_model->get_redirect_info($redirect_url_info, $st, $uid, $get_info['pid'], $to_gooddr);
//        redirect($redirect_url_info);
    }

    public function del_session(){
        $pid = $this->uri->segment(3);
        $this->session->unset_userdata("pid_".$pid);
        $this->session->unset_userdata('survey_one_page');
        $this->session->unset_userdata('session_answer');
        $this->session->unset_userdata('session_answer_finger');
        $this->session->unset_userdata('country');
        $this->session->unset_userdata('province');
        $this->session->unset_userdata('now_finger');
        $this->session->unset_userdata('next_finger');
        $this->session->unset_userdata('pid_'.$pid);
        echo 'session已经被清空，请点击测试甄别问卷开始进行测试！';
        $str = "<script type='application/javascript'>
                    function delayer() {
//                      window.location = '".ADMIN_URL."/project/project/additional_group/'+$pid;
                    }
                     setTimeout('delayer()', 1000);
                     //这里实现延迟5秒跳转
                </script>";
        echo $str;
    }

    ############## 公共方法 ##############
    //礼品时，跳转到收集快递信息地址
    private function redirect_get_address($pid, $survey_uid)
    {
        if (!$pid || !$survey_uid) {return false;}
        $get_pro_prize_log = $this->db->query("SELECT * FROM app_project_prizes_log WHERE pid='{$pid}' AND survey_uid='{$survey_uid}' LIMIT 1")->row_array();
        if (!$get_pro_prize_log) {//没有记录礼品收货地址，需要再次收集，跳转到礼品记录页面
            $survey_uid_md5 = md5($survey_uid.PROJECT_ENCODE_KEY);
            $survey_uid_str = $survey_uid."_".$survey_uid_md5;
            $bk_code = link_encode($pid, 'c', PROJECT_ENCODE_KEY);
            $gift_address_url = "/bk/get_address/".$bk_code."/".$survey_uid_str;
            redirect($gift_address_url);
        }
        return true;
    }

    //及时加分时，跳转微信小程序二维码
    private function redirect_to_wx($pid, $survey_uid)
    {
        if (!$pid || !$survey_uid) {return false;}
        $survey_uid_md5 = md5($survey_uid.PROJECT_ENCODE_KEY);
        $survey_uid_str = $survey_uid."_".$survey_uid_md5;
        $bk_code = link_encode($pid, 'c', PROJECT_ENCODE_KEY);
        $gift_address_url = "/bk/rs/".$bk_code."/".$survey_uid_str;
        redirect($gift_address_url);
        return true;
    }

    //ip重复判断
    private function ip_repeat($ip_repeat_num, $user_ip, $pid, $partner_uid)
    {
        if(!$user_ip || !$pid || !$partner_uid){ return false;}
        $app_project_implement = 'app_project_implement_'.$pid;
        $res_ip_repeat_num = $this->db->where(array('ip'=>$user_ip))->get($app_project_implement)->num_rows();
        if(!empty($ip_repeat_num)){
            if($res_ip_repeat_num > $ip_repeat_num){
                //取出有效的记录，不在这些有效记录的，都是无效记录
                $effective_ip = $this->db->query("SELECT partner_uid FROM {$app_project_implement} WHERE ip=? ORDER BY id ASC LIMIT {$ip_repeat_num}", [$user_ip])->result_array();
                $partner_uids = $effective_ip ? array_column($effective_ip, "partner_uid", "partner_uid") : [];
                if (in_array($partner_uid, $partner_uids)) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return true;
    }

    //限制IP段，允许国家、省份、城市
    public function check_country_province_city($permit_country, $ip_addr)
    {
        if(!$ip_addr || !$permit_country ) { return false;}
        $str_country_ex = explode(',',$permit_country);
        foreach ($str_country_ex as $v_c){
            $str = strpos($ip_addr,$v_c);
            if($str !== false){
                return true;
            }
        }
        return false;
    }

    // 限制ip段 ($ip_limit,$local_ip);
    public function ip_limit($ip_limit, $local_ip)
    {
        if(!$local_ip || !$ip_limit) { return false;}
        $str_limit_ex = explode(',',$ip_limit);
        foreach ($str_limit_ex as $v_l){
            $str = strpos($local_ip,$v_l);
            if($str !== false){
                return true;
            }
        }
        return false;
    }

    //统计项目配额C的完成量
    public function count_project_quta_survey_c($pid){
        if (!$pid) {return false;}
        $app_project_implement = 'app_project_implement_'.$pid;
        //获取项目下的配额信息
        $count_project_quta_survey_c = $this->afunctions->select_table('app_project_quta',array('pid'=>$pid));

        $quta_survey = $this->check_answer_quta_survey_c($count_project_quta_survey_c, $pid);

        foreach ($quta_survey as $k_where => $v_where){
            $project_implement_info_where = $this->db->query( " select count(*) as survey_c_count from {$app_project_implement} where {$v_where} and finish_status = 'c' " )->row_array();
            $project_implement_info_other = $this->db->query( " select count(*) as sample_count from {$app_project_implement} where {$v_where}  " )->row_array();
            $project_implement_info_click_count = $this->db->query( " select count(*) as click_count from {$app_project_implement} where {$v_where}   and finish_status !=''" )->row_array();
            $cond_where = " id = {$k_where}";
            $data_quta['now_c_mount'] = $project_implement_info_where['survey_c_count']; // 该配额已收集的C数量
            $data_quta['all_sample_mount'] = $project_implement_info_other['sample_count']; // 样本总数量
            $data_quta['click_mount'] = $project_implement_info_click_count['click_count']; // 参与总数量
            $this->afunctions->update_table_up('app_project_quta', $cond_where, $data_quta);
        }

        return true;
    }

    //判断是否符合条件的函数
    private function check_answer_quta_survey_c($quta_base_data,$pid)
    {
        if(!$quta_base_data || !$pid) {return false;}
        //需要过滤的数据
        $answer_filter = array(
            "name",
            "mobile",
            "email",
            "id",
            "pid",
            "condition",
            "mount",
            "edit_id",
            "add_time",
            "quta_status",
            "now_c_mount",
            "all_sample_mount",
            "click_mount",
        );

        $app_project_quta_setting =  $this->afunctions->select_table_find('app_project',array('id'=>$pid));
        $arr_quta_setting = json_decode($app_project_quta_setting['quta_setting'],true);
        $quta_setting = array_diff($arr_quta_setting, $answer_filter); // 查询项目配额

        //项目配额答案
        $res_quta_base_data = array();
        foreach ($quta_base_data as $k => $v) {
            $rs_quta_data = $v;
            foreach ($answer_filter as $v_filter) {
                unset($rs_quta_data[$v_filter]);
            }
            if ($v['new_attribute']) {//拆分自定义参数
                $rs_new_attribute = json_decode($v['new_attribute'], true);
                $rs_quta_data = array_merge($rs_quta_data, $rs_new_attribute);
                unset($rs_quta_data['new_attribute']);
            }else{
                unset($rs_quta_data['new_attribute']);
            }
            $rs_quta_data_info = array();
            if ($rs_quta_data) {
                foreach ($rs_quta_data as $k_quta_data => $v_quta_data) {
                    if(in_array($k_quta_data,$quta_setting)){ // 项目配额和project_quta比较配额字段是否都存在
                        if(!empty($v_quta_data)){
                            $rs_quta_data_info[] = $k_quta_data."='".$v_quta_data."'";
                        }
                    }
                }
            }
            if ($rs_quta_data_info) {
                $res_quta_base_data[$v['id']] = implode(" AND ", $rs_quta_data_info);
            }
        }
        return $res_quta_base_data;
    }


    #####   短信验证流程  #####
    //当项目pay_money=1时，先跳转到短信验证页面，验证通过了再跳转到问卷链接中
    public function pro_send_sms()
    {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $scene = $post_data['scene'];
            $verify_mobile = $post_data['verify_mobile'];
            try {
                $country = 68;
                $arr_scene = explode("_", $scene);
                $pid = $arr_scene[0];
                $survey_uid = $arr_scene[1];
                $encrypted_data = $arr_scene[2];
                if (!$pid || !$survey_uid || !$encrypted_data || count($arr_scene) != 3) {//参数有误
                    throw new Exception("参数有误1！");
                }
                $decrypt_scene = $pid.$survey_uid;
                $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
                if ($encrypted_data !== $decrypt_data) {//解密值不等
                    throw new Exception("参数有误2！");
                }
                //member_uid与pid，查询项目执行表中，是否存在此用户信息
                $check_implement = getDataByConditionCi("app_project_implement_{$pid}", " AND survey_uid=? AND finish_status=''","*", true, [$survey_uid]);
                if (!$check_implement) {
                    throw new Exception("用户信息不存在！");
                }
                if (!check_mobile($check_implement['mobile'])) {
                    throw new Exception("手机号有误，请确认后重新输入！[项目]");
                }
                if ($check_implement['mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                //发送短信验证码
                $vcode = rand(10000,99999);
                $sms_log_code = SMS_LOG_CODE_PRO_PAY_MONEY;
                $content = $lang_ary[LABEL_COMM_MOB_TEMPLATE];//国际短信通道使用
                $err_msg = "";
                if ($country == '68') {//中国短信，使用send_cloud发送
                    //创蓝
                    if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") {//本地
                        $st = true;
                    } else {
                        $sms_content = "上医说验证码:{$vcode}，".SMS_ACTIVE_TIME."分钟内有效，请勿泄漏！";//国际短信通道使用
                        $st = chuanglan_single_sms(SMS_LOG_CODE_PRO_PAY_MONEY, $verify_mobile, $sms_content);
                    }
                    if ($st === true) {
                        $send_st = true;
                    } else {
                        $send_st = false;
                        $err_msg = $st;
                    }
                }else{//国外短信，使用国际短信通道
                    $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                    $area = getDataByID("app_sys_dictionary", $country);
                    $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                    if ($st == "OK") {
                        $send_st = true;
                    }else{
                        $send_st = false;
                    }
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_LOGIN;
                if($send_st) {//短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);
                    if($insert_id_sms_code > 0) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    public function sub_pro_pay_money() {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $scene = $post_data['scene'];
            $verify_mobile = $post_data['verify_mobile'];
            //验证数据有效性
            $verify_code = $post_data['verify_code'];
            try {
                $country = 68;
                $arr_scene = explode("_", $scene);
                $pid = $arr_scene[0];
                $survey_uid = $arr_scene[1];
                $encrypted_data = $arr_scene[2];
                if (!$pid || !$survey_uid || !$encrypted_data || count($arr_scene) != 3) {//参数有误
                    throw new Exception("参数有误！");
                }
                $decrypt_scene = $pid.$survey_uid;
                $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
                if ($encrypted_data !== $decrypt_data) {//解密值不等
                    throw new Exception("参数有误！");
                }
                //member_uid与pid，查询项目执行表中，是否存在此用户信息
                $check_implement = getDataByConditionCi("app_project_implement_{$pid}", " AND survey_uid=? AND finish_status=''","*", true, [$survey_uid]);
                if (!$check_implement) {
                    throw new Exception("用户信息不存在！");
                }
                if (!check_mobile($check_implement['mobile'])) {
                    throw new Exception("手机号有误，请确认后重新输入！[项目]");
                }
                if ($check_implement['mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_LOGIN);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                    throw new Exception($lang_ary[LABEL_EXCHANGE_VERIFICATION_CODE_ERROR]);
                }
                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                //验证通过，跳转到问卷链接页
                $link_info = $this->db->query("SELECT * FROM app_project_s_link WHERE pid=? AND survey_uid=?", [$pid, $survey_uid])->row_array();
                if (!$link_info) {
                    $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "p", "", "", "", false);
                } else {
                    $redirect_url = $link_info['survey_link'];
                }
                //存session，方便从短信或者问卷链接进入时，直接跳过验证
                $this->session->set_userdata(["pro_money_{$pid}_{$check_implement['id']}" => 1]);
                _back_msg("success", "验证通过", $redirect_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $survey_uid_code = $this->uri->segment(3);
            if (!$survey_uid_code) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            }
            //验证账号是否有误
            $arr_survey_uid = explode("_", $survey_uid_code);
            $pid = $arr_survey_uid[0];
            $survey_uid = $arr_survey_uid[1];
            $survey_uid_encryption = $arr_survey_uid[2];
            $local_survey_uid = substr(md5($pid.$survey_uid . PROJECT_ENCODE_KEY), 8, 6);
            if (!$pid || !$survey_uid || $local_survey_uid != $survey_uid_encryption) {
                $this->survey_model->get_redirect_info("/bk/rs", "l_err");
            }

            //根据survey_uid获取与项目编号获取session的值是否存在，如果存在，直接跳转到问卷链接
            $pro_imp_info = $this->db->query("SELECT * FROM app_project_implement_{$pid} WHERE survey_uid=?", [$survey_uid])->row_array();
            if ($pro_imp_info) {
                $m_id = $pro_imp_info['id'];
                if (isset($this->session->userdata["pro_money_{$pid}_{$m_id}"]) && $this->session->userdata["pro_money_{$pid}_{$m_id}"] == 1) {
                    //验证通过，跳转到问卷链接页
                    $link_info = $this->db->query("SELECT * FROM app_project_s_link WHERE pid=? AND survey_uid=?", [$pid, $survey_uid])->row_array();
                    if ($link_info) {
                        redirect($link_info['survey_link']);
                    }
                }
            }

            $data = array(
                "scene" => $survey_uid_code,
            );
            $this->load->view("/go/send_sms",$data);
        }
    }

    ### 2248项目专用
    //进入问卷前的短信验证
    public function go_survey_sms()
    {
        $pid = 2248;
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $verify_mobile = $post_data['verify_mobile'];
            ####    AMY 2020-11-04 对别的项目开放 ####
            $code = $post_data['code'];
            if ($code) {
                $arr_code = explode("_", $code);
                $local_code = substr(md5($arr_code[0] . PROJECT_ENCODE_KEY), 8, 6);
                if ($local_code == $arr_code[1]) {
                    $pid = $arr_code[0];
                }
            }
            ####    AMY 2020-11-04 对别的项目开放 ####
            try {
                $country = 68;
                //member_uid与pid，查询项目执行表中，是否存在此用户信息
                $check_implement = getDataByConditionCi("app_project_implement_{$pid}", " AND mobile=?","*", true, [$verify_mobile]);
                if (!$check_implement) {
                    throw new Exception("用户信息不存在！");
                }
                if ($check_implement['finish_status'] != "") {
                    throw new Exception("已经完成问卷，不能重复登录！");
                }

                //发送短信验证码
                $vcode = rand(10000,99999);
                $sms_log_code = SMS_LOG_CODE_PRO_GO_SURVEY;
                $content = $lang_ary[LABEL_COMM_MOB_TEMPLATE];//国际短信通道使用
                $err_msg = "";
                if ($country == '68') {//中国短信，使用send_cloud发送
                    //创蓝
                    if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") {//本地
                        $st = true;
                    } else {
                        $sms_content = "上医说验证码:{$vcode}，".SMS_ACTIVE_TIME."分钟内有效，请勿泄漏！";//国际短信通道使用
                        $st = chuanglan_single_sms(SMS_LOG_CODE_PRO_PAY_MONEY, $verify_mobile, $sms_content);
                    }
                    if ($st === true) {
                        $send_st = true;
                    } else {
                        $send_st = false;
                        $err_msg = $st;
                    }
                }else{//国外短信，使用国际短信通道
                    $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                    $area = getDataByID("app_sys_dictionary", $country);
                    $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                    if ($st == "OK") {
                        $send_st = true;
                    }else{
                        $send_st = false;
                    }
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_LOGIN;
                if($send_st) {//短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);
                    if($insert_id_sms_code > 0) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    //登录后进入问卷链接【2248项目专用】
    public function go_survey() {
        $code = $this->input->get("code", true);
        $pid = 2248;
        if ($code) {
            $arr_code = explode("_", $code);
            $local_code = substr(md5($arr_code[0] . PROJECT_ENCODE_KEY), 8, 6);
            if ($local_code == $arr_code[1]) {
                $pid = $arr_code[0];
            }
        }
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $verify_mobile = $post_data['verify_mobile'];
            //验证数据有效性
            $verify_code = $post_data['verify_code'];
            ####    AMY 2020-11-04 对别的项目开放 ####
            $code = $post_data['code'];
            if ($code) {
                $arr_code = explode("_", $code);
                $local_code = substr(md5($arr_code[0] . PROJECT_ENCODE_KEY), 8, 6);
                if ($local_code == $arr_code[1]) {
                    $pid = $arr_code[0];
                }
            }
            ####    AMY 2020-11-04 对别的项目开放 ####
            try {
                //member_uid与pid，查询项目执行表中，是否存在此用户信息
                $check_implement = getDataByConditionCi("app_project_implement_{$pid}", " AND mobile=?","*", true, [$verify_mobile]);
                if (!$check_implement) {
                    throw new Exception("用户信息不存在！");
                }
                if ($check_implement['finish_status'] != "") {
                    throw new Exception("已经完成问卷，不能重复登录！");
                }

                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_LOGIN);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                    throw new Exception("验证码错误");
                }
                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                //获取问卷链接
                $uid_str = project_link_encode($pid, $check_implement['partner_id'], $check_implement['groupno']);
                $partner_link = DRSAY_WEB . 'go/s/' . $uid_str . '?' . $check_implement['partner_uid'];
                //存session，方便从短信或者问卷链接进入时，直接跳过验证
                $this->session->set_userdata(["pro_money_{$pid}_{$check_implement['id']}" => 1]);
                _back_msg("success", "验证通过", $partner_link);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $data = array("code" => $code);
            $this->load->view("/go/go_survey",$data);
        }
    }
    #### 2248项目专用

    //隐私协议
    public function privacy_policy()
    {
        $this->load->view("/go/privacy_policy");
    }

    //跳转到短信验证页
    private function redirect_to_verify($pid, $survey_uid)
    {
        if (!$pid || !$survey_uid) {return false;}
        $survey_uid_md5 = substr(md5($pid.$survey_uid . PROJECT_ENCODE_KEY), 8, 6);
        $survey_uid_str = $pid."_".$survey_uid."_".$survey_uid_md5;
        $gift_address_url = "/go/sub_pro_pay_money/".$survey_uid_str;
        redirect($gift_address_url);
        return true;
    }
    #####   短信验证流程  #####

    ############## 公共方法 ##############
    // 保存验证码数据
    private function set_vcode($data)
    {
        if (!$data) {return false;}
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }

    /**
     * @param $property
     * @param $member_uid
     * 更新活跃医师
     */
    private function member_active_status($property,$member_uid){
        if($property == VENDOR_INTERNAL) {//内部资源
            if($member_uid > 0){
                $member_id = intval($member_uid);
                if(!empty($member_id)){
                    $this->db->where(['id'=>$member_id,'active_status'=>0])->update('app_member',['active_status'=>MEMBER_ACTIVE_STATUS]);
                }
            }
        }
    }
}
