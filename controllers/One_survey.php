<?php
/**
 * Created by PhpStorm.
 * 用途：一次性的问卷
 * User: Amy
 * Date: 2018/12/26
 */
class One_survey extends CI_Controller
{
    public $first_q;
    public $survey_info;
    function __construct()
    {
        parent::__construct();
//        error_reporting(-1);
//        ini_set('display_errors', 1);

        //第一题
        $this->first_q = [
            "title" => "请选择你院使用过的药品",
            "bg_color" => "#4472c4",
            "input_type" => "checkbox",
            "option" => [
                "1" => "q2",
                "2" => "q3",
                "3" => "q4",
                "4" => "q5",
                "5" => "q6",
                "6" => "q7",
                "7" => "q8",
                "8" => "q9",
            ],
        ];
        $this->survey_info = [
            "q1" => [
                "title" => "N类神经系统类药物(胶囊和口服片剂) 总销售额",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "总销售额",
                ],
            ],
            "q2" => [
                "title" => "百忧解 - 盐酸氟西汀胶囊 - 礼来",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "20mg*7粒",
                    "2" => "20mg*28片",
                ],
            ],
            "q3" => [
                "title" => "来士普 - 草酸艾司西酞普兰片 - 西安杨森",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "5mg*14片",
                    "2" => "5mg*28片",
                    "3" => "10mg*7片",
                    "4" => "10mg*28片",
                    "5" => "20mg*7片",
                    "6" => "20mg*14片",
                ],
            ],
            "q4" => [
                "title" => "乐友 - 盐酸帕罗西汀片 - 浙江华海",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "20mg*10片*1板",
                    "2" => "20mg*10片*2板",
                    "3" => "20mg*10片*3板",
                    "4" => "20mg*7片*1板",
                    "5" => "20mg*7片*2板",
                ],
            ],
            "q5" => [
                "title" => "律康 - 枸橼酸坦度螺酮胶囊 - 四川科瑞德",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "5mg*16粒",
                    "2" => "5mg*24粒",
                    "3" => "10mg*24粒",
                ],
            ],
            "q6" => [
                "title" => "赛乐特 - 盐酸帕罗西汀片 - 中美史克",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "20mg*7片",
                    "2" => "20mg*10片",
                ],
            ],
            "q7" => [
                "title" => "希德 - 枸橼酸坦度螺酮片 - 住友",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "10mg*21*1板",
                    "2" => "10mg*21*2板",
                ],
            ],
            "q8" => [
                "title" => "怡诺思 - 盐酸文拉法辛缓释胶囊 - 惠氏",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "75mg*7*2板",
                    "2" => "150mg*7*2板",
                ],
            ],
            "q9" => [
                "title" => "左洛复 - 盐酸舍曲林片 - 辉瑞",
                "bg_color" => "#4472c4",
                "start_year" => 2010,
                "end_year" => 2018,
                "option" => [
                    "1" => "50mg*14片",
                ],
            ],
        ];
    }

    //关于精神疾病领域的用药情况的调查
    public function survey_spirit()
    {
        $uid = $this->input->get("uid", true);
        $uid = trim($uid);
        if (!$uid) {
            die("Parameter error！");
        }
        $hospital_arr = ['江北区中马街道社区卫生服务中心','杭州市江干区四季青街道社区卫生服务中心','越秀区北京街社区卫生服务中心','南京市鼓楼区中央门社区卫生服务中心','宁陵县张弓集团有限公司职工医院(社区服务站)','海淀区燕园社区卫生服务中心','延庆县张山营镇社区卫生服务中心','北京市朝阳区八里庄社区卫生服务中心','北京市西城区首都医科大学附属复兴医院月坛社区卫生服务中心','北京市东城区东直门街道东直门社区卫生服务站','南京市六合区马鞍镇社区卫生服务中心','杭州市西湖区西溪街道社区卫生服务中心','胜中康健社区卫生服务中心','南京市下关区建宁路社区卫生服务中心','杭州市下城区天水武林街道社区卫生服务中心','北京市石景山区五里坨街道社区卫生服务中心','南京市玄武区新街口社区卫生服务中心','杭州市西湖区北山街道社区卫生服务中心','杭州市拱墅区和睦街道社区卫生服务中心','北京市西城区展览路社区卫生服务中心','东莞市寮步镇社区卫生服务中心','海淀区清河街道社会福利社区卫生服务中心','北京市海淀区永定路社区卫生服务中心','杭州市上城区南星街道社区卫生服务中心','北京市朝阳区垡头社区卫生服务中心','北京市海淀区万寿路社区卫生服务中心','南京市六合区山潘街道社区卫生服务中心','佛山市顺德区龙江社区卫生服务中心','北京市朝阳区潘家园第二社区卫生服务中心','上海市闵行区浦江社区卫生服务中心','历城区花山镇电建二公司社区卫生服务中心','杭州市西湖区灵隐街道社区卫生服务中心','北京市丰台区马家堡街道瑞丽江畔社区卫生服务站','北京市朝阳区奥运村社区卫生服务中心','北京市朝阳区团结湖社区卫生服务中心','广州市海珠区江海街社区卫生服务中心','北京市朝阳区首都医科大学附属北京安贞医院大屯社区卫生服务中心','大连甘井子兴华金南路社区卫生服务中心','南京市秦淮区中华路社区卫生服务中心','北京市朝阳区双井社区卫生服务中心','北京市朝阳区东湖社区卫生服务中心','杭州市上城区紫阳街道社区卫生服务中心','北京邮电大学社区卫生服务中心','杭州市上城区望江街道社区卫生服务中心','常州市新北区三井街道社区卫生服务中心','中国人民大学社区卫生服务中心','南京市下关区热河南路街道社区卫生服务中心','杭州市拱墅区大关上塘街道社区卫生服务中心','上海市虹口区凉城街道社区卫生服务中心','聊城市东昌府区新区街道社区卫生服务中心','杭州市西湖区三墩镇社区卫生服务中心','杭州市滨江区浦沿街道社区卫生服务中心','杭州市上城区小营街道社区卫生服务中心','白云区同和街社区卫生服务中心','杭州市西湖区袁浦社区卫生服务中心','北京市朝阳区双井第二社区卫生服务中心','北京市朝阳区六里屯社区卫生服务中心','潮州市聚和园社区卫生服务站','北京大学医学部社区卫生服务中心','上海市徐汇区虹梅街道社区卫生服务中心','上海市闵行区梅陇社区卫生服务中心','南京市秦淮区红花社区卫生服务中心','北京市朝阳区八里庄第二社区卫生服务中心','东莞市横沥镇社区卫生服务中心','朝阳区和平街社区服务中心','西安市莲湖区北关社区卫生服务中心','北京市朝阳区安贞社区卫生服务中心','乐清市北白象镇第一社区卫生服务中心','江阴市华士社区卫生服务中心','北京市朝阳区十八里店社区卫生服务中心','昆明市盘龙区拓东街道社区服务中心','杭州市余杭区运河镇社区卫生服务中心','北京市朝阳区南磨房社区卫生服务中心','常熟市虞山镇兴隆社区卫生服务中心','南京市白下区淮海路社区卫生服务中心','杭州市萧山区爱心社区卫生服务中心','黄埔区红山街社区卫生服务中心','兵器工业北京北方医院紫竹院社区卫生服务中心','上海市松江区泖港镇社区卫生服务中心','南开区广开街社区卫生服务中心','上海市虹口区曲阳路街道社区卫生服务中心','宁波市海曙区西门望春社区卫生服务中心','北京市丰台区西罗园街道角门东里第二社区卫生服务站','南京市秦淮区双塘街道许家巷社区卫生服务中心','厦门市思明区鼓浪屿街道社区卫生服务中心','瑶海区红光街道社区卫生服务中心','深圳市罗湖医院集团东门街道社区健康服务中心','天河区五山街华农社区卫生服务中心','北京市西城区新街口社区卫生服务中心','上海市崇明县陈家镇社区卫生服务中心','北京市朝阳区左家庄社区卫生服务中心','北京市朝阳区三里屯社区卫生服务中心','北京市朝阳区太阳宫社区卫生服务中心','北京市海淀区海淀乡万柳社区卫生服务站','杭州市江干区丁兰街道社区卫生服务中心','福州市台江区洋中街道玉树社区卫生服务站','北京市朝阳区东风社区卫生服务中心','杭州市余杭区乔司街道社区卫生服务中心','盐城市大丰区大中社区卫生服务中心','北京市朝阳区常营社区卫生服务中心','佛山市顺德区乐从社区卫生服务中心','上海市卢湾区五里桥街道社区卫生服务中心','北京市海淀区蓟门里社区卫生服务中心','杭州市余杭区径山镇社区卫生服务中心','南京市雨花台区西善桥社区卫生服务中心','深圳市罗湖医院集团翠岭社区健康服务中心','南京市鼓楼区模范西路社区卫生服务中心','海口市人民医院龙昆南社区卫生服务中心','闽清县梅城社区卫生服务中心','成都市青羊区苏坡社区卫生服务中心','宁波市社区福利中心医院','厦门市思明区莲前街道社区卫生服务中心','西湖区蒋村文新地段社区卫生服务中心','重庆市江北区寸滩社区卫生服务中心','北京市朝阳区香河园社区卫生服务中心','杭州市西湖区翠苑街道社区卫生服务中心','北京外国语大学社区卫生服务中心','重庆市沙坪坝区天星桥社区卫生服务中心','上海市浦东新区迎博社区卫生服务中心','石河子市15小区社区卫生服务站','厦门市第一医院厦港社区医疗服务中心','瑞安市莘塍街道社区卫生服务中心','杭州市拱墅区祥符街道社区卫生服务中心','杭州市上城区清波街道社区卫生服务中心','南京市玄武区熊猫集团社区卫生服务中心','天河区五山街华工社区卫生服务中心','上海市虹口区江湾镇街道社区卫生服务中心','徐州市泉山区西苑社区卫生服务中心','上海市闵行区莘庄社区卫生服务中心','北京市丰台区东铁营街道南方庄社区卫生服务站','江阴市长泾社区卫生服务中心','天津市滨海新区汉沽街社区卫生服务中心','南京市白下区石门坎社区卫生服务中心','上海市徐汇区长桥街道社区卫生服务中心','上海市闵行区新虹社区卫生服务中心','北京市海淀区学院路社区卫生服务中心','北京市房山区阎村镇社区卫生服务中心','常州市兰陵街道劳动新村第二社区卫生服务站','北京市海淀区甘家口街道航天工业社区卫生服务站','宁波市鄞州区中河街道社区卫生服务中心','石家庄市桥西区彭后棉七社区卫生服务中心','南京白下区朝天宫社区卫生服务中心','南京市雨花台区赛虹桥社区卫生服务中心','上海市松江区叶榭镇社区卫生服务中心','鼓楼区山西路社区卫生服务中心','北京市朝阳区东坝社区卫生服务中心','苏州市相城区黄桥街道社区卫生服务中心','上海市黄浦区打浦桥街道社区卫生服务中心','无锡市惠山区前洲街道社区卫生服务中心','上海市闵行区七宝社区卫生服务中心','宁波市海曙区鄞江镇社区卫生服务中心','北京市石景山区八宝山社区卫生服务中心','江阴市月城社区卫生服务中心','北京市朝阳区呼家楼第二社区卫生服务中心','越秀区大东街社区卫生服务中心','上海市宝山区淞南镇社区卫生服务中心','福州市台江区洋中街道社区卫生服务中心','慈溪市城区社区卫生服务中心','南京市白下区止马营社区卫生服务中心','北京市朝阳区望京街道望京西园四区社区卫生服务站','北京市朝阳区小红门社区卫生服务中心','上海市闵行区颛桥社区卫生服务中心','北京市海淀区永定路街道四街坊社区卫生服务站','上海市虹口区四川北路街道社区卫生服务中心','上海市宝山区大场镇社区卫生服务中心','苏州市立医院二院社区','江阴市祝塘社区卫生服务中心','杭州市江干区彭埠镇社区卫生服务中心普福服务站','珠海高栏港经济区平沙社区卫生服务中心','北京市朝阳区双井街道广泉社区卫生服务站','温州市瓯海区瞿溪街道社区卫生服务中心','市北区阜新路街道健民社区卫生服务中心','南京市雨花台区铁心桥街道社区卫生服务中心','杭州市拱墅区米市巷街道社区卫生服务中心','上海市普陀区石泉街道社区卫生服务中心','珠海市香洲区梅华社区卫生服务中心','上海市宝山区大场镇祁连社区卫生服务中心','苏州市相城区北桥街道社区卫生服务中心','上海市崇明县竖新镇社区卫生服务中心','北京市朝阳区三间房社区卫生服务中心','张店区公园街道王府井社区卫生服务站','杭州市下城区东新街道社区卫生服务中心','上海市虹口区广中路街道社区卫生服务中心','蜀山区稻香村街道社区卫生服务中心','厦门市集美区集美街道社区卫生服务中心','上海市徐汇区华泾镇社区卫生服务中心','成都市武侯区望江路社区卫生服务中心','萧山区开发区社区卫生服务中心','上海市杨浦区五角场社区卫生服务中心','成都市锦江区莲新社区卫生服务中心','义乌市后宅街道社区卫生服务中心','温州市鹿城区莲池街道社区卫生服务中心','南京市白下区蓝旗社区卫生服务中心','南京市鼓楼区江东街道龙江社区卫生服务中心','北京市顺义区南法信社区卫生服务中心','上海市虹口区提篮桥街道社区卫生服务中心','嘉兴市南湖区城南街道社区卫生服务中心','上海市松江区中山街道社区卫生服务中心','上海市黄浦区老西门街道社区卫生服务中心','北京市朝阳区平房社区卫生服务中心','成都市武侯区火车南站社区卫生服务中心','上海市松江区泗泾镇社区卫生服务中心','重庆市南岸区南山街道社区卫生服务服务中心','杭州市西湖区周浦社区卫生服务中心','上海市闵行区吴泾社区卫生服务中心','衢州市柯城区衢化社区卫生服务中心','南京市下关区大桥社区卫生服务中心','南京市雨花台区板桥社区卫生服务中心','上海市松江区九亭镇社区卫生服务中心','衢州市柯城区府山街道社区卫生服务中心','南京市白下区尚书社区卫生服务站','北京市海淀区东升镇社区卫生服务中心','北京市西城区白纸坊社区卫生服务中心','厦门市第一医院滨海社区医疗服务中心','北京市海淀区双榆树社区卫生服务中心','上海市宝山区高境镇社区卫生服务中心','上海市闵行区龙柏社区卫生服务中心','成都市锦江区龙舟路社区卫生服务中心','上海市黄浦区半淞园街道社区卫生服务中心','衢州市柯城区衢化街道昌苑社区卫生服务站','杭州市西湖区古荡街道社区卫生服务中心','崇明县堡镇社区卫生服务中心','上海市崇明县新河镇社区卫生服务中心','苏州工业园区车坊社区卫生服务中心','上海市罗店镇社区卫生服务中心','吉州区古南街道社区卫生服务中心','上海市徐汇区天平街道社区卫生服务中心','杭州市滨江区长河街道社区卫生服务中心','渭南市临渭区杜桥办社区卫生服务中心','宁波市奉化区溪口镇社区卫生服务中心','杭州市滨江区西兴街道社区卫生服务中心','宁波市北仑区大矸街道社区卫生服务中心','北京市丰台区大红门街道木材厂社区卫生服务站','北京市平谷区金海湖镇社区卫生服务中心','常州市天宁区茶山街道朝阳三村社区卫生服务站','北京市朝阳区小关街道惠新苑社区卫生服务站','宁波市鄞州区洞桥镇洞桥社区卫生服务站','上海市普陀区长征镇社区卫生服务中心','江岸区花桥街第二社区卫生服务中心','太仓市城厢镇社区卫生服务中心','天津市津南区天同社区卫生服务中心','北京市东城区东花市社区卫生服务中心','北京市顺义区杨镇社区卫生服务中心','舟山市普陀区东港街道社区卫生服务中心','上海市奉贤区南桥镇社区卫生服务中心','徐汇区斜土街道社区卫生服务中心','上海市嘉定工业区社区卫生服务中心','永嘉县桥头镇社区卫生服务中心','北京市海淀区北下关街道青云社区卫生服务站','上海市宝山区张庙街道长江路社区卫生服务中心','枫林街道社区卫生服务中心','宁波市鄞州区集士港镇社区卫生服务中心','杭州市余杭区仓前街道社区卫生服务中心','北京市朝阳区管庄第二社区卫生服务中心','上海市杨浦区延吉社区卫生服务中心','北京市朝阳区来广营社区卫生服务中心','北京航空航天大学社区卫生服务中心','上海市黄浦区瑞金二路街道社区卫生服务中心','宁波市镇海区招宝山街道社区卫生服务中心','常州市天宁区红梅街道锦绣社区卫生服务站','五山街东莞庄社区卫生服务站','北京市朝阳区望京社区卫生服务中心','上海市宝山区顾村镇社区卫生服务中心','福州市长乐市航城街道社区卫生服务中心','深圳市罗湖医院集团湖景社区健康服务中心','昆明市丰宁社区卫生服务中心','萧山区戴村镇社区卫生服务中心','宁波市海曙区段塘街道社区卫生服务中心','无锡市锡山区锡北镇八士通富社区卫生服务站','上海市杨浦区五角场镇社区卫生服务中心','北京市朝阳区崔各庄社区卫生服务中心','上海市奉贤区四团镇平安社区卫生服务中心','西安市莲湖区环西西站社区卫生服务站','重庆市九龙坡区谢家湾社区服务中心','南京市栖霞区西岗社区卫生服务中心','成都高新区桂溪社区卫生服务中心','烟台市莱山区黄海路街道社区卫生服务中心','杭州市萧山区城厢街道藕湖浜社区卫生服务站','宁德市蕉城区蕉南社区卫生服务中心','涧西区天津路社区卫生服务中心','上海市闵行区虹桥社区卫生服务中心','长沙市芙蓉区五里牌街道社区卫生服务中心','涧西区长安路社区卫生服务中心','上海市松江区洞泾镇社区卫生服务中心','扬州市广陵区文峰街道社区卫生服务中心','新疆生产建设兵团第五师社区卫生服务中心','上海市徐汇区田林街道社区卫生服务中心','上海市崇明县三星镇社区卫生服务中心','杭州市江干区笕桥街道社区卫生服务中心','张家口市桥西区明德南社区卫生服务中心','北京市大兴区旧宫新苑社区卫生服务中心','南京市秦淮区月牙湖社区卫生服务中心','天津河东盛世众康社区卫生服务中心','深圳市罗湖医院集团笋岗社区健康服务中心','仓山区仓前社区卫生服务中心','常州市天宁区天宁街道舣舟亭社区卫生服务站','上海市长宁区华阳街道社区卫生服务中心','北京市海淀区学院路街道建清园社区卫生服务站','烟台市芝罘区奇山街道奇中社区卫生服务站','杭州市上城区湖滨街道社区卫生服务中心','绍兴市上虞区百官街道社区卫生服务中心','深圳市南山区蛇口人民医院东角头社区健康服务中心','漯河市源汇区马路街社区卫生服务中心','上海市杨浦区殷行社区卫生服务中心','黄石胜阳港社区卫生服务中心','北京市丰台区马家堡街道城南嘉园社区卫生服务站','石河子人民医院社区卫生服务中心1小区社区卫生服务站','济南市槐荫区南辛庄办事处社区卫生服务中心','北京市朝阳区高碑店社区卫生服务中心','济南市天桥区泺口办事处新城社区卫生服务站','邢台市卫生街社区卫生服务站','杭州市下城区石桥街道社区卫生服务中心','福州市鼓楼区东街街道大根社区卫生服务站','常州市天宁区青龙街道社区卫生服务中心','北京市朝阳区黑庄户社区卫生服务中心','杭州市余杭区南苑街道社区卫生服务中心','上海市黄浦区小东门街道社区卫生服务中心','定海区城市社区卫生服务中心','石家庄市长安区广安街道办事处社区卫生服务中心','东阳市六石街道社区卫生服务中心','梁园区中州社区卫生服务中心','中国地质大学(北京)社区卫生服务中心','思茅区思茅镇五一路社区卫生服务站','莱山区初家街道社区卫生服务中心','上海市虹口区嘉兴路社区卫生服务中心','烟台市芝罘区白石街道青华社区卫生服务站','上海市嘉定区徐行镇社区卫生服务中心','常州市天宁区兰陵街道劳动新村社区卫生服务站','成都市武侯区浆洗街社区卫生服务中心','苏州市相城区元和街道社区卫生服务中心','长洲街黄船社区卫生服务站','中国石化集团胜利石油管理局胜中康宁社区卫生服务中心','北京丰台区太平桥街道太平桥社区卫生服务站','瑶海区和平路街道社区卫生服务中心','烟台市芝罘区奇山街道四眼桥社区卫生服务站','洪山区梨园街社区卫生服务中心东湖社区卫生服务站','重庆市南岸区弹子石街道社区卫生服务中心','北京市海淀区温泉镇社区卫生服务中心','上海市普陀区甘泉社区卫生服务中心','城中社区卫生服务中心','石家庄市长安区谈固街道办事处社区卫生服务中心','上海市崇明县向化镇社区卫生服务中心','南京市雨花台区岱山社区卫生服务中心','杭州市余杭区东湖街道社区卫生服务中心','杭州市余杭区百丈镇社区卫生服务中心','常州市天宁区茶山街道朝阳一村社区卫生服务站','上海市闵行区华漕社区卫生服务中心','北京市密云区西田各庄镇社区卫生服务中心','珠海市香洲区狮山社区卫生服务中心','深圳市福田区竹园社区健康服务中心','杭州市下城区文晖街道社区卫生服务中心','桥西区电厂东居委会社区卫生服务站','柳州市鱼峰区麒麟社区卫生服务中心','上海市杨浦区四平社区卫生服务中心','金台区石油社区卫生服务站','成都市武侯区晋阳社区卫生服务中心','北京市西城区广内社区卫生服务中心','市北区洛阳路街道前哨社区卫生服务站','常州市天宁区天宁街道怡康花园社区卫生服务站','深圳市罗湖医院集团仙湖社区健康服务中心','重庆市九龙坡区铁马社区卫生服务中心','焦作市解放区七百间社区卫生服务中心','上海市杨浦区定海社区卫生服务中心','上海市闸北区彭浦新村街道社区卫生服务中心','上海市杨浦区大桥社区卫生服务中心','淮安市运河公司社区卫生服务中心','北京市房山区琉璃河镇社区卫生服务中心','上海市松江区佘山镇社区卫生服务中心','杭州市江干区彭埠街道社区卫生服务中心','上海市松江区石湖荡镇社区卫生服务中心','北京市海淀区羊坊店社区卫生服务中心','李沧区虎山路街道社区卫生服务中心','南京市鼓楼区挹江门社区卫生服务中心','成都市锦江区成龙路社区卫生服务中心','新华区天苑社区卫生服务中心','上海市徐汇区龙华街道社区卫生服务中心','常州市天宁区天宁街道古村社区卫生服务站','福州市马尾区罗星街道罗星社区卫生服务中心','无锡市南长区槐古桥社区卫生服务站','上海市奉贤区南桥镇西渡社区卫生服务中心','南阳市靳岗社区卫生服务中心','广州市越秀区农林街社区卫生服务中心','秦皇岛市海港区建设大街社区卫生服务中心','崔各庄社区卫生服务中心京旺部','太原市南寨社区卫生服务中心','广州市海珠区南石头街石岗社区卫生服务站','南京市下关区线路新村社区卫生服务站','温州市龙湾区天河街道社区卫生服务中心','广州市萝岗区夏港街普晖社区卫生服务站','南通市虹桥街道虹桥南村社区卫生服务站','广州市荔湾区多宝街社区卫生服务中心','北京市海淀区田村路社区卫生服务中心','嘉兴市秀洲区新城街道社区卫生服务中心','柳州市柳南区河西街道社区卫生服务中心','抚州市临川区青云社区卫生服务中心','上海市徐汇区徐家汇社区卫生服务中心','硚口区宝丰街公路社区卫生服务站','上海市松江区车墩镇社区卫生服务中心','珠海市香洲区紫荆社区卫生服务站','济南市天桥区天桥东街办事处诚通社区卫生服务站','北京市顺义区空港医院董各庄社区卫生服务站','烟台市芝罘区东山街道厚安社区卫生服务站','延吉市公园社区卫生服务中心','杭州市西湖区蒋村街道社区卫生服务中心','北京市朝阳区亚运村社区卫生服务中心','上海市崇明县横沙乡社区卫生服务中心','上海市徐汇区徐家汇街道社区卫生服务中心','徐州市鼓楼区铜沛社区卫生服务中心','厦门市海沧区海沧街道石塘社区卫生服务中心','北京市朝阳区孙河社区卫生服务中心','北京市通州区北苑街道西果园中医药社区卫生服务站','乌鲁木齐市天山区燕尔窝社区卫生服务中心','常州市天宁区茶山街道清凉新村社区卫生服务站','深圳市罗湖医院集团渔村社区健康服务中心','佛山市禅城区石湾镇街道社区卫生服务中心','济南市天桥区工人新村南村街道办事处二棉社区卫生服务站','兰山区银雀山街道三合屯社区卫生服务站','常州市五星街道西仓社区卫生服务站','洛阳市洛龙区翠云路社区卫生服务中心','厦门市思明区中华街道社区卫生服务中心','李沧区沧口街道永年路社区卫生服务站','北京市海淀区西三旗社区卫生服务中心','抚州市临川区河东社区卫生服务中心','上海市崇明县长兴镇社区卫生服务中心','济南市市中区二七街道办事处车桥社区卫生服务站','云龙区铜山路心理卫生社区卫生服务站','北京市丰台区方庄社区卫生服务中心','北京市朝阳区管庄社区卫生服务中心','北京市通州区于家务回族乡于家务社区卫生服务中心','李沧区浮山路街道百通花园社区卫生服务站','厦门市海沧区新阳街道社区卫生服务中心','北京市朝阳区东风街道石佛营西里社区卫生服务站','慈溪市宗汉街道社区卫生服务中心','北京市丰台区花乡社区卫生服务中心','苏州高新区浒墅关分区阳山花苑社区卫生服务中心','绵阳市涪城区西山社区卫生服务中心','北京市朝阳区潘家园磨房南里社区卫生服务站','上海市崇明县庙镇社区卫生服务中心','焦作市中站区冯封社区卫生服务中心','北京市丰台区南苑乡槐房村社区卫生服务站','建工师医院师机关社区卫生服务中心','高新区创业路社区卫生服务站','北京市海淀区永定路街道八街坊社区卫生服务站','遵义市红花岗区老城街道办事处社区卫生服务中心','济南市天桥区北园办事处一棉社区卫生服务站','苏州工业园区娄葑镇东港社区卫生服务站','青岛市市北区宁夏路街道松山社区卫生服务站','市北区即墨路街道祥和社区卫生服务站','北京市海淀区万寿路街道翠微西里社区卫生服务站','北京市海淀区甘家口社区卫生服务中心','静安区南京西路街道社区卫生服务中心','上海市黄浦区外滩街道社区卫生服务中心','舟山市普陀区朱家尖街道社区卫生服务中心','上海市奉贤区庄行镇社区卫生服务中心','青岛市南泰州路社区卫生服务站','北京语言大学社区卫生服务中心','北京市丰台区南苑乡新宫村社区卫生服务站','北京市丰台区卢沟桥乡太平桥村社区卫生服务站','杭州市西湖区留下街道社区卫生服务中心','杭州市下城区朝晖街道社区卫生服务中心','苏州市吴中区胥口镇社区卫生服务中心','南京市栖霞区马群社区卫生服务中心','上海市宝山区吴淞街道社区卫生服务中心','北京市丰台区花乡草桥村社区卫生服务站','南京市建邺区南湖社区卫生服务中心','成都市锦江区牛市口社区卫生服务中心','焦作市山阳区焦东社区卫生服务中心','成都市成华区保和社区卫生服务中心','深圳市罗湖医院集团文华社区健康服务中心','北京市东城区建国门社区卫生服务中心','宁波市鄞州区瞻岐社区卫生服务中心','北京市顺义区龙湾屯社区卫生服务中心','上海市崇明县新海镇社区卫生服务中心','湖里区湖里街道社区卫生服务中心','北京市海淀区西北旺镇社区卫生服务中心','新乡市卫滨区胜利社区卫生服务中心','陈仓区李家崖社区卫生服务中心','佛山市南海区大沥镇盐步社区卫生服务中心','石家庄市裕华区槐中路社区卫生服务站','青岛市南郓城南路社区卫生服务站','深圳市蛇口人民医院深圳湾社区健康服务中心','北京市丰台区东铁营街道四方景园社区卫生服务站','长乐市漳港街道社区卫生服务中心','舟山市普陀区展茅街道社区卫生服务中心','成都市锦江区东大社区卫生服务中心','苏州市吴中区城区社区卫生服务中心','北京工业大学社区卫生服务中心','杭州市萧山区前进街道社区卫生服务中心','成华区猛追湾社区卫生服务中心','南昌市青云谱区洪都街道社区卫生服务中心','北京市东城区朝阳门社区卫生服务中心','青岛市李沧区虎山路街道金水路社区卫生服务站','新疆天山区和平路社区卫生服务中心','柳州市鱼峰区云屏社区卫生服务站','延庆县四海镇社区卫生服务中心','北京市海淀区北下关社区卫生服务中心','广州市花都区建设北社区卫生服务中心','苏州工业园区跨塘社区卫生服务中心','北京市朝阳区将台社区卫生服务中心','常州市钟楼区南大街街道金色新城社区卫生服务站','北京市丰台区卢沟桥街道莲花池西里社区卫生服务站','西湖区西湖街道社区卫生服务中心','南京市江宁区谷里镇社区卫生服务中心','北京市通州区台湖镇次渠社区卫生服务中心','烟台市向阳社区卫生服务中心','北京市西城区大栅栏社区卫生服务中心','北京市朝阳区常营地区连心园社区卫生服务站','苏州独墅湖科教创新区社区卫生服务中心','天津市滨海新区太平镇社区卫生服务中心','广州市越秀区白云街社区卫生服务中心','广州市海珠区滨江街社区卫生服务中心','台州市路桥区螺洋街道社区卫生服务中心','北京市丰台区二七南社区卫生服务中心','重庆市綦江区万盛街道社区卫生服务中心','常州市钟楼区永红街道广成路社区卫生服务站','常熟市虞山镇藕渠社区卫生服务中心','李沧区浮山路街道民安康社区卫生服务站','烟台市芝罘区毓璜顶街道文化苑社区卫生服务站','石家庄市新华区北苑街道办事处社区卫生服务中心','杭州市江干区闸弄口街道社区卫生服务中心','瀍河区瀍西五股路社区卫生服务中心','北京市顺义区城区社区卫生服务中心','南京市秦淮区中华门社区卫生服务中心','合肥市包河区常青街道东风社区卫生服务站','北京市丰台区卢沟桥乡西局村社区卫生服务站','上海市金山区石化社区卫生服务中心','李沧区浮山路街道福林苑社区卫生服务站','厦门市湖里区禾山街道社区卫生服务中心','佛山市顺德区大良社区卫生服务中心','南通市城东街道新桥社区卫生服务站','上海市宝山区庙行镇社区卫生服务中心','上海市崇明县中兴镇社区卫生服务中心','广州市天河区棠下街第二社区卫生服务中心','奎屯市北京路社区卫生服务中心','佛山市顺德区伦教社区卫生服务中心','杭州市萧山区河上镇社区卫生服务中心大桥分中心','中山市东区社区卫生服务中心','路桥区新桥镇社区卫生服务中心','洪山区卓刀泉街七0九所社区卫生服务中心','江门市蓬江区堤东街道社区卫生服务中心','潍坊市潍城区西关街道社区卫生服务中心','重庆市万盛经济技术开发区万盛街道社区卫生服务中心','石河子市第十五社区卫生服务中心','深圳市罗湖医院集团新兴社区健康服务中心','重庆市南岸区龙门浩社区卫生服务中心','杭州市桐庐县新合社区卫生服务中心','上海市奉贤区庄行镇邬桥社区卫生服务中心','上海市崇明县港沿镇社区卫生服务中心','宝鸡市清姜社区卫生服务中心','江阴市顾山社区卫生服务中心','北京市丰台区花乡高立庄村社区卫生服务站','上海市金山区金山卫镇社区卫生服务中心','无锡市惠山区玉祁街道社区卫生服务中心','天津市南开区兴南街社区卫生服务中心','江门市江海区外海街道麻园社区卫生服务中心','武汉市洪山区青菱街社区卫生服务中心','市北区兴隆路街道联创社区卫生服务中心','广州市天河区沙河街社区卫生服务中心','济南市槐荫区匡山办事处社区卫生服务中心','丰台区东高地街道西洼地社区卫生服务站','上海市静安区共和新路街道社区卫生服务中心','市北区四方街道四机社区卫生服务站','上海市长宁区周家桥街道社区卫生服务中心','南京市秦淮区秦虹社区卫生服务中心','庄河市城关街道社区卫生服务中心','崇川区观音山街道社区卫生服务中心','南山区蛇口人民医院沿山社区健康服务中心','石家庄市新华区东焦街道办事处社区卫生服务中心','石家庄市长安区建北街道办事处社区卫生服务中心','屏南县古峰社区卫生服务中心','天津市西青区中北镇社区卫生服务中心','中国农业大学东区社区卫生服务中心','广州市海珠区昌岗街社区卫生服务中心','上海市虹口区欧阳路街道社区卫生服务中心','烟台市芝罘区白石街道新西社区卫生服务站','常州市天宁区天宁街道青山路社区卫生服务站','苏州市姑苏区苏锦街道苏站社区卫生服务中心','北京市顺义区木林社区卫生服务中心','南京市江宁区秣陵街道百家湖社区卫生服务中心','广州市天河区凤凰街社区卫生服务中心','天津市南开区王顶堤街社区卫生服务中心','新疆生产建设兵团第二师天宇社区卫生服务中心','珠海市香洲区华发新城社区卫生服务中心','南京市玄武区兰园社区卫生服务中心','海陵区城西街道社区卫生服务中心','上海市青浦区练塘镇社区卫生服务中心','开封市顺河回族区工业办事处社区卫生服务中心','北京市顺义区赵全营社区卫生服务中心','上海市青材镇社区卫生服务中心','西工区洛阳桥社区卫生服务站','余杭区余杭街道社区卫生服务中心','天津滨海新区新北街蓝卡社区卫生服务中心','广州市萝岗区夏港街青年社区卫生服务站','上海市黄浦区南京东路街道社区卫生服务中心','台州市椒江区葭芷街道社区卫生服务中心中山社区卫生服务站','上海市奉贤区四团镇社区卫生服务中心','烟台市芝罘区东山街道进德社区卫生服务站','西安市雁塔区小寨路永松社区卫生服务站','大连沙河口黑石礁凌水社区卫生服务中心','福州仓山区下渡街道下藤社区卫生服务站','余姚市兰江街道社区卫生服务中心','上海市宝山区杨行镇社区卫生服务中心','常州市天宁区茶山街道丽华二村社区卫生服务站','北京市丰台区长辛店镇张郭庄村社区卫生服务站','常州市钟楼区南大街街道文亨社区卫生服务站','重庆市江北区华新街社区卫生服务中心','石家庄市桥西区铁中南社区卫生服务站','鼓楼区华大街道北江社区卫生服务站','北京市朝阳区潘家园街道武圣东里社区卫生服务站','上海市普陀区长风街道白玉社区卫生服务中心','上海市奉贤区柘林镇胡桥社区卫生服务中心','长沙市岳麓区桔子洲街道三真社区卫生服务中心','北京市朝阳区金盏第二社区卫生服务中心','新昌县南明街道梅湖社区卫生服务站','石家庄市长安区阜康社区卫生服务中心','西湖区翠苑街道翠苑四区社区卫生服务站','上海市黄浦区淮海中路街道社区卫生服务中心','南京市白下区大光路社区卫生服务中心','渭城区民生东路社区卫生服务中心','杭州市江干区凯旋街道社区卫生服务中心','桥东区三三0二社区卫生服务中心','北京市怀柔区杨宋镇社区卫生服务中心','广州市海珠区华洲街社区卫生服务中心','济南市天桥区北坦办事处北坦社区卫生服务站','桃城区河西街道办事处站前社区卫生服务站','焦作市山阳区定和街道丰收社区卫生服务站','桐庐县钟山乡社区卫生服务中心','苏州市吴中区横泾街道社区卫生服务中心','李沧区振华路街道四流中路社区卫生服务中心','北京市东城区永定门外社区卫生服务中心','江北区大庆村社区卫生服务中心','上海市徐汇区漕河泾街道社区卫生服务中心','舟山市普陀区沈家门街道登步社区卫生服务中心','福州市鼓楼区水部街道福新社区卫生服务站','北京市朝阳区双井街道垂杨柳中区社区卫生服务站','孝感市孝南区广场街道社区卫生服务中心','南京市栖霞区栖霞社区卫生服务中心','上海市普陀区长寿街道社区卫生服务中心','杭州市江干区彭埠镇社区卫生服务中心建华服务站','石家庄市桥西区新中社区卫生服务站','杭州市萧山区益农镇社区卫生服务中心','淮安市淮东社区卫生服务中心','南昌市东湖区贤士湖社区卫生服务中心','文艺路建科大社区卫生服务站','重庆市石桥铺街道社区卫生服务中心石杨路三社区卫生服务站','菏泽市牡丹区南城办事处社区卫生服务中心','密云县太师屯镇社区卫生服务中心','常州市天宁区红梅街道红梅东村社区卫生服务站','杭州市拱墅区半山街道社区卫生服务中心','上海市杨浦区长白社区卫生服务中心','中国兵器工业二0五研究所社区卫生服务站','郑州市二七区和平新村社区卫生服务站','上海市奉贤区奉城镇头桥社区卫生服务中心','福州仓山区金山街道金麟社区卫生服务站','衢州市柯城信安街道康乐社区卫生服务站','杭州市余杭区瓶窑镇社区卫生服务中心','济宁市任城区金城街道（众和）社区卫生服务中心','福州市台江区苍霞街道社区卫生服务中心','上海市金山区山阳镇社区卫生服务中心','烟台市芝罘区通伸街道前进路社区卫生服务站','永嘉县黄田街道社区卫生服务中心','常州市天宁区天宁街道兆丰花苑社区卫生服务站','连云港市海州区浦东社区卫生服务中心','烟台市芝罘区幸福街道支农里社区卫生服务站','广州市天河区石牌街社区卫生服务中心','深圳市南山区人民医院阳光棕榈社区健康服务中心','常州市天宁区红梅街道社区卫生服务中心','北京市延庆区永宁镇社区卫生服务中心','石河子第十一社区卫生服务中心','新昌县南明街道茶亭社区卫生服务站','延庆县千家店镇社区卫生服务中心','天津市北辰区大张庄镇社区卫生服务中心','西安市新城区纱厂东街铁路社区卫生服务站','上海市杨浦区控江社区卫生服务中心','天津市河西区大营门街社区卫生服务中心','武汉市江汉区汉兴街第二社区卫生服务中心','广州市天河区珠吉街吉山社区卫生服务站','北京市海淀区万寿路街道总参军训和兵种部第六干休所社区卫生服务站','北京市顺义区板桥社区卫生服务中心','石家庄市新华区石岗街道办事处社区卫生服务中心','徐州市泉山区奎山社区卫生服务中心','市北区大港街道社区卫生服务站','衢州市柯城区信安街道紫荆社区卫生服务站','福州市晋安区王庄街道社区卫生服务中心','克拉玛依区社区卫生服务管理中心','济南市市中区四里村街道办事处英雄山社区卫生服务中心','宁波市北仑区梅山乡社区卫生服务中心','秦皇岛市海港区港城大街社区卫生服务中心','江宁区秣陵街道东善桥社区卫生服务中心','北京市丰台区卢沟桥乡小瓦窑村社区卫生服务站','葛洲坝西坝社区卫生服务中心','官渡区曙光社区卫生服务站','上海市宝山区顾村镇菊泉新城社区卫生服务中心','济南市历下区千佛山办事处佛山苑社区卫生服务站','北京市门头沟区清水镇社区卫生服务中心','潍城区北关街道齐家社区卫生服务站','鼓楼区华大街道社区卫生服务中心','福州市鼓楼区五凤街道广厦社区卫生服务站','福州市台江区茶亭街道福德社区卫生服务站','郑州市大石桥社区卫生服务中心','郑州市桐柏路郑工社区卫生服务中心','杭州市下城区长庆潮鸣街道社区卫生服务中心','北京市房山区河北镇社区卫生服务中心','成都市青羊区文家社区卫生服务中心','北京市朝阳区双井街道富力社区卫生服务站','慈溪市庵东镇社区卫生服务中心振东村服务站','秦都区中华社区卫生服务中心','梅州市梅江区江南街道江南社区卫生服务中心','青岛市市北区浮山新区街道松山社区卫生服务站','浙江省杭州市建德市新安江街道府东社区卫生服务站','南京市栖霞区八卦洲社区卫生服务中心','温岭市城东街道社区卫生服务中心','厦门市思明区嘉莲街道社区卫生服务中心','内江市市中区壕子口社区卫生服务中心','南京市栖霞区迈皋桥万寿社区卫生服务站','北京市怀柔区龙山街道社区卫生服务中心东关社区卫生服务站','成华区建设路社区卫生服务中心','上海市奉贤区金汇镇社区卫生服务中心','北京市密云区鼓楼社区卫生服务中心','成都市金牛区沙河源社区卫生服务中心','北京市顺义区后沙峪社区卫生服务中心古城社区卫生服务站','李沧区九水路街道佳家康社区卫生服务中心','南京市秦淮区夫子庙社区卫生服务中心','宁波市鄞州区集仕港镇社区卫生服务中心','石家庄市长安区中山东路社区卫生服务中心','丰台区太平桥街道顺驰蓝调社区卫生服务站','北京市丰台区卢沟桥社区卫生服务中心','武义县白洋街道社区卫生服务中心','上海市奉贤区南桥镇光明社区卫生服务中心','济南市天桥区纬北路街道办事处社区卫生服务中心','上海市奉贤区金汇镇齐贤社区卫生服务中心','深圳市罗湖医院集团泥岗社区健康服务中心','北京市房山区佛子庄乡社区卫生服务中心','六安市金安区东市街道七里站社区卫生服务站','上海市浦东新区北蔡镇社区卫生服务中心','泰安市岱岳区天平街道社区卫生服务中心','长兴街长湴社区卫生服务站','宁波市北仑区戚家山街道社区卫生服务中心','北京市海淀区永定路街道六街坊社区卫生服务站','盘龙区鼓楼街道桃源社区卫生服务站','北京市朝阳区豆各庄社区卫生服务中心','上海市浦东新区惠南社区卫生服务中心','青岛黄岛区天和医院社区卫生服务站','成都市锦江区双桂路五福社区卫生服务中心','深圳市罗湖医院集团广岭社区健康服务中心','庐阳区杏花村街道社区卫生服务中心','西工区市府院社区卫生服务中心','北京市丰台区长辛店镇社区卫生服务中心','瑶海区方庙街道社区卫生服务中心','西安市长安区韦曲南街社区卫生服务中心','上海市闸北区芷江西路街道社区卫生服务中心','上海市浦东新区花木社区卫生服务中心','北京市朝阳区管庄地区惠河东里社区卫生服务站','天津市河东区富民路街社区卫生服务中心','北京市丰台区花乡郭公庄村社区卫生服务站','巴东县信陵镇社区卫生服务中心','东台市新桥社区卫生服务站','莱西市威海东路社区卫生服务中心','舟山市新城社区卫生服务中心','北京市房山区张坊镇社区卫生服务中心','苏州市吴中区光福镇社区卫生服务中心','北京市房山区良乡镇社区卫生服务中心','济南市历城区山大路街道办事处建总社区卫生服务站','宁波市北仑区小港街道社区卫生服务中心','老城区西关社区卫生服务中心','五华区黑林铺社区卫生服务中心','北京市海淀区温泉镇航材院社区卫生服务中心','深圳市罗湖区翠达社区健康服务中心','保定市竞秀区保钞社区卫生服务站','烟台市芝罘区幸福街道永和社区卫生服务站','南京市玄武区天山路社区卫生服务站','奎文区廿里堡街道社区卫生服务中心','石家庄市桥西区维明社区卫生服务中心','北京市朝阳区亚运村街道安翔里社区卫生服务站','历下区燕山办事处燕山医院社区卫生服务站','宁波市江东区百丈街道社区卫生服务中心','北京市顺义区天竺社区卫生服务中心','杭州市上城区望江街道兴隆社区卫生服务站','福州仓山区仓前街道航兴社区卫生服务站','黄山市屯溪老街社区卫生服务站','北京市西城区陶然亭社区卫生服务中心','北京市海淀区北太平庄街道冶建院社区卫生服务站','烟台市老年福利服务中心社区卫生服务站','南京市鼓楼区宁海路社区卫生服务中心','杭州市萧山区河上镇社区卫生服务中心','济南市天桥区药山街道办事处天志社区卫生服务站','淮安市清河区钵池山社区卫生服务中心','北京市丰台区卢沟桥国医社区卫生服务中心','北京市大兴区魏善庄镇社区卫生服务中心','遵义市红花岗区长征镇社区卫生服务中心','吴中区郭巷社区卫生服务中心','长春市朝阳区南湖第一社区卫生服务中心','舟山市定海区盐仓街道社区卫生服务中心','无锡市惠山区阳山镇社区卫生服务中心','无锡市新区旺庄街道社区卫生服务中心','葫芦岛市龙港区西街道社区卫生服务中心','福州市仓山区建新镇马榕社区卫生服务站','天津市河西区友谊路街社区卫生服务中心','上海市浦东新区曹路社区卫生服务中心','杭州市上城区紫阳街道春江花月社区卫生服务站','海口市琼山区府城镇大园社区卫生服务中心','萧山区靖江镇甘露社区卫生服务站','潍坊高新区清池街道社区卫生服务中心','胜中康达社区卫生服务中心','青岛市南颐和社区卫生服务站','太谷县白塔社区卫生服务中心','雁塔区高科花园社区卫生服务站','雨花台区安德里社区卫生服务站','柳州市柳南区潭西街道十一冶社区卫生服务中心','石家庄市长安区建安社区卫生服务中心','烟台市福山区德河社区卫生服务站','柳州市鱼峰区宝山社区卫生服务站','无锡市惠山区钱桥街道社区卫生服务中心','常州市红梅街道北环南村社区卫生服务站','西工区唐宫路社区卫生服务中心','连云港市新浦区路南社区卫生服务中心','大连沙河口星海湾星海社区卫生服务中心','青岛市南西藏路社区卫生服务站','北京市海淀区上地社区卫生服务中心','济南市历下区文东办事处山师大社区卫生服务站','常熟市虞山镇莫城社区卫生服务中心','北京市海淀区西北旺镇六里屯社区卫生服务站','柳州市柳北区桂景湾社区卫生服务站','深圳市南山区蛇口人民医院海湾社区健康服务中心','吉安市吉州区福星亭社区卫生服务站','上海市徐汇区康健社区卫生服务中心','太原市万柏林区新友谊社区卫生服务站','上海市崇明县港西镇社区卫生服务中心','常州市新北区河海街道兰翔社区卫生服务站','广州市越秀区光塔街社区卫生服务中心','李沧区九水路街道金水东路社区卫生服务站','大庆油田富强街道社区卫生服务中心','北京市怀柔区九渡河镇社区卫生服务中心','石河子第十二社区卫生服务中心','北京市丰台区卢沟桥乡岳各庄村社区卫生服务站','福州市鼓楼区五凤街道白龙社区卫生服务站','瑞安市飞云街道社区卫生服务中心','深圳市罗湖医院集团怡景社区健康服务中心','焦作市解放区民生北社区卫生服务中心','北京市通州区玉桥街道柳岸方园社区卫生服务站','焦作市马村区九里山社区卫生服务中心','合肥市瑶海区三里街街道社区卫生服务中心','宁波市镇海区庄市街道社区卫生服务中心','石家庄市裕华区裕东街道办事处社区卫生服务中心','烟台市福山区福利莱社区卫生服务站','洛阳市涧西区首电社区卫生服务站','市北区水清沟街道协天和社区卫生服务站','慈溪市宗汉街道社区卫生服务中心新华村服务站','北京市丰台社区卫生服务中心','北京市丰台区丰台街道东大街社区卫生服务站','丰台区丰台社区卫生服务中心(青塔分中心)','青岛市崂山区沙子口街道姜哥庄社区卫生服务站','许昌市魏都区春秋社区卫生服务站','莆田市城厢区龙桥街道社区卫生服务中心','深圳市罗湖医院集团西岭社区健康服务中心','上海市青浦区华新镇社区卫生服务中心','上海市崇明县新村乡社区卫生服务中心','上海市宝山区友谊街道社区卫生服务中心','丰台区马家堡街道角门东里社区卫生服务站','黄石市西塞山区临江社区卫生服务中心','延庆县康庄镇社区卫生服务中心','北京市海淀区马连洼街道天秀花园社区卫生服务站','晋城市城区东街街道办事处社区卫生服务中心','济南市市中区杆石桥经八路社区卫生服务站','丰台区长辛店街道杜家坎社区卫生服务站','晋安区岳峰镇三华社区卫生服务站','福州市鼓楼区洪山镇洪山桥社区卫生服务站','北京市平谷区黄松峪乡社区卫生服务中心','北京市朝阳区呼家楼街道金台北街社区卫生服务站','延庆县八达岭镇社区卫生服务中心','李沧区李村街道滨河路社区卫生服务站','杭州市拱墅区桃源社区卫生服务中心','北京市西城区广安门内街道槐柏树社区卫生服务站','北京市通州区北苑街道怡佳家园社区卫生服务站','头屯河区柯坪北路社区卫生服务中心','镇江市润州区黎明社区卫生服务中心','北京市丰台区花乡樊家村社区卫生服务站','广州市番禺区桥南街陇枕社区卫生服务站','福州仓山区太平洋城社区卫生服务站','西昌市北城社区卫生服务中心','南充市顺庆区医学街社区卫生服务站','武汉市青山区钢花街西区社区卫生服务中心','北京市朝阳区建外街道南郎家园社区卫生服务站','上海市徐汇区凌云社区卫生服务中心','烟台市芝罘区向阳街道兴隆街社区卫生服务站','昆明市西山区碧鸡昆泥社区卫生服务中心','渭滨区经二路社区卫生服务中心','济南市天桥区制锦市街道办事处乐安街社区卫生服务站','烟台市芝罘区幸福社区卫生服务中心','厦门市嘉莲街道长青社区卫生服务站','凌源市北街社区卫生服务中心','伊宁县西一区社区卫生服务站','咸阳市抗战南路社区卫生服务站','南通市崇川区城东街道郭里园社区卫生服务站','市北区敦化路街道福彩鑫桥社区卫生服务站','北京市海淀区时雨园社区卫生服务站','北京市丰台区南苑乡果园鑫福里社区卫生服务站','上海市浦东新区航头社区卫生服务中心','深圳市南山区蛇口人民医院花果山社区健康服务中心','晋安区鼓山镇日出东方社区卫生服务站','丽水市开发区水阁街道社区卫生服务中心','常州市天宁区兰陵街道社区卫生服务中心','海口市龙华区金贸街道金贸社区卫生服务中心','保定市竞秀区宁和社区卫生服务站','烟台市牟平区宁海街道新牟社区卫生服务站','天津市河北区光复道街社区卫生服务中心','锦州市凌河区紫荆街道社区卫生服务中心','上海市普陀区长风街道长风社区卫生服务中心','厦门市湖里区殿前街道社区卫生服务中心','青岛市南合万家社区卫生服务站','常州市天宁区茶山街道丽华一村社区卫生服务室','北京科技大学社区卫生服务中心','常州市钟楼区南大街街道世纪明珠园社区卫生服务站','烟台市芝罘区毓璜顶街道黄山南社区卫生服务站','上海市奉贤区奉城镇塘外社区卫生服务中心','锦州市凌河区榴花街道社区卫生服务中心','北京市朝阳区三里屯街道中纺里社区卫生服务站','上海市奉贤区奉城镇社区卫生服务中心','成都市武侯区机投桥社区卫生服务中心','福州市台江区上海街道交通二社区卫生服务站','厦门市湖里区江头街道社区卫生服务中心','涧西区青岛路一街坊社区卫生服务站','丰城市河洲街道社区卫生服务中心','北京市昌平区南口社区卫生服务中心','上海市浦东新区潍坊社区卫生服务中心','上海市金山区张堰镇社区卫生服务中心','天津市滨海新区古林街社区卫生服务中心','柳州市柳南区南站街道社区卫生服务中心','北京市海淀区永定路街道九街坊社区卫生服务站','上海市普陀区曹杨街道社区卫生服务中心','武汉市江汉区北湖街社区卫生服务中心','徐州市泉山区和平街道民安园社区卫生服务站','天津市和平区劝业场街社区卫生服务中心','密云县新城子镇社区卫生服务中心','合肥经济技术开发区芙蓉社区卫生服务中心','广州市海珠区琶洲街社区卫生服务中心','济南市市中区二七办事处社区卫生服务中心','天津市滨海新区大港社区卫生服务中心','青岛市南长海社区卫生服务站','慈溪市坎墩街道社区卫生服务中心坎西村服务站','北京市大兴区艺苑桐城社区卫生服务站','舟山市定海区小沙街道社区卫生服务中心','北京市海淀区苏家坨镇社区卫生服务中心','南昌市东湖区百花洲街道社区卫生服务中心','石家庄市裕华区裕强街道办事处社区卫生服务中心','永春县桃城社区卫生服务中心','西安市碑林区柏树林社区卫生服务中心','密云县冯家峪镇社区卫生服务中心','聊城市东昌府区古楼街道社区卫生服务中心','北京市昌平区回龙观社区卫生服务中心东村家园社区卫生服务站','北京市密云区果园社区卫生服务中心','成都市金牛区驷马桥社区卫生服务中心','合肥市包河区芜湖路街道社区卫生服务中心','厦门市海沧区嵩屿街道社区卫生服务中心','大连甘井子兴华千山路社区卫生服务中心','成都市武侯区红牌楼社区卫生服务中心','苏州市金阊区留园街道观景社区卫生服务站','新疆五家渠市友谊路社区卫生服务站','烟台开发区银芝社区卫生服务站','密云县不老屯镇社区卫生服务中心','上海市浦东新区宣桥社区卫生服务中心','张店区和平街道体坛社区卫生服务站','大连沙河口星海湾富国社区卫生服务中心','青岛李沧区沧口街道永定路社区卫生服务中心','北京市丰台区王佐镇社区卫生服务中心','北京市海淀区羊坊店街道会城门社区卫生服务站','桐乡市梧桐街道社区卫生服务中心','晋安区象园街道菊园社区卫生服务站','合肥市包河区包公街道河滨社区卫生服务站','深圳市罗湖区松园社区健康服务中心','无锡市惠山区洛社镇杨市社区卫生服务中心','上海市徐汇区康健街道社区卫生服务中心','大连沙河口中山公园联合路社区卫生服务中心','台江区上海街道社区卫生服务中心','老城区洛浦南关社区卫生服务中心','张店区公园街道齐赛园社区卫生服务站','广州市天河区林和街社区卫生服务中心','庐阳区海棠街道社区卫生服务中心','宁波市慈溪市宗汉街道社区卫生服务中心百兴村服务站','瑶海区三里街街道凤阳一村社区卫生服务站','临海市古城街道阳光花城社区卫生服务站','大庆市让胡路区龙岗街道龙新社区卫生服务中心','青岛市南徐州路社区卫生服务站','浉河区五里墩办事处社区卫生服务中心','天津市河西区下瓦房街社区卫生服务中心','东台市第三人民医院新民南路社区服务站','昆明市金辰街道菠萝社区卫生服务站','郑州市中原制药厂社区卫生服务站','福州市台江区义洲街道社区卫生服务中心','建邺区茶亭社区卫生服务站','濮阳市华龙区中原路中玉社区卫生服务中心','南京市江宁区淳化街道社区卫生服务中心','嘉峪关市建设街社区卫生服务中心','焦作市解放区新华西社区卫生服务中心','烟台市芝罘区世回尧街道上尧社区卫生服务站','北京市丰台区花乡新发地村社区卫生服务站','天津市北辰区青光镇社区卫生服务中心','烟台市芝罘区白石街道白春社区卫生服务站','天津市滨海新区中塘镇社区卫生服务中心','徐州市泉山区泰山社区卫生服务中心'];
//        print_r($hospital_arr);
        $first_q = $this->first_q;
        $survey_info = $this->survey_info;

//        $post_data = $this->input->post();
//        if ($post_data) {
//            //检测数据有效性
//            $data_info = $post_data['data_info'];
//            if (!$data_info) {
//                _back_msg("error", "请完善信息再提交！");
//            }
//            $hospital = trim($data_info['hospital']);
//            if (!$hospital) {
//                _back_msg("error", "请填写医院！");
//            }
//            $i = 0;
//            $insert_info = [];
//            $code = date("YmdHis").rand(10,9999);
//            //第一题的价格
//            $first_money = [];
//            //除了第一题的总价格
//            $total_money = [];
//            foreach ($survey_info as $k_q => $v_q) {
//                for($year = $v_q['start_year']; $year <= $v_q['end_year']; $year++){
//                    foreach ($v_q['option'] as $k_option_sales_money => $v_option_sales_money){
//                        $year_sales_money = trim($data_info['year_sales_money'][$k_q][$k_option_sales_money][$year]);
//                        $year_sales_num = trim($data_info['year_sales_num'][$k_q][$k_option_sales_money][$year]);
//
//                        if (!$year_sales_money) {//检测价格是否已填写完整
//                            _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n未填写【销售价格】！");
//                        }
//
//                        if ($k_q != "q1") {//不是第一题
//                            if (!$year_sales_num) {//检测价格是否已填写完整
//                                _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n未填写【销售总量】！");
//                            }
//                        }
//
//                        if ($year_sales_money && !checkNumberPunctuation($year_sales_money)) {
//                            _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n输入值：{$year_sales_money}\n格式有误，请检查是否是数值！");
//                        }
//
//                        if ($year_sales_num && !checkNumberPunctuation($year_sales_num)) {
//                            _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n输入值：{$year_sales_num}\n格式有误，请检查是否是数值！");
//                        }
//                        if ($k_q != "q1") {
//                            $total_money[$year] += $year_sales_money;
//                        } else {
//                            $first_money[$year] = $year_sales_money;
//                        }
//                        $insert_info[$i] = [
//                            "code" => $code,
//                            "hospital" => $this->db->escape_str($hospital),
//                            "med_type" => $v_q['title'],
//                            "sales_year" => $year,
//                            "med_format" => $v_option_sales_money,
//                            "year_sales_money" => $year_sales_money,
//                            "year_sales_num" => $year_sales_num,
//                            "add_time" => time(),
//                        ];
//                        $i++;
//                    }
//                }
//            }
//
//            foreach ($total_money as $k_y => $v_y) {
//                if (($v_y || $first_money[$k_y]) && $first_money[$k_y] < $v_y) {
//                    _back_msg("error", "录入有误，【CNS (胶囊和口服片剂) {$k_y}年总销售额：{$first_money[$k_y]}】不能低于下面所有产品价格【{$k_y}年总金额：{$v_y}】总和！");
//                }
//            }
//
//            if (!$insert_info) {
//                _back_msg("error", "数据有误，无法提交，请联系管理员！");
//            }
//           $res = insert_ignore_bath("zhuyou_chc", $insert_info);
//           if ($res) {
//               _back_msg("success", "提交成功！");
//           } else {
//               _back_msg("error", "提交失败！");
//           }
//        }
        //获取选中状态
        $get_hos_drug = getDataByCondition("zhuyou_chc_hos_drug", " AND uid='{$this->db->escape_str($uid)}'", "*", true);
        $med_type_code = $get_hos_drug['med_type_code'] ? explode(",", $get_hos_drug['med_type_code']) : "";
        $med_type = $get_hos_drug['med_type'] ? explode(",", $get_hos_drug['med_type']) : "";
        $med_data_code = $get_hos_drug['med_data_code'] ? json_decode($get_hos_drug['med_data_code'], true) : "";
        if ($get_hos_drug && $get_hos_drug['is_finish'] == 2) {//已完成
            if ($uid >= 9999001 && $uid <= 9999126) {
                $url = "/one_survey/finish_page";
            } else {
                $url = "https://www.drsay.cn/bk/s/242_e66e7656362a192d?uid={$uid}";
            }
            redirect($url);
        }

        $data = [
            "survey_info" => $survey_info,
            "first_q" => $first_q,
            "hospital_arr" => json_encode($hospital_arr),
            "hospital_info" => $hospital_arr,
            "uid" => $uid,
            "get_hos_drug" => $get_hos_drug,
            "med_type" => $med_type,
            "med_type_code" => $med_type_code,
            "med_data_code" => $med_data_code,
        ];
        $this->load->view("/one_survey/survey_spirit", $data);
    }

    //单个数据添加
    function insert_survey_spirit()
    {
        $q = trim($this->input->post("q", true));//题目
        $q_option = $this->input->post("q_option", true);//选项
        $uid = trim($this->input->post("uid", true));
        $q_type = trim($this->input->post("q_type", true));
        $year = trim($this->input->post("year", true));
        $q_option_name = trim($this->input->post("q_option_name", true));

//        print_r($post_data);
        //查询记录是否已存在
        $get_hos_drug = getDataByCondition("zhuyou_chc_hos_drug", " AND uid='{$this->db->escape_str($uid)}'", "*", true);
        if ($q == "hospital") {//医院
            $q_option = trim($q_option);
            if ($q_option) {//存在记录才添加入库
                if ($get_hos_drug) {//已存在，更新
                    $update_data = [
                        "hospital" => $q_option,
                        "edit_time" => time(),
                    ];
                    $res = upData("zhuyou_chc_hos_drug", $get_hos_drug['id'], $update_data);
                } else {
                    $insert_data = [
                        "uid" => $this->db->escape_str($uid),
                        "hospital" => $q_option,
                        "add_time" => time(),
                    ];
                    $res = saveData("zhuyou_chc_hos_drug", $insert_data);
                }
                $res ?  _back_msg("success", "提交成功！") : _back_msg("error", "提交失败！");
            }
        } else if ($q == "med_type") {//药品
            $rs_med_type = [];
            $med_type_code = [];
            foreach ($q_option as $v_option) {
                if ($v_option) {
                    $rs_med_type[] = $this->survey_info[$this->first_q['option'][$v_option]]['title'];
                    $med_type_code[] = $this->first_q['option'][$v_option];
                }
            }
            if ($get_hos_drug) {//已存在，更新
                $update_data = [
                    "med_type" => $rs_med_type ? implode(",", $rs_med_type) : "",
                    "med_type_code" => $med_type_code ? implode(",", $med_type_code) : "",
                    "edit_time" => time(),
                ];
                upData("zhuyou_chc_hos_drug", $get_hos_drug['id'], $update_data);
            }
        } else {//普通题型问卷入库
            if ($get_hos_drug) {//已存在，更新
                $q_title = $this->survey_info[$q]['title'];
                $q_option_info = $this->survey_info[$q]['option'][$q_option_name];
                $med_data = $get_hos_drug['med_data'] ? json_decode($get_hos_drug['med_data'], true) : [];
                $med_data_code = $get_hos_drug['med_data_code'] ? json_decode($get_hos_drug['med_data_code'], true) : [];
                if ($q_option && !checkNumberPunctuation($q_option)) {
                    _back_msg("error", "产品【{$q_title}】\n年份为：【{$year}】\n规格为：【{$q_option_info}】\n输入值：{$q_option}\n格式有误，请检查是否是数值！");
                }
                $med_data[$q_title][$year][$q_type][$q_option_info] = $q_option;
                $med_data_code[$q][$year][$q_type][$q_option_name] = $q_option;
                $update_data["med_data"] = json_encode($med_data, JSON_UNESCAPED_UNICODE);
                $update_data["med_data_code"] = json_encode($med_data_code, JSON_UNESCAPED_UNICODE);
                upData("zhuyou_chc_hos_drug", $get_hos_drug['id'], $update_data);
            }

        }
//        print_r($q_option);
//        print_r($update_data);
    }

    //项目结束
    function survey_finish()
    {
        $uid = $this->input->post("uid", true);
        $uid = trim($uid);//提交人
        if ($uid){
            //查询记录是否已存在
            $get_hos_drug = getDataByCondition("zhuyou_chc_hos_drug", " AND uid='{$uid}'", "*", true);
            if ($get_hos_drug) {
                if (!$get_hos_drug['med_data']) {//没有数据，不允许完成
                    _back_msg("error", "请至少提交一项药品销售数据！");
                }
                $this->survey_money_check($get_hos_drug);
                $update_data = [
                    "is_finish" => 2,
                    "finish_time" => time(),
                ];
                upData("zhuyou_chc_hos_drug", $get_hos_drug['id'], $update_data);
                if ($uid >= 9999001 && $uid <= 9999126) {
                    $url = "/one_survey/finish_page";
                } else {
                    $url = "https://www.drsay.cn/bk/s/242_e66e7656362a192d?uid={$uid}";
                }
                _back_msg("success", "提交成功！", $url);
            } else {
                _back_msg("error", "请至少提交一项数据信息！");
            }
        }
    }

    //检测输入价格是否合理
    function survey_money_check($get_hos_drug)
    {
        //用户提交的数据
        $med_data_code = $get_hos_drug['med_data_code'];
        $med_data_code_arr = $med_data_code ? json_decode($med_data_code, true) : [];
        $survey_info = $this->survey_info;
        $total_money = $first_money = [];
        $i = 0;
        foreach ($survey_info as $k_q => $v_q) {
            for($year = $v_q['start_year']; $year <= $v_q['end_year']; $year++){
                //特定年份必填
                $is_hav_data = [];
                foreach ($v_q['option'] as $k_option_sales_money => $v_option_sales_money){
                    $year_sales_money = trim($med_data_code_arr[$k_q][$year]["sales_money"][$k_option_sales_money]);//销售额
                    $year_sales_num = trim($med_data_code_arr[$k_q][$year]["sales_num"][$k_option_sales_money]);//销售量
                    if ($k_q != "q1") {//不是第一题
                        if ($year_sales_money && !$year_sales_num) {
                            _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n\n错误原因：您填写了【销售价格】，未填写【销售总量】！");
                        } else if(!$year_sales_money && $year_sales_num){
                            _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n\n错误原因您填写了【销售总量】，未填写【销售价格】！");
                        }
                    }

                    if ($year_sales_money && !checkNumberPunctuation($year_sales_money)) {
                        _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n输入值：{$year_sales_money}\n格式有误，请检查是否是数值！");
                    }

                    if ($year_sales_num && !checkNumberPunctuation($year_sales_num)) {
                        _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n输入值：{$year_sales_num}\n格式有误，请检查是否是数值！");
                    }

                    if ($year_sales_money && $year_sales_num) {
                        if ($year_sales_num * 20 > $year_sales_money) {
                            _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n销售价格:{$year_sales_money}\n销售总量:{$year_sales_num}\n价格输入有误，请检查再提交！");
                        }
                    }

                    if ($k_q != "q1") {
                        $total_money[$year] += $year_sales_money;
                        if (in_array($year, [2015,2016,2017,2018]) && !$year_sales_money) {
                            $is_hav_data[$k_q][$year] = $year;
                        }
                    } else {
                        if (in_array($year, [2015,2016,2017,2018]) && !$year_sales_money) {
                            _back_msg("error", "产品【{$v_q['title']}】\n年份为：【{$year}】\n规格为：【{$v_option_sales_money}】\n\n错误原因：【{$year}年份销售总额需要填写】！");
                        }
                        $first_money[$year] = $year_sales_money;
                    }
                    $i++;
                }
            }
//            //检测某一题型数据是否为空
//            if ($is_hav_data) {
//
//            }
        }

        foreach ($total_money as $k_y => $v_y) {
            if (($v_y || $first_money[$k_y]) && $first_money[$k_y] < $v_y) {
                _back_msg("error", "录入有误，【N类神经系统类药物(胶囊和口服片剂)  {$k_y}年总销售额：{$first_money[$k_y]}】不能低于下面所有产品价格【{$k_y}年总金额：{$v_y}】总和！");
            }
        }
//        _back_msg("success", "提交成功！");
    }

    //结束页
    function finish_page()
    {
        $data = [

        ];
        $this->load->view("/one_survey/finish_page", $data);
    }

    //浙江
    function zhejiang()
    {
        $data = [

        ];
        $this->load->view("/one_survey/zhejiang", $data);
    }

    function iqvia_one()
    {
        $question = [
            [
                "qid" => "s1",
                "title" => "请问您的职称【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A.	主任医师",
                    "2" => "B.	副主任医师",
                    "3" => "C.	主治医师",
                    "4" => "D.	其他",
                ],
            ],
            [
                "qid" => "q1",
                "title" => "2017-2018国内外指南对高血压治疗的理念，你支持以下哪几点【多选】",
                "bg_color" => "#4472c4",
                "input_type" => "checkbox",
                "option" => [
                    "1" => "A.	条件允许的情况下，采取强化降压治疗，能取得最大的心血管获益",
                    "2" => "B.	联合应用降压药物更能达到目标血压",
                    "3" => "C.	联合用药应考虑降压作用机制互补，协同降压，不良反应轻",
                    "4" => "D.	高血压治疗需要考虑成本/效益",
                ],
            ],
            [
                "qid" => "q2",
                "title" => "原研的新型复方制剂奥美沙坦酯奥氯地平片已于2018年在中国获批，其循证医学结果，哪几点您感兴趣【多选】",
                "bg_color" => "#4472c4",
                "input_type" => "checkbox",
                "option" => [
                    "1" => "A.	降压幅度大，降压达标率高",
                    "2" => "B.	24小时平稳降压",
                    "3" => "C.	安全性佳，提高依从性，降低CVD风",
                    "4" => "D.	具有更好的药物经济性",
                ],
            ],
            [
                "qid" => "q3a",
                "title" => "2019年医保调整在即，奥美沙坦酯氨氯地平片价格低于同类型等效产品，您是否会支持其进入医保，造福更多临床患者？【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A.	是",
                    "2" => "B.	否",
                ],
            ],
            [
                "qid" => "q3",
                "title" => "CHA2DS2-VASc评分≥2分的非瓣膜性房颤患者，您会首选以下哪一类药物进行抗凝？【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A.	维生素K拮抗剂",
                    "2" => "B．	低分子肝素",
                    "3" => "C．	新型口服抗凝药（NOAC）",
                    "4" => "D．	阿司匹林或氯吡格雷",
                ],
            ],
            [
                "qid" => "q4",
                "title" => "2018年12月艾多沙班已于国内获批上市，您是否知晓它的产品特点？【多选】",
                "bg_color" => "#4472c4",
                "input_type" => "checkbox",
                "option" => [
                    "1" => "A．	源自亚洲：唯一一个亚洲原研的NOAC（日本）",
                    "2" => "B．	极简：每天仅需服用1次",
                    "3" => "C．	安心：可明显降低出血风险",
                    "4" => "D．	拥有最多东亚房颤患者研究数据，剂量可调整（60mg，30mg）",
                ],
            ],
//            [
//                "qid" => "q5",
//                "title" => "2019年医保调整在即，您是否会支持艾多沙班、奥美沙坦酯氨氯地平片进入医保，造福更多临床患者？【单选】",
//                "bg_color" => "#4472c4",
//                "input_type" => "radio",
//                "option" => [
//                    "1" => "A．	是",
//                    "2" => "B．	否",
//                ],
//            ],
            [
                "qid" => "q6",
                "title" => "2019年医保调整在即，您是否会支持艾多沙班进入医保，造福更多临床患者？【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A．	是",
                    "2" => "B．	否",
                ],
            ],
        ];

        $uid = $this->input->get("uid", true);
        $uid = $this->db->escape_str($uid);
        if (!$uid) {
            die("Error!");
        }

        //获取选中状态
        $get_hos_drug = getDataByConditionCi("iqvia_one", " AND uid=?", "*", true, [$uid]);
        if ($get_hos_drug && $get_hos_drug['is_finish'] == 2) {//已完成
            $url = "https://www.drsay.cn/bk/s/327_5b44252c14175ce5?uid={$uid}";
            redirect($url);
        }
        //查询问卷结果
        $iqvia_info = getDataByConditionCi("iqvia_one", " AND uid=?", "*", true, [$uid]);
        $res_iqvia = [];
        if ($iqvia_info) {
            $res_iqvia = [
                "s1" => $iqvia_info['s1'] ? explode(",", $iqvia_info['s1']) : "",
                "q1" => $iqvia_info['q1'] ? explode(",", $iqvia_info['q1']) : "",
                "q2" => $iqvia_info['q2'] ? explode(",", $iqvia_info['q2']) : "",
                "q3" => $iqvia_info['q3'] ? explode(",", $iqvia_info['q3']) : "",
                "q4" => $iqvia_info['q4'] ? explode(",", $iqvia_info['q4']) : "",
                "q5" => $iqvia_info['q5'] ? explode(",", $iqvia_info['q5']) : "",
                "q3a" => $iqvia_info['q3a'] ? explode(",", $iqvia_info['q3a']) : "",
                "q6" => $iqvia_info['q6'] ? explode(",", $iqvia_info['q6']) : "",
            ];
        } else {//没有记录，即时添加
            $insert_data = [
                "uid" => $this->db->escape_str($uid),
                "create_at" => time(),
                "is_new" => 1,
            ];
            saveData("iqvia_one", $insert_data);
        }
        $data = [
            "question" => $question,
            "uid" => $uid,
            "res_iqvia" => $res_iqvia,
        ];
        $this->load->view("/one_survey/iqvia_one", $data);
    }

    function iqvia_two()
    {
        $question = [
            [
                "qid" => "s1",
                "title" => "请问您的职称【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A.	主任医师",
                    "2" => "B.	副主任医师",
                    "3" => "C.	主治医师",
                    "4" => "D.	其他",
                ],
            ],
            [
                "qid" => "q1",
                "title" => "对于临床的静脉血栓栓塞患者，您是否会应用预测评分评估其危险程度？【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A．	是",
                    "2" => "B．	否",
                    "3" => "C．	视具体情况而定",
                ],
            ],
            [
                "qid" => "q2",
                "title" => "对于临床的静脉血栓栓塞患者，您通常会采取多少时间的抗凝治疗？【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A．	三月",
                    "2" => "B．	六月",
                    "3" => "C．	一年",
                    "4" => "D．	长期",
                    "5" => "E．	视具体情况而定",
                ],
            ],
            [
                "qid" => "q3",
                "title" => "相比于传统口服抗凝药物，您觉得新型口服抗凝药物NOAC有什么优点？【多选】",
                "bg_color" => "#4472c4",
                "input_type" => "checkbox",
                "option" => [
                    "1" => "A．	服用方便",
                    "2" => "B．	无需频繁监测",
                    "3" => "C．	出血风险降低",
                    "4" => "D．	临床疗效显著",
                    "5" => "E．	药物副作用低",
                ],
            ],
            [
                "qid" => "q4",
                "title" => "2018年12月25日，艾多沙班已于国内获批上市，您是否知晓它的产品特点？【多选】",
                "bg_color" => "#4472c4",
                "input_type" => "checkbox",
                "option" => [
                    "1" => "A．	源自亚洲：唯一一个亚洲原研的NOAC（日本）",
                    "2" => "B．	极简：每天仅需服用1次",
                    "3" => "C．	安心：可明显降低出血风险",
                    "4" => "D．	拥有最大规模VTE患者临床研究数据",
                    "5" => "E．	对亚洲人群具有良好的安全性和有效性",
                ],
            ],
            [
                "qid" => "q5",
                "title" => "2019年医保调整在即，您是否会支持艾多沙班进入医保，造福更多临床患者？【单选】",
                "bg_color" => "#4472c4",
                "input_type" => "radio",
                "option" => [
                    "1" => "A．	是",
                    "2" => "B．	否",
                ],
            ],

        ];


        $uid = $this->input->get("uid", true);
        $uid = $this->db->escape_str($uid);
        if (!$uid) {
            die("Error!");
        }

        //获取选中状态
        $get_hos_drug = getDataByConditionCi("iqvia_two", " AND uid=?", "*", true, [$uid]);
        if ($get_hos_drug && $get_hos_drug['is_finish'] == 2) {//已完成
            $url = "https://www.drsay.cn/bk/s/328_e4bcc4f6da9c4991?uid={$uid}";
            redirect($url);
        }
        //查询问卷结果
        $iqvia_info = getDataByConditionCi("iqvia_two", " AND uid=?", "*", true, [$uid]);
        $res_iqvia = [];
        if ($iqvia_info) {
            $res_iqvia = [
                "s1" => $iqvia_info['s1'] ? explode(",", $iqvia_info['s1']) : "",
                "q1" => $iqvia_info['q1'] ? explode(",", $iqvia_info['q1']) : "",
                "q2" => $iqvia_info['q2'] ? explode(",", $iqvia_info['q2']) : "",
                "q3" => $iqvia_info['q3'] ? explode(",", $iqvia_info['q3']) : "",
                "q4" => $iqvia_info['q4'] ? explode(",", $iqvia_info['q4']) : "",
                "q5" => $iqvia_info['q5'] ? explode(",", $iqvia_info['q5']) : "",
            ];
        } else {//没有记录，即时添加
            $insert_data = [
                "uid" => $this->db->escape_str($uid),
                "create_at" => time(),
            ];
            saveData("iqvia_two", $insert_data);
        }

        $data = [
            "question" => $question,
            "uid" => $uid,
            "res_iqvia" => $res_iqvia,
        ];
        $this->load->view("/one_survey/iqvia_two", $data);
    }

    function insert_iqvia_one()
    {
        $q = trim($this->input->post("q", true));//题目
        $q_option = $this->input->post("q_option", true);//选项
        $uid = trim($this->input->post("uid", true));
        $type = trim($this->input->post("type", true));
        if ($q && $uid && $type) {
            //查询uid是否存在
            $iqvia_info = getDataByConditionCi("iqvia_one", " AND uid=?", "*", true, [$uid]);
            if (!$iqvia_info) {//不存在
                $insert_data = [
                    $q => $type == "radio" ? $q_option : implode(",", $q_option),
                    "uid" => $this->db->escape_str($uid),
                    "create_at" => time(),
                    "is_new" => 1,
                ];
                $res = saveData("iqvia_one", $insert_data);
                if ($res) {
                    _back_msg("success", "操作成功！");
                }
            } else {//存在
                $update_data = [
                    $q => $type == "radio" ? $q_option : implode(",", $q_option),
                ];
                $res = upData("iqvia_one", $iqvia_info['id'], $update_data);
                if ($res) {
                    _back_msg("success", "操作成功！");
                }
            }
        }
        _back_msg("error", "操作失败！");
    }

    //项目结束
    function iqvia_one_finish()
    {
        $uid = $this->input->post("uid", true);
        $uid = trim($uid);//提交人
        if ($uid){
            //查询记录是否已存在
            $get_hos_drug = getDataByCondition("iqvia_one", " AND uid='{$uid}'", "*", true);
            if ($get_hos_drug) {
                //没有数据，不允许完成
//                if (!$get_hos_drug['q1'] || !$get_hos_drug['q2'] || !$get_hos_drug['q3'] || !$get_hos_drug['q4'] || !$get_hos_drug['q5'] || !$get_hos_drug['s1']) {
                //问卷调整 2019-04-10
                if (!$get_hos_drug['q1'] || !$get_hos_drug['q2'] || !$get_hos_drug['q3a'] || !$get_hos_drug['q3'] || !$get_hos_drug['q4'] || !$get_hos_drug['s1'] || !$get_hos_drug['q6']) {
                    _back_msg("error", "请完整填写再提交！");
                }
                $update_data = [
                    "is_finish" => 2,
                    "finish_time" => time(),
                ];
                upData("iqvia_one", $get_hos_drug['id'], $update_data);
//                $url = "https://www.drsay.cn/bk/s/327_5b44252c14175ce5?uid={$uid}";
                // rainnie 2019-04-30调整成如下链接
//                $url = "https://www.drsay.cn/bk/s/379_54c5df6546e81622?uid={$uid}";
                // rainnie 2019-04-30调整成如下链接,再次调整
                $url = "https://www.drsay.cn/bk/s/382_f2a10684a75fd92d?uid={$uid}";
                _back_msg("success", "提交成功！", $url);
            } else {
                _back_msg("error", "请至少提交一项数据信息！");
            }
        }
    }


    function insert_iqvia_two()
    {
        $q = trim($this->input->post("q", true));//题目
        $q_option = $this->input->post("q_option", true);//选项
        $uid = trim($this->input->post("uid", true));
        $type = trim($this->input->post("type", true));
        if ($q && $uid && $type) {
            //查询uid是否存在
            $iqvia_info = getDataByConditionCi("iqvia_two", " AND uid=?", "*", true, [$uid]);
            if (!$iqvia_info) {//不存在
                $insert_data = [
                    $q => $type == "radio" ? $q_option : implode(",", $q_option),
                    "uid" => $this->db->escape_str($uid),
                    "create_at" => time(),
                ];
                $res = saveData("iqvia_two", $insert_data);
                if ($res) {
                    _back_msg("success", "操作成功！");
                }
            } else {//存在
                $update_data = [
                    $q => $type == "radio" ? $q_option : implode(",", $q_option),
                ];
                $res = upData("iqvia_two", $iqvia_info['id'], $update_data);
                if ($res) {
                    _back_msg("success", "操作成功！");
                }
            }
        }
        _back_msg("error", "操作失败！");
    }

    //项目结束
    function iqvia_two_finish()
    {
        $uid = $this->input->post("uid", true);
        $uid = trim($uid);//提交人
        if ($uid){
            //查询记录是否已存在
            $get_hos_drug = getDataByCondition("iqvia_two", " AND uid='{$uid}'", "*", true);
            if ($get_hos_drug) {
                //没有数据，不允许完成
                if (!$get_hos_drug['q1'] || !$get_hos_drug['q2'] || !$get_hos_drug['q3'] || !$get_hos_drug['q4'] || !$get_hos_drug['q5'] || !$get_hos_drug['s1']) {
                    _back_msg("error", "请完整填写再提交！");
                }
                $update_data = [
                    "is_finish" => 2,
                    "finish_time" => time(),
                ];
                upData("iqvia_two", $get_hos_drug['id'], $update_data);
//                $url = "https://www.drsay.cn/bk/s/328_e4bcc4f6da9c4991?uid={$uid}";
                // rainnie 2019-04-30调整成如下链接
//                $url = "https://www.drsay.cn/bk/s/380_f11635a4c802a881?uid={$uid}";
                // rainnie 2019-04-30调整成如下链接,再次调整
                $url = "https://www.drsay.cn/bk/s/381_9138ec5ef2b86f4d?uid={$uid}";

                _back_msg("success", "提交成功！", $url);
            } else {
                _back_msg("error", "请至少提交一项数据信息！");
            }
        }
    }




}