<?php
/**
 * Created by PhpStor<PERSON>.
 * User: Amy
 * Date: 2019/11/4
 */
class I_survey_adm extends CI_Controller
{
    function __construct()
    {

        parent::__construct();
//                error_reporting(-1);
//                ini_set('display_errors', 1);

    }
    /**
     * 客户登录
     */
    public function login(){
        $data_post = $this->input->post();
        if(!empty($data_post)){
            if(empty($data_post['account'])) _back_msg('error',"请输入账号！");
            if(empty($data_post['pwd'])) _back_msg('error',"请输入密码！");
            $account = trim($data_post['account']);
            $pwd = md5(trim($data_post['pwd']));
            $get_isuv_user_info = getDataByConditionCi("app_admin", " AND username=? AND password=?", '*', true, [$account, $pwd]);
            if(!$get_isuv_user_info){
                _back_msg('error',"账号或密码错误！");
            }
            if($get_isuv_user_info['status'] != 1){
                _back_msg('error',"账号被禁用！");
            }

            //登录成功，记录SESSION
            $data_session = array(
                'i_survey_adm_name' => $get_isuv_user_info['name'],
                'i_survey_adm_account' => $account,
                'i_survey_adm_id' => $get_isuv_user_info['id'],
            );

            $this->session->set_userdata($data_session);
            _back_msg('success',"登录成功！", "/i_survey_adm/index");
        } else {
            $i_survey_account = $this->session->userdata('i_survey_adm_account');
            if($i_survey_account){
                redirect('/i_survey_adm/index');
            }
            $this->load->view("/i_survey_adm/login");
        }
    }

    /**
     * 登录退出
     */
    public function out(){
        $this->session->unset_userdata('i_survey_adm_name');
        $this->session->unset_userdata('i_survey_adm_account');
        $this->session->unset_userdata('i_survey_adm_id');
        redirect('/i_survey_adm/login');
    }

    //获取进行的项目列表
    public function index(){
        //登录验证
        $this->check_login();
        $data_post = $this->input->post();
        if ($data_post) {
            $data_post = format_data($data_post);
            $act = $data_post['act'];
            $back_url = "";
            if ($act == "change_show") {//是否接受外部支持
                $id = (int)trim($data_post['id']);
                $res_update = $this->db->query("UPDATE app_project SET is_request_support=(IF(is_request_support = 0,1,0)) WHERE id=?", [$id]);

                if ($res_update) {
                    $project_log_arr = [
                        "log_code" => APP_PROJECT_EDIT_I_SURVEY_ADM,
                        "pid" => $id,
                        "add_uid" => $this->session->userdata('i_survey_adm_id'),
                        "add_time" => time(),
                        "new_data" => json_encode($data_post, JSON_UNESCAPED_UNICODE)
                    ];
                    $this->db->insert("app_project_log", $project_log_arr);
                }
            } else if($act == 'edit_project'){
                //医师礼金
                $answer_question_money = $data_post['answer_question_money'];
                //推荐礼金
                $recommend_money = $data_post['recommend_money'];
                $back_url = $data_post['back_url'];
                foreach ($answer_question_money as $k => $v) {
                    $this->db->where("id", $k);
                    $res_update = $this->db->update("app_project", ["answer_question_money" => $v, "recommend_money" => $recommend_money[$k]]);

                    if ($res_update) {
                        $project_log_arr = [
                            "log_code" => APP_PROJECT_EDIT_I_SURVEY_ADM,
                            "pid" => $k,
                            "add_uid" => $this->session->userdata('i_survey_adm_id'),
                            "add_time" => time(),
                            "new_data" => json_encode($data_post, JSON_UNESCAPED_UNICODE)
                        ];
                        $this->db->insert("app_project_log", $project_log_arr);
                    }
                }
            }

            _back_msg("success", "提交成功", $back_url ? $back_url : "/i_survey_adm/index");
        }

        $offset = $this->input->get('per_page', true);
        $page = 20;
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*$page : 0;
        //检测符合展示条件的项目信息
        $pids = [];
        $pids = $this->start_project();
        $project_list = [];
        $pagination = "";
        if ($pids) {
            $pids_info = implode(",", $pids);
            $project_list = getDataByCondition('app_project a left join app_client c on a.client_id=c.id', " and a.id in({$pids_info}) order by a.is_request_support DESC,a.id desc limit {$offset},{$page}", "a.*,c.client_name");

            $total = getDataNumByCondition('app_project', " and id in(".implode(",", $pids).")");
            echo "<!--".$total."-->";
            //查询分页
            $url = '/i_survey_adm/index';
            $pagination = getPagination($total, $url, $page);
        }
        $admin_list = $this->admin_post();

        //项目类型
        $arr_pro_type = $this->config->item("arr_pro_type");
        $project_status = $this->config->item("project_status");


        $data =[
            'title'=> "ISURVEY",
            'pagination'    => $pagination,
            'project_list'    => $project_list,
            'arr_pro_type'    => $arr_pro_type,
            'project_status'    => $project_status,
            'offset'    => $offset,
            'admin_list'    => $admin_list,

        ];
        $this->load->view("/i_survey_adm/dc_index",$data);
    }

    //集团账号
    public function i_survey_user()
    {
        //登录验证
        $i_survey_user = $this->check_login();
        $i_survey_adm_id = $i_survey_user['i_survey_adm_id'];
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_data($post_data);
            $back_url = $post_data['back_url'];
            //老记录，更新
            $i_survey_user_old = $post_data['i_survey_user_old'];
            //新记录，添加
            $i_survey_user_new = $post_data['i_survey_user_new'];
            $i_survey_names = [];
            $i_survey_user_info = $this->db->get("i_survey_user")->result_array();
            if ($i_survey_user_info) {
                foreach ($i_survey_user_info as $v_user_info) {
                    $i_survey_names[] = $v_user_info['code'];
                }
            }
            if ($i_survey_user_new) {
                foreach ($i_survey_user_new as $k_new_check => $v_new_check) {
                    if (in_array($v_new_check['code'], $i_survey_names)) {
                        _back_msg("error", "【{$v_new_check['code']}】账号重复，请更换账号名！");
                    }
                }
                foreach ($i_survey_user_new as $k_new => $v_new) {
                    $insert_data_new = [
                        "mobile" => $v_new['mobile'],
                        "payment_name" => $v_new['payment_name'],
                        "payment_account" => $v_new['payment_account'],
                        "name" => $v_new['name'],
                        "code" => $v_new['code'],
                        "password" => $v_new['password'],
                    ];
                    $this->db->insert("i_survey_user", $insert_data_new);
                    $res_insert_id = $this->db->insert_id();
                    if ($res_insert_id) {
                        //新增变更日志
                        $insert_log_data = [
                            "i_survey_id" => $res_insert_id,
                            "new_data" => $insert_data_new ? json_encode($insert_data_new, JSON_UNESCAPED_UNICODE) : "",
                            "add_uid" => $i_survey_adm_id ?? "",
                            "add_time" => time(),
                        ];
                        $this->db->insert("i_survey_user_log", $insert_log_data);
                    }
                }
            }

            if ($i_survey_user_old) {
                foreach ($i_survey_user_old as $k_old => $v_old) {
                    $update_data_old = [
                        "mobile" => $v_old['mobile'],
                        "payment_name" => $v_old['payment_name'],
                        "payment_account" => $v_old['payment_account'],
                        "name" => $v_old['name'],
                        "password" => $v_old['password'],
                    ];
                    $this->db->where("id", $k_old);
                    $res_update = $this->db->update("i_survey_user", $update_data_old);

                    if ($res_update) {
                        //新增变更日志
                        $insert_log_data = [
                            "i_survey_id" => $k_old,
                            "new_data" => $update_data_old ? json_encode($update_data_old, JSON_UNESCAPED_UNICODE) : "",
                            "add_uid" => $i_survey_adm_id ?? "",
                            "add_time" => time(),
                        ];
                        $this->db->insert("i_survey_user_log", $insert_log_data);
                    }
                }
            }
            _back_msg("success", "提交成功", $back_url ? $back_url : "/i_survey_adm/i_survey_user");
        }
        $offset = $this->input->get('per_page', true);
        $page = 20;
        $offset = (is_numeric($offset) && $offset > 0) ? (intval($offset)-1)*$page : 0;

        $list = getDataByCondition('i_survey_user', " order by `status` ASC, id desc limit {$offset},{$page}");

        $total = getDataNumByCondition('i_survey_user', "");
        //查询分页
        $url = '/i_survey_adm/i_survey_user';
        $pagination = getPagination($total, $url, $page);
        $i_survey_ids = $i_survey_project = $i_survey_c = [];
        if ($list) {
            foreach ($list as $v) {
                $i_survey_ids[$v['id']] = $v['id'];
            }
            //统计每个人的完成量
            $i_survey_project = $this->db->query("SELECT i_survey_id,count(pid) as pid_num,sum(complete_c) as complete_c,sum(finish_c) as finish_c,sum(estimated_revenue) as estimated_revenue,sum(real_income) as real_income FROM i_survey_dc_hand_project WHERE i_survey_id in(".implode(",", $i_survey_ids).") GROUP BY i_survey_id")->result_array();
            foreach($i_survey_project as $v_project_c) {
                $i_survey_c[$v_project_c['i_survey_id']] = $v_project_c;
            }
        }

        $data =[
            'title'=> "集团账号",
            'pagination'    => $pagination,
            'list'    => $list,
            'offset'    => $offset,
            'i_survey_c'    => $i_survey_c,
        ];
        $this->load->view("/i_survey_adm/i_survey_user",$data);
    }

    ############  公共方法  ############

    private function admin_post(){
        $admin_list = getDataByCondition('app_admin',"");
        if(empty($admin_list)){
            return array();exit;
        }
        $res_admin_list = [];
        foreach ($admin_list as $v){
            $res_admin_list[$v['id']] = $v['en_name'];
        }
        return !empty($res_admin_list) ? $res_admin_list : array();
    }

    //获取进行中的项目信息
    private function start_project()
    {
        //进行的项目
//        $project_list = getDataByCondition('app_project', " and pro_status = 3 AND pro_type=1 AND pro_sample_num > c_num AND is_request_support=1");
        $project_list = getDataByCondition('app_project', " and pro_status = 3 AND pro_type=1 AND pro_sample_num > c_num ORDER BY is_request_support DESC");
        echo "<!--".$this->db->last_query()."-->";
        $pids = $res_pids = [];
        if ($project_list) {//检测内部资源的外包是否是进行状态
            foreach ($project_list as $v) {
                $pids[$v['id']] = $v['id'];
            }
            //项目进行，内部资源，外包进行
            $partner_info = getDataByCondition('app_project_partner', " AND project_id in(".implode(',', $pids).") and property = 1 AND status=1");

            if ($partner_info) {
                foreach ($partner_info as $v_partner) {
                    //检测每个项目外包是否有数据
                    $implement_info = getDataByCondition("app_project_implement_{$v_partner['project_id']}", " AND partner_id={$v_partner['partner_id']} limit 1", "*", true);
                    if ($implement_info) {
                        $res_pids[$v_partner['project_id']] = $v_partner['project_id'];
                    }
                }
            }
        }
        return $res_pids;
    }

    //登录验证
    private function check_login()
    {
        $i_survey_account = $this->session->userdata('i_survey_adm_account');
        $i_survey_id = $this->session->userdata('i_survey_adm_id');
        if(empty($i_survey_account)){
            redirect('/i_survey_adm/login');
        }
        return ["i_survey_adm_id" => $i_survey_id,"i_survey_adm_account" => $i_survey_account];
    }


}
