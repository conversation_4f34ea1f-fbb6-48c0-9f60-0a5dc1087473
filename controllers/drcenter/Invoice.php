<?php
class Invoice extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    public function info()
    {
        $this->load->view("/drcenter/invoice/info.html");
    }

    public function menu()
    {
        $this->load->view("/drcenter/invoice/menu.html");
    }

    public function index()
    {
        $this->load->view("/drcenter/invoice/index.html");
    }


    public function index1()
    {
        $this->load->view("/drcenter/invoice/01.html");
    }

    public function index2()
    {
        $this->load->view("/drcenter/invoice/02.html");
    }

    public function index3()
    {
        $this->load->view("/drcenter/invoice/03.html");
    }

    public function index4()
    {
        $this->load->view("/drcenter/invoice/04.html");
    }

    public function index5()
    {
        $this->load->view("/drcenter/invoice/05.html");
    }

    public function index6()
    {
        $this->load->view("/drcenter/invoice/06.html");
    }

    public function index7()
    {
        $this->load->view("/drcenter/invoice/07.html");
    }

    public function index8()
    {
        $this->load->view("/drcenter/invoice/08.html");
    }

    public function index9()
    {
        $this->load->view("/drcenter/invoice/09.html");
    }

    public function index10_1()
    {
        $this->load->view("/drcenter/invoice/10_1.html");
    }

    public function index10_2()
    {
        $this->load->view("/drcenter/invoice/10_2.html");
    }

    public function index11()
    {
        $this->load->view("/drcenter/invoice/11.html");
    }
}
