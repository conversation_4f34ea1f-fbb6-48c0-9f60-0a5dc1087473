<?php
defined('BASEPATH') OR exit('No direct script access allowed');
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Text;
class Dr_wx extends CI_Controller
{

    public $app;
    function __construct()
    {
        parent::__construct();

        $config = [
            //上医说服务号
            'app_id' => 'wxfed3a1a3c17d5fcd',
            'secret' => '7f7ffaff519be23fbe364441b4174ae2',
            'token' => 'drsay',
            'aes_key' => 'hZAGpKDTNnyp0L9Sc7UGXFtbmoBOWgxXn53agPgAINg',

            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];

        $this->app = Factory::officialAccount($config);

        error_reporting(-1);
        ini_set('display_errors', 1);

    }


    //微信后台只能填写一个服务器地址，所以 【服务器验证】 与 【消息的接收与回复】，都在这一个链接内完成交互
    function wx_auth()
    {
//        //微信与服务器第一次验证时，直接返回微信的信息即可
//        $response = $this->app->server->serve();
//        // 将响应输出
//        $response->send();exit;
//        {"ToUserName":"gh_3359cc3afaf8","FromUserName":"o5yMv1E-aSt58esSx_Bu51pzBdM8","CreateTime":"**********","MsgType":"event","Event":"SCAN","EventKey":"doctor_2982098","Ticket":"gQFa7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAycE5JaGMzRHpjTGsxb1FzZ3h2Y1gAAgTcmVBfAwRYAgAA"}
//        $this->get_wx_msg();
        $app = $this->wx_config();
        $postStr = file_get_contents("php://input");
        if (!empty($postStr)) {
            $post_data = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
//            file_put_contents("./tmp/wx_event.txt", json_encode($post_data, JSON_UNESCAPED_UNICODE)."\n", FILE_APPEND | LOCK_EX);
            $insert_data = [
                "msg_type" => $post_data->MsgType ?? "",
                "event_type" => $post_data->Event ?? "",
                "event_key" => $post_data->EventKey ?? "",
                "back_data" => json_encode($post_data, JSON_UNESCAPED_UNICODE),
                "add_time" => time(),
            ];
            $this->db->insert("wx_event", $insert_data);
//            file_put_contents("./tmp/wx_event.txt", $this->db->last_query()."\n", FILE_APPEND | LOCK_EX);
//            $text = new Text('您好！overtrue。'.$this->db->last_query());
//            $message['sql'] = $this->db->last_query();
//            $app->server->push(function ($message) {
//                return "您好！欢迎使用上医说!";
//            });
//            $response = $app->server->serve();
//            // 将响应输出
//            $response->send();
        }



        exit;

        #### 消息推送接口 ###
//        $this->app->server->push(function ($message) {
//            return "您好！欢迎使用上医说!";
//        });
        #### 消息推送接口 ###

//        ### 发起授权    ###
//        $response = $this->app->oauth->scopes(['snsapi_userinfo'])
//            ->redirect("/drcenter/index");
//        ### 发起授权    ###
//
////        $response = $this->app->server->serve();
//        // 将响应输出
//        $response->send();exit;
    }

    //微信服务器返回的信息
    private function get_wx_msg()
    {
        $response = $this->app->server->serve();
        // 将响应输出
        return $response->send();
    }

    //获取授权用户信息
    function get_user()
    {
        echo "<pre />";
        //获取授权用户信息
        $app = $this->wx_config();
        $user = $app->oauth->user();
        print_r($user->toArray());
        die;
    }

    //获取授权用户信息
    function get_user_list()
    {
        echo "<pre />";
        //获取授权用户信息
        $app = $this->wx_config();
        $user = $app->user->list();
        print_r($user);

        //获取单个用户信息
        $one_info = $app->user->get('o5yMv1O5XjUowaBq3lmowDLeZc00');
        print_r($one_info);
        die;
    }

    //创建临时二维码
    function get_qrcode_temp()
    {
//        echo "<pre />";
        $app = $this->wx_config();
        //医生二维码
//        $result = $app->qrcode->temporary('foo', 6 * 24 * 3600);
//        $result = $app->qrcode->temporary('doc_2982098', 6 * 24 * 3600);
        //有效时间：10分钟，最长可设置：604800秒
        $result = $app->qrcode->temporary('doc_2982098', 10 * 60);
        $this->db->insert("wx_fwh_ticket", ["back_data" => json_encode($result, JSON_UNESCAPED_UNICODE), "type" => 1]);
        echo "finish!";
//        //获取二维码内容
//        $ticket = $result['ticket'];
//        $url = $app->qrcode->url($ticket);
//        $content = file_get_contents($url); // 得到二进制图片内容
//        //临时二维码存储路径
//        $dir = "./uploads/qrcode/";
//        if(! is_dir( $dir ) ) {
//            mk_dir($dir);
//        }
//        file_put_contents('/code.jpg', $content); // 写入文件
//        print_r($result);
    }


    private function wx_config()
    {
        $config = [
            //上医说服务号
            'app_id' => 'wxfed3a1a3c17d5fcd',
            'secret' => '7f7ffaff519be23fbe364441b4174ae2',
            'token' => 'drsay',
            'aes_key' => 'hZAGpKDTNnyp0L9Sc7UGXFtbmoBOWgxXn53agPgAINg',

            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];
        $app = Factory::officialAccount($config);
        return $app;
    }

    //更新健康通JKT公众号医师数据
    function update_jkt_user()
    {
        $DB1 = $this->load->database("jkt_service_num", TRUE);
        $info = $DB1->query("SELECT openid FROM jkt_service_num_member WHERE is_info=0 LIMIT 100")->result_array();
        $config = [
            //上医说服务号
            'app_id' => 'wxa3cdce5b958ad929',
            'secret' => 'cd8ed4658d4aa80409fc1843acd91e7f',
            'token' => '5s5t9u7kop58sxcv41th25kn3m6l9q1j',
            'aes_key' => '4WiUgX97avpiGe0YJmCnu0fowPa13JeMfIQW5zeSujC',

            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];
        $app = Factory::officialAccount($config);
        $openids = array_column($info, "openid");
        $users = $app->user->select($openids);
        if (isset($users['user_info_list'])) {
            foreach ($users['user_info_list'] as $v) {
                $openid = $v['openid'];
                $v['tagid_list'] = $v['tagid_list'] ? json_encode($v['tagid_list'], JSON_UNESCAPED_UNICODE) : "";
                unset($v['openid']);
                $v['is_info'] = 1;
                $v['data_info'] = json_encode($v, JSON_UNESCAPED_UNICODE);
                $DB1->update("jkt_service_num_member", $v, ["openid" => $openid]);
            }
        }
        echo "finish!";
    }

    //健康通JKT公众号会员数据拉取
    function get_jkt_user_list()
    {
        die;
        $user = $this->insert_to_jkt_data();
        echo "<pre />";
        print_r($user);
    }

    private function insert_to_jkt_data($nextOpenId = "")
    {
        $config = [
            //上医说服务号
            'app_id' => 'wxa3cdce5b958ad929',
            'secret' => 'cd8ed4658d4aa80409fc1843acd91e7f',
            'token' => '5s5t9u7kop58sxcv41th25kn3m6l9q1j',
            'aes_key' => '4WiUgX97avpiGe0YJmCnu0fowPa13JeMfIQW5zeSujC',

            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];
        $app = Factory::officialAccount($config);
        if ($nextOpenId) {//存在下一个
            $user = $app->user->list($nextOpenId);
        } else {
            $user = $app->user->list();
        }

        if (isset($user['data']['openid']) && $user['data']['openid']) {
            $insert_data = [];
            foreach ($user['data']['openid'] as $v) {
                $insert_data[] = [
                    "openid" => $v
                ];
            }
            if ($insert_data) {
                $this->db->insert_batch("jkt_service_num_member", $insert_data);
            }
        }
        if (isset($user['next_openid']) && $user['next_openid']) {
            return $this->insert_to_jkt_data($user['next_openid']);
        }
        return $user;
    }




}
