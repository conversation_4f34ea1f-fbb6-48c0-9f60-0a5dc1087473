<?php
use EasyWeChat\Factory;
class Dr extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        ####    微信公众号链接进入   ####
        $from_where = trim($this->input->get("from_where", true));
        if ($from_where == "wx") {
            $app = $this->wx_config();
            $user = $app->oauth->user();
            $insert_data = [
                "back_data" => json_encode($user, JSON_UNESCAPED_UNICODE),
                "add_time" => time(),
            ];
            $this->db->insert("wx_event", $insert_data);
            if (isset($user->id) && $user->id) {
                //存session
                $wx_session = array(
                    'wx_open_id' => $user->id,
                );
                $this->session->set_userdata($wx_session);
            }
        }
        ####    微信公众号链接进入   ####
    }

    //首页
    public function index()
    {
        $this->load->view("/drcenter/head", ["title" => "首页".$this->session->userdata("wx_open_id")]);
        $this->load->view("/drcenter/index");
        $this->load->view("/drcenter/menu");
        $this->load->view("/drcenter/footer");
    }

    //学习
    public function study()
    {
        $this->load->view("/drcenter/head", ["title" => "学习"]);
        $this->load->view("/drcenter/study");
        $this->load->view("/drcenter/menu");
        $this->load->view("/drcenter/footer");
    }

    //发现
    public function find()
    {
        $this->load->view("/drcenter/head", ["title" => "发现"]);
        $this->load->view("/drcenter/find");
        $this->load->view("/drcenter/menu");
        $this->load->view("/drcenter/footer");
    }

    //粉丝
    public function fans()
    {
        $this->load->view("/drcenter/head", ["title" => "粉丝"]);
        $this->load->view("/drcenter/fans");
        $this->load->view("/drcenter/menu");
        $this->load->view("/drcenter/footer");
    }

    //我的
    public function mine()
    {
        $this->load->view("/drcenter/head", ["title" => "我的"]);
        $this->load->view("/drcenter/mine");
        $this->load->view("/drcenter/menu");
        $this->load->view("/drcenter/footer");
    }

    public function mine_auth()
    {
        $this->load->view("/drcenter/head", ["title" => "个人资料认证"]);
        $this->load->view("/drcenter/mine_auth");
        $this->load->view("/drcenter/footer");
    }

    public function setting()
    {
        $this->load->view("/drcenter/head", ["title" => "设置"]);
        $this->load->view("/drcenter/setting");
        $this->load->view("/drcenter/footer");
    }

    public function mine_qrcode()
    {
        $this->load->view("/drcenter/head", ["title" => "我的二维码"]);
        $this->load->view("/drcenter/mine_qrcode");
        $this->load->view("/drcenter/footer");
    }

    public function active()
    {
        $this->load->view("/drcenter/head", ["title" => "全部活动"]);
        $this->load->view("/drcenter/active");
        $this->load->view("/drcenter/footer");
    }

    public function online_service()
    {
        $this->load->view("/drcenter/head", ["title" => "在线咨询"]);
        $this->load->view("/drcenter/online_service");
        $this->load->view("/drcenter/footer");
    }

    public function store()
    {
        $this->load->view("/drcenter/head", ["title" => "学习乐园"]);
        $this->load->view("/drcenter/store");
        $this->load->view("/drcenter/menu");
        $this->load->view("/drcenter/footer");
    }

    public function msg_notice()
    {
        $this->load->view("/drcenter/head", ["title" => "消息通知"]);
        $this->load->view("/drcenter/msg_notice");
        $this->load->view("/drcenter/footer");
    }

    public function paitient_info()
    {
        $this->load->view("/drcenter/head", ["title" => "患者管理"]);
        $this->load->view("/drcenter/paitient_info");
        $this->load->view("/drcenter/footer");
    }

    public function doctor_certify()
    {
        $this->load->view("/drcenter/head", ["title" => "职业认证"]);
        $this->load->view("/drcenter/doctor_certify");
        $this->load->view("/drcenter/footer");
    }

    public function point()
    {
        $this->load->view("/drcenter/head", ["title" => "积分"]);
        $this->load->view("/drcenter/point");
        $this->load->view("/drcenter/footer");
    }

    public function login()
    {
        $this->load->view("/drcenter/head", ["title" => "登录"]);
        $this->load->view("/drcenter/login");
        $this->load->view("/drcenter/footer");
    }

    public function active_report()
    {
        $this->load->view("/drcenter/head", ["title" => "活动报表"]);
        $this->load->view("/drcenter/active_report");
        $this->load->view("/drcenter/footer");
    }


    public function questionnaire()
    {
        $this->load->view("/drcenter/head", ["title" => "问卷"]);
        $this->load->view("/drcenter/questionnaire");
        $this->load->view("/drcenter/footer");
    }

    public function statistics()
    {
        $this->load->view("/drcenter/head", ["title" => "统计报表"]);
        $this->load->view("/drcenter/statistics");
        $this->load->view("/drcenter/footer");
    }

    public function doctor_consult()
    {
        $this->load->view("/drcenter/head", ["title" => "患者咨询"]);
        $this->load->view("/drcenter/doctor_consult");
        $this->load->view("/drcenter/footer");
    }

    public function msg_notice_detail()
    {
        $this->load->view("/drcenter/head", ["title" => "消息详情"]);
        $this->load->view("/drcenter/msg_notice_detail");
        $this->load->view("/drcenter/footer");
    }

    public function questionnaire_result()
    {
        $this->load->view("/drcenter/head", ["title" => "答卷结果"]);
        $this->load->view("/drcenter/questionnaire_result");
        $this->load->view("/drcenter/footer");
    }

    public function health_info()
    {
        $this->load->view("/drcenter/head", ["title" => "患者健康信息"]);
        $this->load->view("/drcenter/health_info");
        $this->load->view("/drcenter/footer");
    }

    public function patient_active()
    {
        $this->load->view("/drcenter/head", ["title" => "白鸥全部活动"]);
        $this->load->view("/drcenter/patient_active");
        $this->load->view("/drcenter/footer");
    }

    public function point_details()
    {
        $this->load->view("/drcenter/head", ["title" => "积分明细"]);
        $this->load->view("/drcenter/point_details");
        $this->load->view("/drcenter/footer");
    }

    public function point_withdraw()
    {
        $this->load->view("/drcenter/head", ["title" => "积分提现"]);
        $this->load->view("/drcenter/point_withdraw");
        $this->load->view("/drcenter/footer");
    }


    public function freque_question()
    {
        $this->load->view("/drcenter/head", ["title" => "常见问题"]);
        $this->load->view("/drcenter/freque_question");
        $this->load->view("/drcenter/footer");
    }


    public function withdrawal_record()
    {
        $this->load->view("/drcenter/head", ["title" => "提现记录"]);
        $this->load->view("/drcenter/withdrawal_record");
        $this->load->view("/drcenter/footer");
    }

    public function withdrawal_process()
    {
        $this->load->view("/drcenter/head", ["title" => "提现进度"]);
        $this->load->view("/drcenter/withdrawal_process");
        $this->load->view("/drcenter/footer");
    }

    public function science_details()
    {
        $this->load->view("/drcenter/head", ["title" => "医学信息"]);
        $this->load->view("/drcenter/science_details");
        $this->load->view("/drcenter/footer");
    }

    public function user_certificate()
    {
        $this->load->view("/drcenter/head", ["title" => "用户授权"]);
        $this->load->view("/drcenter/user_certificate");
        $this->load->view("/drcenter/footer");
    }



    private function wx_config()
    {
        $config = [
            //上医说服务号
            'app_id' => 'wxfed3a1a3c17d5fcd',
            'secret' => '7f7ffaff519be23fbe364441b4174ae2',
            'token' => 'drsay',
            'aes_key' => 'hZAGpKDTNnyp0L9Sc7UGXFtbmoBOWgxXn53agPgAINg',

            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];
        $app = Factory::officialAccount($config);
        return $app;
    }
}
