<?php
/**
 * Created by PhpStorm.
 * User: ipmac
 * Date: 2018/2/28
 * Time: 下午12:01
 */
class Api_callback extends CI_Controller{
    public function mobile_back_status()
    {
        //接口文档地址：https://www.juhe.cn/docs/api/id/85
        /**
         * 接受话费\加油卡\流量充值业务 异步通知参数 参考示例
         */
//        $post_data = $this->input->post();
//        $back_info = '{"sporder_id":"J17081610050402898186862","orderid":"DRSAY_3_59","sta":"1","sign":"396301d4b75537c5d3ae6ae05a7af9c5"}';
        $data_post = $this->input->post();
        //打印数据
        file_put_contents("./tmp/mobile_back_status.txt", json_encode($data_post)."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);

        if(!$data_post){
            redirect(DRSAY_WEB);
        }
        $post_data = json_encode($data_post);
        //记录返回日志
        savePaymentOrderMobileBackInfo(array("back_info" => $post_data, "add_time"=>time()));
        $sporder_id          = $this->input->post('sporder_id', true);
        $orderid             = $this->input->post('orderid', true);
        $sta                 = $this->input->post('sta', true);
        $sign                = $this->input->post('sign', true);

        $sporder_id          = isset($sporder_id) ? addslashes($sporder_id) : ""; //聚合订单号
        $orderid             = isset($orderid) ? addslashes($orderid) : ""; //商户的单号
        $sta                 = isset($sta) ? addslashes($sta) : ""; //充值状态
        $sign                = isset($sign) ? addslashes($sign) : ""; //校验值

        //新支付表回调流程
        $this->new_mobile_back_status($sporder_id, $orderid, $sta, $sign);
        //通过orderid查询相关信息
        if ($orderid) {
//            $arr_orderid = explode("_", $orderid);
            $arr_orderid = $orderid;
            if ($arr_orderid) {
                $get_payment_order_info = getPaymentOrderById($arr_orderid);
                $order_id = $arr_orderid;
                $payment_order_detail_info = getPaymentOrderDetailByOutBizNo($orderid);
                $local_sign = md5(RECHARGE_KEY.$sporder_id.$orderid);//本地sign校验值

                //打印数据
//                $put_log = array($sporder_id,$orderid,$sta,$sign,$local_sign,$sign,$order_id,$payment_order_detail_info);
//                file_put_contents("./tmp/mobile_back_status.txt", json_encode($put_log)."**".date("Y-m-d H:i:s")."\n", FILE_APPEND | LOCK_EX);

                if ($local_sign == $sign) {
                    $payment_id = $payment_order_detail_info['id'];
                    $order_status = $order_detail_status = array();
                    if ($sta == '1') {
                        //充值成功,根据自身业务逻辑进行后续处理
                        $order_status = $order_detail_status = array("status" => '2');
                    } elseif ($sta =='9') {
                        //充值失败,根据自身业务逻辑进行后续处理
                        $order_status = $order_detail_status = array("status" => '3');

                    }
                    //更新订单信息
                    $order_detail_status['order_id'] = $sporder_id;
                    $order_detail_status['pay_date'] = time();
                    $res_up_pay_order_detail = upPaymentOrderDetailById($payment_id, $order_detail_status);

                    $order_detail = $this->db->where(array('order_id'=>$sporder_id))->get('app_payment_order_detail')->row_array();
                    if ($res_up_pay_order_detail) {
                        $res = upPaymentOrder($order_detail['payment_order_id'], $order_status);
                        if ($res) {echo "success";die;}
                    }
                }
            }

        }
    }

    /**
     * @param $sporder_id
     * @param $orderid
     * @param $sta
     * @param $sign
     * 新支付订单回调
     */
    public function new_mobile_back_status($sporder_id, $orderid, $sta, $sign){
        //通过orderid查询相关信息
        if ($orderid) {
            $arr_orderid = $orderid;
            if ($arr_orderid) {
                $payment_order_detail_info = getNewPaymentOrderDetailByOutBizNo($orderid);
                $local_sign = md5(RECHARGE_KEY.$sporder_id.$orderid);//本地sign校验值
                if ($local_sign == $sign) {
                    $payment_id = $payment_order_detail_info['id'];
                    $order_status = $order_detail_status = array();
                    if ($sta == '1') {
                        //充值成功,根据自身业务逻辑进行后续处理
                        $order_status = $order_detail_status = array("status" => '2');
                        $order_status["phone_back_status"] = 1;

                    } elseif ($sta =='9') {
                        //充值失败,根据自身业务逻辑进行后续处理
                        $order_status = $order_detail_status = array("status" => '3');
                        $order_status["phone_back_status"] = 2;
                    }
                    //更新订单信息
                    $order_detail_status['order_id'] = $sporder_id;
                    $order_detail_status['pay_date'] = time();
                    $res_up_pay_order_detail = upNewPaymentOrderDetailById($payment_id, $order_detail_status);
                    $order_detail = $this->db->where(array('order_id'=>$sporder_id))->get('app_payment_order_detail_new')->row_array();
                    if($sta == 9){
                        $new_order = $this->db->where(['id'=>$order_detail['payment_order_id']])->get('app_payment_order_new')->row_array();
                        $this->db->where(['id'=>$new_order['param']])->update('app_project_flow',['pay_status'=>3,'error_type'=>2]);
                    }
                    if ($res_up_pay_order_detail) {
                        $res = upNewPaymentOrder($order_detail['payment_order_id'], $order_status);
                        if ($res) {echo "success";die;}
                    }
                }
            }

        }
    }


}
