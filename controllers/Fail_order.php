<?php
/**
 * 失败订单流程处理
 * Created by PhpStorm.
 * User: AMY
 * Date: 2020-11-27
 * Time: 13:39
 */
use EasyWeChat\Factory;
class Fail_order extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('survey_model');
    }

    //验证参数有效性
    function check_fail_order_scene($scene, $is_ajax = true)
    {
        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
        if (!$scene) {
            if ($is_ajax) {
                _back_msg('error', '参数有误！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $arr_scene = explode("_", $scene);
        $order_id = $arr_scene[0];
        $encrypted_data = $arr_scene[1];
        if (!$order_id || !$encrypted_data) {//参数有误
            if ($is_ajax) {
                _back_msg('error', '参数有误！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $decrypt_scene = "order_{$order_id}";
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($encrypted_data !== $decrypt_data) {//解密值不等
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=?", [$order_id])->row_array();
        if (!$order_info) {
            if ($is_ajax) {
                _back_msg('error', '订单不存在！', $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }

        return $order_info;
    }


    //获取短信验证码
    public function pro_send_sms()
    {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $scene = $post_data['scene'];
            $verify_mobile = $post_data['verify_mobile'];
            try {
                $country = 68;

                ####    验证账号有效性
                $order_info = $this->check_fail_order_scene($scene);
                ####    验证账号有效性
                if ($order_info['mobile'] != $verify_mobile) {
                    throw new Exception("您输入的手机号码有误,请重新输入！");
                }
                //发送短信验证码
                $vcode = rand(10000,99999);
                $sms_log_code = SMS_LOG_CODE_FAIL_ORDER;
                $content = $lang_ary[LABEL_COMM_MOB_TEMPLATE];//国际短信通道使用
                $err_msg = "";
                if ($country == '68') {//中国短信，使用send_cloud发送
                    //创蓝
                    if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") {//本地
                        $st = true;
                    } else {
                        $sms_content = "上医说验证码:{$vcode}，".SMS_ACTIVE_TIME."分钟内有效，请勿泄漏！";//国际短信通道使用
                        $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                    }
                    if ($st === true) {
                        $send_st = true;
                    } else {
                        $send_st = false;
                        $err_msg = $st;
                    }
                }else{//国外短信，使用国际短信通道
                    $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                    $area = getDataByID("app_sys_dictionary", $country);
                    $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                    if ($st == "OK") {
                        $send_st = true;
                    }else{
                        $send_st = false;
                    }
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_EXCHANGE;
                if($send_st) {//短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);
                    if($insert_id_sms_code > 0) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    //登录
    public function login() {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            $scene = $post_data['scene'];
            $verify_mobile = $post_data['verify_mobile'];
            //验证数据有效性
            $verify_code = $post_data['verify_code'];
            try {
                ####    验证账号有效性
                $order_info = $this->check_fail_order_scene($scene);
                ####    验证账号有效性

                if (!check_mobile($order_info['mobile'])) {
                    throw new Exception("手机号有误，请确认后重新输入！[订单]");
                }
                if ($order_info['mobile'] != $verify_mobile) {//验证手机号码
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                $user_verification_code = get_verification_code($verify_mobile, VERIFICATION_CODE_EXCHANGE);
                if (strcasecmp($verify_code, $user_verification_code['vcode']) !== 0) {//检测两个字符串是否相等
                    throw new Exception($lang_ary[LABEL_EXCHANGE_VERIFICATION_CODE_ERROR]);
                }
                if ($user_verification_code) {
                    //更新验证码为已使用状态
                    update_verification_code($user_verification_code['id']);
                }
                //存session，方便从短信或者问卷链接进入时，直接跳过验证
                $this->session->set_userdata(["fail_order_{$order_info['uid']}" => $order_info['uid']]);
                $redirect_url = "/fail_order/modify_payment/?scene={$scene}";
                _back_msg("success", "验证通过", $redirect_url);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            $scene = trim($this->input->get("scene", true));
            ####    验证账号有效性
            $order_info = $this->check_fail_order_scene($scene, false);
            ####    验证账号有效性
            file_put_contents('./tmp/fail_order_login.txt', $scene."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);

            //通过pid及uid查询是否已经做过提现操作
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            if ($order_info['status'] !=3 || $order_info['order_status'] != 1) {
                redirect($redirect_url);
            }

            if (isset($this->session->userdata["fail_order_{$order_info['uid']}"]) && $this->session->userdata["fail_order_{$order_info['uid']}"] == $order_info['uid']) {
                redirect("/fail_order/modify_payment/?scene={$scene}");
            }

            $data = array(
                "scene" => $scene,
            );
            $this->load->view("/fail_order/login",$data);
        }
    }

    // 保存验证码数据
    private function set_vcode($data)
    {
        if (!$data) {return false;}
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }



    //修改支付方式
    function modify_payment()
    {
        //调取支付失败的订单
        $scene = trim($this->input->get("scene", true));
        ####    验证账号有效性
        $order_info = $this->check_fail_order_scene($scene, false);
        $order_id = $order_info['id'];
        $member_uid = $order_info['uid'];
        ####    验证账号有效性

        if (!isset($this->session->userdata["fail_order_{$member_uid}"]) || $this->session->userdata["fail_order_{$member_uid}"] != $member_uid) {
            file_put_contents('./tmp/modify_payment.txt', $order_id."_".$member_uid."_".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
            redirect("/fail_order/login/?scene={$scene}");
        }
        // 支付方式
        $sys_dictionary_phone = getDataByCondition('app_sys_dictionary',"and big_class_id =11");
        $res_dictionary = [];
        foreach ($sys_dictionary_phone as $v_dic){
            $res_dictionary[$v_dic['id']] =$v_dic['val'];
        }

        //订单编号
        $pid_info = payment_pid_format($order_info);
        //微信提现二维码
        $file_name = $this->wx_payment_qrcode($order_info['id']);
        $data = array(
           "scene"  => $scene,
           "order_info"  => $order_info,
           "res_dictionary"  => $res_dictionary,
           "pid_info"  => $pid_info,
           "file_name"  => $file_name ? file_get_contents("./uploads/wechat/fail_order/{$file_name}") : "",
        );
        $this->load->view("/fail_order/modify_payment",$data);
    }

    //支付方式提交
    function payment_sub()
    {
        $data_post = $this->input->post();
        $data_post = format_data($data_post);
        if (!$data_post) {
            _back_msg("error", "参数错误！");
        }
        $payment_type = $data_post['payment_type'];
        $payment_name = $data_post['payment_name'];
        $payment_account = $data_post['payment_account'];
        $scene = $data_post['scene'];
        ####    验证账号有效性
        $payment_order = $this->check_fail_order_scene($scene);
        $pid = $payment_order['pid'];
        $order_id = $payment_order['id'];
        $member_uid = $payment_order['uid'];
        ####    验证账号有效性
        if ($payment_order['order_status'] != 1) {//不是有效订单
            _back_msg("error", "订单无效，请联系管理员");
        }
        if ($payment_order['status'] != 3) {//不是支付失败记录
            $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
            _back_msg("error", "订单信息不存在", $redirect_url);
        }
        $local_data = [
            "payment_type" => $payment_order['payment_type'],
            "payment_name" => $payment_order['payment_name'],
            "payment_account" => $payment_order['payment_account'],
        ];

        // 支付方式
        if (!in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO])) {
            _back_msg("error", "请选择支付方式");
        }

        if ($payment_type == EXCHANGE_ALIPAY) {//支付宝
            if (!$payment_name || !$payment_account) {
                _back_msg("error", "请输入【真实姓名】、【支付宝账号】");
            }
        }
        if ($payment_type == EXCHANGE_WEBCHAT_AUTO) {//微信
            $account_info = $this->db->query("SELECT * FROM app_payment_fail_order WHERE order_id=? LIMIT 1", [$order_id])->row_array();
            //查询微信唯一码是否存在
            if (!$account_info) {
                _back_msg("error", "您选择转账到微信零钱，请扫码授权再提交");
            }
            if (!$payment_name) {
                _back_msg("error", "请输入【微信实名姓名】");
            }
            $payment_account = $account_info['payment_account'];//微信扫码账号从扫码记录中获取
        }

        try {
            //事务开始
            $this->db->trans_start();
            //先删除存在的订单失败信息，再提交提现信息【可能是微信扫码产生的记录】
            $delete_fail_order = $this->db->query("DELETE FROM app_payment_fail_order WHERE order_id=? LIMIT 1", [$order_id]);
            if (!$delete_fail_order) {
                throw new Exception("提交失败，请重新操作[重置]！");
            }
            $insert_data = [
                "order_id" => $order_id,
                "pid" => $pid,
                "uid" => $member_uid,
                "payment_type" => $payment_type,
                "payment_name" => $payment_name,
                "payment_account" => $payment_account,
                "add_time" => time(),
                "local_data" => json_encode($local_data, JSON_UNESCAPED_UNICODE),
            ];
            $insert_fail_order_log = $this->db->insert("app_payment_fail_order_log", $insert_data);
            if (!$insert_fail_order_log) {
                throw new Exception("提交失败，请重新操作[log]！");
            }
            //更新支付账号到订单表中
            $update_data = [
                "payment_type" => $payment_type,
                "payment_name" => $payment_name,
                "payment_account" => $payment_account,
                "status" => 1,//订单改成待支付状态
            ];
            $res_update_order = $this->db->update("app_payment_order_new", $update_data, ["id" => $order_id]);
            if (!$res_update_order) {
                throw new Exception("提交失败，请重新操作[订单]！");
            }

            #### AMY 2020-12-21 检查是否是项目支付,新的支付方式同步到流水表中
            //订单来源是流水表的记录时，需要同步信息到流水表中
            if ($payment_order['log_code'] == ORDER_SOURCE_STATUS_PROJECT && $payment_order['param'] > 0) {
                $flow_id = $payment_order['param'];
                //通过流水编号查询流水信息
                $flow_info = $this->db->query("SELECT * FROM app_project_flow WHERE id=?", [$flow_id])->row_array();
                if (!$flow_info) {
                    throw new Exception("提交失败，请联系管理员处理[流水]！");
                }
                //流水表里的记录是失败的并且状态为正常的记录才能变更
                if ($flow_info['pay_status'] == FLOW_PAY_STATUS_FAIL && $flow_info['status'] == PROJECT_FLOW_STATUS_NORMAL) {
                    $old_flow_data = [
                        "pid" => $flow_info['pid'],
                        "uid" => $flow_info['member_uid'],
                        "payment_type" => $flow_info['payment_type'],
                        "payment_name" => $flow_info['payment_name'],
                        "payment_account" => $flow_info['payment_account'],
                        "pay_status" => $flow_info['pay_status'],
                    ];
                    $update_flow_data = [
                        "payment_type" => $payment_type,
                        "payment_name" => $payment_name,
                        "payment_account" => $payment_account,
                        "pay_status" => 1,//流水改成待支付状态
                    ];
                    $insert_flow_log_data = [
                        "flow_id" => $flow_id,
                        "pid" => $pid,
                        "uid" => $member_uid,
                        "payment_type" => $payment_type,
                        "payment_name" => $payment_name,
                        "payment_account" => $payment_account,
                        "add_time" => time(),
                        "local_data" => json_encode($old_flow_data, JSON_UNESCAPED_UNICODE),
                    ];
                    $insert_fail_flow_log = $this->db->insert("app_payment_fail_flow_log", $insert_flow_log_data);
                    if (!$insert_fail_flow_log) {
                        throw new Exception("提交失败，请重新操作[流水log]！");
                    }
                    $res_update_flow = $this->db->update("app_project_flow", $update_flow_data, ["id" => $flow_id]);
                    if (!$res_update_flow) {
                        throw new Exception("提交失败，请重新操作[流水]！");
                    }
                }
            }
            #### AMY 2020-12-21 检查是否是项目支付,新的支付方式同步到流水表中

            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                throw new Exception("提交失败，请重新操作[操作]！");
            }
            $first_name = $payment_name ? mb_substr($payment_name , 0 , 1) : "";
            $redirect_to_rs = $this->survey_model->get_redirect_info("/bk/rs", "fail_order_suc", "", "", "doctor_name={$first_name}老师", false);
            if (file_exists("./uploads/wechat/fail_order/order_{$scene}.png")) {
                unlink("./uploads/wechat/fail_order/order_{$scene}.png");
            }

            ##  AMY 2020-12-09 默认支付账号设置
            $this->payment_account_setting($member_uid, $update_data, $order_id);
            ##  AMY 2020-12-09 默认支付账号设置

            //删除session
            $this->session->unset_userdata("fail_order_{$member_uid}");
            _back_msg("success", "操作成功！", $redirect_to_rs);
        } catch (Exception $e) {
            _back_msg("error", $e->getMessage());
        }
    }

    //提现账号默认设置
    private function payment_account_setting($member_uid, $payment_info, $order_id)
    {
        try {
            if (!$member_uid) {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "1、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            $payment_type = $payment_info['payment_type'] ?? "";
            $payment_name = $payment_info['payment_name'] ?? "";
            $payment_account = $payment_info['payment_account'] ?? "";
            if (!$payment_type || !in_array($payment_type, [EXCHANGE_ALIPAY, EXCHANGE_WEBCHAT_AUTO]) || !$payment_name || !$payment_account) {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "2、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            //事务开始
            $this->db->trans_start();
            //SQL数据处理
            //检测提现账号是否正确
            $check_account = getDataByConditionCi("app_ex_payment_account", " AND uid=? AND payment_type=? ORDER BY id DESC","*", true, [$member_uid, $payment_type]);
            $local_data = [];
            $new_data = [
                "payment_type" => $payment_type,
                "payment_name" => $payment_name ? $payment_name : "",
                "payment_account" => $payment_account,
            ];
            if (!$check_account) {
                $insert_account_data = [
                    "uid" => $member_uid,
                    "payment_type" => $payment_type,
                    "payment_name" => $payment_name ? $payment_name : "",//支付账号表不存在支付名称时，用明细表的姓名填充
                    "payment_account" => $payment_account,
                    "add_time" => time(),
                ];
                $res_account = $this->db->insert("app_ex_payment_account", $insert_account_data);
                if (!$res_account) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "3、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }
                $payment_account_id = $this->db->insert_id();
            } else {
                //更新账号
                $res_update_account_info = $this->db->update("app_ex_payment_account", $new_data, ["id" => $check_account['id']]);
                if (!$res_update_account_info) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "4、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }
                $local_data = [
                    "payment_type" => $check_account['payment_type'],
                    "payment_name" => $check_account['payment_name'],
                    "payment_account" => $check_account['payment_account'],
                ];
                $payment_account_id = $check_account['id'];
            }
            $n_data = array_merge($new_data, ["order_id" => $order_id]);
            $insert_account_log = [
                "payment_account_id" => $payment_account_id,
                "uid" => $member_uid,
                "local_data" => $local_data ? json_encode($local_data, JSON_UNESCAPED_UNICODE) : "",
                "new_data" => $new_data ? json_encode($n_data, JSON_UNESCAPED_UNICODE) : "",
                "add_time" => time(),
                "change_type" => ACCOUNT_LOG_PAYMENT_ORDER,
            ];
            $res_account_log = $this->db->insert("app_ex_payment_account_log", $insert_account_log);
            if (!$res_account_log) {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "5、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            //设置默认账号
            if ($payment_account_id) {
                //再把最新的记录变更成默认账号
                $res_update_account = $this->db->update("app_ex_payment_account", ["is_default" => 0], ['uid' => $member_uid]);
                if (!$res_update_account) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "6、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }
                //再把最新的记录变更成默认账号
                $res_update_default_account = $this->db->update("app_ex_payment_account", ["is_default" => 1], ["id" => $payment_account_id]);
                if (!$res_update_default_account) {
                    file_put_contents('./tmp/fail_order_payment_account_setting.txt', "7、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                    throw new Exception(false);
                }

            }
            //事务结束
            $this->db->trans_complete();
            if ($this->db->trans_status() === FALSE)
            {
                file_put_contents('./tmp/fail_order_payment_account_setting.txt', "8、".$member_uid."**".json_encode($payment_info, JSON_UNESCAPED_UNICODE)."**".date("Y-m-d H:i:s")."\n",FILE_APPEND | LOCK_EX);
                throw new Exception(false);
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    //检测微信扫码结果
    function check_order_info()
    {
        $scene = $this->input->post("scene", true);
        ####    验证账号有效性
        $order_info = $this->check_fail_order_scene($scene);
        ####    验证账号有效性
        $order_id = $order_info['id'];
        //查询微信唯一码是否存在
        $account_info = $this->db->query("SELECT * FROM app_payment_fail_order WHERE order_id=? AND payment_type=? LIMIT 1", [$order_id, EXCHANGE_WEBCHAT_AUTO])->row_array();
        if ($account_info) {
            _back_msg("success", "绑定成功");
        } else {
            _back_msg("error", "未绑定");
        }
    }

    //微信提现二维码生成
    private function wx_payment_qrcode($order_id)
    {
        if (!$order_id) {return false;}
        $config = [
            //上医说
            'app_id' => DRSAY_WECHAT_APPLET_APP_ID,
            'secret' => DRSAY_WECHAT_APPLET_APP_SECRET,
            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];

        $app = Factory::miniProgram($config);
        //临时二维码，由于小程序二维码参数最多只能传32位，因此要控制参数长度
        $scene = "order_".$order_id;
        $scene = $scene."_".substr(md5($scene . PROJECT_ENCODE_KEY), 8, 6);
        //检测订单信息
        $order_info = $this->db->query("SELECT * FROM app_payment_order_new WHERE id=?", [$order_id])->row_array();
        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
        //订单不存在/订单无效/不是支付失败的订单，不能处理
        if (!$order_info || ($order_info && $order_info['order_status'] != 1) || ($order_info && $order_info['status'] != 3)) {
            redirect($redirect_url);
        }
        $response = $app->app_code->getUnlimit($scene, [
            //上医说页面
            'page'  => 'pages/drsayExchange/drsayExchange',
            'width' => 50,
        ]);
        // 或
        if ($response instanceof \EasyWeChat\Kernel\Http\StreamResponse) {
            if (!is_dir("./uploads/wechat/fail_order/")) {
                mk_dir("./uploads/wechat/fail_order/", 0777, true);
            }
            $filename = $response->saveAs("./uploads/wechat/fail_order/", $scene.'.png');
        } else {
            return false;
//            echo "error_code:{$response['errcode']}";
//            die;
        }
        return $filename;
    }

}
