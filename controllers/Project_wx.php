<?php
class Project_wx extends MY_<PERSON>
{
    function __construct()
    {
        parent::__construct();
    }

    //获取短信验证码
    public function pro_send_sms()
    {
        error_reporting(-1);
        ini_set('display_errors', 1);
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            try {
                $username = $post_data['username'];
                $password = md5($post_data['password']);

                if (!$username || !$post_data['password']) {
                    throw new Exception("登录失败，账号或者密码错误！");
                }

                $admin_info = $this->db->where([
                    'username' => $username,
                    'password' => $password,
                    'status' => 1])->get('app_admin')->row_array();
                if(empty($admin_info)){
                    throw new Exception("请确认账号是否正确11！".$this->db->last_query());
                }
                $send_single_sms = [];
                $smg = "";
                $sign = 1;//手机验证码
                $vcode = rand(10000,99999);
                if($sign == 1){
                    $mobile = $admin_info['mobile'];
                    if($admin_info['country_id'] == 68){
                        $phone_regular = login_regular();
                        if(!preg_match($phone_regular, $mobile)){
                            _back_msg("error","手机号不正确，请联系管理员！".$phone_regular.$mobile);
                        }
                        //创蓝单个发送短信
                        $msg = "上医说验证码:{$vcode}，5分钟内有效，请勿泄漏！";
                        $send_single_sms = chuanglan_single_sms(SMS_LOG_CODE_SURVEY_VERIFICATION_CODE, $mobile, $msg);
                    } else {
                        //国际通道
                        //短信摸板id
                        $template_ids = array(
                            'login' =>array(
                                SEND_SINGLE_SMS => SMS_INVITE_TEMPLETE,       //send_cloud
                                AZURE_SMS_TYPE  => AZURE_SMS_TPL_DRSAY_INVITE,//微软
                                CHUANGLAN_SMS   => CHUANGLAN_SMS_TEMPLETE,   //创蓝
                            )
                        );
                        $country = getDataByID('app_sys_dictionary',$admin_info['country_id']);
                        $rand = rand(1, 2);
                        if($rand == 2){
                            $content = '健康通验证码:%VCODE%，%VTIME%分钟内有效，请勿泄漏';
                            $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                            $re_mobile = $mobile;
                            $send_single_sms = send_single_azure_sms(SMS_LOG_CODE_SURVEY_INVITE,$re_mobile,$template_ids['login'],$content,CHUANGLAN_SMS,'global');
                        } else {
                            $content = $lang_ary[LABEL_COMM_MOB_TEMPLATE];
                            $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                            $send_single_sms = send_global_sms($country['remark'], $mobile, $content,SMS_LOG_CODE_SURVEY_VERIFICATION_CODE);
                        }
                    }
                    $smg = "验证码已经发送到账户绑定的手机上，请查收！";
                }

                if($send_single_sms){
                    $verify_data = array(
                        'mobile' => !empty($mobile) ? $mobile : '',
                        'email' => !empty($email) ? $email : '',
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => '9'
                    );
                    $this->db->insert('app_member_verify', $verify_data);
                    _back_msg("success",$smg);
                } else {
                    throw new Exception("验证码发送失败！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    //登录
    public function login() {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证数据有效性
            $verify_code = $post_data['verify_code'];
            try {
                $username = $post_data['username'];
                $password = md5($post_data['password']);
                if (!$username || !$post_data['password']) {
                    throw new Exception("登录失败，账号或者密码错误！");
                }
                $admin_info = $this->db->where([
                    'username' => $username,
                    'password' => $password,
                    'status'=>1])->get('app_admin')->row_array();

                if(empty($admin_info)){
                    throw new Exception("登录失败，账号或者密码错误！");
                }
                $verify_mobile = $admin_info['mobile'];
                if (!check_mobile($verify_mobile)) {
                    throw new Exception("手机号有误，请确认后重新输入！");
                }

                //短信验证
                $res_verify = $this->db->query("select * from app_member_verify where `type`='".VERIFICATION_CODE_ADMIN_LOGIN."' and  (mobile= '{$admin_info['mobile']}' or email='{$admin_info['email']}') and status = 1 order by id desc")->row_array();
                $end_date= date('Y-m-d H:i:s',time());
                $start_date = date('Y-m-d H:i:s',$res_verify['create_time']);
                $minute = floor((strtotime($end_date)-strtotime($start_date))%86400/60);
                if($verify_code == $res_verify['vcode']){
                    if($minute >=5){
                        throw new Exception("验证码已超时");
                    }
                    $this->db->where(array('id'=>$res_verify['id']))->update('app_member_verify',array('status'=>2,'verify_time'=>time()));
                } else {
                    throw new Exception("您输入的验证码不正确！");
                }
                //存session
                $this->session->set_userdata(["project_wx" => $admin_info['id']]);
                $project_wx_list = "/project_wx/list";
                _back_msg("success", "验证通过", $project_wx_list);
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            if ($this->session->userdata["project_wx"]) {
                redirect("/project_wx/list");
            }

            $data = array(

            );
            $this->load->view("/project_wx/login",$data);
        }
    }

    //项目列表
    public function list()
    {
        if (!isset($this->session->userdata["project_wx"])) {
            redirect("/project_wx/login");
        }
        $admin_uid = $this->session->userdata["project_wx"];
        //根据管家编号，获取管家符合条件的项目列表
        $list = $this->db->query("SELECT a.* FROM project_wx a left join app_project b on a.pid=b.id WHERE a.admin_uid=? AND b.pro_status=3 LIMIT 50", [$admin_uid])->result_array();

        $data = array(
            "list" => $list,
        );
        $this->load->view("/project_wx/list",$data);
    }

}
