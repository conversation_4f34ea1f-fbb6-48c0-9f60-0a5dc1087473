<?php
defined('BASEPATH') OR exit('No direct script access allowed');
use EasyWeChat\Factory;
class Amy_api_wx extends CI_Controller
{

    public $app;
    function __construct()
    {
        parent::__construct();

//        $config = [
//            'app_id' => 'wx33d03b0f9b327925',
//            'secret' => '50e4d154ed30ed59d8fdd00c58c2026f',
//            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
//            'response_type' => 'array',
//        ];
//        //微信小程序
//        $this->app = Factory::miniProgram($config);

        $config = [
            //上医说服务号
            'app_id' => 'wxfed3a1a3c17d5fcd',
            'secret' => '7f7ffaff519be23fbe364441b4174ae2',
            'token' => 'drsay',
            'aes_key' => 'hZAGpKDTNnyp0L9Sc7UGXFtbmoBOWgxXn53agPgAINg',

            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',
        ];

        $this->app = Factory::officialAccount($config);

        error_reporting(-1);
        ini_set('display_errors', 1);

    }

    //微信后台只能填写一个服务器地址，所以 【服务器验证】 与 【消息的接收与回复】，都在这一个链接内完成交互
    function wx_auth()
    {
        //微信与服务器第一次验证时，直接返回微信的信息即可
//        $response = $this->app->server->serve();
//        // 将响应输出
//        $response->send();exit;


        #### 消息推送接口 ###
//        $this->app->server->push(function ($message) {
//            return "您好！欢迎使用上医说!";
//        });
        #### 消息推送接口 ###

        ### 发起授权    ###
        $response = $this->app->oauth->scopes(['snsapi_userinfo'])
            ->redirect("/drcenter/index");
        ### 发起授权    ###

//        $response = $this->app->server->serve();
        // 将响应输出
        $response->send();exit;

    }

//    function get_code()
//    {
//        //当前台传输的数据格式为：时'content-type': 'application/json'，用下面的方式获取
//        $post_data = file_get_contents('php://input');
//        //当为application/x-www-form-urlencoded时，可通过下面的方式获取数据
////        $post_data = $this->input->post();
//        if ($post_data) {
//            $json_post_data = json_decode($post_data);
//            $code = $json_post_data->code;
//            $get_wx_auth = $this->app->auth->session($code);
//            if (isset($get_wx_auth['errcode'])) {//失败
//                _back_msg("error", $get_wx_auth);
//            } else {
//                _back_msg("success", "登录成功！");
//            }
//        }
//
//    }




}
