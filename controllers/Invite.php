<?php
/**
 * Created by PhpStorm.
 * 用途：上医说邀请
 * User: Amy
 * Date: 2017/12/29
 */
class Invite extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('member_model');
    }

    public function index()
    {
//        $uid = 2982004;
//
//        //生成二维码
//        $qr = "./theme/images/qr_code.png";
//        $avatar = DRSAY_WEB.LOGO_PIC;
//        $qr_url = '/invite/'.$uid;
//        $out_path = "qr_".$uid.".png";
//        $res = set_qr_code($qr_url, $qr, $avatar, $out_path);
//        echo $res;
//
//        //合成图片
//        $qr = "/uploads/qr_code/201712/qr_".$uid.".png";
//        $res = create_qr_code($qr, "share_qr_".$uid.".png");
//        echo $res;
        $sub = $this->input->post();
        if ($sub) {//注册提交
            $invite_id = (int)$this->input->post('invite_id', true);
            $invite_id = $invite_id ? : 1;
            $mobile = addslashes(trim($this->input->post('mobile' , true)));
//            $password = md5(trim($this->input->post('password' , true)));
            $verification_code = addslashes(trim($this->input->post('verify' , true)));
            $lang_ary = get_lang($this->input->post('lang', true));
            $type = VERIFICATION_CODE_REG;//验证码类型
            //检测验证码有效性
            $res_sms_verify = get_verification_code($mobile, $type);
//            print_r($res_sms_verify);
//            print_r($sub);
//            die;
            if (!$res_sms_verify || $verification_code != $res_sms_verify['vcode']) {
                _back_msg('error', $lang_ary[LABEL_ACCOUNT_VERIFICATION_CODE_ERROR]);
            }

            //检测验证码的有效性
            $is_vcode = check_verify_code($mobile, $verification_code, $type);
            if (!$is_vcode) {
                _back_msg('error', $lang_ary[LABEL_VERIFICATION_CODE_INVALID]);
            }

            //推荐者相关信息
            $invite_member_info = $this->member_model->get_member_info($invite_id);

            //检测手机号码是否已存在
            $where_member = "mobile='{$mobile}'";
            $member_info = $this->member_model->get_member_info_by_field($where_member);
            if ($member_info) {//已存在
                _back_msg('error', $lang_ary[LABEL_MOB_EXIST]);
            }
            //插入数据库
            $insert_data = array(
                "mobile" => $mobile,
                "inviter_id" => $invite_id,
                "data_from" => INVITE_FROM_WHERE_QR,
                "country" => $invite_member_info['country'],
                "lang" => $invite_member_info['country'],
                "lang" => $invite_member_info['lang'],
            );
            $member_id = $this->member_model->save_user($insert_data);
            if ($member_id) {
                //操作日志
                user_log(LOG_CODE_REG, "", $member_id);
                //更新验证码使用状态
                update_verification_code($res_sms_verify['id']);
                _back_msg('success', $lang_ary[LABEL_SUBMIT_SUCCESS], "/invite/index/".$invite_id);
            } else {
                _back_msg('error', $lang_ary[LABEL_SUBMIT_SUCCESS]);
            }
        } else {//注册页面
            //注册二维码
            $uid = (int)$this->uri->segment(3) ? : 1;//无推荐人，默认用账号1作为推荐人
            $member_info = $this->member_model->get_member_info($uid);

            //获取语言版本
            $lang = $member_info['lang'];
            $get_lang = get_lang($lang);
            $data = array(
                'invite_id' => $uid,
                'member_info' => $member_info,
                'get_lang' => $get_lang,
                'lang' => $lang,
            );
            $this->load->view('/invite/register',$data);
        }
    }



}