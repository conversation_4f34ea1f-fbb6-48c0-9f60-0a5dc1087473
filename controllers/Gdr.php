<?php
/**
 * Created by PhpStorm.
 * 用途：上医说官网
 * User: Amy
 * Date: 2017/11/21
 */
class Gdr extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
    }

    //测试生成日志文件
    public function test_log()
    {
//        //按问卷添加时间去生成日志文件
//        $tmp_time_path = "./tmp/bk/2019/10";
//        $pid = 1;
//        if(! is_dir( $tmp_time_path ) ) {
//            mk_dir($tmp_time_path);
//        }
//        $tmp_bak_url = $tmp_time_path."/".$pid.".txt";
//        file_put_contents($tmp_bak_url, "test_info".PHP_EOL, FILE_APPEND | LOCK_EX);
        unlink("./tmp/bk/2019/10/1.txt");
        echo "finish!";
    }

    public function tg()
    {
        $query_string = $_SERVER['QUERY_STRING'];
        if ($query_string) {
            $info = $query_string."\t".date("Y-m-d H:i:s");
            file_put_contents('./tmp/tg.txt', $info.PHP_EOL, FILE_APPEND | LOCK_EX);
        }

        $data = array();
        $this->load->view('/gdr/tg', $data);
    }

    public function tg_click_new(){
        $info = $this->db->query("SELECT CONCAT('uid=',survey_uid,'	',FROM_UNIXTIME(click_time,'%Y-%m-%d %H:%i:%s')) as data_info FROM app_project_implement_510 WHERE survey_uid!='' ORDER BY click_time ASC")->result_array();
        $data_info = array();
        $i = 1;
        foreach ($info as $v) {
            $data_info[] = $i."	".$v['data_info'];
            $i++;
        }
        if ($data_info) {
            $str = implode(PHP_EOL, $data_info);
            file_put_contents('./tmp/tg_new.txt', $str.PHP_EOL);
        }
    }

//    public function tg_res_new()
//    {
//        echo '<pre>';
//        $res = file_get_contents('./tmp/tg_new.txt');
//        echo $res;
//    }


    public function tg_res()
    {
        echo '<pre>';
        $res = file_get_contents('./tmp/tg_new.txt');
        echo $res;
    }

    public function igl()
    {
        $data = array();
        $this->load->view('/gdr/index', $data);
    }

    public function i()
    {
        $pic_num = $this->uri->segment(3);
//        if ($pic_num == 5) {
//            redirect("/theme/markting/drsay.pdf");
//        } else {
//            echo "<div align='center' style='padding:0px;margin:0px;'><img src='/theme/markting/".$pic_num.".jpg' /></div>";
//        }
//        echo "<div align='center' style='padding:0px;margin:0px;'><img src='/theme/markting/".$pic_num.".jpg' /></div>";
        $data = array(
            "pic_url" => "/theme/markting/".$pic_num.".jpg",
        );
        $this->load->view('/gdr/i', $data);
    }

    public function g()
    {
        redirect("/theme/markting/gooddr.pdf");
    }

    public function survey()
    {
        $data = array(

        );
        $this->load->view('/gdr/survey', $data);
    }

//    public function card_info_data()
//    {
//
//        for($i=0;$i<=29;$i++){
//            $rand_code = get_rand_code(8, 2);
//            $this->db->query("INSERT INTO one_survey_njqxj(user_name)VALUES('{$rand_code}')");
//            echo $this->db->last_query()."<br />";
//        }
//        echo "finish!";
//    }



    public function card_info()
    {
        $g_qtkey = trim($this->input->get("qtkey"));
        if (!$g_qtkey) {
            die("链接无效");
        }
        $get_njgxj = getDataByConditionCi("one_survey_njqxj", " AND user_name=?","*",true, [$g_qtkey]);
        if (!$get_njgxj) {
            die("链接无效");
        } else {
            if ($get_njgxj['enter_num'] >= 5) {
                die("链接已失效");
            }
            $this->db->query("UPDATE one_survey_njqxj SET enter_num=enter_num+1 WHERE id=?",[$get_njgxj['id']]);
        }
        echo '<style>body{padding:0px;margin:0px;}</style><iframe width="100%" height="100%" src="http://local.drsay.cn/theme/markting/0516_2.pdf"></iframe>';
    }
}