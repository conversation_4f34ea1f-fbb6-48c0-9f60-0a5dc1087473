<?php
/**
 * 针对特定项目，医师直接注册进入执行项目
 * Created by PhpStorm.
 * User: AMY
 * Date: 2020-07-07
 * Time: 15:08
 */

class Reg_survey extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('survey_model');
    }

    function login(){
//        $survey_code = "1346_868b80";
        $survey_code = $this->uri->segment(3);
        $info = $this->check_validity($survey_code, false, false);
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############
        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            $survey_code = $post_data['survey_code'];
            $verify_mobile = $post_data['verify_mobile'];
            //验证数据有效性
            $verify_code = $post_data['verify_code'];
            try {
                $quota_info = $this->check_validity($survey_code, true, true);
                if (!$info) {
                    throw new Exception("参数有误！");
                }
                //查询是否存在医师信息
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        }

        $this->load->view('/reg_survey/login.php', ["survey_code" => $survey_code]);
    }

    function reg(){
        $this->load->view('/reg_survey/reg.php');
    }

    //检测链接有效性
    private function check_validity($survey_code, $is_quota = false, $is_ajax = true){
        $redirect_url = $this->survey_model->get_redirect_info("/bk/rs", "l_err", "", "", "", false);
        if (!$survey_code) {
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $arr_survey_code = explode("_", $survey_code);
        $pid = (int)trim($arr_survey_code[0]);
        $local_encryption = $arr_survey_code[1];
        if ($pid <= 0) {
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        //项目的加密code
        $pid_code = "sd09rcub";
        $decrypt_scene = $pid."_".$pid_code;
        $decrypt_data = substr(md5($decrypt_scene . PROJECT_ENCODE_KEY), 8, 6);
        if ($local_encryption != $decrypt_data) {
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        $info = $this->db->query("SELECT * FROM app_project WHERE id = ? LIMIT 1", [$pid])->row_array();
        if (!$info) {
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        //查询项目是否进行中
        if ($info['pro_status'] == 3) {//项目不是进行状态
            if ($is_ajax) {
                _back_msg("error", "", $redirect_url);
            } else {
                redirect($redirect_url);
            }
        }
        if ($is_quota) {
            //根据项目编号，查询有效配额，随机分配一个配额给你医生
            $quota_info = $this->db->query("SELECT * FROM app_project_quta WHERE pid={$pid} AND is_db=1 AND quta_status=1 AND now_c_mount<mount ORDER BY RAND() limit 1")->row_array();
            if (!$quota_info) {
                if ($is_ajax) {
                    _back_msg("error", "", $redirect_url);
                } else {
                    redirect($redirect_url);
                }
            }
            return ["quota_id" => $quota_info['id']];
        } else {
            return true;
        }
    }

    //获取验证码
    public function send_sms()
    {
        ############### 获取语言包   ###############
        $lang = 140;
        $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        if (!$lang_ary) {//不存在语言包缓存
            //生成语言包
            setting_lang($this->lang_version);
            $lang_ary = Redis_db::getInstance()->get("web_lang", "web_".$this->lang_version."_".$lang);
        }
        ############### 获取语言包   ###############

        $post_data = $this->input->post();
        if ($post_data) {
            $post_data = format_post_data($post_data);
            //验证码
            $survey_code = $post_data['survey_code'];
            $verify_mobile = $post_data['verify_mobile'];
            try {
                $country = 68;
                $quota_info = $this->check_validity($survey_code);
                if (!$quota_info) {
                    throw new Exception("参数有误！");
                }

                //发送短信验证码
                $vcode = rand(10000,99999);
                $sms_log_code = SMS_LOG_CODE_SURVEY_REG;
                $content = $lang_ary[LABEL_COMM_MOB_TEMPLATE];//国际短信通道使用
                $err_msg = "";
                if ($country == '68') {//中国短信，使用send_cloud发送
                    //创蓝
                    if ($_SERVER['HTTP_HOST'] == "local.drsay.cn") {//本地
                        $st = true;
                    } else {
                        $sms_content = "上医说验证码:{$vcode}，".SMS_ACTIVE_TIME."分钟内有效，请勿泄漏！";//国际短信通道使用
                        $st = chuanglan_single_sms($sms_log_code, $verify_mobile, $sms_content);
                    }
                    if ($st === true) {
                        $send_st = true;
                    } else {
                        $send_st = false;
                        $err_msg = $st;
                    }
                }else{//国外短信，使用国际短信通道
                    $content = str_replace(array('%VCODE%', '%VTIME%'), array($vcode, SMS_ACTIVE_TIME), $content);
                    $area = getDataByID("app_sys_dictionary", $country);
                    $st = send_global_sms($area['remark'], $verify_mobile, $content, $sms_log_code);
                    if ($st == "OK") {
                        $send_st = true;
                    }else{
                        $send_st = false;
                    }
                }

                //1、注册 2、修改密码 3、找回密码 4、修改手机 5、绑定手机 6、第三方登录绑定 7、短信验证码登录 8、兑换验证码
                $type = VERIFICATION_CODE_SURVEY_REG;
                if($send_st) {//短信发送成功,记录入库
                    $verify_data = array(
                        'mobile' => $verify_mobile,
                        'vcode' => $vcode,
                        'create_time' => time(),
                        'type' => (string)$type
                    );
                    $insert_id_sms_code = $this->set_vcode($verify_data);
                    if($insert_id_sms_code > 0) {
                        _back_msg('success', '短信发送成功，请查收！');
                    } else {
                        throw new Exception("短信发送失败，请重试！");
                    }
                } else {
                    throw new Exception($err_msg ? $err_msg : "短信发送失败，请重试！");
                }
            } catch (Exception $e) {
                _back_msg("error", $e->getMessage());
            }
        } else {
            _back_msg("error", "请完善信息再提交");
        }
    }

    // 保存验证码数据
    private function set_vcode($data)
    {
        if (!$data) {return false;}
        $this->db->insert('app_member_verify', $data);
        return $this->db->insert_id();
    }
}
