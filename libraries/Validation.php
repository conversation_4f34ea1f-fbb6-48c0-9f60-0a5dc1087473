<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Validation {

    protected $display = [
        'numeric'      =>  '必须为数字',
        'integer'      =>  '必须为整数',
        'required'     =>  '验证的字段必须存在于输入数据中，但不可以为空',
        'phone'        =>  '检测电话号码格式不正确',
        'email'        =>  '检测电子邮件格式不正确',
        'check_url'    =>  '检测url不合法',
        'array'        =>  '验证字段必须为数组，请传入数组',
        'min'          =>  '验证字段小于最小值',
        'max'          =>  '验证字段大于最大值',
        'isChineseName'=>  '验证字段必须为中文姓名，请输入中文',
        'checkIsString'=>  '验证字段必须为字符串，请输入字符串',
        'checkIsStringAndNumeric'    =>  '检测非数字与字母组合字符,仅允许输入数字与字母组合字符',
        'checkIsStringAndNumericPlus'=>  '检测非数字与字母组合字符(附带中横杠,下划扛)仅允许输入数字与字母组合字符(附带中横杠,下划扛)',


	];

    private $vars;


    public function __construct() {

    }

    //传入示例
    // $arr = [

    //     'username:用户名'  => 'required',
    //     'password:密码'  => 'required|numeric',
    //     'myphone:手机号码'   => 'phone',
    //     'url:url'       => 'check_url',
    //     'name:姓名'      => 'required|isChineseName|checkIsString',
    //     'email:邮箱'     => 'email',
    //     'arr1:数组'      => 'array',
    //     'num:数字'       => 'min:5|max:10|integer',
    // ];

    public function check($data , $arr) {

        foreach($arr as $key => $val) {
            //'username:用户名'  => 'required',
            $cf = explode(':',$key);
            $character = isset($cf[1]) ? $cf[1] : $key; //用户名部分
            $key  = $cf[0]; //username部分
            
            if(!in_array($key , array_keys($data))) {
                continue; //跳过多的验证
            };
           
            $valid = explode('|',$val); 
            foreach($valid as $vali) {
                
                if($vali == 'required') {
                    if(empty($data[$key])){
                       echo $character.$this->display['required'];exit;
                       //return $this->output($character.$this->display['required']);
                    }
                }
    
                if($vali == 'numeric') {
                    if(!is_numeric($data[$key])){
                        echo $character.$this->display['numeric'];exit;
                    }
                }
                
                if($vali == 'integer') {
                    if(!(floor($data[$key]) == $data[$key])){
                        echo $character.$this->display['integer'];exit;
                    }
                }
    
                if($vali == 'email') {
                    if( !preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", trim($data[$key]))){
                        echo $character.$this->display['email'];exit;
                    }
                }
    
                if($vali == 'phone') {
                    if( !preg_match("/^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/", trim($data[$key]))){
                        echo $character.$this->display['phone'];exit;
                    }
                }
    
                if($vali == 'array') {
                    if(!is_array($data[$key])){
                        echo $character.$this->display['array'];exit;
                    }
                }
               
                if(substr($vali, 0 , 3) == 'min') {
                    $str = substr(strrchr($vali, ":"), 1);
                    if($data[$key] < $str){
                        echo $character.$this->display['min'].$str;exit;
                    }
                }
                if(substr($vali, 0 , 3) == 'max') {
                    $str = substr(strrchr($vali, ":"), 1);
                    if($data[$key] > $str){
                        echo $character.$this->display['max'].$str;exit;
                    }
                }
    
                if($vali == 'check_url') {
                    if( !(filter_var($data[$key], FILTER_VALIDATE_URL) !== false)){
                        echo $character.$this->display['check_url'];exit;
                    }
                }
    
                if($vali == 'isChineseName') {
                    if( !preg_match('/^([\xe4-\xe9][\x80-\xbf]{2}){2,4}$/', $data[$key])){
                        echo $character.$this->display['isChineseName'];exit;
                    }
                }
    
                if($vali == 'checkIsString') {
                    if(!preg_match("/^(\-?)(\D+)$/", trim($data[$key]), $matches)){
                        echo $character.$this->display['checkIsString'];exit;
                    }
                }
    
                if($vali == 'checkIsStringAndNumeric') {
                    if(!preg_match("/^([a-zA-Z0-9])+$/i", trim($data[$key]))){
                        echo $character.$this->display['checkIsStringAndNumeric'];exit;
                    }
                }
              
                if($vali == 'checkIsStringAndNumericPlus') {
                    if(!preg_match("/^([_-a-zA-Z0-9])+$/i", trim($data[$key]))){
                        echo $character.$this->display['checkIsStringAndNumericPlus'];exit;
                    }
                }
    
            }
            //print_r($tmp);exit;
           
    
        }

    }


    public function output($msg = '' , $code = 400) {
        $data['msg'] = $msg;
        $data['code'] = $code;
        echo json_encode($data);      
    }

}