<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

/**
 * Class Tools
 * @access  public
 */
class Tools
{
    public function __construct()
    {
        $this->CI = &get_instance();
    }

    /**
     * url_post请求
     * @param $url
     * @param array $data
     * @return bool|string
     */
    public function curl_post($url, $data = array())
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        // POST数据
        curl_setopt($ch, CURLOPT_POST, 1);

        // 把post的变量加上
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $output = curl_exec($ch);

        curl_close($ch);
        return $output;
    }

    /**
     * url_get请求
     * @param $url
     * @param $pram
     * @return bool|string
     */
    public function curl_get($url, $pram)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        curl_setopt($ch, CURLOPT_URL, $url);

        curl_setopt($ch, CURLOPT_POST, 1);

        curl_setopt($ch, CURLOPT_POSTFIELDS, $pram);

        $data = curl_exec($ch);

        curl_close($ch);

        return $data;
    }


    /*
     * 个人中心 职业资格证照片
     * @param $user_id 用户id
     * @return $savePath
     */
    public function profession_picture_address($user_id)
    {
        $savePath = '/data/' . MEMBER . PROFESSION . '/' . date("Y") . date("m") . date("d") . '/' . $user_id . '/';
        return $savePath;
    }

    /**
     * 个人中心 执业照片
     * @param $user_id
     * @return string
     */
    public function practice_picture_address($user_id)
    {
        $savePath = '/data/' . MEMBER . PRACTICE . date("Y") . date("m") . date("d") . '/' . $user_id . '/';
        return $savePath;
    }


    /**
     * uploads 图片文件夹地址
     * @param $user_id
     * @return string
     */
    public function picture_address()
    {
        $savePath = '/uploads/image/' . date("Y") . date("m") . date("d") . '/';
        return $savePath;
    }


    /**
     * uploads 考核指南文件地址
     * @param $user_id
     * @return string
     */
    public function data_file_txt()
    {
        $savePath = '/uploads/data_file_txt/' . date("Y") . date("m") . date("d") . '/';
        return $savePath;
    }

    /**
     * uploads 视频课件文件地址
     * @param $user_id
     * @return string
     */
    public function data_file_video()
    {
        $savePath = '/uploads/data_file_video/' . date("Y") . date("m") . date("d") . '/';
        return $savePath;
    }

    /**
     * uploads banner 图片上传位置!
     * @return string
     */
    public function banner_file()
    {
        $savePath = '/uploads/banner/' . date("Y") . date("m") . date("d") . '/';
        return $savePath;
    }

    /**
     * uploads hospital_logo 图片上传位置!
     * @return string
     */
    public function hospital_logo()
    {
        $savePath = '/uploads/hospital_logo/' . date("Y") . date("m") . date("d") . '/';
        return $savePath;
    }

    /**
     * 支付凭证地址
     * @param $user_id
     * @return string
     */
    public function pay_picture_address()
    {
        $savePath = '/data/' . PAY_ADDRESS . '/' . date("Y") . date("m") . date("d") . '/';
        return $savePath;
    }


    /**
     * 受限制的图片访问方法
     * @user gaosiqi
     * @param $path
     */
    public function file_open($path)
    {
        header('content-type:image/jpeg');
        $handle = fopen($path, 'rb+'); //读写二进制，图片的可移植性
        $res = fread($handle, filesize($path));
        fclose($handle);
        echo $res;
    }


    public static function getESClient()
    {
        $client = \Elasticsearch\ClientBuilder::create()->setHosts([
            [
            'host'   => ES_HOST,
            'port'   => '9200',
            'scheme' => 'http',
            'user'   => 'elastic',
            'pass'   => 'NmImkYqiK6QjoPt1'
            ]
        ])->setConnectionPool('\Elasticsearch\ConnectionPool\SimpleConnectionPool', [])->setRetries(10)->build();
        // $client = \Elasticsearch\ClientBuilder::create()->build();
        return $client;
    }


    /**
     * 截取字符串
     *
     * 截取字符串
     * @access public
     */
    public function cutStr($string, $sublen, $start, $code)
    {
        if ($code == 'UTF-8') {
            $pa = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|\xe0[\xa0-\xbf][\x80-\xbf]|[\xe1-\xef][\x80-\xbf][\x80-\xbf]|\xf0[\x90-\xbf][\x80-\xbf][\x80-\xbf]|[\xf1-\xf7][\x80-\xbf][\x80-\xbf][\x80-\xbf]/";
            preg_match_all($pa, $string, $t_string);

            if (count($t_string[0]) - $start > $sublen) {
                return join('', array_slice($t_string[0], $start, $sublen)) . "...";
            }

            return join('', array_slice($t_string[0], $start, $sublen));
        } else {
            $start = $start * 2;
            $sublen = $sublen * 2;
            $strlen = strlen($string);
            $tmpstr = '';

            for ($i = 0; $i < $strlen; $i++) {
                if ($i >= $start && $i < ($start + $sublen)) {
                    if (ord(substr($string, $i, 1)) > 129) {
                        $tmpstr .= substr($string, $i, 2);
                    } else {
                        $tmpstr .= substr($string, $i, 1);
                    }
                }
                if (ord(substr($string, $i, 1)) > 129) {
                    $i++;
                }
            }
            if (strlen($tmpstr) < $strlen) {
                $tmpstr .= "...";
            }

            return $tmpstr;
        }
    }


    /**
     * 生成登录签名
     * @param account 腾讯IM账户名
     */
    //生成登录签名
    public function get_tencent_sig($account)
    {
        $this->CI->load->library('tlssigapi');
        $api = new Tlssigapi();
        $api->SetAppid(TENCENT_IM_APPID); //设置在腾讯云申请的appid
        $api->SetPrivateKey(TENCENT_IM_PRIVATEKET); //生成usersig需要先设置私钥
        $api->SetPublicKey(TENCENT_IM_PUBLICKEY); //校验usersig需要先设置公钥
        $sig = $api->genSig($account); //生成usersig
        return $sig;
    }

    /**
     * 自动生成项目编号
     * @param $number
     * @return string
     */
    public function project_no($number)
    {
        $number = $number+1;
        $newNumber = substr(strval($number + 100000), 1, 5);
        $project_no = 'PT' . $newNumber;
        return $project_no;
    }

    /**
     * 腾讯IM发送post请求
     * @param url 请求地址
     * @param post_data 请求参数
     */
    //腾讯IM发送post请求
    public function curl_request($url, $post_data)
    {
        //生成签名
        $sig = $this->get_tencent_sig(TENCENT_IM_ADMIN);

        //设置参数
        $param = [
            'sdkappid' => TENCENT_IM_APPID,
            'identifier' => TENCENT_IM_ADMIN,
            'usersig' => $sig,
            'random' => rand(********, ********),
            'contenttype' => 'json',
        ];
        $paramurl = http_build_query($param);

        $url = $url . '?' . $paramurl;
        //初始化
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置返回值头信息不输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        //设置post数据
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
        // https请求 不验证证书和hosts
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        //执行命令
        $data = curl_exec($curl);
        //关闭URL请求
        curl_close($curl);
        //显示获得的数据
        return json_decode($data, true);
    }



    /**
     * 根据省份/城市 区划编码、医院名 搜素ES
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string  $index    ES index
     * @param string  $type     ES type
     * @param string  $province 省份
     * @param string  $city     城市
     * @param string  $name     医院名
     * @param integer $num      分页数量
     * @return boolean/array
     */
    public static function searchEsByAreaHospitalName($index, $type, $province = false, $city = false, $name = false, $num = 5)
    {
        if ($name === false || empty($name)) {
            return false;
        }

        $client = self::getESClient();

        if ($province === false && $city === false) {
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'must' => [
                                'match' => [  // 搜索条件
                                    'hname' => $name,
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        } else {
            $area_index = $city === false ? 'province_code' : 'city_code';
            $area_val = $city === false ? $province : $city;
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'must' => [
                                'match' => [  // 搜索条件
                                    'hname' => $name,
                                ]
                            ],
                            'filter' => [
                                'term' => [
                                    $area_index => $area_val
                                ]
                            ],
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        }
        return $client->search($params);
    }





    /**
     * 根据省份/城市 区划编码、医院名 搜素ES
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string  $index    ES index
     * @param string  $type     ES type
     * @param string  $province 省份
     * @param string  $city     城市
     * @param string  $name     医院名
     * @param integer $num      分页数量
     * @return boolean/array
     */
    public static function searchEsByAreaPovName($index, $type, $province = false, $city = false, $name = false, $num = 5)
    {
        if ($name === false || empty($name)) {
            return false;
        }

        $client = self::getESClient();

        if ($province === false && $city === false) {
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'must' => [
                                'match' => [  // 搜索条件
                                    'unit' => $name,
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        } else {
            $area_index = $city === false ? 'province_code' : 'city_code';
            $area_val = $city === false ? $province : $city;
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'filter' => [
                                'term' => [
                                    $area_index => $area_val
                                ]
                            ],
                            'must' => [
                                'match' => [  // 搜索条件
                                    'unit' => $name,
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        }
        return $client->search($params);
    }


    /**
     * 根据省份/城市 区划编码、地址 搜素ES
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string  $index    ES index
     * @param string  $type     ES type
     * @param string  $province 省份
     * @param string  $city     城市
     * @param string  $district 区/县
     * @param string  $address  地址
     * @param integer $adr_type 搜索字段 1:zaddress, 2:waddress
     * @param integer $num      分页数量
     * @return boolean/array
     */
    public static function searchEsByAreaAddress($index, $type, $province = false, $city = false, $district = false, $address = false, $adr_type = 1, $num = 5)
    {
        if ($address === false || empty($address)) {
            return false;
        }

        $column = $adr_type == 1 ? 'address' : 'waddress';

        $client = self::getESClient();

        if ($province === false && $city === false && $district == false) {
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'must' => [
                                'match' => [  // 搜索条件
                                    $column => $address,
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        } else {
            $area_index = $district === false ? ($city === false ? 'province_code' : 'city_code') : 'district_code';
            $area_val = $district === false ? ($city === false ? $province : $city) : $district;

            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'filter' => [
                                'term' => [
                                    $area_index => $area_val
                                ]
                            ],
                            'must' => [
                                'match' => [  // 搜索条件
                                    $column => $address,
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        }
        return $client->search($params);
    }





    /**
     * 根据省份/城市 区划编码、地址 搜素ES
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string  $index    ES index
     * @param string  $type     ES type
     * @param string  $province 省份
     * @param string  $city     城市
     * @param string  $district 区/县
     * @param string  $address  地址
     * @param integer $num      分页数量
     * @return boolean/array
     */
    public static function searchEsPovByAreaAddress($index, $type, $province = false, $city = false, $district = false, $address = false, $num = 5)
    {
        if ($address === false || empty($address)) {
            return false;
        }

        $client = self::getESClient();

        if ($province === false && $city === false && $district == false) {
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'must' => [
                                'match' => [  // 搜索条件
                                    'address' => $address,
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        } else {
            $area_index = $district === false ? ($city === false ? 'province_code' : 'city_code') : 'district_code';
            $area_val = $district === false ? ($city === false ? $province : $city) : $district;

            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'filter' => [
                                'term' => [
                                    $area_index => $area_val
                                ]
                            ],
                            'must' => [
                                'match' => [  // 搜索条件
                                    'address' => $address,
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        }
        return $client->search($params);
    }





    /**
     * 根据省份/城市 区划编码、医院名 搜素ES
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string  $index    ES index
     * @param string  $type     ES type
     * @param string  $province 省份
     * @param string  $city     城市
     * @param string  $name     医院名
     * @param string  $address  地址
     * @param integer $adr_type 搜索字段 1:zaddress, 2:waddress
     * @param integer $num      分页数量
     * @return boolean/array
     */
    public static function searchEsByAreaNameAddress($index, $type, $province = false, $city = false, $name = false, $address = false, $adr_type = 1, $num = 5)
    {
        if ($name === false || empty($name) || $address === false || empty($address)) {
            return false;
        }

        $column = $adr_type == 1 ? 'address' : 'waddress';

        $client = self::getESClient();

        if ($province === false && $city === false) {
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'should' => [
                                [
                                    'match' => [  // 搜索条件
                                        'hname' => $name,
                                    ]
                                ],
                                [
                                    'match' => [  // 搜索条件
                                        $column => $address,
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        } else {
            $area_index = $city === false ? 'province_code' : 'city_code';
            $area_val = $city === false ? $province : $city;
            $params = [
                'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
                'type' => $type,//['my_type1', 'my_type2'],
                'body' => [
                    'query' => [
                        "bool" => [
                            'filter' => [
                                'term' => [
                                    $area_index => $area_val
                                ]
                            ],
                            'should' => [
                                [
                                    'match' => [  // 搜索条件
                                        'hname' => $name,
                                    ]
                                ],
                                [
                                    'match' => [  // 搜索条件
                                        $column => $address,
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'from' => '0',  // 分页
                    'size' => $num,  // 每页数量
                ]
            ];
        }
        return $client->search($params);
    }


    /**
     * 搜索ES区划编码
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string  $index    ES index
     * @param string  $type     ES type
     * @param string  $name     地区名
     * @param string  $level    等级 1省份 2城市 3区/县 4乡/镇/街道 5村/居委会
     * @param string  $prov     省份序号
     * @param string  $city     城市序号
     * @return boolean/array
     */
    public static function searchEsArea($index = '', $type = '', $name = false, $level='1', $prov='', $city='')
    {
        if (empty($index) || empty($type)|| empty($name)) {
            return false;
        }

        $client = self::getESClient();

        $params = [
            'index' => $index,   //['my_index1', 'my_index2'],可以通过这种形式进行跨库查询
            'type' => $type,//['my_type1', 'my_type2'],
            'body' => [
                'query' => [
                    "bool" => [
                        'filter' => [
                            'bool' => [
                                'must' => [
                                    [
                                        'term' => [
                                            'level' => $level
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'must' => [
                            'match' => [  // 搜索条件
                                'name' => $name,
                            ]
                        ]
                    ]
                ],
                'from' => '0',  // 分页
                'size' => 3  // 每页数量
            ]
        ];
        if ($prov != '' && intval($level) >= 1) {
            $params['body']['query']['bool']['filter']['bool']['must'][]=['term' => ['province' => $prov]];
        }
        if ($city != '' && intval($level) >= 2) {
            $params['body']['query']['bool']['filter']['bool']['must'][]=['term' => ['city' => $city]];
        }

        return $client->search($params);
    }


    /**
     * ES 使用 IK 中文分词 智能分词
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string $str 需要分词的字符串
     * @return boolean/array
     */
    public static function esIKSmart($str = false)
    {
        if ($str === false || empty($str)) {
            return false;
        }

        $client = self::getESClient();

        $params = [
            'body' => [
                'text' => $str,
                // 'analyzer'=>'ik_max_word' //ik_max_word 精细  ik_smart 粗略
                'analyzer'=>'ik_smart' //ik_max_word 精细  ik_smart 粗略
            ]
        ];
        $srow = $client->indices()->analyze($params);
        $res = [];
        foreach ($srow['tokens'] as $row) {
            $res[] = $row['token'];
        }
        return $res;
    }

    /**
     * ES 使用 IK 中文分词 精细分词
     * https://www.elastic.co/guide/cn/elasticsearch/guide/current/combining-filters.html  官方文档
     * @access public
     * @param string $str 需要分词的字符串
     * @return boolean/array
     */
    public static function esIKMax($str = false)
    {
        if ($str === false || empty($str)) {
            return false;
        }

        $client = self::getESClient();

        $params = [
            'body' => [
                'text' => $str,
                'analyzer'=>'ik_max_word' //ik_max_word 精细  ik_smart 粗略
            ]
        ];
        $srow = $client->indices()->analyze($params);
        $res = [];
        foreach ($srow['tokens'] as $row) {
            $res[] = $row['token'];
        }
        return $res;
    }


    /**
     * 显示字符串关键字
     * @access public
     * @param string $str   字符串
     * @param string $words 关键字列表
     * @return string
     */
    public static function showKeyWord($str = false, $keystr = false, $color = 'red')
    {
        if ($str == false || $keystr == false) {
            return false;
        }
        if ($str == $keystr) {
            return '<font color="'.$color.'">'.$keystr.'</font>';
        }
        if (strpos($str, $keystr) != false) {
            return str_replace($keystr, '<font color="'.$color.'">'.$keystr.'</font>', $str);
        }
        if (strpos($keystr, $str) != false) {
            return '<font color="'.$color.'">'.$str.'</font>';
        }

        $client = self::getESClient();

        $params = [
            'body' => [
                'text' => $keystr,
                'analyzer'=>'ik_smart' //ik_max_word 精细  ik_smart 粗略
            ]
        ];
        $srow = $client->indices()->analyze($params);
        $nstr = $str;
        foreach ($srow['tokens'] as $w) {
            if (strlen($w['token']) > 3) {
                $nstr = str_replace($w['token'], '<font color="'.$color.'">'.$w['token'].'</font>', $nstr);
            }
        }
        return $nstr;
    }

    public static function addESItem($params)
    {
        if (empty($index) || empty($params)) {
            return false;
        }
        $client = self::getESClient();
        return $client->index($params);
    }



    /**
     * csv_get_lines 读取CSV文件中的某几行数据
     * @param $csvfile csv文件路径
     * @param $lines 读取行数
     * @param $offset 起始行数
     * @return array
     */
    public static function csvGetLine($csvfile, $lines, $offset = 0)
    {
        $data = array();
        $spl_object = new \SplFileObject($csvfile, 'rb');
        $spl_object->seek($offset);
        while ($lines-- && !$spl_object->eof()) {
            $data[] = $spl_object->fgetcsv();
            $spl_object->next();
        }
        return $data;
    }

    /**
     * countFileLine 获取文件行数
     * @param $file 文件路径
     * @return int
     */
    public static function countFileLine($file)
    {
        $line = 0;
        $fp = fopen($file, 'r');
        if ($fp) {
            //获取文件的一行内容，注意：需要php5才支持该函数；
            while (stream_get_line($fp, 81920, "\n")) {
                $line++;
            }
            fclose($fp);//关闭文件
        }
        return $line;
    }

    /**
     * 获取CSV数据
     * @access public
     * @param string $file   文件路径
     * @param string $sheet_name   sheet名称
     * @return boolean/array
     */
    public static function readCsv($file, $sheet_name)
    {
        if (!file_exists($file)) {
            return false;
        }
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Csv');
        $reader->setLoadSheetsOnly($sheet_name);
        $spreadsheet = $reader->load($file);
        $res = $spreadsheet->getActiveSheet()->toArray();
        return $res;
    }

    /**
     * 获取Excel数据
     * @access public
     * @param string $file   文件路径
     * @param string $sheet_name   sheet名称
     * @return boolean/array
     */
    public static function readExcel($file, $sheet_name)
    {
        if (!file_exists($file)) {
            return false;
        }
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
        // $worksheet = $spreadsheet->getActiveSheet();
        $worksheet = $spreadsheet->getSheetByName($sheet_name);
        $highestRow = $worksheet->getHighestRow(); // 总行数
        $highestColumn = $worksheet->getHighestColumn(); // 总列数
        $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn); // e.g. 5
        for ($row = 1; $row <= $highestRow; ++$row) {
            $tmp = [];
            for ($i = 1; $i <= $highestColumnIndex; $i ++) {
                $tmp[] = $worksheet->getCellByColumnAndRow($i, $row)->getValue();
            }
            $result[] = $tmp;
        }
        unset($spreadsheet);
        unset($worksheet);
        return $result;
    }

    /**
     * 下载Excel数据
     * @access public
     * @param string $filename   文件名
     * @param string $header 列头
     * @param array $data 需写入的数据
     * @return boolean/array
     */
    public static function downExcel($filename = false, $header = [], $data = false)
    {
        if ($filename == false || empty($header) || $data == false) {
            return false;
        }
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $worksheet = $spreadsheet->getActiveSheet();
        foreach ($header as $hk=>$hv) {
            $worksheet->setCellValueByColumnAndRow($hk+1, 1, $hv);
        }
        $i = 2;
        foreach ($data as $row) {
            for ($si = 1; $si <= count($row); $si++) {
                $worksheet->setCellValueByColumnAndRow($si, $i, $row[$si-1]);
            }
            $i++;
        }
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$filename.'"');
        header('Cache-Control: max-age=0');

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        die();
    }

    /**
     * 下载CSV数据
     * @access public
     * @param string $filename   文件名
     * @param string $header 列头
     * @param array $data 需写入的数据
     * @return boolean/array
     */
    public static function downCsv($filename = false, $header = [], $data = false)
    {
        if ($filename == false || empty($header) || $data == false) {
            return false;
        }
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $worksheet = $spreadsheet->getActiveSheet();
        foreach ($header as $hk=>$hv) {
            $worksheet->setCellValueByColumnAndRow($hk+1, 1, $hv);
        }
        $i = 2;
        foreach ($data as $row) {
            for ($si = 1; $si <= count($row); $si++) {
                $worksheet->setCellValueByColumnAndRow($si, $i, $row[$si-1]);
            }
            $i++;
        }
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename='.$filename.'.csv');
        header('Cache-Control: max-age=0');

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Csv');
        $writer->save('php://output');
        die();
    }

    /**
     * 根据生日获取年龄
     * @access public
     * @param string $birthday [生日 格式 yyyy-mm-dd]
     * @return int
     */
    public static function getAge($birthday)
    {
        list($by, $bm, $bd) = explode('-', $birthday);
        $cm = date('n');
        $cd = date('j');
        $age = date('Y') - $by - 1;
        if ($cm > $bm || $cm == $bm && $cd > $bd) {
            $age++;
        }
        return $age;
    }

    /**
     * 检测是否为合法URL
     * @access public
     * @param string $url
     * @return bool 合法URL 返回true
     */
    public static function checkUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }


    /**
     * func 验证中文姓名
     * @param $name
     * @return bool 合法姓名 返回true
     */
    public static function isChineseName($name)
    {
        if (preg_match('/^([\xe4-\xe9][\x80-\xbf]{2}){2,4}$/', $name)) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 验证日期格式
     * @access public
     * @param string $date   需要验证的日期字符串
     * @param string $format 验证的格式
     * @return boolean
     */
    public static function validateDate($date, $format = 'Y-m-d H:i:s')
    {
        if ($date == date($format, strtotime($date))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取IP地址
     * @access public
     * @return string
     */
    public static function getRealIp()
    {
        if (isset($_SERVER)) {
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);

                foreach ($arr as $ip) {
                    $ip = trim($ip);

                    if ($ip != 'unknown') {
                        $realip = $ip;
                        break;
                    }
                }
            } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $realip = $_SERVER['HTTP_CLIENT_IP'];
            } elseif (isset($_SERVER['REMOTE_ADDR'])) {
                $realip = $_SERVER['REMOTE_ADDR'];
            } else {
                $realip = '0.0.0.0';
            }
        } elseif (getenv('HTTP_X_FORWARDED_FOR')) {
            $realip = getenv('HTTP_X_FORWARDED_FOR');
        } elseif (getenv('HTTP_CLIENT_IP')) {
            $realip = getenv('HTTP_CLIENT_IP');
        } else {
            $realip = getenv('REMOTE_ADDR');
        }

        preg_match('/[\\d\\.]{7,15}/', $realip, $onlineip);
        $realip = (!empty($onlineip[0]) ? $onlineip[0] : '0.0.0.0');
        return $realip;
    }

    /**
     * 检测手机号
     * @access public
     * @param string $mobile 检测的手机号
     * @return boolean 合法返回true
     */
    public static function checkMobile($mobile)
    {
        return !preg_match("/^(13|14|15|16|17|18|19)[0-9]{9}$/", trim($mobile)) ? false : true;
    }

    /**
     * 检测阿拉伯数字
     * @access public
     * @param string $numeric 检测的字符串
     * @return boolean 合法返回true
     */
    public static function checkIsNumeric($numeric)
    {
        return !preg_match("/^(\-?)(\d+)$/", trim($numeric)) ? false : true;
    }

    /**
     * 检测非数字字符
     * @access public
     * @param string $string 参数
     * @return boolean 合法返回true
     */
    public static function checkIsString($string)
    {
        return !preg_match("/^(\-?)(\D+)$/", trim($string), $matches) ? false : true;
    }

    /**
     * 检测非数字与字母组合字符,仅允许输入数字与字母组合字符
     * @access public
     * @param string $string 参数
     * @return boolean 合法返回true
     */
    public static function checkIsStringAndNumeric($string)
    {
        return !preg_match("/^([a-zA-Z0-9])+$/i", $string) ? false : true;
    }

    /**
     * 检测非数字与字母组合字符(附带中横杠,下划扛)仅允许输入数字与字母组合字符(附带中横杠,下划扛)
     * @access public
     * @param string $string 参数
     * @return boolean 合法返回true
     */
    public static function checkIsStringAndNumericPlus($string)
    {
        return !preg_match("/^([_-a-zA-Z0-9])+$/i", $string) ? false : true;
    }

    /**
     * 检测电话号码
     * @access public
     * @param string $phone 电话号码
     * @return boolean 合法返回true
     */
    public static function checkPhone($phone)
    {
        return !preg_match("/^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/", trim($phone)) ? false : true;
    }


    /**
     * 检查电子邮件格式
     * @access public
     * @param string $email 电子邮件
     * @return boolean 合法返回true
     */
    public static function checkEmail($email)
    {
        return (!preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $email)) ? false : true;
    }



    /**
     * 生成随机数,自动生成字母和数字混合随机数
     * @access public
     * @param int $length 生成随机长度
     * @param int $mode 组合形态 0: 大小写字母和数字在组合; 1: 仅数字; 2: 仅小写字母; 3: 仅大写字母
     */
    public static function getCode($length = 8, $mode = 0)
    {
        switch ($mode) {
            case '1':
                $str = '1234567890';
                break;
            case '2':
                $str = 'abcdefghijklmnopqrstuvwxyz';
                break;
            case '3':
                $str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                break;
            case '4':
                $str = '1234567890abcdefghijklmnopqrstuvwxyz';
                break;
            default:
                $str = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                break;
        }

        $result = '';
        $l = strlen($str) - 1;
        $num = 0;

        for ($i = 0; $i < $length; $i++) {
            $num = rand(0, $l);
            $a = $str[$num];
            $result = $result . $a;
        }
        return $result;
    }

    /**
     * @param $count 列的数量
     * @return array 列名一维数组
     */
    public static function getCol($count)
    {
        $columnFlag = [
            0 => 'Z', 1 => 'A', 2 => 'B', 3 => 'C', 4 => 'D', 5 => 'E', 6 => 'F', 7 => 'G', 8 => 'H',
            9 => 'I', 10 => 'J', 11 => 'K', 12 => 'L', 13 => 'M', 14 => 'N', 15 => 'O', 16 => 'P', 17 => 'Q',
            18 => 'R', 19 => 'S', 20 => 'T', 21 => 'U', 22 => 'V', 23 => 'W', 24 => 'X', 25 => 'Y', 26 => 'Z'
        ];

        if ($count == 0) {
            return [];
        }

        $column = [];
        for ($index = 1; $index <= $count; $index++) {
            if ($index <= 26) {
                $column[] = $columnFlag[$index];
            } else {
                $value = floor($index / 26);
                if ($index % 26 == 0) {
                    $value -= 1;
                }
                $column[] = $columnFlag[$value] . $columnFlag[floor($index % 26)];
            }
        }
        return $column;
    }
}
