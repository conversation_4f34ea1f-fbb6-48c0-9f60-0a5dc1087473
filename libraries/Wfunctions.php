<?php
/**
 * Created by PhpStorm.
 * User: ipmac
 * Date: 2017/10/23
 * Time: 下午1:10
 */
class Wfunctions
{
    /**
     * @param $pid
     * @param $partner_id
     * @param $link_group_id
     * @param int $hasin
     * @return bool|string
     *
     */
    public function project_link_encode($pid, $partner_id, $link_group_id, $hasin = 1)
    {
        //系统生成问卷链接 : 项目id_合作伙伴id_链接组号
        if (!$pid || !$partner_id || !$link_group_id) return false;
        // return $this->link_encode($pid, $partner_id, $link_group_id);

        $str = $pid . '_' . $partner_id . '_' . $link_group_id;
        $encode_str = substr(md5($str . PROJECT_ENCODE_KEY), 8, 16);
        return $hasin == 1 ? $str . '_' . $encode_str : $encode_str;
    }

    /**
     * 获取IP地址
     *
     * @access public
     */
    public function getIP()
    {
        if (getenv('HTTP_X_FORWARDED_FOR') != '') {
            $client_ip = (!empty($_SERVER['REMOTE_ADDR'])) ? $_SERVER['REMOTE_ADDR'] : ((!empty($_ENV['REMOTE_ADDR'])) ? $_ENV['REMOTE_ADDR'] : $REMOTE_ADDR);
            $entries = explode(',', getenv('HTTP_X_FORWARDED_FOR'));
            reset($entries);
            while (list(, $entry) = each($entries)) {
                $entry = trim($entry);
                if (preg_match("/^([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)/", $entry, $ip_list)) {
                    $private_ip = array('/^0\./', '/^127\.0\.0\.1/', '/^192\.168\..*/', '/^172\.((1[6-9])|(2[0-9])|(3[0-1]))\..*/', '/^10\..*/', '/^224\..*/', '/^240\..*/');
                    $found_ip = preg_replace($private_ip, $client_ip, $ip_list[1]);

                    if ($client_ip != $found_ip) {
                        $client_ip = $found_ip;
                        break;
                    }
                }
            }
        } else {
            $client_ip = (!empty($_SERVER['REMOTE_ADDR'])) ? $_SERVER['REMOTE_ADDR'] : ((!empty($_ENV['REMOTE_ADDR'])) ? $_ENV['REMOTE_ADDR'] : $REMOTE_ADDR);
        }
        return $client_ip;
    }

    ####    短信发送流程：对接统一短信发送平台（负责人：miles） BEGIN    ####
    /**
     * 发送验证码
     *
     * @param array $params 请求数据
     * @param string $template 模板
     */
    public function sendSms($params, $template = '')
    {
        $other_param = $params['other_param'] ?? [];
        unset($params['other_param']);// 本平台专用参数
        if (!empty($template)) {
            $params['template'] = $template;
        }
        $params['from'] = DRSAY_WEB;
        $params['key'] = IDR_SMS_APP_ID;
        $params['timestamp'] = time();
        $sign = $this->generateSign($params, IDR_SMS_APP_SECRET);
        $params['sign'] = $sign;
        // 加日志
        $log_code = $other_param['log_code'] ?? '';
        $mobile = $params['mobile'] ?? '';
        $msg = $params['template'] ?? '';
        // 发送短信
        $result = $this->curl_post_data(json_encode($params), IDR_SMS_API);
        $result_json = json_decode($result, true);// 短信公共平台返回的结果
        sms_log($log_code, 0, $mobile, $msg, ['back_result' => json_encode($result_json, JSON_UNESCAPED_UNICODE)]);
        return  $result;
    }

    function curl_post_data($data, $url, $header = "", $php_auth_user = "", $php_auth_pw = "")
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, $php_auth_user . ":" . $php_auth_pw);
        $timeout = curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        if ($header) {
            curl_setopt($ch,CURLOPT_HTTPHEADER,$header);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $rs = curl_exec($ch);
        $url_http = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $response_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        if ($url_http >= 400) {
            curl_close($ch);
            return $rs;
        }
        if ($response_time > $timeout) {
            curl_close($ch);
            return $rs;
        }
        curl_close($ch);
        header("Content-Type: text/html;charset=utf-8");
        return $rs;
    }

    public function generateSign($data, $secret)
    {
        ksort($data);
        $params = http_build_query($data);
        return md5($params . $secret);
    }
    ####    短信发送流程：对接统一短信发送平台（负责人：miles） END    ####
}
