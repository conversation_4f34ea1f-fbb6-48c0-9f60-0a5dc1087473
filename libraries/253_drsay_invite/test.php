<?php
/**
 * Created by PhpStorm.
 * User: idodo
 * Date: 2019-02-26
 * Time: 13:48
 */
include "config.php";
include "Chuanglan_api.php";

//echo password_hash('xxxx', PASSWORD_DEFAULT);


//$result = Chuanglan_api::yang_mao_dun('xxxx');

//$result = Chuanglan_api::send_international('this is a test from dodo', '821091939303');

$result = Chuanglan_api::send_international('谢晨炜老师，现有一学术观点问答250.88元，点击 ipn.cc/8BOT55EX 进入，感谢您的贡献！退订回T。', '8618018691818', '【健康通】');
print_r($result);

//$result = Chuanglan_api::send('谭哲老师，现有一学术观点问答150.00元，点击 ipn.cc/8BOT55EP 进入，感谢您的贡献！退订回T。', 'xxxx', '【上医说】');



//$result = Chuanglan_api::send_international('인증코드는 XXXX입니다. 감사합니다.', '821091939303', '【iPanelOnline】');

//$result = Chuanglan_api::query_balance_international();

//$res = Azure_sms::get_all_template();
//print_r($res);
//exit;
//$res = Azure_sms::send('xxxx', 'drsay_礼金' , array(
//    'NAME' => 'dodo',
//    'AWARDS' => '5.0',
//    'UCODE' => '12345',
//
//));
//var_dump($res);



