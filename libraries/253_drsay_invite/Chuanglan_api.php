<?php
/**
 * Created by PhpStorm.
 * User: idodo
 * Date: 2019-02-26
 * Time: 13:36
 */

class Chuanglan_api{

    const LOG_TYPE_YANG_MAO_DUN = 'YMD';
    const LOG_TYPE_INTERNATIONAL_SMS = 'IS';
    const LOG_TYPE_SMS = 'SMS';
    const LOG_TYPE_INTERNATIONAL_SMS_QUERY_BALANCE = 'ISQB';
    const LOG_TYPE_PULL_MO = 'PM';
    const LOG_TYPE_PULL_REPORT = 'PR';

    //-----羊毛盾 start-----
    //请求链接
    const YANG_MAO_DUN_URL = 'https://api.253.com/open/wool/wcheck';
    //成功状态
    const YANG_MAO_DUN_REQUEST_STATUS_SUCCESS_CODE = 200000;
    const YANG_MAO_DUN_STATUS_W1 = 'W1'; //白名单
    const YANG_MAO_DUN_STATUS_B1 = 'B1'; //黑名单
    const YANG_MAO_DUN_STATUS_B2 = 'B2'; //可信任度低
    const YANG_MAO_DUN_STATUS_N = 'N'; //未找到
    //-----羊毛盾 end-----


    //-----国际短信 start-----
    //发送短信请求链接
    const INTERNATIONAL_SMS_URL = 'http://intapi.253.com/send/json?';
    //查询余额链接
    const INTERNATIONAL_SMS_BALANCE_QUERY_URL = 'http://intapi.253.com/balance/json?';
    //-----国际短信 end-----


    //-----国内营销短信 start-----
    //发送短信营销请求链接
    const SMS_URL = 'http://smssh1.253.com/msg/send/json?';
    const SMS_VAR_URL = 'http://smssh1.253.com/msg/variable/json?';
    //-----国内营销短信 end-----

    //-----上行明细接口 start-----
    const PULL_MO_URL = 'http://smssh1.253.com/msg/pull/mo';
    //-----上行明细接口 end-----

    //-----拉取状态报告 start-----
    const PULL_REPORT_URL = 'http://smssh1.253.com/msg/pull/report';
    //-----拉取状态报告 end-----

    /**
     * 羊毛盾检测
     * @param $mobile
     * @param string $ip
     * @return Chuanglan_api_result
     */
    static function yang_mao_dun($mobile, $ip = '') {
        $params = array(
            'appId' => CHUANGLAN_YANGMAODUN_API_ID, // appId,登录万数平台查看
            'appKey' => CHUANGLAN_YANGMAODUN_API_KEY, // appKey,登录万数平台查看
            'mobile' => $mobile, // 要检测的手机号，限单个，仅支持11位国内号码
            'ip' => $ip // 检测手机号的IP地址，非必传(重要，建议传入)
        );
        $result = self::request(self::YANG_MAO_DUN_URL, http_build_query($params));
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        self::log(self::LOG_TYPE_YANG_MAO_DUN, $chuanglan_api_result);
        if($result === false) {
            return $chuanglan_api_result;
        }
        if(intval($result->code) !== self::YANG_MAO_DUN_REQUEST_STATUS_SUCCESS_CODE) {
            return $chuanglan_api_result;
        }
        $chuanglan_api_result->result = $result->data->status;
        return $chuanglan_api_result->result;
    }

    /**
     * 发送国际短信
     * @param $msg
     * @param $mobile
     * @param string $sign
     * @return Chuanglan_api_result
     */
    static function send_international($msg, $mobile, $sign = '【IPANEL】') {
        $params = array (
            'account'  =>  DRSAY_CHUANGLAN_INTERNATIONAL_SMS_API_ID,
            'password' => DRSAY_CHUANGLAN_INTERNATIONAL_SMS_API_KEY,
            'msg' => $sign . $msg,
            'mobile' => $mobile
        );
        $result = self::request(self::INTERNATIONAL_SMS_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->code)  && $result->code === '0') {
            $chuanglan_api_result->result = true;
        }
        self::log(self::LOG_TYPE_INTERNATIONAL_SMS, $chuanglan_api_result);
        return $chuanglan_api_result;
    }

    /**
     * 发送国内短信
     * @param $msg
     * @param $mobile
     * @param string $sign
     * @return Chuanglan_api_result
     */
    static function send($msg, $mobile, $sign = '【健康通】') {
        $params = array (
            'account'  =>  CHUANGLAN_SMS_NOTICE_API_ID,
            'password' => CHUANGLAN_SMS_NOTICE_API_KEY,
            'msg' => $sign . $msg,
            'phone' => $mobile,
            'report' => true
        );
        $result = self::request(self::SMS_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->code)  && $result->code === '0') {
            $chuanglan_api_result->result = true;
        }
        self::log(self::LOG_TYPE_SMS, $chuanglan_api_result);
        return $chuanglan_api_result;
    }

    /**
    $msg = '{$var}{$var}已支付礼金{$var}到您{$var}，请注意查收';
    //HCP0000815淋巴瘤病历卡项目第三期已支付礼金300.00元到您支付宝，请注意查收
    $params = [
        [
            'mobile' => 'xxxx',//手机号
            'pj_id' => 'HCP0000815',
            'name' => '淋巴瘤病历卡项目第三期',
            'bonus' => '200.0',
            'channel' => '支付宝',
        ],
        [
            'mobile' => 'xxxx',//手机号
            'pj_id' => 'DEF1234567',
            'name' => '这只是个测试项目请无视',
            'bonus' => '4400',
            'channel' => '手机话费',
        ]
    ];
    $result = Chuanglan_api::send_batch($msg, $params);
     */
    /**
     * 发送国内批量短信
     * @param $msg
     * @param $params
     * @param string $sign
     * @return Chuanglan_api_result
     */
    static function send_batch($msg, $params, $sign = '【健康通】') {
        $data = [];
        foreach($params as $param) {
            $data[] = implode(',', $param);
        }
        $params = array (
            'account'  =>  CHUANGLAN_SMS_NOTICE_API_ID,
            'password' => CHUANGLAN_SMS_NOTICE_API_KEY,
            'msg' => $sign . $msg,
            'params' => implode(';', $data),
            'report' => true
        );

        $result = self::request(self::SMS_VAR_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->code)  && $result->code === '0') {
            $chuanglan_api_result->result = true;
        }
        self::log(self::LOG_TYPE_SMS, $chuanglan_api_result);
        return $chuanglan_api_result;
    }

    /**
     * 发送国内营销短信
     * @param $msg
     * @param $mobile
     * @param string $sign
     * @param string $marketing_suffix
     * @return Chuanglan_api_result
     */
    static function send_marketing($msg, $mobile, $sign = '【健康通】', $marketing_suffix = '退订回T') {
        $params = array (
            'account'  =>  CHUANGLAN_SMS_API_ID,
            'password' => CHUANGLAN_SMS_API_KEY,
            'msg' => $sign . $msg . $marketing_suffix,
            'phone' => $mobile,
            'report' => true
        );

        $result = self::request(self::SMS_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->code)  && $result->code === '0') {
            $chuanglan_api_result->result = true;
        }
        self::log(self::LOG_TYPE_SMS, $chuanglan_api_result);
        return $chuanglan_api_result;
    }


    /**
    $msg = '{$var}老师，现有一学术观点问答，礼金{$var}元，点击 ipn.cc/{$var} 进入，感谢您对医疗事业的贡献！';
    $params = [
        [
            'mobile' => 'xxxx',//手机号
            'name' => '谭',
            'bonus' => '200.0',
            'code' => 'abcdefg',
        ],
        [
            'mobile' => 'xxxx',//手机号
            'name' => '巫',
            'bonus' => '100.0',
            'code' => '1234567',
        ]
    ];
    $result = Chuanglan_api::send_marketing_batch($msg, $params);
     */
    /**
     * 发送国内营销短信
     * @param $msg
     * @param $params
     * @param string $sign
     * @param string $marketing_suffix
     * @return Chuanglan_api_result
     */
    static function send_marketing_batch($msg, $params, $sign = '【健康通】', $marketing_suffix = '退订回T') {
        $data = [];
        foreach($params as $param) {
            $data[] = implode(',', $param);
        }
        $params = array (
            'account'  =>  CHUANGLAN_SMS_API_ID,
            'password' => CHUANGLAN_SMS_API_KEY,
            'msg' => $sign . $msg . $marketing_suffix,
            'params' => implode(';', $data),
            'report' => true
        );

        $result = self::request(self::SMS_VAR_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->code)  && $result->code === '0') {
            $chuanglan_api_result->result = true;
        }
        self::log(self::LOG_TYPE_SMS, $chuanglan_api_result);
        return $chuanglan_api_result;
    }

    /**
     * 查询国际短信余额
     * @return Chuanglan_api_result
     */
    static function query_balance_international() {
        //查询参数
        $params = array (
            'account' => CHUANGLAN_INTERNATIONAL_SMS_API_ID,
            'password' => CHUANGLAN_INTERNATIONAL_SMS_API_KEY,
        );
        $result = self::request(self::INTERNATIONAL_SMS_BALANCE_QUERY_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->code)  && $result->code === '0'){
            $chuanglan_api_result->result = $result->balance;
        }
        self::log(self::LOG_TYPE_INTERNATIONAL_SMS_QUERY_BALANCE, $chuanglan_api_result);
        return $chuanglan_api_result;
    }

    /**
     * 查询上行明细(客户短信回复内容)
     *
     * @return Chuanglan_api_result
     */
    static function query_pull_mo(){
        //查询参数
        $params = array (
            'account' => CHUANGLAN_SMS_API_ID,
            'password' => CHUANGLAN_SMS_API_KEY,
            'count' => 5000,//最长5000条
        );
        $result = self::request(self::PULL_MO_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->ret)  && $result->ret == '0'){
            $chuanglan_api_result->result = true;
        }
        self::log(self::LOG_TYPE_PULL_MO, $chuanglan_api_result);
        return $chuanglan_api_result;
    }

    /**
     * 拉取状态报告(短信发送状态)
     *
     * @return Chuanglan_api_result
     */
    static function query_pull_report(){
        //查询参数
        $params = array (
            'account' => CHUANGLAN_SMS_API_ID,
            'password' => CHUANGLAN_SMS_API_KEY,
            'count' => 5000,//最长5000条
        );
        $result = self::request(self::PULL_REPORT_URL, json_encode($params), true);
        $chuanglan_api_result = new Chuanglan_api_result(false, $params, $result);
        if(isset($result->ret)  && $result->ret == '0'){
            $chuanglan_api_result->result = true;
        }
        self::log(self::LOG_TYPE_PULL_REPORT, $chuanglan_api_result);
        return $chuanglan_api_result;
    }

    static function request($url, $params, $is_json = false) {
        $ch = curl_init();
        if($is_json) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json; charset=utf-8'
                )
            );
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $result = curl_exec($ch);
        if($result === false) {
            return false;
        }
        curl_close($ch);
        $ret = json_decode($result);
        if(is_null($ret)) {
            return false;
        }
        return $ret;
    }

    /**
     * @param string $type
     * @param Chuanglan_api_result $chuanglan_api_result
     */
    static function log($type, $chuanglan_api_result) {
        if(CHUANGLAN_LOG) {
            file_put_contents(
                CHUANGLAN_LOG_PATH . date('Y-m-d') . '_' . $type,
                date('Y-m-d H:i:s'). '|-|request:' . json_encode($chuanglan_api_result->request, JSON_UNESCAPED_UNICODE) . '|-|respond:' . json_encode($chuanglan_api_result->respond, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        }
    }
}


class Chuanglan_api_result{
    public $result; //返回结果
    public $request; //请求参数
    public $respond; //请求返回结果
    //移除隐私key
    private static $clear_request_keys = array('appKey', 'password');
    function __construct($result, $request, $respond)
    {
        foreach($request as $k=>$v) {
            if(in_array($k, self::$clear_request_keys)) {
                unset($request[$k]);
            }
        }
        $this->result = $result;
        $this->request = $request;
        $this->respond = $respond;
    }
}

