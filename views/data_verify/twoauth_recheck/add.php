<style>
    .lable-title {
        margin-top: 5px;
    }

    .input-group {
        display: block;
    }

    .input-group>.custom-file {
        height: 34px;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: center;
        align-items: center;
    }

    .custom-file-label {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        font-size: 14px;
        color: #878787;
        height: 34px;
        padding-left: 10px;
        line-height: 34px;
        z-index: 1;
        border: 1px solid #e5e6e7;
        border-radius: 3px;
        background: #fff;
    }

    .custom-file-label::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 3;
        display: block;
        padding: 0 10px;
        color: #878787;
        content: "Browse";
        background-color: #e9ecef;
        border-radius: 3px;
    }
</style>

<!-- 上传文件 css js -->
<link href="/theme/manage/css/plugins/jasny/jasny-bootstrap.min.css" rel="stylesheet">
<script src="/theme/manage/js/plugins/jasny/jasny-bootstrap.min.js"></script>

<div class="wrapper wrapper-content animated fadeInRight" style="margin-top: -20px;">
    <div class="row">
        <div class="col-lg-12" style="padding-right: 0px;padding-left: 0px; border:0px solid #FFFFFF;">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>二要素复核添加</h5>
                    <div class="ibox-tools">
                        <div class="btn-group" style="margin-right: -2px;">
                            <!-- <a href="javascript:;" class="btn btn-outline btn-success" tabindex="0" aria-controls="DataTables_Table_0"><i class="fa fa-plus"></i>添加</a> -->
                            <a href="/data_verify/twoauth_recheck/index" class="btn btn-outline btn-success" tabindex="0" aria-controls="DataTables_Table_0"><i class="fa fa-pie-chart"></i>列表</a>
                        </div>
                    </div>
                </div>
                <div class="ibox-content" style="padding-left:5px; padding-right:5px; margin-left:0px; margin-right:0px; font-size:12px;">
                    <!-- <div class="table-responsive"> -->
                    <div>
                        <!-- <form action="" method="get">
                            <div class="col-sm-12 m-b-xs">
                                <div class="input-group pull-right">
                                    <input type="text" placeholder="输入关键字搜索..." id="search_name" name="search_name" value="" class=" form-control m-b " style="float: left;width: 260px;">
                                    <input type="text" placeholder="输入关键字搜索..." id="search_name" name="search_name" value="" class=" form-control m-b " style="float: left;width: 260px;">
                                    <span style="float: left;" class="input-group-btn">
                                        <button type="submit" class="btn btn-success">搜 索</button>
                                    </span>
                                </div>
                            </div>
                        </form> -->
                        <form action="/data_verify/twoauth_recheck/insert" id="formbox" class="form-horizontal" onsubmit="return common_js.form_sumbit(this, 'show_msg', '处理中', 'post', '');" enctype="multipart/form-data">
                            <table id="add_info" class="table table-stripped table-bordered">
                                <thead>
                                    <tr>
                                        <th style="width:20px;">
                                            <input type="checkbox" name="" class="i-checks select_modify">
                                        </th>
                                        <th>序号</th>
                                        <th>姓名</th>
                                        <th>手机号</th>
                                        <th>上传图片</th>

                                        <th style=" width:200px; text-align:center;">
                                            <div class="btn-group">
                                                <button class="btn btn btn-outline btn-success btn-sm" onclick="add()" type="button"><i class="fa fa-plus"></i><span style="margin-left:4px;">添加</span></button>
                                                <!-- <input type="hidden" name="pid" value="" /> -->
                                                <button class="btn btn btn-outline btn-success btn-sm" type="submit"><i class="fa fa-check"></i>提交</button>
                                                <!--<button class="btn btn btn-outline btn-success btn-sm" onclick="resets()" type="button"><i class="fa fa-undo"></i><span style="margin-left:4px;">重置</span></button> -->
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="list">

                                    <!-- <tr>
                                                <td><input type="checkbox" name="" class="i-checks checked_input ids"></td>
                                                <td><?php echo $v['id']; ?></td>
                                                <td><input class="form-control" type="text" name="edit[<?php echo $v['id']; ?>][tpl_name]" value="" /></td>
                                                <td><input class="form-control" type="text" name="edit[<?php echo $v['id']; ?>][tpl_info]" value="" /></td>
                                                <td><input class="form-control" type="text" name="edit[<?php echo $v['id']; ?>][tpl_url]" value="" /></td>
                                                <td><a href="" target="_blank">预览</a></td>
                                                <td class="text-right footable-visible footable-last-column">
                                                    <div class="btn-group" style="white-space : normal nowrap">

                                                        <a class="btn btn-outline btn-success btn-xs" href="javascript:;" data-target="#model" data-toggle="modal">编辑</a>
                                                        <a class="btn btn-outline btn-success btn-xs" href="javascript:;" onclick="delete_info(1);">删除</a>
                                                    </div>
                                                </td>
                                            </tr> -->

                                    <!--判断无值时显示-->
                                    <tr>
                                        <td colspan="13" style="text-align:center;">请添加复核数据</td>
                                    </tr>
                                    <!--判断无值时显示-->

                                </tbody>
                                <tfoot>
                                    <!--分页-->
                                    <tr>
                                        <td colspan="13">
                                            <div class="form-group">
                                                <!-- <div class="col-sm-1 ">
                                                    <label class="checkbox iCheck-helper">
                                                        <input name="selAll" id="selAll" class="i-checks select_modify" onclick="selAll(this)" type="checkbox" /><span style="margin-left:10px;">全选</span> </label>

                                                </div>
                                                <div class="col-sm-2 " style="margin-top: 3px;">
                                                    <div class="input-group">
                                                        <select class="input-sm  form-control-sm form-control input-s-sm inline">
                                                            <option value="0">选择处理状态...</option>
                                                            <option value="1">Option 2</option>
                                                            <option value="2">Option 3</option>
                                                            <option value="3">Option 4</option>
                                                        </select>
                                                        <span class="input-group-btn"> <button type="button" class="btn btn-sm btn-primary">Go!</button> </span>
                                                    </div> -->


                                            </div>
                                            <div class="col-sm-4 "> </div>
                                            <div class="col-sm-4 float-right " style="margin-top: 8px;">
                                                <?php echo $print_page ?>
                                            </div>
                    </div>
                    </td>
                    </tr>
                    <!--分页-->
                    </tfoot>
                    </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

</div>

<table class="prepend_content" style="display:none">
    <tr>
        <td><input type="checkbox" name="" class="i-checks checked_input ids"></td>
        <td>%ID%</td>
        <td><input type="text" class="form-control" placeholder="姓名" name="add[%ID%][name]" /></td>
        <td><input type="text" class="form-control" placeholder="手机号" name="add[%ID%][mobile]" /></td>
        <td>

            <!-- <input type="file" class="form-control" placeholder="上传图片" name="files[%ID%]" /> -->

            <div class="input-group">
                <div class="custom-file">
                    <input id="inputGroupFile01" type="file" class="custom-file-input" name="files[%ID%]">
                    <label class="custom-file-label" for="inputGroupFile01">请选择图片</label>
                </div>
            </div>


        </td>
        <td><button class="btn btn-white delete_btn"><i class="fa fa-trash"></i></button></td>
    </tr>
</table>
<input type="hidden" class="table_index" name="table_index" value="0" />
<input type="hidden" class="max_id" name="max_id" value="<?php echo $list ? $list[0]['id'] : 0; ?>" />

<script type="text/javascript">
    //增加新行
    function add() {
        var rowCount = $(".list").find("tr").length;
        if (rowCount >= 21) {
            alert("最多只能添加20条数据");
            return;
        }
        var index = $(".table_index").val()
        index = parseInt(index);
        var max_id = $(".max_id").val()
        max_id = parseInt(max_id);
        var newx_index = index + 1;
        var max_index = max_id + 1;
        var str = $(".prepend_content tbody").html()
        var reg = new RegExp("%INDEX%", "g")
        var reg2 = new RegExp("%ID%", "g")
        var info = str.replace(reg, index);
        info = info.replace(reg2, max_index);
        $(".list").prepend(info)
        $(".table_index").val(newx_index)
        $(".max_id").val(max_index)
        bsCustomFileInput.init()
    }

    $(function() {
        // 绑定icheck
        $(".i-checks").iCheck({
            checkboxClass: "icheckbox_square-green",
            radioClass: "iradio_square-green",
        });

        // 单选
        $("#add_info").on("click", "tr td:first-child", function() {
            if (
                $(this).children(".icheckbox_square-green").hasClass("checked")
            ) {
                $(this)
                    .children(".icheckbox_square-green")
                    .removeClass("checked");
                $(this)
                    .children(".icheckbox_square-green")
                    .children(".ids")
                    .prop("checked", false);
            } else {
                $(this).children(".icheckbox_square-green").addClass("checked");
                $(this)
                    .children(".icheckbox_square-green")
                    .children(".ids")
                    .prop("checked", true);
            }
        });

        //全部选择
        $(".select_modify").on("ifChecked", function(event) {
            var is_hidden = $(".select_modify").is(":checked") ? 1 : 0;
            if (is_hidden) {
                $(".ids").prop("checked", true);
                $(".ids").parent().addClass("checked");
            }
        });

        //全部反选
        $(".select_modify").on("ifUnchecked", function(event) {
            $(".ids").prop("checked", false);
            $(".ids").parent().removeClass("checked");
        });


        //   删除指定的一行
        $("table").on("click", "tbody tr .delete_btn", function() {
            $(this).parent().parent().remove();
        })
    })

    function delete_info(id) {
        swal({
            title: "你确定删除吗?",
            text: "一经删除，将不可恢复，请谨慎操作!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: "/tpl_backup/delete_info",
                type: 'post',
                dataType: 'json',
                data: {
                    del_id: id,
                    act: "delete",
                },
                success: function(res) {
                    if (res.rs_code == 'success') {
                        swal("operation!", "Your operation success.", "success");
                        $(".sa-button-container").click(function() {
                            location.reload();
                        });
                    } else {
                        swal("operation!", res.rs_msg, "error");
                    }
                }
            });
        });
    }
</script>