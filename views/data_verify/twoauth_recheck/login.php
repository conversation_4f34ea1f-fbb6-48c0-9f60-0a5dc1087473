<?php
$this->load->helper('cookie');
$username = get_cookie("username");
$password = get_cookie("password");
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DRSAY.CN</title>
    <script src="/theme/admin/common/layui/jquery-1.9.1.min.js"></script>
    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <style media="screen">
        html, body {height:auto;}
        .loginscreen.middle-box {max-width:500px; width:500px;}
        .middle-box {padding-top:0;}
        .box {padding-bottom:5px; margin:150px auto 0; background-color:#fff;}
        .text-center {text-align:left;}
        .top {max-height:80px; min-height:60px; line-height:60px; overflow:hidden; background-color:#1ab394; text-align:center;}
        .font {font-size:35px; font-weight:400; color:#fff; letter-spacing:5px;}
        .form-group {margin:0 20px 20px;}
        .btn-primary {margin-left:20px;}
        .m-t {margin-top:20px;}
        .form-control {height:44px;}

        .foot {
            font-size: 12px;
            color: #a8a8a8;
            text-align: center;
        }

        .foot>p>a {
            color: #a8a8a8;
            text-decoration: none;
        }
    </style>
</head>

<body class="gray-bg">
<div class="middle-box text-center loginscreen animated fadeInDown box">
    <div>
        <div class="top">
            <h5 class="logo-name font">健康通</h5>
        </div>
        <?php if (isset($msg)) { ?>
        <div class="alert alert-danger alert-dismissable">
            <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
            <?php echo $msg; ?>
        </div>
        <?php }?>
        <!-- <h3>Welcome to drsay.cn</h3> -->
        <form class="m-t" role="form" id="isForm" action="" method="post">
            <div class="form-group">
                <input type="text" name="username" class="form-control" value="<?php echo isset($username) ? $username : ""; ?>" id="username" placeholder="账号" >
            </div>
            <div class="form-group">
                <input type="password" name="password" class="form-control" value="<?php echo isset($password) ? $password : ""; ?>" id="password" placeholder="密码" >
            </div>
            <div class="form-group row">
                <div class="col-sm-3 col-sm-offset-2">
                    <button class="btn btn-white btn-sm" style="width:100px;" type="reset">重置</button>
                </div>
                <div class="col-sm-4 col-sm-offset-1">
                    <button class="btn btn-primary btn-sm full-width" style="width:100px;" type="submit">登陆</button>
                </div>
            </div>
            <!-- <input type="hidden" name="act" class="form-control" value="jktdownload" id="act" > -->
        </form>
    </div>
</div>
<br />
<div align="center" class="foot">
    <p>Copyright © 2013-<?php echo date("Y");?>
        <a href="" target="_blank">Drsay Inc</a>
        . All Rights Reserved.
        <br />
        <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备13039326号-8</a >
        <a id="imgurl"
           href="https://zzlz.gsxt.gov.cn/businessCheck/verifKey.do?showType=p&serial=91110101074137173U-SAIC_SHOW_10000091110101074137173U1589176645610&signData=MEQCIEavKy3xNbv8Ibb91cWsX26It8sbAoY8H1zH8ICjl7/YAiAq8mIK3VifKDKiDi2ioVG61zHCLA+VJpbhyqkGCB5Qxw=="
           title="电子营业执照" target="_blank"><img src="/theme/images/lz4.png" width="25" height="25" border="0"><br></a>
    </p>
</div>
<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>

</body>
</html>
<script type="application/javascript">

    $(document).ready(function () {
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });

    window.onload = function(){
        var oForm = document.getElementById('loginForm');
        var oUser = document.getElementById('user');
        var oPswd = document.getElementById('pswd');

        var username = $('#username').val();
        var pass = $('#password').val();


        //页面初始化时，如果帐号密码cookie存在则填充
        if(getCookie('user') && getCookie('pswd')){
            oUser.value = getCookie('user');
            oPswd.value = getCookie('pswd');
        }
        
    };
    
    $('form').on('submit', function (e) {
        e.preventDefault()
    })

    $('.full-width').click(function () {
        var username = $('#username').val();
        var pass = $('#password').val();

        if(username == ''){
            alert('账号不能为空');
            return false;
        }

        if(pass == ''){
            alert('密码不能为空');
            return false;
        }
       
        $.post('/data_verify/twoauth_recheck/land',$('#isForm').serialize(),function (t) {
            if(t.code == 200){
                window.location.href="/data_verify/twoauth_recheck/index";
            }else{
                alert(t.msg);
                //window.location.href="/data_verify/twoauth_recheck/login";
            }
        },'json');
    })





</script>
