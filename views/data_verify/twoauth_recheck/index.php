<style>
    #DataTables_Table_0_paginate a:focus {
        outline: none;
    }

    #DataTables_Table_0_paginate .page-item.active .page-link {
        background-color: #1ab394;
        border-color: #1ab394;
    }
    .page-item.active .page-link {
        z-index: 1;
        color: #fff;
    }
    /* 缩略图 */
    .opacityBottom{
        width: 100%;
        height: 100%;
        position: fixed;
        background:rgba(0,0,0,0.8);
        z-index:1000;
        top: 0;
        left: 0
    }
    .none-scroll{
        overflow: hidden;
        height: 80%;
    }
    .bigImg{
        /* width:70%; */
        max-height: 80%;
        left:25%;
        top:18%;
        position:fixed;
        z-index: 10001;
        border:12px
        #1c84c6 solid;
    }

</style>
<link href="<?php echo TEMPLETE_PATH ?>css/plugins/iCheck/custom.css" rel="stylesheet">
<script src="<?php echo TEMPLETE_PATH ?>js/plugins/iCheck/icheck.min.js"></script>
<div class="wrapper wrapper-content animated fadeInRight" style="margin-top: -20px;">
    <div class="row">
        <div class="col-lg-12" style="padding-right: 0px;padding-left: 0px; border:0px solid #FFFFFF;">
            <div class="ibox float-e-margins">
               <div class="ibox-title">
                    <h5>复核列表【合计：<?php echo $total; ?>】</h5>

                      <div class="ibox-tools">
                        <div class="btn-group" style="margin-right: -2px;">
                    <a href="/data_verify/twoauth_recheck/add" class="btn btn-outline btn-success" tabindex="0" aria-controls="DataTables_Table_0"><i class="fa fa-plus"></i>添加</a>
                    <!-- <a href="" class="btn btn-outline btn-success" tabindex="0" aria-controls="DataTables_Table_0" onclick="excel();"><i class="fa fa-pie-chart"></i>下载</a>  -->

                        </div>
                      </div>
                </div>
                <div class="ibox-content" style="padding-left:5px; padding-right:5px; margin-left:0px; margin-right:0px; font-size:12px;">
                    <!-------------------- 表单 -------------------->
                <form action="/data_verify/twoauth_recheck/index" method="get">
                        <div class="col-sm-12 m-b-xs">
                            <div class="input-group pull-right">
                                <input type="text" placeholder="输入关键字搜索姓名" id="search_name" name="search_name" value="<?php echo $search_name?>" class=" form-control m-b " style="float: left;width: 260px;">
                                <input type="text" placeholder="输入关键字搜索手机号" id="search_mobile" name="search_mobile" value="<?php echo $search_mobile?>" class=" form-control m-b " style="float: left;width: 260px;">
                                <select name="search_status" id="search_status" class="form-control attribute_select" onchange="attribute_select(this.value)" style="width: 120px;float: left">
                                    <option value="">请选择</option>
                                    <?php foreach ($check_status_ary as $k => $v) {?>
                                        <option value="<?=$k?>"  <?= ($k == $search_status && $search_status!='') ? 'selected' : ''?> ><?=$v?></option>
                                    <?php }?>
                                </select>
                                <select name="date" id="status" class="form-control attribute_select" onchange="attribute_select(this.value)" style="width: 120px;float: left">
                                    <option value="0">请选择</option>
                                    <?php foreach ($dates as $date) {?>
                                        <option value="<?=$date['days']?>"  <?=$date['days'] == $date_time ? 'selected' : ''?> ><?=$date['days']?></option>
                                    <?php }?>
                                </select>
                                <input type='hidden' name='flag' id='flag'>
                                <span style="float: left;" class="input-group-btn">
                                    <button type="submit" class="btn btn-success" onclick='javascript:$("#flag").val("0")'>搜 索</button>
                                    <button type="submit" class="btn btn-default" onclick='javascript:$("#flag").val("1")'> 下载</button>
                                </span>
                            </div>
                        </div>
                    </form>
                    <table class="table table-bordered dataTables-example">
                        <thead>
                            <tr>
                              <!-- <th data-toggle="true" style="width:20px;">
                                <input type="checkbox" name="" class="i-checks select_modify">
                            </th> -->
                            <th data-hide="all">序号</th>
                            <th data-hide="all">创建时间</th>
                            <th data-hide="all">姓名</th>
                            <th data-hide="all">手机号</th>
							<th data-hide="all" >凭证图片</th>
                            <th data-hide="all">复核状态</th>
                            <th data-hide="all">复核结果</th>

                            <!-- <th data-hide="all" style="width: 120px;">操 作</th> -->
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($list) {?>
                                <?php foreach ($list as $row) {?>
                                    <tr>
                                        <!-- <td>
                                            <input type="checkbox" name="" class="i-checks ids" >
                                        </td> -->
                                        <td><?=$row['id']?></td>
                                        <td><?=date('Y-m-d H:i:s', $row['add_time'])?></td>
                                        <td><?=$row['name']?></td>
                                        <td><?=$row['mobile']?></td>
                                        <td> <img src="<?=$row['img']?> " alt="" style="width: 50px; height: 50px; " class="image_click"></td>
                                        <td><?= isset($st_arr[$row['st']]) ? $st_arr[$row['st']] : '' ?></td>
                                        <td>
                                            <font color="<?php if ($row['st'] == 3){echo 'red'; } ?>">
                                                <?= isset($check_status_ary[$row['check_status']]) ? $check_status_ary[$row['check_status']] : '' ?>
                                            </font>
                                        </td>
                                    </tr>
                            <!--内容无显示-->

                                     <?php }?>
                                      <?php } else {?>

                                    <tr>
                                    <td colspan="15" style="text-align:center;">暂无数据</td>
                                    </tr>
                                   <?php }?>

                              <!--内容无显示-->

                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="15">



                                    <div class="form-group">
                                       <!--  多选全选控制结束-->
                                        <!-- <div class="col-sm-1 " style="margin-left:-15px;">
                                            <label class="checkbox iCheck-helper">
                                            <input name="selAll" id="selAll" class="i-checks select_modify" onclick="selAll(this)" type="checkbox" /><span style="margin-left:10px;">全选</span> </label>

                                        </div> -->
                                      <!--  多选全选控制结束-->
                                      <!--  底部筛选操作-->
                                        <!-- <div class="col-sm-2 " style="margin-top: 3px;">
                                            <div class="input-group">
                                                <select class="input-sm  form-control-sm form-control input-s-sm inline">
                                                    <option value="0">选择处理状态...</option>
                                                    <option value="1">Option 2</option>
                                                    <option value="2">Option 3</option>
                                                    <option value="3">Option 4</option>
                                                </select>
                                                <span class="input-group-btn"> <button type="button" class="btn btn-sm btn-primary">submit</button> </span>
                                            </div>


                                        </div> -->

                                     <!--  底部筛选操作-->
                                     <!--  底部分页-->
                                        <div class="col-sm-5 "> </div>
                                        <div class="col-sm-4 float-right " style="margin-top: 8px;">
                                            <?php echo $print_page ?>
                                        </div>
                                     <!--  底部分页-->
                                    </div>
                                  </td>
                            </tr>

                        </tfoot>
                    </table>
                    <!-------------------- 列表 -------------------->
                      <!--<div class="dataTables_paginate paging_simple_numbers" id="DataTables_Table_0_paginate"><ul class="pagination"><li class="paginate_button page-item previous disabled" id="DataTables_Table_0_previous"><a href="#" aria-controls="DataTables_Table_0" data-dt-idx="0" tabindex="0" class="page-link">Previous</a></li><li class="paginate_button page-item active"><a href="#" aria-controls="DataTables_Table_0" data-dt-idx="1" tabindex="0" class="page-link">1</a></li><li class="paginate_button page-item "><a href="#" aria-controls="DataTables_Table_0" data-dt-idx="2" tabindex="0" class="page-link">2</a></li><li class="paginate_button page-item "><a href="#" aria-controls="DataTables_Table_0" data-dt-idx="3" tabindex="0" class="page-link">3</a></li><li class="paginate_button page-item next" id="DataTables_Table_0_next"><a href="#" aria-controls="DataTables_Table_0" data-dt-idx="4" tabindex="0" class="page-link">Next</a></li></ul></div>-->


                </div>
            </div>
        </div>
    </div>

</div>

<!--添加项目-->
<div class="modal inmodal" id="model" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content animated flipInY">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                    <span class="sr-only">Close</span>
                </button>
                <div class="modal_act_delete top_msg">
                    <h4 class="modal-title">复核结果</h4>
                    <small class="font-bold" style="color:#FF0000;">说明：复核不一致的，需要等待运营商进一步复核</small>
                </div>


            </div>
            <form class="form-horizontal" action="" onsubmit="return common_js.form_sumbit(this, 'show_msg', '处理中', 'post', '');">
                <div class="modal-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label"><span class="red">*</span>复核结果:</label>
                        <div class="col-sm-10 pay_plan">
                            <span id='fuheres'> </span>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <input type="hidden" name="add[implement_invoice_time]" id="implement_invoice_time" value="" />
                    <button type="button" class="btn btn-white" data-dismiss="modal" id="guanbi_i"  style="margin-top: 1px;">关闭</button>
                    <!-- <input type="hidden" name="back_url" value="<?php echo $_SERVER['REQUEST_URI']?>" />
                    <button type="submit" class="btn btn-primary">确认提交</button> -->
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function() {
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
        //全部选择
        $(".select_modify").on('ifChecked', function(event) {
            var is_hidden = $('.select_modify').is(':checked') ? 1 : 0;
            if (is_hidden) {
                $('.ids').iCheck('check');
            }
        });
        //全部反选
        $('.select_modify').on('ifUnchecked', function(event) {
            $('.ids').iCheck('uncheck');
        });
    });

    function delete_info(id){
        swal({
            title: "你确定删除吗?",
            text: "一经删除，将不可恢复，请谨慎操作!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: "/tpl_backup/delete",
                type: 'post',
                dataType: 'json',
                data: {
                    del_id: id,
                    act: "delete",
                },
                success: function(res) {
                    if (res.rs_code == 'success') {
                        swal("operation!", "Your operation success.", "success");
                        $(".sa-button-container").click(function() {
                            location.reload();
                        });
                    } else {
                        swal("operation!", res.rs_msg, "error");
                    }
                }
            });
        });
    }


    function check(obj , mobile , id) {
            var name = obj.name;
            var mobile = mobile;
            var id = id;

            $.ajax({
                url: "/data_verify/twoauth_recheck/check",
                type: 'post',
                dataType: 'json',
                data: {
                    id: id,
                    name: name,
                    mobile: mobile,
                },
                success: function(res) {
                    //console.log(res.code);
                    if (res.code == 20000) {
                        // data-target="#model" data-toggle="modal"
                        $("#model").attr("style","display:block");
                        $("#fuheres").html(res.msg);

                            //alert(res.msg);
                            //window.location.reload();

                    } else {
                        $("#model").attr("style","display:block");
                        $("#fuheres").html(res.rs_msg);
                    }

                }
            });
        }

        //导出
        function excel() {
            window.location.href="/data_verify/twoauth_recheck/excel";
        }

        $(".image_click").click(function () {
            var imgsrc = $(this).attr("src");
            var opacityBottom = '<div id="opacityBottom" style="display: none"><img class="bigImg" src="'+ imgsrc +'" ></div>';
            $(document.body).append(opacityBottom);
                toBigImg();//变大函数
            });

			function toBigImg(){
			  $("#opacityBottom").addClass("opacityBottom");
			  $("#opacityBottom").show();
			  $("html,body").addClass("none-scroll");//下层不可滑动
			  $(".bigImg").addClass("bigImg");

			  /*隐藏*/
			  $("#opacityBottom").bind("click",clickToSmallImg);

			  $(".bigImg").bind("click",clickToSmallImg);

			  var imgHeight = $(".bigImg").prop("height");
              var h = 1;
			  if(imgHeight < h){
			    $(".bigImg").css({"top":(h-imgHeight)/2 + 'px'});
			  }else{
			    $(".bigImg").css({"top":'70px'});
			  }
			  function clickToSmallImg() {
			    $("html,body").removeClass("none-scroll");
			    $("#opacityBottom").remove();
			  }
			};

</script>
