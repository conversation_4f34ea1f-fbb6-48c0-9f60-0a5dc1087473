<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        #app {
            height: 100vh;
            width: 100%;
            max-width: 1920px;
            margin: 0 auto;
        }

        #app main {
            width: 100%;
            padding: 20px 20px 60px 20px;
            height: 100vh;
            position: relative;
            background: #f6f7fb;
            overflow: hidden;
        }

        #app main .bg img {
            width: 100%;
            height: 100vh;
            object-fit: cover;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        #app main .box {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        #app main .login {
            width: 1120px;
            height: calc(100vh - 140px);
            max-height: 675px;
            min-height: 500px;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            position: relative;
        }

        #app main .login .left .login_banner {
            width: 410px;
            height: 403px;
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
        }

        #app main .login .left .login_bg {
            width: 581px;
            position: absolute;
            left: 0;
            top: 362px;
        }

        #app main .login .right {
            width: 400px;
            position: absolute;
            top: 50%;
            right: 108px;
            transform: translateY(-50%);
        }

        #app main .login .right h4 {
            text-align: center;
            font-size: 20px;
            margin-bottom: 20px;
        }

        #app main .login .right .form {
            padding-top: 10px;
        }

        #app main .login .right .form .input {
            height: 50px;
            line-height: 50px;
            background: #F6F7FB;
            margin-bottom: 20px;
            position: relative;
        }

        #app main .login .right .form .input input {
            height: 100%;
            width: 100%;
            border: none;
            background: #F6F7FB;
            outline: none;
            padding: 3px 20px;
            font-size: 16px;
        }

        #app main .login .right .form .input span {
            position: absolute;
            right: 20px;
            color: #37A3EB;
            cursor: pointer;
        }

        #app main .login .right .btn {
            width: 100%;
            background: #37A3EB;
            color: #fff;
            cursor: pointer;
            text-align: center;
            height: 50px;
            line-height: 50px;
            border-radius: 3px;
            border: none;
        }

        #app main .login .right .agree {
            position: absolute;
            bottom: -50px;
            font-size: 14px;
            color: #86909c;
            display: flex;
            align-items: center;
            gap: 8px;
        }


        #app main .login .right .agree a {
            color: #37A3EB;
            text-decoration: none;
        }

        #app main .login .right .agree label {
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        #app main .login .right .agree .checkbox {
            width: 16px;
            height: 16px;
            border: 1px solid #ddd;
            border-radius: 50%;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            flex-shrink: 0;
            margin-right: 8px;
            vertical-align: middle;
        }

        #app main .login .right .agree .checkbox.checked {
            background: #37A3EB;
            border-color: #37A3EB;
        }

        #app main .login .right .agree .checkbox.checked::after {
            content: '';
            position: absolute;
            left: 4px;
            top: 1px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        #app main .login .right .agree input[type="checkbox"] {
            display: none;
        }

        #app main .copyright {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 10px 0;
            text-align: center;
            color: var(--secondary-word-color);
            font-size: 12px;
            border-top: 1px solid #f0f0f0;
            z-index: 1000;
        }

        /* 提示信息样式 */
        .error_tip {
            color: #ff4757;
            font-size: 12px;
            margin-bottom: 10px;
            min-height: 16px;
            text-align: center;
        }

        .success_tip {
            color: #2ed573;
            font-size: 12px;
            margin-bottom: 10px;
            min-height: 16px;
            text-align: center;
        }

        /* 获取验证码按钮状态 */
        .get_code.send {
            background: #f5f5f5;
            border-color: #ddd;
            color: #999;
            cursor: not-allowed;
        }

        /* 手机端（屏幕宽度≤768px） */
        @media (max-width: 768px) {
            #app main .box {
                width: 100%;
                top: 0;
                left: 0;
                transform: translate(0, 0);
            }

            #app main .login {
                width: 100%;
                height: 100%;
            }

            #app main .login .left {
                display: none;
            }

            #app main .login .right {
                width: 100%;
                position: static;
                transform: translate(0, 0);
            }

            #app main .copyright {
                font-size: 10px;
                padding: 8px 0;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <main>
            <div class="bg">
                <img src="/theme/project/image/login_bg.png" alt="">
            </div>

            <div class="box">
                <div class="login">
                    <div class="left">
                        <img class="login_banner" src="/theme/project/image/login-banner.png" alt="">
                        <img class="login_bg" src="/theme/project/image/login-bg.png" alt="">
                    </div>

                    <div class="right">
                        <h4>登录</h4>
                        <!-- 提示信息 -->
                        <div class="error_tip"></div>
                        <div class="success_tip"></div>

                        <div class="form">
                            <div class="input">
                                <input type="text" readonly name="phone" placeholder="请输入手机号" value="<?php echo $mobile; ?>">
                            </div>
                            <div class="input">
                                <input style="width: 50%;" type="text" name="code" placeholder="请输入手机验证码">
                                <span class="get_code">获取验证码</span>
                            </div>

                            <div class="btn login_btn">
                                登录
                            </div>
                        </div>
                        <div class="agree">
                            <label>
                                <input type="checkbox" id="agreement" style="display: none;">
                                <div class="checkbox"></div>
                                我已阅读并同意
                            </label>
                            <a href="/project/project/privacy_policy" target="_blank">《隐私服务条款》</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="copyright">
                <p>Copyright © 2013-2025 <a href="" target="_blank">Drsay Inc</a>
                    . All Rights Reserved.
                    <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备13039326号-8</a>
                    <!-- <a id="imgurl" href="https://zzlz.gsxt.gov.cn/businessCheck/verifKey.do?showType=p&amp;serial=91110101074137173U-SAIC_SHOW_10000091110101074137173U1589176645610&amp;signData=MEQCIEavKy3xNbv8Ibb91cWsX26It8sbAoY8H1zH8ICjl7/YAiAq8mIK3VifKDKiDi2ioVG61zHCLA+VJpbhyqkGCB5Qxw==" title="电子营业执照" target="_blank"><img src="/theme/images/lz4.png" width="25" height="25" border="0"><br></a> -->
                </p>
            </div>
        </main>
    </div>
</body>

<script src="/theme/js/jquery-3.7.1.min.js"></script>
<script>
    var token = "<?php echo $token; ?>";

    $(function() {
        // 禁用选择输入历史
        $("input").attr('autocomplete', 'off');

        // 复选框效果
        $(".checkbox").click(function() {
            $(this).toggleClass("checked");
            $("#agreement").prop('checked', $(this).hasClass("checked"));
            $(".error_tip").text("");
        });

        // 手机验证
        // $("input[name='phone']").blur(function() {
        //     if (!/^1[3456789]\d{9}$/.test($(this).val().trim())) {
        //         $(".error_tip").text("请输入正确的手机号");
        //         $(this).removeClass("correct");
        //     } else {
        //         $(".error_tip").text("");
        //         $(this).addClass("correct");
        //     }
        // });

        // 获取验证码
        var scene = "<?php echo $scene; ?>";
        $(".get_code").click(function() {
            // 是否同意隐私协议的验证
            if (!$(".checkbox").hasClass("checked")) {
                $(".error_tip").text("请先阅读并同意隐私协议");
                return;
            }

            if (!$(this).hasClass("send")) {
                var verify_mobile = $("input[name='phone']").val().trim();

                if (verify_mobile) {
                    $.ajax({
                        url: "/project/project/send_code",
                        type: "post",
                        data: {
                            scene: scene,
                            token: token,
                            // verify_mobile: verify_mobile
                        },
                        dataType: "json",
                        success: function(info) {
                            console.log(info);
                            if (info.rs_code == "success") {
                                $(".success_tip").text(info.rs_msg);
                                $(".error_tip").text("");

                                var time = 60;
                                getRandomCode();

                                // 倒计时
                                function getRandomCode() {
                                    if (time === 0) {
                                        time = 60;
                                        $(".get_code").removeClass("send");
                                        $(".get_code").text("获取验证码");
                                        return;
                                    } else {
                                        time--;
                                        $(".get_code").addClass("send");
                                        $(".get_code").text(time + "秒后重新发送");
                                    }
                                    setTimeout(function() {
                                        getRandomCode();
                                    }, 1000);
                                }
                            } else {
                                $(".error_tip").text(info.rs_msg);
                                $(".success_tip").text("");
                            }
                        }
                    });
                } else {
                    $(".error_tip").text("请输入手机号");
                    $(".success_tip").text("");
                }
            }
        });

        // 登录
        $(".login_btn").click(function() {
            // 是否同意隐私协议的验证
            if (!$(".checkbox").hasClass("checked")) {
                $(".error_tip").text("请先阅读并同意隐私协议");
                return;
            }

            var verify_code = $("input[name='code']").val();
            var verify_mobile = $("input[name='phone']").val().trim();

            $.ajax({
                type: "post",
                url: "/project/project/code_login",
                data: {
                    scene: scene,
                    verify_mobile: verify_mobile,
                    verify_code: verify_code,
                    token: token,
                },
                dataType: "json",
                success: function(info) {
                    console.log(info);
                    if (info.rs_code == "success") {
                        $(".success_tip").text("登录成功，正在跳转...");
                        $(".error_tip").text("");

                        if (info.rs_backurl) {
                            window.location.href = info.rs_backurl;
                        } else {
                            window.location.href = "/";
                        }
                    } else {
                        $(".error_tip").text(info.rs_msg);
                        $(".success_tip").text("");
                        return false;
                    }
                }
            });
        });
    });
</script>

</html>