<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html,
        body {
            height: 100%;
            background: #F6F7FB;
        }

        .container {
            width:1088px;
            margin: 0 auto;
            padding: 20px 0;
        }

        .container .top {
            display: flex;
            justify-content: space-between;
        }

        .container .top>div {
            background: #fff;
            border-radius: 8px;
            flex: 1;
            padding: 30px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container .top>div:first-child {
            margin-right: 20px;
        }

        .container .top>div .title {
            font-size: 18px;
            color: #86909C;
        }

        .container .top>div .title img {
            width: 20px;
            vertical-align: middle;
            margin-right: 5px;
        }

        .container .top>div .num {
            font-weight: 600;
            font-size: 24px;
            color: #1D2129;
        }

        .container .mobile_list .item {
            display: none;
        }

        .container .list .item {
            background: #fff;
            border-radius: 8px;
            margin-top: 20px;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .container .list .item .current_push {
            position: absolute;
            width: 74px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            background: linear-gradient(315deg, #3B86F6 0%, #5F9FFF 100%);
            border-radius: 8px 0px 8px 0px;
            font-size: 14px;
            color: #FFFFFF;
            left: 0;
            top: 0;
        }

        .container .list .item .money {
            font-weight: 600;
            font-size: 26px;
            color: #F53F3F;
            margin: 0 30px 0 20px;
        }


        .container .list .item .money span {
            font-size: 14px;
        }

        .container .list .item .project {
            flex: 1;
        }

        .container .list .item .project h4 {
            font-weight: 600;
            font-size: 20px;
            color: #1D2129;
            line-height: 28px;
            margin-bottom: 10px;
        }

        .container .list .item .project .desc {
            display: flex;
            font-size: 14px;
            color: #86909C;
        }

        .container .list .item .project .desc p {
            margin-right: 20px;
        }

        .container .list .item .project .desc p span {
            color: #1D2129;
        }

        .container .list .item .project .desc .type {
            border-radius: 4px;
            padding: 1px 8px;
            font-size: 12px;
        }

        .container .list .item .project .desc .type1 {
            color: #3B86F6;
            background: rgba(59, 134, 246, 0.2);
        }

        .container .list .item .project .desc .type2 {
            color: #37C458;
            background: rgba(55, 196, 88, 0.2);
        }

        .container .list .item .btn {
            width: 164px;
            height: 44px;
            color: #fff;
            text-align: center;
            line-height: 44px;
            background: #5498FE;
            border-radius: 72px;
        }

        /* 手机端（屏幕宽度≤768px） */
        @media (max-width: 768px) {
            body {
                background: linear-gradient(225deg, #D9E8FC 0%, #FAFCFE 100%);
                padding: 0 10px;
            }

            .container {
                width: 100%;
                padding: 10px 0;
            }

            .container .top>div {
                padding: 10px;
            }

            .container .top>div:first-child {
                margin-right: 10px;
            }

            .container .top>div .title {
                font-size: 14px;
            }

            .container .top>div .title img {
                width: 16px;
                margin-right: 0;
            }

            .container .top>div .num {
                font-size: 16px;
            }

            .container .list .item {
                display: none;
            }

            .container .mobile_list .item {
                display: block;
                background: #fff;
                border-radius: 8px;
                margin-top: 10px;
                padding: 20px;
                position: relative;
            }

            .container .mobile_list .item .current_push {
                width: 46px;
                height: 18px;
                text-align: center;
                line-height: 18px;
                background: linear-gradient(315deg, #3B86F6 0%, #5F9FFF 100%);
                border-radius: 8px 0px 8px 0px;
                font-size: 9px;
                color: #FFFFFF;
                position: absolute;
                left: 0;
                top: 0;
            }


            .container .mobile_list .item .num {
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid #F2F3F5;
                padding-bottom: 5px;
            }

            .container .mobile_list .item .num .desc {
                font-weight: 400;
                font-size: 13px;
                color: #86909C;
            }

            .container .mobile_list .item .num .desc .type {
                border-radius: 4px;
                padding: 1px 8px;
                font-size: 12px;
            }

            .container .mobile_list .item .num .desc .type1 {
                color: #3B86F6;
                background: rgba(59, 134, 246, 0.2);
            }

            .container .mobile_list .item .num .desc .type2 {
                color: #37C458;
                background: rgba(55, 196, 88, 0.2);
            }

            .container .mobile_list .item .num .money {
                font-weight: 600;
                font-size: 16px;
                color: #F53F3F;
            }


            .container .mobile_list .item .num .money span {
                font-size: 12px;
                font-weight: 300;
            }

            .container .mobile_list .item .project {
                padding: 10px 0;

            }

            .container .mobile_list .item .project h4 {
                font-weight: 500;
                font-size: 15px;
                color: #1D2129;
                margin-bottom: 8px;
            }

            .container .mobile_list .item .project p {
                font-size: 13px;
                color: #86909C;
            }

            .container .mobile_list .item .project p span {
                color: #1D2129;
            }


            .container .mobile_list .item .btn {
                width: 100%;
                height: 38px;
                text-align: center;
                line-height: 38px;
                background: #5498FE;
                border-radius: 8px 8px 8px 8px;
                color: #fff;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="top">
            <div class="left">
                <div class="title">
                    <img src="/theme/project/image/bill-line.png" alt="">
                    <span>项目总数</span>
                </div>
                <div class="num"><?php echo $total; ?></div>
            </div>
            <div class="right">
                <div class="title">
                    <img src="/theme/project/image/money-cny.png" alt="">
                    <span>礼金总额</span>
                </div>
                <div class="num"><?php echo $total_money; ?></div>
            </div>
        </div>

        <!-- pc端列表 -->
        <div class="list">
            <?php if (!empty($list)) { ?>
                <?php foreach ($list as $key => $item) { ?>
                    <div class="item">
                        <?php if ($item['id'] == $send_project_id) { // 发送项目ID
                        ?>
                            <div class="current_push">当前推送</div>
                        <?php } ?>
                        <div class="money"><span>¥</span><?php echo $item['money']; ?></div>
                        <div class="project">
                            <h4><?php echo htmlspecialchars($item['pro_name']); ?></h4>
                            <div class="desc">
                                <p>预计时长：<span><?php echo $item['min_time_limit']; ?>min</span></p>
                                <p>项目编号：<span><?php echo cus_pid($item['id']); ?></span></p>
                                <div class="type <?php echo $item['pro_type'] == 1 ? 'type1' : 'type2'; ?>">
                                    <?php echo $item['pro_type_name']; ?>
                                </div>
                            </div>
                        </div>
                        <div class="btn" data-link="<?php echo $item['partner_link']; ?>">立即参与</div>
                    </div>
                <?php } ?>
            <?php } else { ?>
                <div class="item" style="text-align: center; padding: 50px;">
                    <p style="color: #86909C;">暂无项目数据</p>
                </div>
            <?php } ?>
        </div>

        <!-- 移动端列表 -->
        <div class="mobile_list">
            <?php if (!empty($list)) { ?>
                <?php foreach ($list as $key => $item) { ?>
                    <div class="item">
                        <?php if ($item['id'] == $send_project_id) { // 发送项目ID
                        ?>
                            <div class="current_push">当前推送</div>
                        <?php } ?>

                        <div class="num">
                            <div class="desc">项目编号：<?php echo cus_pid($item['id']); ?>
                                <span class="type <?php echo $item['pro_type'] == 1 ? 'type1' : 'type2'; ?>">
                                    <?php echo $item['pro_type_name']; ?>
                                </span>
                            </div>
                            <div class="money"><span>¥</span><?php echo $item['money']; ?></div>
                        </div>

                        <div class="project">
                            <h4><?php echo htmlspecialchars($item['pro_name']); ?></h4>
                            <p>预计时长：<span><?php echo $item['min_time_limit']; ?>min</span></p>
                        </div>

                        <div class="btn" data-link="<?php echo $item['partner_link']; ?>">立即参与</div>
                    </div>
                <?php } ?>
            <?php } else { ?>
                <div class="item" style="text-align: center; padding: 30px;">
                    <p style="color: #86909C;">暂无项目数据</p>
                </div>
            <?php } ?>
        </div>
    </div>

    <script>
        // 为所有"立即参与"按钮添加点击事件
        document.addEventListener('DOMContentLoaded', function() {
            var buttons = document.querySelectorAll('.btn');
            buttons.forEach(function(button) {
                button.addEventListener('click', function() {
                    var link = this.getAttribute('data-link');
                    if (link) {
                        window.open(link, '_blank');
                    }
                });
            });
        });
    </script>
</body>

</html>