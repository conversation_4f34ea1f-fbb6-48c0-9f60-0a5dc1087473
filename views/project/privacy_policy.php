<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        #app {
            height: 100vh;
            width: 100%;
            max-width: 1920px;
            margin: 0 auto;
        }

        #app main {
            width: 100%;
            padding: 20px;
            height: 100vh;
            position: relative;
            background: #f6f7fb;
        }

        #app main .bg img {
            width: 100%;
            height: 100vh;
            object-fit: cover;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        #app main .box {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        #app main .privacy {
            width: 1120px;
            height: 675px;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            position: relative;
            display: flex;
        }

        #app main .privacy .left {
            width: 300px;
            padding: 20px;
            border-right: 1px solid #f0f0f0;
        }

        #app main .privacy .left h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        #app main .privacy .left .nav-item {
            padding: 10px 15px;
            margin-bottom: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s ease;
            font-size: 14px;
            color: #666;
        }

        #app main .privacy .left .nav-item:hover {
            background: #f6f7fb;
            color: #37A3EB;
        }

        #app main .privacy .left .nav-item.active {
            background: #37A3EB;
            color: #fff;
        }

        #app main .privacy .right {
            flex: 1;
            padding: 20px 20px 60px 20px;
            overflow-y: auto;
            height: 635px;
        }

        #app main .privacy .right h1 {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        #app main .privacy .right h2 {
            font-size: 18px;
            color: #333;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        #app main .privacy .right h3 {
            font-size: 16px;
            color: #333;
            margin: 15px 0 10px 0;
            font-weight: 600;
        }

        #app main .privacy .right p {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 12px;
            text-align: justify;
        }

        #app main .privacy .right .list-item {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }

        #app main .privacy .right .list-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 8px;
            width: 4px;
            height: 4px;
            background: #37A3EB;
            border-radius: 50%;
        }

        #app main .copyright {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 10px 0;
            text-align: center;
            color: var(--secondary-word-color);
            font-size: 12px;
            border-top: 1px solid #f0f0f0;
            z-index: 1000;
        }

        /* 滚动条样式 */
        #app main .privacy .right::-webkit-scrollbar {
            width: 6px;
        }

        #app main .privacy .right::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        #app main .privacy .right::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        #app main .privacy .right::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 手机端（屏幕宽度≤768px） */
        @media (max-width: 768px) {
            #app main .box {
                width: 100%;
                top: 0;
                left: 0;
                transform: translate(0, 0);
            }

            #app main .privacy {
                width: 100%;
                height: 100%;
                flex-direction: column;
            }

            #app main .privacy .left {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #f0f0f0;
                padding: 15px;
                height: auto;
            }

            #app main .privacy .left h3 {
                font-size: 16px;
                margin-bottom: 15px;
            }

            #app main .privacy .left .nav-item {
                display: inline-block;
                margin-right: 10px;
                margin-bottom: 10px;
                padding: 8px 12px;
                font-size: 13px;
            }

            #app main .privacy .right {
                height: calc(100% - 100px);
                padding: 15px 15px 60px 15px;
            }

            #app main .privacy .right h1 {
                font-size: 20px;
                margin-bottom: 15px;
            }

            #app main .privacy .right h2 {
                font-size: 16px;
                margin: 15px 0 10px 0;
            }

            #app main .privacy .right h3 {
                font-size: 14px;
                margin: 12px 0 8px 0;
            }

            #app main .privacy .right p {
                font-size: 13px;
                margin-bottom: 10px;
            }

            #app main .privacy .right .list-item {
                font-size: 13px;
                margin-bottom: 6px;
            }

            #app main .copyright {
                font-size: 10px;
                padding: 8px 0;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <main>
            <div class="bg">
                <img src="/theme/project/image/login_bg.png" alt="">
            </div>

            <div class="box">
                <div class="privacy">
                    <div class="left">
                        <h3>目录导航</h3>
                        <div class="nav-item active" data-section="intro">协议说明</div>
                        <div class="nav-item" data-section="service">服务内容</div>
                        <div class="nav-item" data-section="register">用户注册</div>
                        <div class="nav-item" data-section="rights">权利义务</div>
                        <div class="nav-item" data-section="privacy">隐私声明</div>
                        <div class="nav-item" data-section="disclaimer">免责声明</div>
                        <div class="nav-item" data-section="breach">违约赔偿</div>
                        <div class="nav-item" data-section="modify">协议修改</div>
                        <div class="nav-item" data-section="law">法律管辖</div>
                        <div class="nav-item" data-section="other">其他条款</div>
                    </div>

                    <div class="right">
                        <h1>隐私政策</h1>

                        <section id="intro">
                            <h2>健康通医生平台医生用户隐私保护条款</h2>
                            <p>健康通（健康通（北京）网络科技有限公司）对需要使用健康通医生平台（网址：www.gooddr.com）的医生用户提示：任何使用健康通的"医生用户"（即需要通过健康通发布医生个人信息、建立个人主页的医生用户）应仔细阅读本服务协议，医生用户可选择不使用健康通，医生用户使用健康通的行为将被视为对本协议全部内容的认可。本服务协议内容包括本协议正文及所有健康通发布或将来可能发布的各类管理规定。所有管理规定为本协议不可分割的一部分，与本协议具有同等效力。</p>
                        </section>

                        <section id="service">
                            <h2>一、服务内容</h2>
                            <h3>1.1</h3>
                            <p>健康通医生平台系指一个医生信息发布平台，由健康通提供相应的平台技术服务（下称"平台服务"），医生用户自行发布其个人信息及相关内容，医生用户应确保其发布的信息的真实性、合法性、时效性。医生用户通过健康通发布的所有其个人基本信息、医学相关内容如医学参考资料、文献等，并不代表健康通赞同其观点或证实其内容的真实性，也不能作为临床诊断及医疗的依据，且不能替代正规医院医生与病人面对面的诊疗。</p>

                            <h3>1.2</h3>
                            <p>健康通保留随时变更、中断或终止部分或全部平台服务的权利，但健康通会尽可能的事先通知用户并保持平台服务的稳定性。健康通在提供平台服务时，可能会对部分平台服务收取一定费用，健康通会在需要进行收费前给予明确的提示，如拒绝支付此类费用，健康通有权拒绝提供相关平台服务。</p>
                        </section>

                        <section id="register">
                            <h2>二、医生用户的注册</h2>
                            <h3>2.1</h3>
                            <p>医生用户需先成为健康通的普通用户，才能进一步成为"医生用户"，除本协议外，医生用户需同样遵守适用于所有用户的《健康通服务条款》。</p>
                        </section>

                        <section id="rights">
                            <h2>三、医生用户的权利和责任</h2>
                            <h3>3.1</h3>
                            <p>医生用户可在健康通上发布信息，建立个人页面等。健康通根据实际情况，可以强制要求医生本人事先进行实名注册认证，具体认证方式由健康通另行发布。同时，所有医生用户应当了解，健康通对医生的认证并不代表健康通对医生发布的信息有任何性质的保证或需要承担任何性质的法律责任。</p>

                            <h3>3.2</h3>
                            <p>医生用户对自己在健康通上发布的信息承担责任，医生用户不得发布各类违法或违规信息。医生用户承诺自己在使用健康通提供的服务时实施的所有行为均遵守国家法律、法规和健康通的相关规定以及各种社会公共利益或公共道德。如有违反导致任何法律后果的发生，医生用户将以自己的名义独立承担所有相应的法律责任，并对健康通因此遭受的损失进行赔偿。</p>

                            <h2>四、健康通的权利和责任</h2>
                            <h3>4.1</h3>
                            <p>健康通会尽可能提供正确、有益的信息，但健康通作为平台服务提供者，并不对医生用户发布信息的来源和正确性负责，不参与医生信息的编辑、收集，不对医生信息的结果承担任何责任。</p>

                            <h3>4.2</h3>
                            <p>如健康通认为医生用户发布的信息违法或有其他问题，健康通有权自行删除该医生用户发布的信息，而无需向医生用户进行通知或说明，但健康通会尽力做到公平公正。</p>

                            <h3>4.3</h3>
                            <p>如发生下列任何一种情形，健康通有权随时中断或终止向医生用户提供本协议项下的网络服务而无需承担任何责任：</p>
                            <div class="list-item">医生用户提供的个人资料不真实；</div>
                            <div class="list-item">医生用户违反本协议中的规定；</div>
                            <div class="list-item">健康通认为其他不适宜的情况。</div>

                            <h3>4.4</h3>
                            <p>虽然健康通可能会对医生用户进行认证，但健康通并没有义务对医生用户的注册数据、行为以及与之有关的其它事项进行事先审查，也没有义务对医生用户的任何行为承担任何性质的法律责任。</p>

                            <h3>4.5</h3>
                            <p>健康通有权对医生用户的注册数据及活动行为进行查阅，发现注册数据或活动行为中存在任何问题或怀疑，均有权向医生用户发出询问及要求改正的通知或者直接作出删除等处理；</p>

                            <h3>4.6</h3>
                            <p>许可使用权：医生用户以此授予健康通独家的、全球通用的、永久的、免费的许可使用权利，使健康通有权(全部或部分、有偿或无偿) 使用、复制、修订、改写、发布、翻译、分发、执行、展示、向他人再许可医生用户提供于健康通的各类个人注册身份信息、联系方式等，及医生用户发布的著作权作品和数据信息。</p>

                            <h2>五、服务变更、中断或终止</h2>
                            <h3>5.1</h3>
                            <p>鉴于网络服务的特殊性，虽健康通会尽力保持服务的稳定性，但健康通有权随时变更、中断或终止部分或全部的平台服务。对于上述情形，健康通虽会尽力事先通知医生用户，但没有义务通知医生用户，也无需对任何医生用户或任何第三方承担任何责任。</p>

                            <h3>5.2</h3>
                            <p>医生用户理解，健康通需要定期或不定期地对提供平台服务的设备进行检修或者维护，如因此类情况而造成平台服务的中断，健康通无需为此承担任何责任，但健康通应尽可能事先进行通告。</p>

                            <h3>5.3</h3>
                            <p>健康通会按照公平公正的原则，运营平台服务，但健康通同时保留在不事先通知医生用户的情况下随时中断或终止医生用户部分或全部平台服务的权利（如注销医生用户账号），对于所有服务的中断或终止而造成的任何损失，健康通无需对医生用户或任何第三方承担任何责任。</p>

                            <h3>5.4</h3>
                            <p>医生用户服务终止后，健康通仍有以下权利：</p>
                            <div class="list-item">医生用户注销后，健康通有权保留该医生用户的注册数据、发布信息及以前的行为记录，并有权继续按本协议约定方式使用上述信息</div>
                            <div class="list-item">医生用户注销后，如医生用户在注销前在健康通平台上存在违法行为或违反条款的行为，健康通仍可行使本服务条款所规定的权利；</div>
                        </section>

                        <section id="privacy">
                            <h2>六、隐私声明</h2>
                            <h3>6.1 信息收集</h3>
                            <h3>6.1.1</h3>
                            <p>医生用户在使用健康通服务时，会向健康通提供个人信息，例如医生用户的姓名、电子邮件地址、电话号码。医生用户同意，健康通在医生用户使用服务的过程中获取信息。此类信息包括但不限于：</p>
                            <div class="list-item">设备信息--例如医生用户的硬件型号、操作系统版本、唯一设备识别码以及包括电话号码在内的移动网络信息。</div>
                            <div class="list-item">位置信息--例如来自医生用户设备的传感器数据就可以提供附近 Wi-Fi 接入点和基站的信息。</div>
                            <div class="list-item">本地存储--使用浏览器网络存储等机制（包括 HTML 5）和应用程序数据缓存，在医生用户的设备上收集信息（包括个人信息）并进行本地存储。</div>
                            <div class="list-item">Cookie和匿名标示符等工具--cookie主要的功能是便于医生用户使用网站产品和/或服务，以及帮助网站统计独立访客数量等。运用cookie技术，健康通能够为医生用户提供更加周到的个性化服务。</div>
                            <div class="list-item">医生用户在健康通上发布、储存的信息。</div>

                            <h3>6.2</h3>
                            <p>健康通承诺，会按本协议约定方式使用所收集到的信息。</p>

                            <h3>6.3</h3>
                            <p>健康通在以下情形时，可以披露信息：</p>
                            <div class="list-item">按本协议约定方式使用时。</div>
                            <div class="list-item">如医生用户是符合资格的知识产权投诉人并已提起投诉，应被投诉人要求，向被投诉人披露，以便双方处理可能的权利纠纷。</div>
                            <div class="list-item">根据法律的有关规定，或者行政或司法机构的要求，向第三方或者行政、司法机构披露。</div>
                            <div class="list-item">如果医生用户出现违反中国有关法律或者网站政策的情况，需要向第三方披露。</div>
                            <div class="list-item">为改善网站服务，需要向第三方披露。</div>
                            <div class="list-item">其他健康通认为合适的披露，包括但不限于在不违反法律和政府政策的前提下出于商业合作目的，向第三方合作伙伴进行的披露。</div>
                        </section>

                        <section id="disclaimer">
                            <h2>七、免责声明</h2>
                            <h3>7.1</h3>
                            <p>医生用户使用健康通服务所存在的风险将完全由其自己承担；因其使用健康通服务而产生的一切后果也由其自己承担，健康通对医生用户不承担任何责任。</p>

                            <h3>7.2</h3>
                            <p>健康通不担保网络服务一定能满足医生用户的要求，也不担保平台服务不会中断，对平台服务的及时性、安全性、准确性也都不作担保。健康通不对医生用户所发布信息的删除或储存失败负责。</p>
                        </section>

                        <section id="breach">
                            <h2>八、违约赔偿</h2>
                            <h3>8.1</h3>
                            <p>医生用户同意保障和维护健康通及其他医生用户的利益，如因医生用户违反有关法律、法规或本条款项下的任何条款而给健康通或任何其他第三人造成损失，医生用户同意承担由此造成的损害赔偿责任，其中包括服务提供方为此而支付的律师费用。</p>
                        </section>

                        <section id="modify">
                            <h2>九、协议修改</h2>
                            <h3>9.1</h3>
                            <p>健康通将有权随时修改本条款的有关条款，一旦本条款的内容发生变动，健康通将会通过适当方式向医生用户提示修改内容。</p>

                            <h3>9.2</h3>
                            <p>如果不同意健康通对本服务协议相关条款所做的修改，医生用户有权停止使用平台服务。如果医生用户继续使用的，则视为医生用户接受健康通对服务条款相关条款所做的修改。</p>
                        </section>

                        <section id="law">
                            <h2>十、法律管辖</h2>
                            <h3>10.1</h3>
                            <p>本服务条款的订立、执行和解释及争议的解决均应适用中国法律。</p>

                            <h3>10.2</h3>
                            <p>如双方就本服务条款内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向健康通所在地的人民法院提起诉。</p>
                        </section>

                        <section id="other">
                            <h2>十一、其他</h2>
                            <h3>11.1</h3>
                            <p>如本服务协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。</p>

                            <h3>11.2</h3>
                            <p>本服务协议中的标题仅为方便而设，在理解、解释本条款时应被忽略。</p>
                        </section>
                    </div>
                </div>
            </div>

            <div class="copyright">
                <p>Copyright © 2013-2025 <a href="" target="_blank">Drsay Inc</a>
                    . All Rights Reserved.
                    <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备13039326号-8</a>
                    <!-- <a id="imgurl" href="https://zzlz.gsxt.gov.cn/businessCheck/verifKey.do?showType=p&amp;serial=91110101074137173U-SAIC_SHOW_10000091110101074137173U1589176645610&amp;signData=MEQCIEavKy3xNbv8Ibb91cWsX26It8sbAoY8H1zH8ICjl7/YAiAq8mIK3VifKDKiDi2ioVG61zHCLA+VJpbhyqkGCB5Qxw==" title="电子营业执照" target="_blank"><img src="/theme/images/lz4.png" width="25" height="25" border="0"><br></a> -->
                </p>
            </div>
        </main>
    </div>

    <script src="/theme/js/jquery-3.7.1.min.js"></script>
    <script>
        $(function () {
            // 导航点击事件
            $('.nav-item').click(function () {
                $('.nav-item').removeClass('active');
                $(this).addClass('active');

                var section = $(this).data('section');
                if (section) {
                    // 滚动到对应章节
                    var target = $('#' + section);
                    if (target.length) {
                        $('.right').animate({
                            scrollTop: target.offset().top - $('.right').offset().top + $('.right').scrollTop() - 20
                        }, 500);
                    }
                }
            });

            // 滚动时高亮对应导航
            $('.right').scroll(function () {
                var scrollTop = $(this).scrollTop();
                var sections = ['intro', 'service', 'register', 'rights', 'privacy', 'disclaimer', 'breach', 'modify', 'law', 'other'];

                for (var i = sections.length - 1; i >= 0; i--) {
                    var section = $('#' + sections[i]);
                    if (section.length) {
                        var sectionTop = section.offset().top - $('.right').offset().top + scrollTop - 100;
                        if (scrollTop >= sectionTop) {
                            $('.nav-item').removeClass('active');
                            $('.nav-item[data-section="' + sections[i] + '"]').addClass('active');
                            break;
                        }
                    }
                }
            });
        });
    </script>
</body>

</html>