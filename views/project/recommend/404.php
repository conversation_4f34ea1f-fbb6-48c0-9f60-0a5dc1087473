<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>医汇通</title>
    <style>
       .erro-wrap {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding-top: 130px;
       }
       .erro-wrap>img {
            width: 100px;
            height: 100px;
            margin-bottom:24px;
       }
    </style>
</head>

<!-- 公用js -->
<script>
    document.write('<script src="/theme/project/recommend/js/common.js?_t=' + Date.now() + '"><\/script>');
</script>
<style>
</style>


<div id="app">
    <div class="erro-wrap">
        <img v-if="erroStatus == 1" src="/theme/project/recommend/img/page-erro.png" alt="">
        <img v-if="erroStatus == 2" src="/theme/project/recommend/img/page-nofount.png" alt="">
        <img v-if="erroStatus == 3" src="/theme/project/recommend/img/page-con-empty.png" alt="">
        <div style="font-size: 14px;color: #1D2129;line-height: 22px;margin-bottom: 12px;">{{erroText}}</div>
        <div style="font-size: 13px;color: #86909C;line-height: 20px;">请刷新试试，<span style="color: #2354F5;" @click="handleReload">立即刷新</span></div>
    </div>
</div>

<script type="module" setup>    
    const app = createApp({
        setup() {
            const erroStatus = ref(1);  //错误类型
            const erroText = ref('网络错误');  //错误文案
            const handleReload = () => {
                window.location.reload();
            }
            return {
                erroStatus,
                erroText,
                handleReload
            }
        }
    });
    app.use(vant);
    app.mount('#app');
</script>