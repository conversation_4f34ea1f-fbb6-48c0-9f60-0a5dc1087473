<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>信息提交成功</title>
    <style>
        .sub-error-wrap {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 16px;
            box-sizing: border-box;
            background: #FFFFFF;
            min-height: 100vh;
        }
        .sub-error-wrap .sub-title {
            font-weight: 600;
            font-size: 18px;
            color: #1D2129;
            line-height: 26px;
            margin-bottom: 20px;
        }
        .sub-error-wrap .erro-info-wrap {
            background: #FFFFFF;
            box-shadow: 0px 2px 6px 0px rgba(158,158,158,0.3);
            border-radius: 8px;
            width: 100%;
            padding-bottom: 16px;
            margin-bottom: 35px;
        }
        .sub-error-wrap .erro-info-wrap .notice-dec {
            background: #FFF4EB;
            border-radius: 8px 8px 0px 0px;
            padding: 12px;
            box-sizing: border-box;
            font-size: 14px;
            color: #E11C1C;
            line-height: 22px;
            margin-bottom: 32px;
        }
        .sub-error-wrap .erro-info-wrap .error-item {
            padding: 0 12px;
            box-sizing: border-box;
            font-size: 14px;
            color: #1D2129;
            line-height: 22px;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .sub-error-wrap .erro-info-wrap .error-item::before {
            content: '';
            display: block;
            height: 4px;
            width: 4px;
            border-radius: 4px;
            background: #1D2129;
            margin-right: 8px;
        }
         .sub-error-wrap .erro-info-wrap .error-edit-btn {
            margin-top: 20px;
            width: calc(100% - 24px);
            margin-left: 12px;
        }
        .sub-error-wrap .qr-wrap {
            background: #F6F8FA;
            border-radius: 8px;
            padding: 20px 20px 16px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        .sub-error-wrap .qr-wrap>img {
            width: 120px;
            height: 120px;
            border-radius: 16px;
            border: 2px solid #D6D9E4;
            margin-bottom: 16px;
        }
    </style>
</head>

<!-- 公用js -->
<script src="/theme/project/recommend/js/common.js?_t=<?php echo time(); ?>"></script>
<style>
</style>


<div id="app">
    <div class="sub-error-wrap">
        <img style="width: 56px;height: 56px;margin-bottom: 16px;" src="/theme/project/recommend/img/err-notice.png" alt="">
        <div class="sub-title">审核失败</div>
        <div class="erro-info-wrap">
            <div class="notice-dec">你提交的信息可能存在问题，请根据以下反馈内容进行修改</div>
            <div class="error-item" v-if="rejectReasons.length > 0" v-for="(reason, index) in rejectReasons" :key="index">
                <div>{{ reason }}</div>
            </div>
            <div class="error-item" v-else>
                <div>信息审核未通过，请修改后重新提交</div>
            </div>
            <div class="error-edit-btn public-btn" @click="handleEditInfo">修改信息</div>
        </div>
        <div class="qr-wrap">
            <!-- 更换为某个小助手二维码图片 -->
            <img src="/theme/project/recommend/img/test-qr.jpg" alt="">
            <div style="font-size: 14px;color: #86909C;line-height: 22px;">想咨询更多审核细节，扫码添加小助手</div>
        </div>
    </div>
</div>

<script>
// 等待DOM和所有资源加载完成
window.addEventListener('load', function() {
    // 多次检查直到Vue和Vant都加载完成
    let checkCount = 0;
    const checkLibs = () => {
        checkCount++;
        
        if (typeof Vue !== 'undefined' && typeof vant !== 'undefined') {
            initApp();
        } else if (checkCount < 20) {
            setTimeout(checkLibs, 100);
        }
    };
    
    const initApp = () => {
        // 检查是否已经挂载过
        if (document.querySelector('#app').__vue_app__) {
            return;
        }
        
        const { createApp, ref, onMounted } = Vue;
        const app = createApp({
            setup() {
                // 从PHP获取驳回原因
                const rejectReasonText = <?php echo json_encode($reject_reason ?? ''); ?>;
                const token = <?php echo json_encode($token ?? ''); ?>;
                
                // 处理驳回原因，支持多行文本和分号分隔
                const rejectReasons = ref([]);
                
                if (rejectReasonText && rejectReasonText.trim()) {
                    // 先按分号分隔，再按换行符分隔
                    let reasons = rejectReasonText.split(/[;；]/).map(item => item.trim()).filter(item => item);
                    if (reasons.length === 0) {
                        // 如果没有分号，按换行符分隔
                        reasons = rejectReasonText.split(/\n/).map(item => item.trim()).filter(item => item);
                    }
                    if (reasons.length === 0) {
                        // 如果都没有，直接使用原文本
                        reasons = [rejectReasonText.trim()];
                    }
                    rejectReasons.value = reasons;
                } else {
                    rejectReasons.value = ['信息审核未通过，请修改后重新提交'];
                }
                
                // 修改信息按钮点击事件
                const handleEditInfo = () => {
                    if (token) {
                        window.location.href = '/project/recommend/editUser/' + token + '?edit=1';
                    } else {
                        window.location.href = '/project/recommend/editUser?edit=1';
                    }
                };
                
                // 移除心跳检测，只保留用户主动触发的续期
                
                onMounted(() => {
                    // 页面初始化（不需要心跳检测）
                });
                
                return {
                    rejectReasons,
                    handleEditInfo
                };
            }
        });
        
        app.use(vant);
        app.mount('#app');
    };
    
    checkLibs();
});  
</script>