<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>信息提交成功</title>
    <style>
        .sub-suc-wrap {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 51px 24px 0;
            box-sizing: border-box;
            background: #FFFFFF;
            min-height: 100vh;
        }
        .sub-suc-wrap .sub-title {
            font-weight: 600;
            font-size: 18px;
            color: #1D2129;
            line-height: 26px;
            margin-bottom: 12px;
        }
        .sub-suc-wrap .sub-dec {
            font-weight: 400;
            font-size: 14px;
            color: #86909C;
            line-height: 22px;
            margin-bottom: 40px;
            text-align: center;
            padding: 0 18px;
        }
        .sub-suc-wrap .qr-wrap {
            background-image: url('/theme/project/recommend/img/qr-wrap.png');
            background-size: 100%;
            background-repeat: no-repeat;
            width: 327px;
            position: relative;
            padding: 148px 0 46px;
            height: 374px;
        }
        .sub-suc-wrap .qr-wrap>img {
            width: 180px;
            height: 180px;
            margin-left: calc(50% - 90px);
            border-radius: 16px;
            border: 2px solid #CDE1FF;
        }
    </style>
</head>

<!-- 公用js -->
<script src="/theme/project/recommend/js/common.js?_t=<?php echo time(); ?>"></script>
<style>
</style>


<div id="app">
    <div class="sub-suc-wrap">
        <img v-if="sucStatus == 1" style="width: 160px;height: 110px;margin-bottom: 20px;" src="/theme/project/recommend/img/suc-notice.png" alt="">
        <img v-if="sucStatus == 2" style="width: 160px;height: 110px;margin-bottom: 20px;" src="/theme/project/recommend/img/review-loading.gif" alt="">
        
        <div class="sub-title">提交成功</div>
        <div class="sub-dec" v-if="sucStatus == 1">您已经成功提交个人信息，请耐心等候信息的审核，审核通过后即可答题。</div>
        <div class="sub-dec" v-if="sucStatus == 2">后台正在抓紧时间给您分配合适的任务</div>
        <div class="qr-wrap">
            <!-- 更换为某个小助手二维码图片 -->
            <img src="/theme/project/recommend/img/test-qr.jpg" alt="">
        </div>
    </div>
</div>

<script>
window.addEventListener('load', function() {
    let checkCount = 0;
    const checkLibs = () => {
        checkCount++;
        
        if (typeof Vue !== 'undefined' && typeof vant !== 'undefined') {
            initApp();
        } else if (checkCount < 20) {
            setTimeout(checkLibs, 100);
        }
    };
    
    const initApp = () => {
        if (document.querySelector('#app').__vue_app__) {
            return;
        }
        
        const { createApp, ref, onMounted } = Vue;
        const app = createApp({
            setup() {
                const sucStatus = ref(2); // 提交审核成功状态
                const token = '<?php echo $token; ?>';
                
                // 移除心跳检测，只保留用户主动触发的续期
                
                onMounted(() => {
                    // 页面初始化（不需要心跳检测）
                });
                
                return {
                    sucStatus
                }
            }
        });
        app.use(vant);
        app.mount('#app');
    };
    
    checkLibs();
});
</script>