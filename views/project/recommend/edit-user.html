<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>完善个人信息</title>
    <style>
        .edit-user-wrap {
            background-image: url('./img/user-bg.png');
            background-size: 100%;
            background-repeat: no-repeat;
            min-height: 100vh;
            background-color: #F2F2F2;
            padding: 20px 16px;
            box-sizing: border-box;
        }
        .edit-user-wrap .notice-wrap {
            display: flex;
            column-gap: 14px;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        .edit-user-wrap .notice-wrap .notice-text {
            flex: 1;
            background: rgba(240,246,255,0.7);
            border: 1px solid #FFFFFF;
            padding: 6px 16px;
            box-sizing: border-box;
            font-weight: 400;
            font-size: 14px;
            color: #1569E8;
            line-height: 26px;
            border-radius: 12px;
            position: relative;
        }
        .edit-user-wrap .notice-wrap .notice-text>img {
            width: 14px;
            height: 14px;
            position: absolute;
            top: 18px;
            right: -9px;
        }
        .edit-user-wrap .notice-wrap>img {
            width: 58px;
            height: 66px;
            flex-shrink: 0;
        }
        .edit-user-wrap .from-wrap {
            background-color: #FFFFFF;
            border-radius: 8px;
        }
        .edit-user-wrap .from-wrap .from-item {
            padding: 8px 0;
            box-sizing: border-box;
        }
        .edit-user-wrap .notice-pay-text {
            background: rgba(240,246,255,0.7);
            border: 1px solid #FFFFFF;
            padding: 12px 16px;
            box-sizing: border-box;
            font-weight: 400;
            font-size: 14px;
            color: #1569E8;
            line-height: 26px;
            border-radius: 12px;
            margin-bottom: 12px;
            margin-top: 12px;
         }
    </style>
</head>

<!-- 公用js -->
<script>
    document.write('<script src="./js/common.js?_t=' + Date.now() + '"><\/script>');
</script>
<style>
    .van-field__label--required:before {
        margin-right: 0px;
        content: "";
    }
     .van-field__label--required::after {
        margin-right: 2px;
        color: #DF1919;
        margin-left: 4px;
        content: "*";
        vertical-align: middle;
    }
    .edit-user-wrap .user-btn {
        margin-top: 16px;
    }
</style>


<div id="app">
    <div class="edit-user-wrap">
        <div class="notice-wrap">
                <div class="notice-text">
                    哎哟，系统未查询到您的信息，请填写以下信息
                    <img src="./img/user-notice-arrow.png" alt="">
                </div>
                <img src="./img/user-notice.png" alt="">
        </div>
        <div class="from-wrap">
            <div class="from-item">
                <van-field
                    v-model="name"
                    label="姓名"
                    required
                    placeholder="请输入您的姓名"
                    input-align="right"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="city"
                    label="城市"
                    required
                    readonly
                    placeholder="请选择您的省市"
                    input-align="right"
                    right-icon="./img/from-arrow.png"
                    @click="cityShow = true"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="hospital"
                    label="医院"
                    required
                    placeholder="请输入医院名称"
                    input-align="right"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="job"
                    label="科室"
                    required
                    placeholder="请输入科室名称"
                    input-align="right"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="position"
                    label="职称"
                    readonly
                    required
                    placeholder="请选择您的职称"
                    input-align="right"
                    right-icon="./img/from-arrow.png"
                    @click="positionShow = true"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="positionType"
                    label="职业类别"
                    readonly
                    required
                    placeholder="请选择职业类别"
                    input-align="right"
                    right-icon="./img/from-arrow.png"
                    @click="positionTypeShow = true"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="zhiyeNum"
                    label="执业证号"
                    placeholder="请输入您的执业证号"
                    input-align="right"
                />
            </div>
        </div>
        <div class="notice-pay-text">为了更快给您发放奖励，请填写收款账号信息</div>
        <div class="from-wrap">
            <div class="from-item">
                <van-field
                    v-model="payName"
                    label="支付宝姓名"
                    placeholder="请输入您的支付宝姓名"
                    input-align="right"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="payCount"
                    label="支付宝账号"
                    placeholder="请输入您的支付宝账户"
                    input-align="right"
                />
            </div>
        </div>
        <div class="user-btn public-btn" @click="handleSubmit">提交</div>
    </div>
    <van-popup v-model:show="cityShow" round position="bottom">
        <van-cascader
            v-model="cascaderValue"
            title="请选择所在地区"
            :options="options"
            @close="show = false"
            @finish="onFinish"
        />
    </van-popup>
    <van-popup v-model:show="positionShow" destroy-on-close round position="bottom">
        <van-picker
            :model-value="pickerValue"
            :columns="columns"
            @cancel="positionShow = false"
            @confirm="onConfirm"
        />
    </van-popup>
    <van-popup v-model:show="positionTypeShow" destroy-on-close round position="bottom">
        <van-picker
            :model-value="pickerTypeValue"
            :columns="positionTypeColumns"
            @cancel="positionTypeShow = false"
            @confirm="positionTypeConfirm"
        />
    </van-popup>

    
</div>

<script type="module" setup>    
    const app = createApp({
        setup() {
            const name = ref('');
            const hospital = ref('');
            const job = ref('');
            const zhiyeNum = ref('');
            const payName = ref('');
            const payCount = ref('');
            const city = ref('');
            const cityShow = ref(false);
            const cascaderValue = ref('');
            const position = ref('');
            const positionType = ref('');
             
            const options = [
                {
                    text: '浙江省',
                    value: '330000',
                    children: [{ 
                        text: '杭州市',
                        value: '330100',
                        children: [{ text: '市', value: '100' }],
                    }],
                },
                {
                    text: '江苏省',
                    value: '320000',
                    children: [{ text: '南京市', value: '320100' }],
                },
            ];

            const columns = [
                { text: '职称1', value: '1' },
                { text: '职称2', value: '2' },
                { text: '职称3', value: '3' },
                { text: '职称4', value: '4' },
                { text: '职称5', value: '5' },
            ];
            const positionTypeColumns = [
                { text: '职称类别1', value: '1' },
                { text: '职称类别2', value: '2' },
                { text: '职称类别3', value: '3' },
                { text: '职称类别4', value: '4' },
                { text: '职称类别5', value: '5' },
            ];

            const positionShow = ref(false);
            const positionTypeShow = ref(false);

            const pickerValue = ref([]);
            const pickerTypeValue = ref([]);

            const onConfirm = ({ selectedValues, selectedOptions }) => {
                positionShow.value = false;
                pickerValue.value = selectedValues;
                position.value = selectedOptions[0].text;
            };
            const positionTypeConfirm = ({ selectedValues, selectedOptions }) => {
                positionTypeShow.value = false;
                pickerTypeValue.value = selectedValues;
                positionType.value = selectedOptions[0].text;
            };

            // 全部选项选择完毕后，会触发 finish 事件
            const onFinish = ({ selectedOptions }) => {
                cityShow.value = false;
                city.value = selectedOptions.map((option) => option.text).join('/');
                cascaderValue.value = selectedOptions.map((option) => option.value).join(',');
            };

          
            const handleSubmit =()=>{
                 if(!name.value) {
                    vant.showToast('请输入您的姓名');
                    return;
                }
                if(!city.value) {
                    vant.showToast('请选择省市');
                    return;
                }
                if(!hospital.value) {
                    vant.showToast('请输入医院名称');
                    return;
                }
                if(!job.value) {
                    vant.showToast('请输入科室名称');
                    return;
                }
                
                if(!position.value) {
                    vant.showToast('请选择您的职称');
                    return;
                }
                if(!positionType.value) {
                    vant.showToast('请选择职业类别');
                    return;
                }
                vant.showLoadingToast({
                    message: '提交...',
                    forbidClick: true
                });
                setTimeout(()=>{
                    vant.showToast('提交成功');
                    setTimeout(()=>{
                        window.location.href = 'file:///C:/Users/<USER>/Desktop/project/shangyishuo/sub-success.html'
                    }, 1500)
                   
                })
            }
            return {
                name,
                hospital,
                job,
                zhiyeNum,
                payName,
                payCount,
                city,
                cityShow,
                cascaderValue,
                options,
                onFinish,

                positionType,
                positionTypeColumns,
                pickerTypeValue,
                positionTypeShow,
                positionTypeConfirm,

                position,
                columns,
                onConfirm,
                positionShow,

                handleSubmit
            }
        }
    });
    app.use(vant);
    app.mount('#app');
</script>