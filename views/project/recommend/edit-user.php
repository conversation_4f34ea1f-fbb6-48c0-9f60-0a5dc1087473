<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>完善个人信息</title>
    <!-- 防止favicon.ico 404错误 -->
    <link rel="icon" href="data:;base64,iVBORw0KGgo=" />
    <style>
        .edit-user-wrap {
            background-image: url('/theme/project/recommend/img/user-bg.png');
            background-size: 100%;
            background-repeat: no-repeat;
            min-height: 100vh;
            background-color: #F2F2F2;
            padding: 20px 16px;
            box-sizing: border-box;
        }
        .edit-user-wrap .notice-wrap {
            display: flex;
            column-gap: 14px;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        .edit-user-wrap .notice-wrap .notice-text {
            flex: 1;
            background: rgba(240,246,255,0.7);
            border: 1px solid #FFFFFF;
            padding: 6px 16px;
            box-sizing: border-box;
            font-weight: 400;
            font-size: 14px;
            color: #1569E8;
            line-height: 26px;
            border-radius: 12px;
            position: relative;
        }
        .edit-user-wrap .notice-wrap .notice-text>img {
            width: 14px;
            height: 14px;
            position: absolute;
            top: 18px;
            right: -9px;
        }
        .edit-user-wrap .notice-wrap>img {
            width: 58px;
            height: 66px;
            flex-shrink: 0;
        }
        .edit-user-wrap .from-wrap {
            background-color: #FFFFFF;
            border-radius: 8px;
            overflow: visible;
        }
        .edit-user-wrap .from-wrap .from-item {
            padding: 8px 0;
            box-sizing: border-box;
            overflow: visible;
        }
        .edit-user-wrap .notice-pay-text {
            background: rgba(240,246,255,0.7);
            border: 1px solid #FFFFFF;
            padding: 12px 16px;
            box-sizing: border-box;
            font-weight: 400;
            font-size: 14px;
            color: #1569E8;
            line-height: 26px;
            border-radius: 12px;
            margin-bottom: 12px;
            margin-top: 12px;
         }
    </style>
</head>

<!-- 公用js -->
<script src="/theme/project/recommend/js/common.js?_t=<?php echo time(); ?>"></script>
<style>
    .van-field__label--required:before {
        margin-right: 0px;
        content: "";
    }
     .van-field__label--required::after {
        margin-right: 2px;
        color: #DF1919;
        margin-left: 4px;
        content: "*";
        vertical-align: middle;
    }
    .edit-user-wrap .user-btn {
        margin-top: 16px;
    }
    
    /* 医院搜索下拉框样式 */
    .hospital-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        max-height: 200px;
        overflow-y: auto;
        z-index: 9999 !important;
        margin-top: 2px;
    }
    
    .hospital-dropdown-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f5f5f5;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .hospital-dropdown-item:last-child {
        border-bottom: none;
    }
    
    .hospital-dropdown-item:hover {
        background-color: #f8f9fa;
    }
    
    .hospital-dropdown-item.loading,
    .hospital-dropdown-item.no-result {
        text-align: center;
        color: #999;
        cursor: default;
    }
    
    .hospital-dropdown-item.loading:hover,
    .hospital-dropdown-item.no-result:hover {
        background-color: white;
    }
    
    .hospital-name {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
    }
    
    .hospital-address {
        font-size: 12px;
        color: #999;
    }
</style>


<div id="app">
    <div class="edit-user-wrap">
        <div class="notice-wrap">
                <div class="notice-text">
                    哎哟，系统未查询到您的信息，请填写以下信息
                    <img src="/theme/project/recommend/img/user-notice-arrow.png" alt="">
                </div>
                <img src="/theme/project/recommend/img/user-notice.png" alt="">
        </div>
        
        <div class="from-wrap">
            <div class="from-item">
                <van-field
                    v-model="name"
                    label="姓名"
                    required
                    placeholder="请输入您的姓名"
                    input-align="right"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="regionState.displayText"
                    label="城市"
                    required
                    readonly
                    placeholder="请选择您的城市"
                    input-align="right"
                    right-icon="/theme/project/recommend/img/from-arrow.png"
                    @click="handleCityClick"
                />
            </div>
            <div class="from-item" style="position: relative;">
                <van-field
                    v-model="hospitalState.keyword"
                    label="医院"
                    required
                    placeholder="请选择省市后输入医院名称"
                    input-align="right"
                    @input="onHospitalInput"
                    @blur="onHospitalBlur"
                />
                
            </div>
            <div class="from-item" style="position: relative;">
                <van-field
                    v-model="departmentState.keyword"
                    label="科室"
                    required
                    placeholder="请输入科室名称"
                    input-align="right"
                    @input="onDepartmentInput"
                    @blur="onDepartmentBlur"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="practiceSort"
                    label="执业类别"
                    readonly
                    required
                    placeholder="请选择执业类别"
                    input-align="right"
                    right-icon="/theme/project/recommend/img/from-arrow.png"
                    @click="practiceSortShow = true"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="jobTitle"
                    label="职称"
                    readonly
                    required
                    placeholder="请选择您的职称"
                    input-align="right"
                    right-icon="/theme/project/recommend/img/from-arrow.png"
                    @click="handleJobTitleClick"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="licenseNo"
                    label="执业证号"
                    placeholder="请输入您的执业证号"
                    input-align="right"
                />
            </div>
        </div>
        <div class="notice-pay-text">为了更快给您发放奖励，请填写收款账号信息</div>
        <div class="from-wrap">
            <div class="from-item">
                <van-field
                    v-model="payName"
                    label="支付宝姓名"
                    required
                    placeholder="请输入您的支付宝姓名"
                    input-align="right"
                />
            </div>
            <div class="from-item">
                <van-field
                    v-model="payCount"
                    label="支付宝账号"
                    required
                    placeholder="请输入您的支付宝账户"
                    input-align="right"
                />
            </div>
        </div>
        <div class="user-btn public-btn" @click="handleSubmit">提交</div>
    </div>
    <van-popup v-model:show="regionState.show" round position="bottom">
        <van-cascader
            v-model="regionState.values"
            title="请选择所在地区"
            :options="regionState.options"
            @close="regionState.show = false"
            @finish="onFinish"
        />
    </van-popup>
    <van-popup v-model:show="jobTitleShow" destroy-on-close round position="bottom">
        <van-picker
            :model-value="jobTitlePickerValue"
            :columns="jobTitleColumns"
            @cancel="jobTitleShow = false"
            @confirm="jobTitleConfirm"
        />
    </van-popup>
    <van-popup v-model:show="practiceSortShow" destroy-on-close round position="bottom">
        <van-picker
            :model-value="practiceSortPickerValue"
            :columns="practiceSortColumns"
            @cancel="practiceSortShow = false"
            @confirm="practiceSortConfirm"
        />
    </van-popup>


    
</div>

<!-- 内联下拉搜索结果，放在Vue外面 -->
<div id="inlineDropdown" style="background: white; border: 1px solid #e8e8e8; 
     border-radius: 6px; padding: 0;
     box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: none; max-height: 200px; overflow-y: auto;
     position: fixed; z-index: 99999;">
</div>

<!-- 科室内联下拉搜索结果 -->
<div id="inlineDepartmentDropdown" style="background: white; border: 1px solid #e8e8e8; 
     border-radius: 6px; padding: 0;
     box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: none; max-height: 200px; overflow-y: auto;
     position: fixed; z-index: 99999;">
</div>

<script>
// 等待DOM和所有资源加载完成
window.addEventListener('load', function() {
    // 多次检查直到Vue和Vant都加载完成
    let checkCount = 0;
    const checkLibs = () => {
        checkCount++;
        
        if (typeof Vue !== 'undefined' && typeof vant !== 'undefined') {
            initApp();
        } else if (checkCount < 20) {
            setTimeout(checkLibs, 100);
        }
    };
    
    const initApp = () => {
        // 检查是否已经挂载过
        if (document.querySelector('#app').__vue_app__) {
            return;
        }
        
        const { createApp, ref, onMounted } = Vue;
    const app = createApp({
        setup() {
            // 从PHP获取用户数据用于回显
            const userInfo = <?php echo json_encode($user_info ?? []); ?>;
            const isEditMode = userInfo.is_edit_mode || false;
            const token = '<?php echo $token; ?>';
            
            // 基本表单字段
            const name = ref(userInfo.name || '');
            const licenseNo = ref(userInfo.license_no || '');
            const payName = ref(userInfo.pay_name || '');
            const payCount = ref(userInfo.pay_count || '');
            
            // 🎯 优雅的地区选择器状态管理
            const regionState = ref({
                displayText: '',        // 显示文本：如 "北京市/北京市/朝阳区"
                ids: {                  // ID集合
                    province: userInfo.province_id || null,
                    city: userInfo.city_id || null,
                    district: userInfo.district_id || null
                },
                show: false,            // 弹窗显示状态
                options: [],            // 级联数据
                values: userInfo.province_id && userInfo.city_id && userInfo.district_id ? 
                    [parseInt(userInfo.province_id), parseInt(userInfo.city_id), parseInt(userInfo.district_id)] : []
            });

            // 医院搜索状态
            const hospitalState = ref({
                keyword: '',
                id: userInfo.hospital_id || null,
                list: []
            });
            
            // 科室搜索状态  
            const departmentState = ref({
                keyword: '',
                id: userInfo.department_id || null,
                list: []
            });

            // 执业类别和职称
            const practiceSort = ref('');
            const practiceSortId = ref(userInfo.practice_sort_id || '');
            const practiceSortColumns = ref([]);
            const practiceSortShow = ref(false);
            const practiceSortPickerValue = ref([]);
            
            const jobTitle = ref('');
            const jobTitleId = ref(userInfo.job_title_id || '');
            const jobTitleColumns = ref([]);
            const jobTitleShow = ref(false);
            const jobTitlePickerValue = ref([]);

            // 职称选择确认
            const jobTitleConfirm = ({ selectedValues, selectedOptions }) => {
                jobTitleShow.value = false;
                jobTitlePickerValue.value = selectedValues;
                jobTitle.value = selectedOptions[0].text;
                jobTitleId.value = selectedOptions[0].value;
            };
            
            // 执业类别选择确认
            const practiceSortConfirm = async ({ selectedValues, selectedOptions }) => {
                practiceSortShow.value = false;
                practiceSortPickerValue.value = selectedValues;
                practiceSort.value = selectedOptions[0].text;
                practiceSortId.value = selectedOptions[0].value;
                
                // 清空职称选择
                jobTitle.value = '';
                jobTitleId.value = '';
                jobTitlePickerValue.value = [];
                jobTitleColumns.value = [];
                
                // 显示加载提示
                vant.showLoadingToast({
                    message: '加载职称数据...',
                    forbidClick: true
                });
                
                try {
                    // 根据选择的执业类别加载职称
                    await loadJobTitles(selectedOptions[0].value);
                    vant.closeToast();
                } catch (error) {
                    vant.closeToast();
                    vant.showToast('加载职称数据失败');
                    console.error('加载职称失败:', error);
                }
            };


            // 处理职称点击事件
            const handleJobTitleClick = async () => {
                if (!practiceSortId.value) {
                    vant.showToast('请先选择执业类别');
                    return;
                }
                
                // 如果职称数据为空，尝试重新加载
                if (jobTitleColumns.value.length === 0) {
                    vant.showLoadingToast({
                        message: '加载职称数据...',
                        forbidClick: true
                    });
                    
                    try {
                        await loadJobTitles(practiceSortId.value);
                        vant.closeToast();
                    } catch (error) {
                        vant.closeToast();
                        vant.showToast('加载职称数据失败');
                        return;
                    }
                }
                
                // 直接打开选择器，不再判断数据是否为空
                jobTitleShow.value = true;
            };

            // 🎯 重新设计的地区选择器 - 预加载所有数据
            const regionSelector = {
                // 初始化所有地区数据
                async init() {
                    try {
                        vant.showLoadingToast({
                            message: '加载地区数据...',
                            forbidClick: true
                        });

                        // 获取所有省份
                        const provinceResponse = await fetch('/project/recommend/getProvinces');
                        const provinceResult = await provinceResponse.json();

                        if (provinceResult.code !== 200) {
                            throw new Error('获取省份数据失败');
                        }

                        const options = [];

                        // 为每个省份加载城市和区县
                        for (const province of provinceResult.data) {
                            const provinceOption = {
                                text: province.f_val || province.val,
                                value: parseInt(province.id),
                                children: []
                            };

                            try {
                                // 获取该省的所有城市
                                const cityResponse = await fetch(`/project/recommend/getCities?province_id=${province.id}`);
                                const cityResult = await cityResponse.json();

                                if (cityResult.code === 200 && cityResult.data) {
                                    for (const city of cityResult.data) {
                                        const cityOption = {
                                            text: city.f_val || city.val,
                                            value: parseInt(city.id),
                                            children: []
                                        };

                                        try {
                                            // 获取该市的所有区县
                                            const districtResponse = await fetch(`/project/recommend/getDistricts?city_id=${city.id}`);
                                            const districtResult = await districtResponse.json();

                                            if (districtResult.code === 200 && districtResult.data) {
                                                cityOption.children = districtResult.data.map(district => ({
                                                    text: district.f_val || district.val,
                                                    value: parseInt(district.id)
                                                }));
                                            }
                                        } catch (error) {
                                            console.warn(`获取城市 ${city.id} 的区县失败:`, error);
                                        }

                                        provinceOption.children.push(cityOption);
                                    }
                                }
                            } catch (error) {
                                console.warn(`获取省份 ${province.id} 的城市失败:`, error);
                            }

                            options.push(provinceOption);
                        }

                        regionState.value.options = options;
                        vant.closeToast();

                    } catch (error) {
                        vant.closeToast();
                        console.error('初始化地区数据失败:', error);
                        vant.showToast('初始化地区数据失败');
                    }
                },

                // 完成选择（必须选到区/县，三级完整）
                onFinish({ selectedOptions }) {
                    if (!selectedOptions || selectedOptions.length !== 3) {
                        vant.showToast('请完整选择省市区');
                        return;
                    }
                    const state = regionState.value;
                    state.displayText = selectedOptions.map(opt => opt.text).join('/');
                    state.values = selectedOptions.map(opt => parseInt(opt.value));
                    state.show = false;
                    const idKeys = ['province', 'city', 'district'];
                    idKeys.forEach((key, index) => {
                        state.ids[key] = selectedOptions[index]?.value || null;
                    });
                    regionSelector.resetHospital();
                },

                // 重置医院选择
                resetHospital() {
                    const hospital = hospitalState.value;
                    hospital.keyword = '';
                    hospital.id = null;
                    hospital.list = [];
                }
            };

            // 执业类别和职称加载
            const loadPracticeSorts = async () => {
                try {
                    const response = await fetch('/project/recommend/getPracticeSorts');
                    const result = await response.json();
                    if (result.code === 200) {
                        practiceSortColumns.value = result.data;
                    }
                } catch (error) {
                    console.error('加载执业类别失败:', error);
                    vant.showToast('加载执业类别失败');
                }
            };

            const loadJobTitles = async (practiceSortId) => {
                try {
                    const response = await fetch(`/project/recommend/getJobTitles?practice_sort_id=${practiceSortId}`);
                    const result = await response.json();
                    
                    if (result.code === 200) {
                        jobTitleColumns.value = result.data || [];
                    } else {
                        throw new Error(result.msg || '获取职称数据失败');
                    }
                } catch (error) {
                    console.error('加载职称失败:', error);
                    throw error;
                }
            };

            // ✨ 简洁优雅的事件处理
            const handleCityClick = () => regionState.value.show = true;
            const onFinish = (...args) => regionSelector.onFinish(...args);

            // 创建搜索管理器
            const createSearchManager = (type) => {
                const config = {
                    hospital: {
                        keyword: hospitalState.value.keyword,
                        id: hospitalState.value.id,
                        list: hospitalState.value.list,
                        dropdownId: 'inlineDropdown',
                        searchUrl: '/project/recommend/searchHospitals',
                        placeholder: '医院',
                        validateLocation: () => !!(regionState.value.ids.province && regionState.value.ids.city && regionState.value.ids.district),
                        locationError: '请先选择省市区',
                        buildSearchUrl: (keyword) => {
                            const { province, city, district } = regionState.value.ids;
                            return `${config.hospital.searchUrl}?province_id=${province}&city_id=${city}&district_id=${district}&keyword=${encodeURIComponent(keyword)}`;
                        }
                    },
                    department: {
                        keyword: departmentState.value.keyword,
                        id: departmentState.value.id,
                        list: departmentState.value.list,
                        dropdownId: 'inlineDepartmentDropdown',
                        searchUrl: '/project/recommend/searchDepartments',
                        placeholder: '科室',
                        validateLocation: () => true,
                        locationError: '',
                        buildSearchUrl: (keyword) => {
                            return `${config.department.searchUrl}?keyword=${encodeURIComponent(keyword)}`;
                        }
                    }
                };
                
                const cfg = config[type];
                let searchTimeout = null;
                let isSelecting = false;
                
                const manager = {
                    onInput: () => {
                        if (isSelecting) {
                            isSelecting = false;
                            return;
                        }
                        
                        // 清空ID，只有选择才能设置
                        if (type === 'hospital') {
                            hospitalState.value.id = null;
                        } else {
                            departmentState.value.id = null;
                        }
                        
                        if (searchTimeout) {
                            clearTimeout(searchTimeout);
                        }
                        searchTimeout = setTimeout(() => manager.search(), 500);
                    },
                    
                    search: async () => {
                        const state = type === 'hospital' ? hospitalState.value : departmentState.value;
                        const keyword = state.keyword;
                        
                        if (!keyword || keyword.length < 2) {
                            manager.hideDropdown();
                            state.list = [];
                            state.id = null;
                            return;
                        }
                        
                        if (!cfg.validateLocation()) {
                            vant.showToast(cfg.locationError);
                            return;
                        }
                        
                        try {
                            const response = await fetch(cfg.buildSearchUrl(keyword));
                            const result = await response.json();
                            
                            if (result.code === 200 && result.data?.length > 0) {
                                state.list = result.data;
                                manager.showDropdown(result.data);
                            } else {
                                state.list = [];
                                state.id = null;
                                manager.hideDropdown();
                            }
                        } catch (error) {
                            state.list = [];
                            state.id = null;
                            manager.hideDropdown();
                        }
                    },
                    
                    select: (item) => {
                        isSelecting = true;
                        const state = type === 'hospital' ? hospitalState.value : departmentState.value;
                        const displayName = type === 'hospital' ? item.display_name : item.name;
                        state.keyword = displayName;
                        state.id = item.id;
                        manager.hideDropdown();
                        setTimeout(() => { isSelecting = false; }, 100);
                    },
                    
                    validate: () => {
                        const state = type === 'hospital' ? hospitalState.value : departmentState.value;
                        if (state.keyword && !state.id) {
                            state.keyword = '';
                            manager.hideDropdown();
                            // 同步DOM
                            const input = document.querySelector(`input[placeholder*="${cfg.placeholder}"]`);
                            if (input) input.value = '';
                        }
                    },
                    
                    onBlur: () => {
                        setTimeout(() => manager.validate(), 300);
                    },
                    
                    showDropdown: (data) => {
                        const dropdown = document.getElementById(cfg.dropdownId);
                        const input = document.querySelector(`input[placeholder*="${cfg.placeholder}"]`);
                        
                        if (!dropdown || !input || !data.length) return;
                        
                        const rect = input.getBoundingClientRect();
                        dropdown.style.position = 'fixed';
                        dropdown.style.top = (rect.bottom + 2) + 'px';
                        dropdown.style.left = rect.left + 'px';
                        dropdown.style.width = rect.width + 'px';
                        
                        let html = '';
                        data.forEach(item => {
                            const displayName = type === 'hospital' ? item.display_name : item.name;
                            const address = type === 'hospital' ? item.address : '';
                            html += `
                                <div onclick="window.searchManagers.${type}.select(${JSON.stringify(item).replace(/"/g, '&quot;')})" 
                                     style="padding: 12px 16px; border-bottom: 1px solid #f0f0f0; cursor: pointer; background: white;"
                                     onmouseover="this.style.backgroundColor='#f8f9fa'"
                                     onmouseout="this.style.backgroundColor='white'">
                                    <div style="font-weight: bold; color: #333; margin-bottom: 4px; font-size: 14px;">${displayName}</div>
                                    ${address ? `<div style="font-size: 12px; color: #666;">${address}</div>` : ''}
                                </div>
                            `;
                        });
                        
                        dropdown.innerHTML = html;
                        dropdown.style.display = 'block';
                        dropdown.style.visibility = 'visible';
                    },
                    
                    hideDropdown: () => {
                        const dropdown = document.getElementById(cfg.dropdownId);
                        if (dropdown) {
                            dropdown.style.display = 'none';
                            dropdown.style.visibility = '';
                            dropdown.innerHTML = '';
                        }
                    }
                };
                
                return manager;
            };
            
            // 创建搜索管理器实例
            const hospitalManager = createSearchManager('hospital');
            const departmentManager = createSearchManager('department');
            
            // 暴露到全局供HTML调用
            window.searchManagers = {
                hospital: hospitalManager,
                department: departmentManager
            };
            
            // 简化的事件处理函数
            const onHospitalInput = hospitalManager.onInput;
            const onHospitalBlur = hospitalManager.onBlur;
            const onDepartmentInput = departmentManager.onInput;
            const onDepartmentBlur = departmentManager.onBlur;
            
            // 初始化编辑模式的数据回显
            const initEditModeData = async () => {
                if (!isEditMode) return;
                
                try {
                    // 1. 回显地区信息
                    await initRegionDisplay();
                    
                    // 2. 回显医院和科室名称
                    if (userInfo.hospital_id) {
                        await loadHospitalName(userInfo.hospital_id);
                    }
                    if (userInfo.department_id) {
                        await loadDepartmentName(userInfo.department_id);
                    }
                    
                    // 3. 回显执业类别和职称
                    if (userInfo.practice_sort_id) {
                        await loadPracticeSortName(userInfo.practice_sort_id);
                        if (userInfo.job_title_id) {
                            await loadJobTitleName(userInfo.job_title_id);  
                        }
                    }
                    
                } catch (error) {
                    console.error('初始化编辑数据失败:', error);
                }
            };
            
            // 初始化地区显示文本
            const initRegionDisplay = async () => {
                const ids = regionState.value.ids;
                if (!ids.province || !ids.city || !ids.district) return;
                
                try {
                    const names = [];
                    let provinceName = '', cityName = '', districtName = '';
                    
                    // 并行获取所有数据
                    const [provinceResponse, cityResponse, districtResponse] = await Promise.all([
                        fetch('/project/recommend/getProvinces'),
                        fetch(`/project/recommend/getCities?province_id=${ids.province}`),
                        fetch(`/project/recommend/getDistricts?city_id=${ids.city}`)
                    ]);
                    
                    // 处理省份
                    const provinceResult = await provinceResponse.json();
                    if (provinceResult.code === 200) {
                        const province = provinceResult.data.find(p => p.id == ids.province);
                        if (province) provinceName = province.f_val || province.val;
                    }
                    
                    // 处理城市
                    const cityResult = await cityResponse.json();
                    if (cityResult.code === 200) {
                        const city = cityResult.data.find(c => c.id == ids.city);
                        if (city) cityName = city.f_val || city.val;
                    }
                    
                    // 处理区县
                    const districtResult = await districtResponse.json();
                    if (districtResult.code === 200) {
                        const district = districtResult.data.find(d => d.id == ids.district);
                        if (district) districtName = district.f_val || district.val;
                    }
                    
                    // 只要有名称就拼接显示，即使不完整
                    const nameArray = [provinceName, cityName, districtName].filter(name => name);
                    if (nameArray.length > 0) {
                        regionState.value.displayText = nameArray.join('/');
                    }
                    
                    console.log('地区回显结果:', {
                        ids,
                        names: { provinceName, cityName, districtName },
                        displayText: regionState.value.displayText
                    });
                    
                } catch (error) {
                    console.error('初始化地区显示失败:', error);
                }
            };
            
            
            // 根据执业类别ID获取名称并设置
            const loadPracticeSortName = async (practiceSortId) => {
                try {
                    // 等待执业类别数据加载完成
                    await new Promise(resolve => {
                        const checkPracticeSorts = () => {
                            if (practiceSortColumns.value.length > 0) {
                                resolve();
                            } else {
                                setTimeout(checkPracticeSorts, 100);
                            }
                        };
                        checkPracticeSorts();
                    });
                    
                    const practiceSortItem = practiceSortColumns.value.find(item => item.value == practiceSortId);
                    if (practiceSortItem) {
                        practiceSort.value = practiceSortItem.text;
                        practiceSortPickerValue.value = [practiceSortItem.value];
                    }
                } catch (error) {
                    console.error('加载执业类别名称失败:', error);
                }
            };
            
            // 根据职称ID获取名称并设置
            const loadJobTitleName = async (jobTitleId) => {
                try {
                    // 先加载职称数据
                    await loadJobTitles(userInfo.practice_sort_id);
                    
                    const jobTitleItem = jobTitleColumns.value.find(item => item.value == jobTitleId);
                    if (jobTitleItem) {
                        jobTitle.value = jobTitleItem.text;
                        jobTitlePickerValue.value = [jobTitleItem.value];
                    }
                } catch (error) {
                    console.error('加载职称名称失败:', error);
                }
            };
            
            // 根据医院ID获取名称并设置
            const loadHospitalName = async (hospitalId) => {
                try {
                    const response = await fetch(`/project/recommend/searchHospitals?hospital_id=${hospitalId}`);
                    const result = await response.json();
                    
                    if (result.code === 200 && result.data?.length > 0) {
                        const hospital = result.data[0];
                        hospitalState.value.keyword = hospital.display_name || hospital.name;
                        hospitalState.value.id = hospital.id;
                    } else {
                        console.warn('无法获取医院名称，ID:', hospitalId);
                    }
                } catch (error) {
                    console.error('加载医院名称失败:', error);
                }
            };
            
            // 根据科室ID获取名称并设置
            const loadDepartmentName = async (departmentId) => {
                try {
                    const response = await fetch(`/project/recommend/searchDepartments?department_id=${departmentId}`);
                    const result = await response.json();
                    
                    if (result.code === 200 && result.data?.length > 0) {
                        const department = result.data[0];
                        departmentState.value.keyword = department.name;
                        departmentState.value.id = department.id;
                    } else {
                        console.warn('无法获取科室名称，ID:', departmentId);
                    }
                } catch (error) {
                    console.error('加载科室名称失败:', error);
                }
            };

            // 简化：移除心跳检测，只保留用户主动触发的续期

            // 🚀 页面初始化 - 简洁明了
            onMounted(async () => {
                // 并行加载基础数据
                await Promise.all([
                    regionSelector.init(),
                    loadPracticeSorts()
                ]);
                
                // 如果是编辑模式，初始化回显数据
                if (isEditMode) {
                    await initEditModeData();
                }
                
                console.log('✅ 页面初始化完成');
                
                // 添加全局点击事件，点击其他地方时关闭下拉框
                document.addEventListener('click', (event) => {
                    // 检查是否点击了医院相关元素
                    const hospitalInput = document.querySelector('input[placeholder*="医院"]');
                    const hospitalDropdown = document.getElementById('inlineDropdown');
                    
                    if (hospitalInput && hospitalDropdown && 
                        !hospitalInput.contains(event.target) && 
                        !hospitalDropdown.contains(event.target)) {
                        setTimeout(() => hospitalManager.validate(), 150);
                        hospitalManager.hideDropdown();
                    }
                    
                    // 检查是否点击了科室相关元素
                    const departmentInput = document.querySelector('input[placeholder*="科室"]');
                    const departmentDropdown = document.getElementById('inlineDepartmentDropdown');
                    
                    if (departmentInput && departmentDropdown && 
                        !departmentInput.contains(event.target) && 
                        !departmentDropdown.contains(event.target)) {
                        setTimeout(() => departmentManager.validate(), 150);
                        departmentManager.hideDropdown();
                    }
                });
            });

          
            const handleSubmit = async () => {
                // 前端验证
                if(!name.value) {
                    vant.showToast('请输入您的姓名');
                    return;
                }
                if (!regionState.value.displayText || !regionState.value.ids.province || !regionState.value.ids.city || !regionState.value.ids.district) {
                    vant.showToast('请完整选择省市区三级');
                    return;
                }
                if(!hospitalState.value.keyword || !hospitalState.value.id) {
                    vant.showToast('请选择医院');
                    return;
                }
                if(!departmentState.value.keyword || !departmentState.value.id) {
                    vant.showToast('请选择科室');
                    return;
                }
                if(!practiceSort.value || !practiceSortId.value) {
                    vant.showToast('请选择执业类别');
                    return;
                }
                if(!jobTitle.value || !jobTitleId.value) {
                    vant.showToast('请选择职称');
                    return;
                }
                if(!payName.value) {
                    vant.showToast('请输入支付宝姓名');
                    return;
                }
                if(!payCount.value) {
                    vant.showToast('请输入支付宝账号');
                    return;
                }

                // 显示加载提示
                vant.showLoadingToast({
                    message: '提交中...',
                    forbidClick: true
                });

                try {
                    // 准备提交数据
                    const submitData = {
                        name: name.value,
                        city: regionState.value.displayText, // 保留显示用的城市名称
                        province_id: regionState.value.ids.province,
                        city_id: regionState.value.ids.city,
                        district_id: regionState.value.ids.district,
                        hospital_id: hospitalState.value.id,
                        hospital_name: hospitalState.value.keyword,
                        department_id: departmentState.value.id,
                        department_name: departmentState.value.keyword,
                        practice_sort_id: practiceSortId.value,
                        practice_sort_name: practiceSort.value,
                        job_title_id: jobTitleId.value,
                        job_title_name: jobTitle.value,
                        license_no: licenseNo.value,
                        pay_name: payName.value,
                        pay_count: payCount.value,
                        token: token
                    };

                    // 调用提交API
                    const response = await fetch('/project/recommend/submitUserInfo', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams(submitData)
                    });

                    const result = await response.json();
                    vant.closeToast(); // 关闭加载提示

                    if (result.code === 200) {
                        vant.showSuccessToast('提交成功');
                        setTimeout(() => {
                            // 使用后端返回的跳转链接，或默认跳转
                            const redirectUrl = result.data?.redirect_url || '/project/recommend/sub-success';
                            window.location.href = redirectUrl;
                        }, 1500);
                    } else {
                        vant.showFailToast(result.msg || '提交失败，请重试');
                    }

                } catch (error) {
                    vant.closeToast();
                    console.error('提交失败:', error);
                    vant.showFailToast('网络错误，请稍后重试');
                }
            }
            return {
                // 基本表单字段
                name,
                licenseNo,
                payName,
                payCount,
                
                // 🎯 优雅的状态管理
                regionState,
                hospitalState,
                departmentState,
                
                // 事件处理
                handleCityClick,
                onFinish,
                lazyLoadData,

                // 搜索事件处理
                onHospitalInput,
                onHospitalBlur,
                onDepartmentInput,
                onDepartmentBlur,

                // 执业类别和职称
                practiceSort,
                practiceSortId,
                practiceSortColumns,
                practiceSortPickerValue,
                practiceSortShow,
                practiceSortConfirm,
                jobTitle,
                jobTitleId,
                jobTitleColumns,
                jobTitleConfirm,
                jobTitleShow,
                handleJobTitleClick,

                // 提交
                handleSubmit
            }
        }
    });
    app.use(vant);
    const mountedApp = app.mount('#app');
    
    // 保存Vue应用实例的全局引用，供外部函数使用
    window.vueApp = app;
    };
    
    checkLibs();
});

</script>