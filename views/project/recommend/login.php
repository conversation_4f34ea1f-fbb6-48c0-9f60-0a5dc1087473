<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>登录</title>
    <style>
        .login-wrap {
            min-height: 100vh;
            background: url('/theme/project/recommend/img/login-bg.png');
            background-size: 100%;
            background-repeat: no-repeat;
            background-color: #FFFFFF;
            overflow: hidden;
        }
        .login-wrap .login-con {
            background: #FFFFFF;
            border-radius: 20px;
            margin: 204px 10px 0;
            width: calc(100% - 20px);
            padding: 24px 14px;
            box-sizing: border-box;
            height: 60vh;
            position: relative;
        }
        .login-wrap .login-con .form-item-wrap {
            padding: 6px 16px;
            box-sizing: border-box;
            background: #F6F8FA;
            border-radius: 10px;
            margin: 0 8px 16px;
            display: flex;
            align-items: center;
        }
        .login-wrap .login-con .form-item-wrap>img {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }
        .login-wrap .login-con .form-item-code-wrap {
            display: flex;
            align-items: center;
            padding-right: 16px;
        }
        .login-wrap .login-con .form-item-code-wrap .code-text {
            text-align: center;
            font-size: 14px;
            max-width: 86px;
            color: #2354F5;
            line-height: 22px;
            white-space: nowrap;
        }
        .login-wrap .login-con .form-item-code-wrap .code-text-sub {
            color: #86909C;
        }
        .login-wrap .login-con .login-btn {
            margin: 66px 8px 0;
        }

        .login-wrap .login-privacy {
            position: absolute;
            bottom: 24px;
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #86909C;
            line-height: 20px;
            flex-wrap: wrap;
            margin: 0 16px;
        }
        .login-wrap .login-privacy>img {
            width:20px;
            height: 20px;
            margin-right: 4px;
        }
        .login-wrap .login-privacy .privacy-title {
            color: #3B86F6;
        }
         @media screen and (min-width: 320px) {
            .login-wrap .login-con {
                margin: 176px 10px 0;
            }
        }
        @media screen and (min-width: 360px) {
            .login-wrap .login-con {
                margin: 198px 10px 0;
            }
        }
        @media screen and (min-width: 375px) {
            .login-wrap .login-con {
                margin: 206px 10px 0;
            }
        }
        @media screen and (min-width: 390px) {
            .login-wrap .login-con {
                margin: 214px 10px 0;
            }
        }
        @media screen and (min-width: 414px) {
            .login-wrap .login-con {
                margin: 226px 10px 0;
            }
        }
        @media screen and (min-width: 430px) {
            .login-wrap .login-con {
                margin: 236px 10px 0;
            }
        }
    </style>
</head>

<!-- 公用js -->
<script src="/theme/project/recommend/js/common.js?_t=<?php echo time(); ?>"></script>
<style>
    .van-cell {
        background: none;
        font-size: 14px;
        padding-left: 6px;
    }
</style>

<div id="app" v-cloak>
    <div class="login-wrap">
        <div class="login-con">
            <div class="form-item-wrap">
                <img src="/theme/project/recommend/img/login-phone.png" alt="">
                <div>
                    <van-field v-model="mobile" placeholder="请输入手机号" />
                </div>
                
            </div>
            <div class="form-item-wrap form-item-code-wrap">
                <img src="/theme/project/recommend/img/login-code.png" alt="">
                <div style="flex:1;">
                    <van-field v-model="code" placeholder="请输入验证码" />
                </div>
                
                <div class="row-line"></div>
                <div class="code-text"  @click="handleCode" v-if="codeText=='获取验证码'">{{codeText}}</div>
                <div class="code-text code-text-sub" v-if="codeText!='获取验证码'">{{codeText}}</div>
            </div>

            <div class="login-btn public-btn" @click="handleLogin">登录</div>

            <div class="login-privacy"  @click="handleAgreee" v-if="isShowPrivace" >
                <img v-if="!isAgree" src="/theme/project/recommend/img/pra-uncheck.png" alt="">
                <img v-if="isAgree" src="/theme/project/recommend/img/pra-checked.png" alt="">
                
                我已阅读并同意 <span class="privacy-title" @click.stop="handleUserMent">《用户协议》</span> 和 <span class="privacy-title" @click.stop="handleServerPrivacy">《隐私声明》</span>
            </div>
        </div>
    </div>
    <van-action-sheet style="height: 50vh;" v-model:show="privacyShow" :title="privacyTitle">
        <div class="content" style="padding:0 16px 40px;" v-html="privacyContent"></div>
    </van-action-sheet>
</div>

<script type="module" setup>    
    const app = createApp({
        setup() {
            const mobile = ref('');
            const code = ref('');
            const token = ref('<?php echo $token; ?>');
            const codeText = ref('获取验证码');
            const isAgree = ref(false);
            const isShowPrivace = ref(true);
            const privacyShow = ref(false);
            const privacyTitle = ref('用户协议');
            const privacyContent = ref('<p>用户协议</p>');
            onMounted(()=>{
                let originalHeight = window.innerHeight;
                window.addEventListener('resize', () => {
                    const currentHeight = window.innerHeight;
                    const keyboardHeight = originalHeight - currentHeight;
                    // 键盘弹出时，将元素移出可视区域
                    if (keyboardHeight > 100) { // 阈值避免误判
                        isShowPrivace.value = false;
                    } else {
                        isShowPrivace.value = true;
                    }
                });
            });
            function handleCode() {
                if(codeText.value == '获取验证码') {
                    if(!mobile.value) {
                        vant.showToast('请输入手机号');
                        return;
                    }
                    vant.showLoadingToast({
                        message: '发送中...',
                        forbidClick: true
                    });
                    
                    ajaxRequest('/project/recommend/sendSms', { 
                        mobile: mobile.value,
                        token: token.value
                    }, function(res) {
                        vant.closeToast(); // 关闭loading
                        res = JSON.parse(res);
                        if(res.rs_code == 'success') {
                            vant.showToast('发送成功');
                            // 启动60秒倒计时
                            let codeTime = 60;
                            codeText.value = codeTime + 's后重新获取';
                            let codeTimer = setInterval(() => {
                                codeTime--;
                                codeText.value = codeTime + 's后重新获取';
                                if(codeTime <= 0) {
                                    codeText.value = '获取验证码';
                                    clearInterval(codeTimer);
                                }
                            }, 1000);
                        } else {
                            vant.showToast(res.rs_msg);
                        }
                    }, function(xhr, status, error) {
                        vant.closeToast(); // 关闭loading
                        vant.showToast('发送失败，请重试');
                        console.error('Error:', status, error);
                    });
                }
            }
            function handleLogin () {
                if(!mobile.value) {
                    vant.showToast('请输入手机号');
                    return;
                }
                if(!code.value) {
                    vant.showToast('请输入验证码');
                    return;
                }
                if(!isAgree.value) {
                    vant.showToast('请阅读并同意用户隐私声明');
                    return;
                }

                ajaxRequest('/project/recommend/codeLogin', { 
                    mobile: mobile.value,
                    verify_code:code.value,
                    token: token.value
                }, function(res) {
                    res = JSON.parse(res);
                    if (res.rs_code == 'success') {
                        vant.showToast('登录成功');
                        window.location.href = res.rs_backurl;
                    } else {
                        vant.showToast(res.rs_msg);
                    }
                }, function(xhr, status, error) {
                    console.error('Error:', status, error);
                });
            }
            const handleAgreee = () => {
                isAgree.value = !isAgree.value;
                
            }
            const handleUserMent = () => {
                privacyShow.value = true;
                privacyTitle.value = '用户协议';
                privacyContent.value = '<p>用户协议</p>';
            }
            const handleServerPrivacy = () => {
                privacyShow.value = true;
                privacyTitle.value = '隐私声明';
                privacyContent.value = '<p>隐私声明</p>';
            }
            return {
                mobile,
                code,
                codeText,
                isAgree,
                isShowPrivace,
                privacyTitle,
                privacyShow,
                privacyContent,
                handleAgreee,
                handleUserMent,
                handleServerPrivacy,
                handleCode,
                handleLogin,
                
            }
        }
    });
    app.use(vant);
    app.mount('#app');
</script>