<!DOCTYPE>
<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="上医说" />
    <meta name="description" content="上医说" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>健康通-上医说</title>
    <link rel="shortcut icon" href="/theme/images/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/theme/management/font-awesome/css/font-awesome.css" />
    <script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
    <script src="/theme/new_manage/js/plugins/sweetalert/sweetalert.min.js"></script>
    <link href="/theme/css/web_style.css" rel="stylesheet">
    <!--防止被网络广告劫持弹出广告-->
    <style>
        html {
            display: none;
        }

        body,
        html {
            overflow-x: hidden;
        }

        .logo {
            width: 100%;
            margin: 0px 0 5px 15px;
        }

        .logo .web {
            float: right;
            font-size: 24px;
            margin-right: 30px;
            margin-top: 20px;
            color: #0b3c82;

        }

        .logo img {
            width: auto;
            height: 45px;
        }

        .line {
            width: 100%;
            height: 1px;
            background: #a2bde8;
            margin: 15px 0;
        }

        input {
            -webkit-appearance: none;
            outline: none;
        }

        button {
            cursor: pointer;
        }

        .payment_style label {
            text-align: left;
            display: block;
            padding: 5px 15px;
        }

        /* .payment_style label input {
            width: 20px !important;
        } */

        .alpay {
            padding: 20px 0;
        }

        .alpay label {
            display: block;
        }

        .alpay label span {
            display: inline-block;
            margin-bottom: 10px;
        }

        .alpay label input {
            width: 100% !important;
            height: 44px;
        }

        .single .input {
            display: inline-block;
            width: 22px;
            height: 22px;
            border: 1px solid #ccc;
            border-radius: 50%;
            box-sizing: border-box;
            margin-right: 10px;
            vertical-align: middle;
        }

        .submit {
            width: 90%;
            padding: 10px;
            border-radius: 5px;
            background: #fff;
            border: none;
            font-size: 22px;
            color: #fff;
            outline: none;
            letter-spacing: 10px;
            background: #53a4f4;
        }

        .box_pic_center {
            padding-bottom: 30px;
        }

        /* 对话框 */
        .dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            position: fixed;
            left: 0;
            top: 0;
        }

        .dialog {
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            background: #CAE1FF;
            text-align: center;
            width: 86%;
            color: #fff;
            min-height: 260px;
            font-size: 24px;
            border-radius: 3px;
            border: 1px solid #010810;
            box-sizing: border-box;
        }

        .dialog .title {
            background: #103e80;
            padding: 15px 0;
            border-bottom: 1px solid #072a56;
            color: #fff;
        }

        .dialog .word {
            padding: 40px 0;
            color: #041b3d;
            word-break: break-word;
        }

        .dialog .button {
            width: 100%;
        }

        .dialog .button button {
            word-break: break-word;
            box-sizing: border-box;
            width: 100%;
            border: none;
            outline: none;
            padding: 10px;
            background: #53a4f4;
            font-size: 22px;
            color: #fff;
            border-top: 1px solid #CAE1FF;
        }

        .dialog .last button {
            border-bottom: 1px solid #406eb0;
        }

        .dialog .first button {
            border-top: 1px solid #406eb0;
        }

        .dialog .bg {
            height: 20px;
            background: #3e6fb7;
            margin-top: 30px;
        }


        .dialog .button button:hover {
            background: #eda124;
        }

        .confirm_dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            position: fixed;
            top: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .confirm_dialog {
            width: 80%;
            min-height: 150px;
            margin: 0 auto;
            background: #fff;
            border: 1px solid #252424;
            border-radius: 5px;
            overflow: hidden;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .confirm_dialog .notice_title {
            padding: 10px 0;
            border-bottom: 1px solid #252424;
            background: #aa90da;
            font-size: 20px;
        }

        .confirm_dialog .notice_info {
            padding: 50px 0;
            font-size: 18px;
            border-bottom: 1px solid #252424;
        }

        .confirm_dialog .button {
            height: 44px;
            padding: 0px 30px;
        }

        .confirm_dialog .button button {
            height: 100%;
            min-width: 75px;
            border: 1px solid #ccc;
            outline: none;
            border-bottom: none;
            border-top: none;
            border-radius: 15px;
            font-size: 16px;
        }

        .confirm_dialog .button .cancel {
            background: #efefef;
            float: left;
        }

        .confirm_dialog .button .confirm {
            background: #aa90da;
            float: right;
        }

        .code {
            padding-top: 20px;
        }

        .code p {
            height: 40px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            border-radius: 0.15rem;
        }

        .code p input {
            height: 100%;
            padding: 0 10px;
            border: none;
        }

        /*.code p button {*/
            /*height: 100%;*/
            /*padding: 0 10px;*/
            /*border: none;*/
            /*outline: none;*/
            /*background: #a6ebcd;*/
            /*float: right;*/
        /*}*/

        .code p button {
            height: 100%;
            width: 120px;
            border: none;
            outline: none;
            background: #a6ebcd;
            float: right;
        }

        .code input[name="verify_code"] {
            width: 40%;
            float: left;
        }

        .disable {
            pointer-events: none;
        }
    </style>
    <script>
        if (self == top) {
            document.documentElement.style.display = 'block';
        } else {
            top.location = self.location;
        }
    </script>
    <!--防止被网络广告劫持弹出广告-->
    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(document.documentElement.getBoundingClientRect().width);
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + 'px';
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
<div class="layout">
    <div class="logo">
        <a href="http://www.gooddr.com/" target="_blank" title="drsay">
            <img src="/theme/admin/im/img/jkt_logo.png" title="drsay" alt="drsay" border="0" />
        </a>

        <img style="float: right; margin-right: 30px;" src="http://www.drsay.cn/theme/images/jkt.jpg" />
    </div>
    <div class="box_pic_top"></div>
    <div class="box_pic_center">
        <h1 class="title_font">
            <span class="STYLE1">感谢您的参与</span>
        </h1>
        <h2 style="text-align: center;">
<!--            <form onsubmit="return common_js.form_sumbit(this,'show_msg','处理中','post','');"  class="form-horizontal" id="item_form">-->
            <form method="post" class="form-horizontal">
                <p class="status" style="font-size:22px;padding: 10px 0;">问卷状态选择</p>
                <p class="line"></p>
                <div class="payment_style" style="font-size:20px;">
                    <label class="checkbox-inline i-checks">
                        <input class="finish_status" type="radio" name="finish_status" value="c" /> 访问成功 </label>

                    <label class="checkbox-inline i-checks">
                        <input class="finish_status" type="radio" name="finish_status" value="s" /> 访问失败 </label>


                    <p style="padding:20px 0;">
                        <input type="hidden" name="s_u" value="<?php echo $s_u;?>" />
                        <input type="hidden" name="s_c" value="<?php echo $s_c;?>" />
                        <button class="submit" type="submit" onclick="return check_from();">确认</button>
                    </p>
                </div>
            </form>
        </h2>
        <br />
    </div>
    <div class="box_pic_bottom"></div>
</div>


<div class="footer" style="clear:both;">
    Copyright &copy; <?php echo date('Y'); ?> Powered by 健康通
</div>

<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>
<script src="/theme/new_manage/assets/js/jquery.form.js"></script>
<script src="/theme/new_manage/assets/js/comm_init_swal.js"></script>
<script>
    $(document).ready(function() {
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });

    function check_from()
    {
        var finish_status = $(".finish_status:checked").val();
        if (finish_status == undefined || finish_status == "") {
            alert("请答题！");
            return false;
        }
        return true;
    }
</script>
</body>

</html>
