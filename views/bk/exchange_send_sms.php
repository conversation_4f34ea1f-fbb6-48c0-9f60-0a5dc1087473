<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no" />
    <title>健康通-上医说-身份确认</title>
    <script src="/theme/bk/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/comm_init.js?<?php echo time(); ?>"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
        }

        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
        }

        input {
            -webkit-appearance: none;
        }

        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }

        .title {
            text-align: center;
            /*padding-top: 50px;*/
            padding-top: 20px;
            font-weight: 700;
            font-size: 30px;
        }

        .content {
            text-align: center;
            /*padding-top: 50px;*/
            padding-top: 20px;
        }

        .content .select {
            position: relative;
            color: #666;
            margin: 0 auto;
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            height: 36px;
            text-align: left;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 14px;
        }

        .content .select_wy {
            position: relative;
            color: #666;
            margin: 0 auto;
            border: 2px solid #03DBC6;
            padding: 5px;
            width: 80%;
            height: 36px;
            text-align: left;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 14px;
        }

        .content select {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            opacity: 0;
        }

        /*.content input[type="text"] {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 36px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }*/

        .content .input {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 36px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 14px;
        }

        .content .input_wy {
            border: 2px solid #03DBC6;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 36px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 14px;
        }

        .msg {
            margin-top: 15px;
            color: red;
        }

        .btn {
            text-align: center;
            width: 90%;
            margin: 0 auto;
            margin-top: 40px;
        }

        .btn button {
            cursor: pointer;
            font-size: 24px;
            width: 45%;
            padding: 10px;
            border: none;
            outline: none;
            border-radius: 2px;
        }

        .btn .confirm {
            color: #fff;
            background: #f85691;
        }

        .btn .confirm_wy {
            color: #fff;
            background: #03DBC6;
        }

        .btn .cancel_class {
            color: #000;
            background: transparent;
            border: 1px solid #fd5592;
        }

        .btn .cancel_class_wy {
            color: #000;
            background: transparent;
            border: 1px solid #03DBC6;
        }

        .content {
            text-align: center;
            padding-top: 50px;
        }

        /*.content .input_tag {*/
        /*border: 2px solid #fd5592;*/
        /*padding: 5px;*/
        /*width: 70%;*/
        /*outline: none;*/
        /*height: 46px;*/
        /*font-size: 14px;*/
        /*border-radius: 2px;*/
        /*margin-bottom: 20px;*/
        /*}*/

        .content .tips {
            font-size: 12px;
            text-align: left;
            width: 70%;
            margin: 0 auto;
            margin-top: 5px;
        }

        .content .code {
            width: 80%;
            /*height: 46px;*/
            margin: 0 auto;
            position: relative;
        }

        .content .code input {
            width: 100%;
        }

        .content .code .get_code_class {
            position: absolute;
            right: 0;
            height: 36px;
            line-height: 36px;
            background: #f85691;
            color: #fff;
            padding: 0 10px;
            font-size: 14px;
            cursor: pointer;
        }

        .content .code .get_code_class_wy {
            position: absolute;
            right: 0;
            height: 36px;
            line-height: 36px;
            background: #03DBC6;
            color: #fff;
            padding: 0 10px;
            font-size: 14px;
            cursor: pointer;
        }

        .error_msg {
            margin-bottom: 10px;
            margin-top: 10px;
            color: red;
        }

        .isAgree label {
            margin-right: 5px;
            cursor: pointer;
        }

        .isAgree label span {
            margin-right: 4px;
            display: inline-block;
            width: 15px;
            height: 15px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            border-radius: 50%;
            vertical-align: middle;
        }

        .isAgree label span.active {
            border: 5px solid #f33a7f;
        }


        .isType label {
            margin-right: 5px;
            cursor: pointer;
        }

        .isType label span {
            margin-right: 4px;
            display: inline-block;
            width: 15px;
            height: 15px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            vertical-align: middle;
        }

        .isType label span.active {
            border: 4px solid #f33a7f;
        }
    </style>
</head>

<body>
    <?php
    $border_class = "select";
    $input_class = "input";
    $get_code_class = "get_code_class";
    $btn_submit_class = "confirm";
    $btn_cancel_class = "cancel_class";
    $project_sys_id = $this->session->userdata("project_sys_id");
    if (in_array($project_sys_id, [7, 8, 9])) { //网医
        $border_class = "select_wy";
        $input_class = "input_wy";
        $get_code_class = "get_code_class_wy";
        $btn_submit_class = "confirm_wy";
        $btn_cancel_class = "cancel_class_wy";
    }
    ?>
    <div class="container">
        <div class="title">授权确认</div>

        <form id="the_from" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
            <div class="content">
                <?php if ($member_info) { ?>
                    <p class="<?php echo $border_class; ?>">
                        <span><?php echo $member_info['province']; ?></span>
                    </p>
                    <p class="<?php echo $border_class; ?>">
                        <span><?php echo $member_info['city']; ?></span>
                    </p>
                    <input class="<?php echo $input_class; ?> input_tag" type="text" placeholder="请输入姓名[必填]" value="<?php echo $imp_name; ?>" readonly />
                    <input class="<?php echo $input_class; ?> input_tag" type="text" placeholder="请输入医院[必填]" value="<?php echo $imp_unit_name && $member_info['unit_name'] != $imp_unit_name ? $imp_unit_name : $member_info['unit_name']; ?>" readonly />
                    <input class="<?php echo $input_class; ?> input_tag" type="text" placeholder="请输入身份证号[必填]" value="<?php echo data_desensitization($member_info['id_card'], 6, 10); ?>" readonly />
                    <input class="<?php echo $input_class; ?> input_tag" type="text" placeholder="请输入科室[必填]" value="<?php echo $member_info['department']; ?>" readonly />
                <?php } else { ?>
                    <p class="<?php echo $border_class; ?>">
                        <span><?php echo $member_info ? $member_info['province'] : "请选择省份[必选]"; ?></span>
                        <select name="province" onchange="get_city_district('province', this.value);">
                            <option value="">请选择省份</option>
                            <?php foreach ($province_list as $k_pro => $v_pro) { ?>
                                <option value="<?php echo $k_pro; ?>"><?php echo $v_pro; ?></option>
                            <?php } ?>
                        </select>
                    </p>
                    <p class="<?php echo $border_class; ?>">
                        <span>请选择城市[必选]</span>
                        <select name="city" id="city">
                            <option value="">请选择城市</option>
                        </select>
                    </p>
                    <input class="<?php echo $input_class; ?> input_tag" type="text" name="dr_name" placeholder="请输入姓名[必填]" autocomplete="off" readonly value="<?php echo $imp_name; ?>" />
                    <input class="<?php echo $input_class; ?> input_tag" type="text" name="unit_name" placeholder="请输入医院[必填]" autocomplete="off" />
                    <input class="<?php echo $input_class; ?> input_tag" type="text" name="indentity_code" placeholder="请输入身份证号[必填]" autocomplete="off" />
                    <input class="<?php echo $input_class; ?> input_tag" type="text" name="department" placeholder="请输入科室[必填]" autocomplete="off" />
                <?php } ?>

                <input class="<?php echo $input_class; ?> input_tag" type="text" id="phone" name="verify_mobile" placeholder="请输入手机号码" value="<?php echo $imp_mobile; ?>" <?php echo $imp_mobile ? "readonly" : ""; ?> autocomplete="off" />
                <p class="code">
                    <input class="<?php echo $input_class; ?> input_tag" type="text" id="code" name="verify_code" placeholder="请输入验证码" autocomplete="off" />
                    <span class="get_code <?php echo $get_code_class; ?>">获取验证码</span>
                </p>

                <input class="<?php echo $input_class; ?> input_tag" type="text" id="project_evaluation" name="project_evaluation" placeholder="请您对本次问卷进行评价，谢谢。" value="<?php echo $project_payment_log ? $project_payment_log['project_evaluation'] : ""; ?>" autocomplete="off" />

                <div style="border: 1px dashed #f33a7f;width: 80%;margin: 0 auto;padding: 10px;">
                    <!--<p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;">请问如果在数据使用中对您填写的信息有疑问您是否愿意公开您的非隐私信息（医院、名字、科室）便于我们再次联系您？</p>
                    <p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;" class="isAgree">
                        <label><span class="<?php if ($project_payment_log && $project_payment_log['is_agree'] == 1) {echo "active";}?>"></span><input name="is_agree" type="radio" value="1" <?php if ($project_payment_log && $project_payment_log['is_agree'] == 1) {echo "checked";}?> />愿意</label>
                        <label><span class="<?php if ($project_payment_log && $project_payment_log['is_agree'] == 2) {echo "active";}?>"></span><input name="is_agree" type="radio" value="2" <?php if ($project_payment_log && $project_payment_log['is_agree'] == 2) {echo "checked";}?> />不愿意</label>
                    </p>-->
                    <p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;">非常感谢您完成的调研，您的专业意见对我们的研究非常重要。我们在研究分析过程中可能还会涉及问卷相关问题需要向您简短确认（约 1-2 分钟），结束后会有一份心意礼金。我们可以按您方便的时间联系。请问您是否方便参与？</p>
                    <p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;" class="isAgree">
                        <label><span class="<?php echo $member_info && $member_info['is_join'] == 1 ? "active" : "";?>"></span><input name="is_join" type="radio" value="1" <?php echo $member_info && $member_info['is_join'] == 1 ? "checked" : "";?> />1.愿意参与</label>
                    </p>
                    <p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;padding-left:20px;" class="isAgree">
                        <span style="width: 85px; display: inline-block;">方便的时间段：</span>
                        <input style="width:50% !important;height:24px !important;margin-bottom: 0px !important;font-size:12px !important;" class="<?php echo $input_class; ?> input_tag" type="text" name="join_time" placeholder="请输入" autocomplete="off" value="<?php echo $member_info['join_time'] ?? "";?>" />
                    </p>
                    <p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;padding-left:20px;" class="isType">
                        <span style="width: 85px;display: inline-block;">参与方式：</span>
                        <label><span class="<?php echo !empty($join_type) && in_array(1, $join_type) ? "active" : "";?>"></span><input name="join_type[]" type="checkbox" value="1" <?php echo !empty($join_type) && in_array(1, $join_type) ? "checked" : "";?> />电话</label>
                        <label><span class="<?php echo !empty($join_type) && in_array(2, $join_type) ? "active" : "";?>"></span><input name="join_type[]" type="checkbox" value="2" <?php echo !empty($join_type) && in_array(2, $join_type) ? "checked" : "";?> />微信</label>
                    </p>
                    <p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;" class="isAgree">
                        <label><span class="<?php echo $member_info && $member_info['is_join'] == 2 ? "active" : "";?>"></span><input name="is_join" type="radio" value="2" <?php echo $member_info && $member_info['is_join'] == 2 ? "checked" : "";?> />2.不方便参与</label>
                    </p>
                    <p style="font-size: 12px;text-align: left;display: inline-block;width: 100%;" class="isAgree">
                        <label><span class="<?php echo $member_info && $member_info['is_join'] == 3 ? "active" : "";?>"></span><input name="is_join" type="radio" value="3" <?php echo $member_info && $member_info['is_join'] == 3 ? "checked" : "";?> />3.后续再联系</label>
                    </p>
                </div>
                <p class="tips">为确保调研礼金支付给答卷者，请获取输入验证码，证明是您本人操作，谢谢！</p>
                <div class="error_msg"></div>
            </div>

            <div class="btn">
                <input type="hidden" name="bk_code" value="<?php echo $bk_code; ?>" />
                <input type="hidden" name="survey_uid_code" value="<?php echo $survey_uid_code; ?>" />
                <button class="<?php echo $btn_submit_class; ?> submit" type="submit">确 认</button>
                <button class="cancel <?php echo $btn_cancel_class; ?>">重 置</button>
            </div>
        </form>
    </div>


    <script type="text/html" id="city_tpl">
        <option value="">请选择城市</option>
        {{each list v i }}
        <option value="{{v.id}}">{{v.val}}</option>
        {{/each}}
    </script>

    <script src="/theme/js/template-web.js?<?php echo time(); ?>"></script>

    <script>
        $(function() {
            // 获取授权码
            $(".get_code").click(function() {
                var that = $(this);
                if (!that.hasClass("send")) {
                    var verify_mobile = $("#phone").val().trim();
                    if (verify_mobile) {
                        $.ajax({
                            url: "/bk/pro_send_sms",
                            type: "post",
                            data: {
                                bk_code: '<?php echo $bk_code; ?>',
                                survey_uid_code: '<?php echo $survey_uid_code; ?>',
                                verify_mobile: verify_mobile
                            },
                            dataType: "json",
                            success: function(info) {
                                $(".error_msg").text(info.rs_msg)
                                if (info.rs_code == "success_to_pay") {
                                    location.href = info.rs_backurl;
                                } else {
                                    if (info.rs_code == "success") {
                                        that.addClass("send");
                                        var seconds = 60;
                                        var timeId = setInterval(function() {
                                            seconds--;
                                            if (seconds > 0) {
                                                that.text(seconds + "s后重新发送");
                                            } else {
                                                clearInterval(timeId);
                                                that.removeClass("send");
                                                that.text("获取授权码");
                                            }
                                        }, 1000);
                                    }
                                }
                            }
                        })
                    } else {
                        $(".error_msg").text("请输入手机号")
                    }
                }
            });



            // 单选题点击效果
            $(".isAgree label input[type='radio']").click(function() {
                if ($(this).is(":checked")) {
                    $(this).prev().addClass("active").parent().parent().siblings(".isAgree").children().children("span").removeClass("active")
                }
            })


            // 多选题点击效果
            $(".isType label input[type='checkbox']").click(function() {
                if ($(this).is(":checked")) {
                    $(this).prev().addClass("active")
                } else {
                    $(this).prev().removeClass("active")
                }
            })


        });

        $(function() {
            // 下拉框的选中效果
            $(".container").on("change", "select", function() {
                var text = $(this).children("select option:selected").text();
                $(this).parent().children("span").text(text);
            });

            // 清空表单内容
            $(".cancel").click(function() {
                $("form")[0].reset();
            });
        });

        //二级联动
        function get_city_district(type, parent_id) {
            if (type == "province") {
                $("#city").html("");
                $("#district").html("");
            }
            $.ajax({
                type: "POST",
                url: "/bk/get_city_district",
                data: "parent_id=" + parent_id,
                success: function(msg) {
                    var json_msg = jQuery.parseJSON(msg);
                    if (json_msg.rs_code == "error") {
                        return false;
                    }
                    if (type == "province") { //当前操作的级别
                        $("#city").html(template("city_tpl", {
                            list: json_msg.rs_msg
                        }))
                    }
                }
            });
        }
    </script>
</body>

</html>