<?php
$btn_width = "width:33.3% !important;";
if($project_info && $project_info['is_az'] == 1){
    $btn_width = "width:50% !important;";
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no" />
    <title>健康通-上医说</title>
    <script src="/theme/bk/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
            box-sizing: border-box;
        }

        a {
            text-decoration: none;
        }

        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
            position: relative;
        }

        input[type="text"] {
            -webkit-appearance: none;
        }

        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }

        .title {
            text-align: center;
            padding-top: 5px;
            padding-bottom: 5px;
            font-weight: 700;
            font-size: 30px;
        }

        .tab {
            height: 40px;
        }

        /*.tab ul {
            height: 100%;
            border-bottom: 1px solid #2b3a99;
            border-top: 1px solid #2b3a99;
        }*/

        .tab .ul_class {
            height: 100%;
            border-bottom: 1px solid #2b3a99;
            border-top: 1px solid #2b3a99;
        }

        .tab .ul_class_wy {
            height: 100%;
            border-bottom: 1px solid #03DBC6;
            border-top: 1px solid #03DBC6;
        }

        /*.tab ul li {
            border-right: 1px solid #2b3a99;
            list-style: none;
            float: left;
            height: 100%;
            width: 33.3%;
            line-height: 40px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }

        .tab ul li:last-child {
            border-right: none;
        }*/

        .tab ul .li_class {
            border-right: 1px solid #2b3a99;
            list-style: none;
            float: left;
            height: 100%;
            width: 33.3%;
            line-height: 40px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }
        .tab ul .li_class_wy {
            border-right: 1px solid #03DBC6;
            list-style: none;
            float: left;
            height: 100%;
            width: 33.3%;
            line-height: 40px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }

        .tab ul li:last-child {
            border-right: none;
        }

        .tab_content {
            text-align: center;
            padding-top: 15px;
        }

        .privacy,
        .checkbox {
            padding-left: 40px;
            text-align: left;
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            vertical-align: middle;
            margin-right: 5px;
        }

        /*.tab_content input[type="text"] {*/
        /*border: 2px solid #fd5592;*/
        /*padding: 5px;*/
        /*width: 80%;*/
        /*outline: none;*/
        /*height: 46px;*/
        /*font-size: 14px;*/
        /*border-radius: 2px;*/
        /*margin-bottom: 20px;*/
        /*}*/

        /*input[type="text"] {*/
        /*border: 2px solid #fd5592;*/
        /*padding: 5px;*/
        /*width: 80%;*/
        /*outline: none;*/
        /*height: 46px;*/
        /*font-size: 14px;*/
        /*border-radius: 2px;*/
        /*margin-bottom: 20px;*/
        /*}*/

        .input {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 36px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }
        .input_wy {
            border: 2px solid #03DBC6;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 36px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .tab_content .select {
            position: relative;
            color: #666;
            margin: 0 auto;
            border: 2px solid #fd5592;
            padding: 10px 5px 5px;
            width: 80%;
            height: 46px;
            text-align: left;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .tab_content select {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            opacity: 0;
        }

        .wx {
            background: #fff;
            position: relative;
            text-align: center;
        }

        .wx .ewm img {
            width: 240px;
        }

        .wx .ewm .tip {
            width: 100%;
            color: #2b3a99;
        }

        .wx .ewmed {
            width: 240px;
            margin: 0 auto;
            text-align: center;
            display: none;
        }

        .wx .ewmed img {
            width: 240px;
        }

        .error_msg {
            margin-top: 15px;
            color: red;
        }

        .btn {
            /*position: absolute;*/
            /*text-align: center;*/
            /*width: 88%;*/
            /*bottom: 20px;*/
            /*left: 50%;*/
            /*transform: translateX(-50%);*/
            text-align: center;
            margin: 0 auto;
            margin-top: 20px;
        }

        .btn button {
            cursor: pointer;
            font-size: 24px;
            /*width: 45%;*/
            width: 40%;
            padding: 10px;
            border: none;
            outline: none;
            border-radius: 2px;
        }

        .btn .confirm_class {
            color: #fff;
            background: #f85691;
        }

        .btn .cancel_class {
            color: #000;
            background: transparent;
            border: 1px solid #fd5592;
        }

        .btn .confirm_class_wy {
            color: #fff;
            background: #03DBC6;
        }

        .btn .cancel_class_wy {
            color: #000;
            background: transparent;
            border: 1px solid #03DBC6;
        }

        .privacy_dialog_box {
            z-index: 999;
            display: none;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .privacy_dialog {
            width: 80%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
        }

        /*.privacy_dialog h4 {
            color: #fff;
            background: #2b3a99;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }*/

        .privacy_dialog .dialog_top_class {
            color: #fff;
            background: #2b3a99;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }

        .privacy_dialog .dialog_top_class_wy {
            color: #000;
            background: #b3efe9;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }

        .privacy_dialog .privacy_content {
            padding: 20px;
            border-left: 1px solid #ccc;
            border-right: 1px solid #ccc;
        }

        /*.privacy_dialog .privacy_btn button {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #fd5592;
            color: #fff;
            cursor: pointer;
        }*/

        .privacy_dialog .privacy_btn .dialog_bottom_button {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #fd5592;
            color: #fff;
            cursor: pointer;
        }

        .privacy_dialog .privacy_btn .dialog_bottom_button_wy {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            border: none;
            background: #03DBC6;
            color: #fff;
            cursor: pointer;
        }



        .success_dialog_box {
            z-index: 999;
            display: none;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .success_dialog {
            width: 80%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
        }

        .success_dialog h4 {
            color: #fff;
            background: #2b3a99;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }

        .success_dialog .success_content {
            padding: 20px;
            border-left: 1px solid #ccc;
            border-right: 1px solid #ccc;
            text-align: center;
        }

        .success_dialog .success_btn button {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #fd5592;
            color: #fff;
            cursor: pointer;
        }

        .tab_content .other input[type="radio"] {
            display: none;
        }

        .tab_content .other .radio {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 1px solid #ccc;
            border-radius: 50%;
            vertical-align: middle;
        }

        .checked {
            border:5px solid #445ad4 !important;
        }
    </style>
</head>

<body>
<?php
$input_class = "input";
$btn_submit_class = "confirm_class";
$btn_cancel_class = "cancel_class";
$ul_class = "ul_class";
$li_class = "li_class";
$dialog_top_class = "dialog_top_class";//弹出层顶部
$dialog_bottom_button = "dialog_bottom_button";//弹出层底部按钮
$menu_bg_color = "#2b3a99";
$project_sys_id = $this->session->userdata("project_sys_id");
if (in_array($project_sys_id, [7,8,9])) {//网医
    $border_class = "select_wy";
    $input_class = "input_wy";
    $get_code_class = "get_code_class_wy";
    $btn_submit_class = "confirm_class_wy";
    $btn_cancel_class = "cancel_class_wy";
    $ul_class = "ul_class_wy";
    $li_class = "li_class_wy";
    $dialog_top_class = "dialog_top_class_wy";
    $dialog_bottom_button = "dialog_bottom_button_wy";//弹出层底部按钮
    $menu_bg_color = "#03DBC6";
}
?>
<div class="container">
    <!-- 隐私协议对话框 -->
    <div class="privacy_dialog_box">
        <div class="privacy_dialog">
            <h4 class="<?php echo $dialog_top_class;?>">个人所得税代缴代付协议</h4>
            <div class="privacy_content">
                <p>受访者须知：</p>
                <p style="margin-top: 15px">
                    根据根据中国人民共和国有关税法的规定，个人劳务报酬依法应当缴纳个人所得税，服务单位有义务代扣代缴个人所得税。因此请您理解并同意本公司【健康通(北京)网络科技有限公司】在向您支付劳务报酬时，为您代扣代缴个人所得税，该过程需要使用您的个人信息进行登记申报
                </p>
                <p style="margin-top: 20px">
                    特别说明：缴纳个税不影响您的项目礼金支付金额。
                </p>
                <p style="margin-top: 20px">健康通（北京）网络科技有限公司</p>
            </div>
            <div class="privacy_btn">
                <button class="<?php echo $dialog_bottom_button;?>">确定</button>
            </div>
        </div>
    </div>

    <div class="success_dialog_box">
        <div class="success_dialog">
            <h4>提交成功！！！</h4>
            <div class="success_content">
                <p>七个工作日内支付</p>
            </div>
            <div class="success_btn">
                <?php if(isset($exist_sxo_url['url']) && !empty($exist_sxo_url['url'])){?>
                    <button><a href="<?php echo $exist_sxo_url['url'];?>" target="_blank"><button>参与互联网活动[50]</button></a></button>
                <?php }?>
                <button id="next_pro" style="display: none;">下一个项目</button>
                <button><a style="color:#fff;" href="http://www.gooddr.com?code=<?php echo $gooddr_code; ?>" target="_blank">健康通官网</a></button>
            </div>
        </div>
    </div>
    <?php
    //付款金额
    $show_payment_amount = $pid == 2248 ? "200" : $point / 100;
//    $close_wx = $this->input->get("close_wx");
    $close_wx = $this->input->get("close_wx") == 1 || $show_payment_amount > 500 ? 1 : 0;//关闭微信提现功能
//    $close_wx = 0;//先放开大额礼金领取
    ?>
    <div class="title">确认收款账户<font color="#66a325">(<?php echo $show_payment_amount;?>元)</font></div>

    <input type="hidden" name="clicked_request" value="" />
    <input type="hidden" name="wx_request" value="<?php echo $user_payment_info[EXCHANGE_WEBCHAT_AUTO] ? "success" : ""; ?>" />

    <div class="tab">
        <ul class="<?php echo $ul_class;?>">
            <li class="pay_alipay <?php echo $li_class;?>" data-type="alipay" data-id="206" style="background: <?php echo $menu_bg_color;?>; color: #fff;<?php echo $btn_width;?>">支付宝</li>
            <li class="pay_wx <?php echo $li_class;?>" data-type="wx" data-id="8779" style="<?php echo $btn_width;?>">微信</li>
            <?php if($project_info && $project_info['is_az'] != 1){?>
                <li class="pay_bank <?php echo $li_class;?>" data-type="other" data-id="other" style="<?php echo $btn_width;?>">其它</li>
            <?php }?>
        </ul>
    </div>

    <form action="/project_exchange_test/payment_sub_n" id="the_from" onsubmit="return common_js.form_sumbit(this, 'error_msg')">

        <div class="tab_content">
            <!-- 支付宝 -->
            <div class="alipay">
                <div class="content">
                    <input class="<?php echo $input_class;?>" type="text" name="alipay_payment_name" placeholder="请输入本人支付宝用户名[实名]" value="<?php echo $imp_name; ?>" readonly />
                    <input class="<?php echo $input_class;?>" type="text" name="alipay_payment_account" placeholder="请输入本人支付宝收款账号" value="<?php echo isset($user_payment_info[EXCHANGE_ALIPAY]) ? $user_payment_info[EXCHANGE_ALIPAY]['payment_account'] : ""; ?>" />
                </div>
            </div>

            <!-- 微信 -->
            <div class="wx" style="display: none">
                <?php if ($close_wx == 1) {?>
                    近期微信支付系统升级，暂不支持相关服务。我们会尽快恢复，敬请谅解。
                <?php } else {?>
                <div class="is_close_wx">
                    <?php if(!$user_payment_info[EXCHANGE_WEBCHAT_AUTO]){
                        $file_name = file_get_contents("./uploads/wechat/{$pid}/{$filename}");
                        ?>
                        <div class="ewm">
                            <div class="tips">扫描二维码绑定健康通授权微信收款</div>
                            <img src="data:image/jpg/png/gif;base64,<?php echo base64_encode($file_name); ?>" alt="" />
                            <div class="tip">(截图通过微信扫描识别二维码)</div>
                        </div>

                        <div class="ewmed">
                            <img src="/theme/go/image/success.png" alt="">
                            <p>已授权健康通支付</p>
                        </div>
                    <?php } else {?>
                        <div class="ewmed" style="display: block;">
                            <img src="/theme/go/image/success.png" alt="">
                            <p>已授权健康通支付</p>
                        </div>
                    <?php }?>
                    <input class="<?php echo $input_class;?>" type="text" name="wx_payment_name" placeholder="请输入本人微信用户名[实名]" style="margin-top: 10px;" value="<?php echo $imp_name; ?>" readonly />
                </div>
                <?php }?>
            </div>

            <!-- 其它 -->
            <?php if($project_info && $project_info['is_az'] != 1){?>
                <div class="other" style="display: none">
                    <div class="content" style="font-size:20px;text-align:left;padding-left: 40px;">
                        <!--去掉转剩余积分
                        <div>
                            <label>
                                <span class="radio"></span>
                                <input class="other_payment_type" type="radio" name="other_payment_type" value="8774">转入我的会员积分
                            </label>
                        </div>-->
                        <div>
                            <label>
                                <span class="radio"></span>
                                <input class="other_payment_type" type="radio" name="other_payment_type" value="101" />慈善捐赠<br />
                            </label>
                            <span style="font-size: 16px;">（上海红十字会合作代赠）</span>
                        </div>
                    </div>
                </div>
            <?php }?>
        </div>


        <!--独立多选框 start-->
        <div class="checkbox" id="default_payment_type" style="margin-bottom: 50px;">
            <!--            <label>-->
            <!--                <input type="checkbox" />-->
            <!--                <span>选中为默认首选收款账户</span>-->
            <!--            </label>-->
            <p class="lock_account" style="margin-left: 30px; font-size: 12px; color: red;display:none;">
                <!--                (注:默认收款账户已设置,重设请先撤销)-->
            </p>
        </div>
        <!--独立多选框 end-->

        <div class="btn other_close_wx">
            <!--            <div id="id_card_info"><input type="text" name="id_card" placeholder="请输入收款对象身份证号码" value="" /></div>-->
            <div class="privacy">
                <label>
                    <input type="checkbox" name="privacy" value="1" />
                    <span>阅读并同意<a href="javascript:;">《个税代缴代付协议》</a></span>
                </label>
            </div>


            <input type="hidden" name="payment_type_default" />
            <input type="hidden" name="scene" value="<?php echo $scene; ?>" />
            <input type="hidden" name="bk_code" value="<?php echo $bk_code; ?>" />
            <input type="hidden" name="survey_uid_code" value="<?php echo $survey_uid_code; ?>" />
            <input type="hidden" name="payment_type" value="<?php echo EXCHANGE_ALIPAY; ?>">
            <!-- 提示信息 -->
            <div class="error_msg" style="margin-bottom: 10px"></div>
            <button class="confirm <?php echo $btn_submit_class;?>" type="submit">确 认</button>
            <?php if (!in_array($project_sys_id, [7,8,9])) {?>
                <button class="cancel <?php echo $btn_cancel_class;?>" type="reset">重 置</button>
            <?php }?>

        </div>
    </form>
</div>


<script>

    // 单选框的点击效果
    $(" .tab_content .other input").click(function () {
        if ($(this).prop("checked"))  {
            $(".tab_content .other .radio").removeClass("checked")
            $(this).prev().addClass("checked")
        }
        var payment_type = $(this).val();
        console.log(payment_type)
        if (payment_type == 209) {//手机号码
            $("#other_phone").show();
        } else {
            $("#other_phone").hide();
        }
    })

    var timeId;
    // 下拉框的选中效果
    $(".container").on("change", "select", function() {
        var text = $(this).children("select option:selected").text();
        $(this).parent().children("span").text(text);
    });
    // 点击效果
    $(".tab ul li").click(function() {
        //独立多选框 start
        $(".checkbox input[type='checkbox']").prop("checked", false);
        $(".checkbox input[name='payment_type_default']").val("");
        $(".checkbox .lock_account").hide()
        //独立多选框 end

        // 设置input的value
        $("input[name='payment_type']").val($(this).data("id"))
        $(this)
            .css({
                background: "<?php echo $menu_bg_color;?>",
                color: "#fff"
            })
            .siblings()
            .css({
                background: "#fff",
                color: "#000"
            });
        var type = $(this).data("type");
        $("." + type)
            .show()
            .siblings()
            .hide();

        // 微信支付请求检测是否已做支付
        $("#default_payment_type").show();
        $("#id_card_info").show();
        $(".is_close_wx").show();
        $(".other_close_wx").show();
        if (type == "wx") {
            <?php if ($close_wx == 1) {?>
            $(".is_close_wx").hide();
            $(".other_close_wx").hide();
            <?php }?>
            if ($("input[name='wx_request']").val() == "success") {
                clearInterval("1")
            } else {
                var wtimeId = setInterval("check_order_info()", 2000);
                $("input[name='clicked_request']").val(wtimeId);
            }
        } else if(type == "other"){//其它支付方式
            $("#default_payment_type").hide();
            $("#id_card_info").hide();
            clearInterval($("input[name='clicked_request']").val())
            clearInterval("1")
        } else{
            clearInterval($("input[name='clicked_request']").val())
            clearInterval(timeId)
        }

    });

    // lock_account的显示
    var lock_account = '<?php echo $lock_account ? $lock_account : ''; ?>';

    if (lock_account) {
        // 支付宝
        if (lock_account == "206") {
            console.log(1);
            $(".is_close_wx").show();
            $(".other_close_wx").show();
            // 默认绑定支付方式
            $(".tab .pay_alipay").trigger("click")
            $(".checkbox input").prop("checked", true)
            $(".checkbox .lock_account").show()
        }
        // 微信
        if (lock_account == "8779") {
            <?php if ($close_wx == 1) {?>
            //默认显示支付宝
            $(".is_close_wx").hide();
            $(".tab .pay_alipay").trigger("click")
            $(".checkbox input").prop("checked", true)
            $(".checkbox .lock_account").show()
            $(".other_close_wx").show();
            <?php } else {?>
            // 默认绑定支付方式
            $(".tab .pay_wx").trigger("click")
            $(".checkbox input").prop("checked", true)
            $(".checkbox .lock_account").show()
            <?php }?>
        }
        // 银行
        if (lock_account == "208") {
            $(".is_close_wx").show();
            $(".other_close_wx").show();
            // 默认绑定支付方式
            $(".tab .pay_bank").trigger("click")
            $(".checkbox input").prop("checked", true)
            $(".checkbox .lock_account").show()
        }

    }




    //多选框的点击效果
    $("input[type='checkbox']").click(function() {
        //独立多选框 start
        // $("input[name='payment_type_default']").val($("input[name='payment_type']").val())
        //独立多选框 end

        // //分类型的checkbox效果
        // if ($(this).prop("checked")) {
        //     $("input[name='payment_type_default']").val($(this).val())
        //     $("input[type='checkbox']").prop("disabled", true);
        //     $(this).prop("disabled", false);
        // } else {
        //     $("input[name='payment_type_default']").val("")
        //     $("input[type='checkbox']").prop("disabled", false);
        // }

        // 多选框的点击效果
        // $(".checkbox input[type='checkbox']").click(function() {
        //     if ($(this).prop("checked")) {
        //         $(".checkbox input[type='checkbox']").prop("disabled", true);
        //         $(this).prop("disabled", false);
        //     } else {
        //         $(".checkbox input[type='checkbox']").prop("disabled", false);
        //     }
        // });
    });

    // $(".other_payment_type").click(function() {
    //     var payment_type = $(this).val();
    //     if (payment_type == 209) {//手机号码
    //         $("#other_phone").show();
    //     } else {
    //         $("#other_phone").hide();
    //     }
    // });

    <?php if ($user_payment_info[EXCHANGE_WEBCHAT_AUTO]) { ?>
    $(".ewm").hide()
    $(".ewmed").show()
    <?php } else { ?>
    // //  定时请求接口
    // if ($("input[name='wx_request']").val() == "success") {
    //     $(".ewm").hide()
    //     $(".ewmed").show()
    // } else {
    //     timeId = setInterval("check_order_info()", 2000)
    // }
    <?php } ?>

    //  请求接口
    function check_order_info() {
        $.ajax({
            type: "POST",
            url: "/bk/check_order_info",
            data: "scene=<?php echo $scene; ?>",
            success: function(str) {
                var json_msg = $.parseJSON(str);
                if (json_msg.rs_code == "success") {
                    $(".ewm").hide()
                    $(".ewmed").show()
                    clearInterval(timeId)
                    clearInterval($("input[name='clicked_request']").val())
                    $("input[name='wx_request']").val("success")
                }
            }
        });
    }

    // 隐私协议
    $(document).click(function() {
        $(".privacy_dialog_box").hide();
    });
    $(".privacy_dialog").click(function(e) {
        e.stopPropagation();
    });
    $(".privacy a").click(function(e) {
        e.stopPropagation();
        $(".privacy_dialog_box").show();
        // 对话框的确定按钮
        $(".privacy_dialog_box .privacy_btn button").click(function() {
            $(".privacy_dialog_box").hide();
            $(".privacy input").prop("checked", true)
        });
    });


    var common_js = {
        //提交表单 表单对象，显示提示信息的
        form_sumbit : function (item_form, msg_id){
            $(item_form).ajaxSubmit({
                target:'',
                beforeSubmit : null,
                success : common_js.show_response(msg_id),
                url : item_form.location,
                type : 'post',
                dataType : 'text',
                clearForm : false,
                resetForm : false,
                cache : false,
            });
            return false;
        },
        //接受PHP返回信息并重绘提示框
        show_response : function (msg_id){
            return function(str){
                var res = jQuery.parseJSON(str);
                if (res.rs_code == "success_to_pay") {
                    location.href = res.rs_backurl;
                } else {
                    if (res.rs_code == 'success') {
                        $(".success_dialog_box").show();
                        $("." + msg_id).html(res.rs_msg);
                        if (res.rs_msg != '' && res.rs_msg.point > 0) {
                            $("#next_pro").text("继续参与下一个问卷[" + res.rs_msg.point + "]");
                            $("#next_pro").show();
                            $("#next_pro").click(function () {
                                window.location.href = res.rs_msg.partner_link
                            })
                        }
                    } else {
                        $("." + msg_id).html(res.rs_msg);
                    }
                }
            }
        },
        tver : '1.0.1'
    }
    common_js;
</script>
</body>

</html>
