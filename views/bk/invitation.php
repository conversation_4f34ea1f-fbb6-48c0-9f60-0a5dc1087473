<!DOCTYPE>
<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="上医说" />
    <meta name="description" content="上医说" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>健康通-上医说</title>
    <link rel="shortcut icon" href="/theme/images/favicon.ico" type="image/x-icon" />
    <!--    <link rel="stylesheet" href="http://medical.drrenew.com/theme/3sbioinc/font-awesome/css/font-awesome.css" />-->
    <link rel="stylesheet" href="/theme/management/font-awesome/css/font-awesome.css" />
    <script src="/theme/<?= TEMPLATE_DIR ?>/js/jquery-3.1.1.min.js"></script>
    <link href="/theme/css/web_style.css" rel="stylesheet">
    <!--防止被网络广告劫持弹出广告-->
    <style>
        html {
            display: none;
        }

        body,html {
            overflow-x: hidden;
        }

        .logo {
            width: 100%;
            margin: 0px 0 5px 15px;
        }

        .logo .web {
            float: right;
            font-size: 24px;
            margin-right: 30px;
            margin-top: 20px;
            color: #0b3c82;

        }

        .logo img {
            width: auto;
            height: 45px;
        }

        .line {
            width: 100%;
            height: 1px;
            background: #a2bde8;
            margin: 15px 0;
        }

        input {
            -webkit-appearance: none;
            outline: none;
        }

        button {
            cursor: pointer;
        }

        .payment_style label {
            text-align: left;
            display: block;
            padding: 5px 15px;
        }

        /* .payment_style label input {
            width: 20px !important;
        } */

        .alpay {
            padding: 20px 0;
        }

        .alpay label {
            display: block;
        }

        .alpay label span {
            display: inline-block;
            margin-bottom: 10px;
        }

        .alpay label input {
            width: 100% !important;
            height: 44px;
        }

        .single .input {
            display: inline-block;
            width: 22px;
            height: 22px;
            border: 1px solid #ccc;
            border-radius: 50%;
            box-sizing: border-box;
            margin-right: 10px;
            vertical-align: middle;
        }

        .submit {
            width: 90%;
            padding: 10px;
            border-radius: 5px;
            background: #fff;
            border: none;
            font-size: 22px;
            color: #fff;
            outline: none;
            letter-spacing: 10px;
            background: #53a4f4;
        }

        .box_pic_center {
            padding-bottom: 30px;
        }

        /* 对话框 */
        .dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            position: fixed;
            left: 0;
            top: 0;
        }

        .dialog {
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            background: #CAE1FF;
            text-align: center;
            width: 86%;
            color: #fff;
            min-height: 260px;
            font-size: 24px;
            border-radius: 3px;
            border: 1px solid #010810;
            box-sizing: border-box;
        }

        .dialog .title {
            background: #103e80;
            padding: 15px 0;
            border-bottom: 1px solid #072a56;
            color: #fff;
        }

        .dialog .word {
            padding: 40px 0;
            color: #041b3d;
            word-break: break-word;
        }

        .dialog .button {
            width: 100%;
        }

        .dialog .button button {
            word-break: break-word;
            box-sizing: border-box;
            width: 100%;
            border: none;
            outline: none;
            padding: 10px;
            background: #53a4f4;
            font-size: 22px;
            color: #fff;
            border-top: 1px solid #CAE1FF;
        }

        .dialog .last button {
            border-bottom: 1px solid #406eb0;
        }

        .dialog .first button {
            border-top: 1px solid #406eb0;
        }

        .dialog .bg {
            height: 20px;
            background: #3e6fb7;
            margin-top: 30px;
        }


        .dialog .button button:hover {
            background: #eda124;
        }

        .confirm_dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            position: fixed;
            top: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .confirm_dialog {
            width: 80%;
            min-height: 150px;
            margin: 0 auto;
            background: #fff;
            border: 1px solid #252424;
            border-radius: 5px;
            overflow: hidden;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .confirm_dialog .notice_title {
            padding: 10px 0;
            border-bottom: 1px solid #252424;
            background: #aa90da;
            font-size: 20px;
        }

        .confirm_dialog .notice_info {
            padding: 50px 0;
            font-size: 18px;
            border-bottom: 1px solid #252424;
        }

        .confirm_dialog .button {
            height: 44px;
            padding: 0px 30px;
        }

        .confirm_dialog .button button {
            height: 100%;
            min-width: 75px;
            border: 1px solid #ccc;
            outline: none;
            border-bottom: none;
            border-top: none;
            border-radius: 15px;
            font-size: 16px;
        }

        .confirm_dialog .button .cancel {
            background: #efefef;
            float: left;
        }

        .confirm_dialog .button .confirm {
            background: #aa90da;
            float: right;
        }

        .code {
            padding-top: 20px;
        }

        .code p {
            height: 40px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            border-radius: 0.15rem;
        }

        .code p input {
            height: 100%;
            padding: 0 10px;
            border: none;
        }

        .code p button {
            height: 100%;
            padding: 0 10px;
            border: none;
            outline: none;
            background: #a6ebcd;
            float: right;
        }

        .code input[name="verify_code"] {
            width: 40%;
            float: left;
        }

        .disable {
            pointer-events: none;
        }
    </style>
    <script>
        if (self == top) {
            document.documentElement.style.display = "block";
        } else {
            top.location = self.location;
        }
    </script>
    <!--防止被网络广告劫持弹出广告-->
    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(
                document.documentElement.getBoundingClientRect().width
            );
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + "px";
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
    <div class="layout">
        <div class="logo">
            <a href="http://www.gooddr.com/" target="_blank" title="drsay">
                <img src="http://www.drsay.cn/theme/admin/im/img/jkt_logo.png" title="drsay" alt="drsay" border="0" />
            </a>
        </div>
        <div class="box_pic_top"></div>

        <div class="box_pic_center">
            <!-- <input type="hidden" name="scene" value="" /> -->
            <h1 class="title_font">
                <span class="STYLE1">感谢您的参与</span>
            </h1>

            <h2 style="text-align: center;">
                <p class="status" style="font-size:22px;padding: 10px 0;">恭喜老师成功完成本次调研问卷!请选择支付方式提交后7个工作日到账！</p>
                <p style="font-size: 28px;padding: 10px;color: #66a325;font-weight: 700">获得积分报酬</p>
                <p style="font-size:28px;font-weight:700;color: #d0021b;padding: 20px 0;">100<span style="margin:0 5px;">积分</span>=<span style="margin-left:5px;">1</span><span style="margin:0 5px;">元</span></p>
                <!-- 分界线返回状态是c且未申请 -->
                <p class="line"></p>
                <div class="payment_style" style="font-size: 20px;">
                    <p class="single">
                        <label>
                            <span class="input"></span><input type="radio" style="display: none;" data-name="alpay" name="payment_type" value="206" />转入我的支付宝<font color="green">(建议)</font>
                        </label>
                    </p>
                    <!-- 支付宝信息栏 -->
                    <div class="alpay" style="
                display: none;
                background: #cae1ff;
                padding: 10px 0;
                width: 90%;
                margin: 0 auto;
                font-size: 16px;
                margin: 10px;
              ">
                        <label class="zfb_name">
                            <span>真实姓名</span>
                            <input type="text" name="payment_name" placeholder="请输入真实姓名" value="" />
                        </label>
                        <label class="zfb_num">
                            <span>支付宝账号</span>
                            <input type="text" name="payment_account" placeholder="支付宝账号" value="" />
                        </label>
                    </div>
                    <!-- 支付宝信息栏 -->
                    <p class="single">
                        <label id="wechat">
                            <span class="input"></span><input type="radio" style="display: none;" data-name="wechat" name="payment_type" value="8779" />转入我的微信零钱
                        </label>
                    </p>

                    <!-- 微信信息栏 -->
                    <div class="wechat" style="
                display: none;
                background: #cae1ff;
                padding: 10px 0;
                width: 90%;
                margin: 0 auto;
                font-size: 12px;
                margin: 10px;
              ">
                        <p>
                            请使用您的手机微信扫码或长按下图提现
                        </p>
                        <input type="hidden" name="wechat_open_id" />
                    </div>
                    <!-- 微信信息栏 -->

                    <p class="single">
                        <label>
                            <span class="input"></span><input type="radio" style="display: none;" data-name="point" name="payment_type" value="8774" />转入我的会员积分
                            <input type="hidden" name="point" />
                        </label>
                    </p>

                    <!-- 手机 -->

                    <p class="single">
                        <label>
                            <span class="input"></span><input type="radio" data-name="phone" style="display: none;" data-name="phone" name="payment_type" value="209" />手机充值<font color="red">(不建议)</font>
                            <input type="hidden" name="phone" />
                        </label>
                    </p>
                    <!-- 手机信息 -->
                    <div class="phone" style="
                display: none;
                background: #cae1ff;
                padding: 10px 0;
                width: 90%;
                margin: 0 auto;
                font-size: 16px;
                margin: 10px;
              ">
                        <label class="phone_num">
                            <span>手机号码</span>
                            <input type="text" style="height: 44px;" name="mobile_payment_account" placeholder="请输入手机号码" value="" />
                        </label>
                    </div>

                    <!-- 手机信息 -->

                    <!-- 慈善捐赠 -->
                    <p class="single">
                        <label>
                            <span class="input"></span><input type="radio" style="display: none;" data-name="donation" name="payment_type" value="1111" />慈善捐赠
                        </label>
                    </p>
                    <!-- 慈善捐赠 -->

                    <!-- 手机验证码 -->
                    <div class="code">
                        <p>
                            <input name="verify_mobile" type="text" placeholder="请输入手机号码" />
                        </p>
                        <p>
                            <input name="verify_code" type="text" placeholder="请输入验证码" />
                            <button>获取验证码</button>
                        </p>
                    </div>

                    <p style="padding: 20px 0;">
                        <span style="color: red; display: block; padding: 10px 0;" class="submit_confirm"></span>
                        <button class="submit">确认</button>
                    </p>

                    <!-- 分界线返回状态是c且未申请 -->
                </div>

                <!-- <p>请使用您的手机微信<font color='red'><b>扫一扫</b></font>扫码提现</p> -->

                <!-- 您的分值太低，无法进行提现，请联系项目管理员！ -->
            </h2>
            <br />
        </div>
        <div class="box_pic_bottom"></div>
    </div>

    <div class="dialog_container">
        <div class="dialog">
            <p class="title">提交成功!!!</p>
            <p class="word">7个工作日内支付</p>
            <p class="button first">
                <button></button>
            </p>
            <p class="button last">
                <a href="http://www.gooddr.com" target="_blank"><button>健康通官网</button></a>
            </p>
            <p class="bg"></p>
        </div>
    </div>

    <div class="confirm_dialog_container">
        <div class="confirm_dialog">
            <p class="notice_title">确认提醒</p>
            <p class="notice_info">您确认要选择这种支付方式吗?</p>
            <p class="button">
                <button class="cancel">取消</button>
                <button class="confirm">确认</button>
            </p>
        </div>
    </div>

    <div class="footer" style="clear: both;">
        Copyright &copy; Powered by 健康通
    </div>

    <script>
        // 单选题点击效果
        $(".payment_style input").click(function() {
            // 清空提示信息
            $(".submit_confirm").text("");
            // 点击样式
            if ($(this).prop("checked")) {
                $(this).prev().css({
                    border: "7px solid #600ca9",
                });
                $(this)
                    .parent()
                    .parent()
                    .siblings(".single")
                    .children()
                    .children(".input")
                    .css({
                        border: "1px solid #ccc",
                    });
            }
            // 点击联动效果
            // 支付宝
            if ($(this).data("name") == "alpay") {
                $(".alpay").show();
                $(".wechat").hide();
                $(".phone").hide();
            }
            // 微信
            if ($(this).data("name") == "wechat") {
                $(".wechat").show();
                $(".alpay").hide();
                $(".phone").hide();
                // 微信支付即时检查
                // 定时请求
            }

            // 积分
            if ($(this).data("name") == "point") {
                $(".alpay").hide();
                $(".wechat").hide();
                $(".phone").hide();
            }
            // 手机充值
            if ($(this).data("name") == "phone") {
                $(".phone").show();
                $(".alpay").hide();
                $(".wechat").hide();
            }
            // 慈善捐赠
            if ($(this).data("name") == "donation") {
                $(".alpay").hide();
                $(".wechat").hide();
                $(".phone").hide();
            }
        });

        var timeId;
        // 提交按钮
        $(".submit").click(function() {
            // 选择的支付方式支付的方式
            var payment = $(".payment_style input:checked").data("name");
            // 如果没有选择提示选择支付方式
            if (!payment) {
                $(".submit_confirm").text("请选择");
            } else {
                // 如果支付宝支付没有填写支付宝信息 提示填写完整信息
                if (
                    payment == "alpay" &&
                    (!$("input[name='payment_name']").val() ||
                        !$("input[name='payment_account']").val())
                ) {
                    $(".submit_confirm").text("请填写支付宝的完整信息");
                    if (!$("input[name='payment_name']").val()) {
                        $("input[name='payment_name']").trigger("focus");
                    } else {
                        $("input[name='payment_account']").trigger("focus");
                    }
                }

                // 如果手机充值没有填写信息 提示填写完整信息
                if (
                    payment == "phone" &&
                    !$("input[name='mobile_payment_account']").val()
                ) {
                    $(".submit_confirm").text("请填写手机号码");
                    $("input[name='mobile_payment_account']").trigger("focus");
                }

                // 如果没有填写手机号或者验证码提示填写
                if (
                    !$(".code input[name='verify_mobile']").val() ||
                    !$(".code input[name='verify_code']").val()
                ) {
                    $(".submit_confirm").text("请填写手机号或者验证码");
                }

                if (!$(".submit_confirm").text()) {
                    // 如果完整填写信息显示确认框
                    $(".confirm_dialog_container").show();
                }

                $(".confirm_dialog_container .cancel").click(function() {
                    $(".confirm_dialog_container").hide();
                });
                $(".confirm_dialog_container .confirm")
                    .off("click")
                    .on("click", function() {
                        $(".confirm_dialog_container").hide();

                        // 如果积分支付
                        if (payment == "point") {
                            $(".submit_confirm").text("");
                        }

                        var payment_type = $(".single input:checked").val();
                        var payment_name = $("input[name='payment_name']").val();
                        var payment_account = $("input[name='payment_account']").val();
                        var mobile_payment_account = $(
                            "input[name='mobile_payment_account']"
                        ).val();
                        var verify_mobile = $("input[name='verify_mobile']").val();
                        var verify_code = $("input[name='verify_code']").val();

                        var data = {
                            // scene: scene,
                            payment_type: payment_type,
                            verify_mobile: verify_mobile,
                            verify_code: verify_code,
                        };

                        if (payment_type == 206) {
                            data.payment_name = payment_name;
                            data.payment_account = payment_account;
                        }
                        if (payment_type == 209) {
                            data.mobile_payment_account = mobile_payment_account;
                        }

                        if (!$(".submit_confirm").text()) {
                            console.log(data);
                        }
                    });
            }
        });

        // 支付宝姓名的验证
        $(".zfb_name input").blur(function() {
            var zfb_name = $(this).val();
            if (zfb_name) {
                var reg = /^([\u4e00-\u9fa5][·][\u4e00-\u9fa5]){1,20}|([\u4e00-\u9fa5][.][\u4e00-\u9fa5]){1,20}|([\u4e00-\u9fa5]){2,10}$/;
                if (!reg.test(zfb_name)) {
                    $(this).val("");
                    $(this).attr("placeholder", "请输入正确的姓名");
                }
            }
        });

        // 支付宝账号的验证
        $(".zfb_num input").blur(function() {
            var zfb_num = $(this).val();
            if (zfb_num) {
                var reg_num = /^1[3456789]\d{9}$/;
                var reg_email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                if (!reg_num.test(zfb_num) && !reg_email.test(zfb_num)) {
                    $(this).val("");
                    $(this).attr("placeholder", "请输入正确的支付宝账号");
                }
            }
        });

        // 手机验证
        $(".phone_num input").blur(function() {
            var phone_num = $(this).val();
            if (phone_num) {
                var reg_num = /^1[3456789]\d{9}$/;
                if (!reg_num.test(phone_num)) {
                    $(this).val("");
                    $(this).attr("placeholder", "您输入的手机号不合法");
                }
            }
        });

        // 验证码
        // 验证码的手机验证
        $(".code input[name='verify_mobile']").blur(function() {
            var verify_mobile = $(this).val();
            if (verify_mobile) {
                var reg_num = /^1[3456789]\d{9}$/;
                if (!reg_num.test(verify_mobile)) {
                    $(this).val("");
                    $(this).attr("placeholder", "您输入的手机号不合法");
                }
            }
        });

        // 获取验证码
        $(".code button").click(function() {
            var verify_mobile = $(".code input[name='verify_mobile']").val();
            // 如果手机号正确,发送验证码
            if (verify_mobile) {
                var that = $(this);
                // 按钮禁用点击效果
                that.addClass("disable");
                // 倒计时60s之后重新发送
                var minute = 60;
                var time_id = setInterval(function() {
                    minute--;
                    that.text(minute + "秒后重新发送");
                    // 倒计时结束清除定时器 按钮恢复点击效果
                    if (minute == 0) {
                        clearInterval(time_id);
                        that.text("重新发送");
                        that.removeClass("disable");
                    }
                }, 1000);
            }
        });
    </script>
</body>

</html>