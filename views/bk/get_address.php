<!DOCTYPE>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="上医说" />
    <meta name="description" content="上医说" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>上医说</title>
    <link rel="shortcut icon" href="http://www.drsay.cn/theme/images/favicon.ico" type="image/x-icon" />
    <script src="<?= base_url()?>theme/<?= TEMPLATE_DIR?>/js/jquery-3.1.1.min.js"></script>
    <link href="<?= base_url()?>theme/<?= TEMPLATE_DIR?>/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?= base_url()?>theme/<?= TEMPLATE_DIR?>/css/animate.css" rel="stylesheet">
    <!--防止被网络广告劫持弹出广告-->
    <style>
        html{display:none;}
    </style>
    <script>
        if( self == top ) {
            document.documentElement.style.display = 'block' ;
        } else {
            top.location = self.location ;
        }
        /**
         * parallelRoll 左右无缝滚动
         * boxName : 最外层盒子类名
         * tagName : 滚动标签元素
         * time : 滚动间隔时间
         * direction : 滚动方向  right-->向右    left-->向左
         * visual : 可视数
         * prev : 上一张
         * next : 下一张
         * */
        (function($){
            $.fn.parallelRoll = function(options){
                var opts = $.extend({}, $.fn.parallelRoll.defaults, options);
                var _this = this;
                var l = _this.find(opts.tagName).length;
                var autoRollTimer;
                var flag = true; // 防止用户快速多次点击上下按钮
                var arr = new Array();
                /**
                 * 如果当  (可视个数+滚动个数 >滚动元素个数)  时  为不出现空白停顿   将滚动元素再赋值一次
                 * 同时赋值以后的滚动元素个数是之前的两倍  2 * l.
                 * */
                if(opts.amount + opts.visual > l){
                    _this[0].innerHTML += _this[0].innerHTML;
                    l = 2 * l;
                }else{
                    l = l;
                }
                var w = $(opts.tagName).outerWidth(true); //计算元素的宽度  包括补白+边框
                _this.css({width: (l * w) + 'px'}); // 设置滚动层盒子的宽度
                return this.each(function(){
                    _this.closest('.'+opts.boxName).hover(function(){
                        clearInterval(autoRollTimer);
                    },function(){
                        switch (opts.direction){
                            case 'left':
//                                autoRollTimer = setInterval(function(){left();},opts.time);
                                autoRollTimer = left();
                                break;
                            case 'right':
//                                autoRollTimer = setInterval(function(){right();},opts.time);
                                autoRollTimer = right();
                                break;
                            default :
                                alert('参数错误！');
                                break;
                        }
                    }).trigger('mouseleave');
                    $('.'+opts.prev).on('click',function(){flag ? left() : "";});
                    $('.'+opts.next).on('click',function(){flag ? right() : "";});
                });
                function left(){
                    flag = false;
                    _this.animate({marginLeft : -(w*opts.amount)},1000,function(){
                        _this.find(opts.tagName).slice(0,opts.amount).appendTo(_this);
                        _this.css({marginLeft:0});
                        flag = true;
                    });
                };
                function right(){
                    flag = false;
                    arr = _this.find(opts.tagName).slice(-opts.amount);
                    for(var i = 0; i<opts.amount; i++){
                        $(arr[i]).css({marginLeft : -w*(i+1)}).prependTo(_this);
                    }
                    _this.animate({marginLeft : w*opts.amount},1000,function(){
                        _this.find(opts.tagName).removeAttr('style');
                        _this.css({marginLeft:0});
                        flag = true;
                    });
                };
            };

            //插件默认选项
            $.fn.parallelRoll.defaults = {
                boxName : 'box',
                tagName : 'dd',
                time : 3000,  //
                direction : 'left', // 滚动方向
                visual : 1 , //可视数
                prev : 'prev',
                next : 'next',
                amount : 1   // 滚动数  默认是1
            };
        })(jQuery);
        $(document).ready(function(){
            $("#roll").parallelRoll({
                amount : 1
            });
        });
    </script>

    <!--防止被网络广告劫持弹出广告-->
    <style type="text/css">
        *{margin: 0;padding: 0;}
        html,body{font: 12px/24px "微软雅黑";}
        .box{
            /*height: 130px;*/
            /*height: 330px;*/
            border: 1px solid #ccc;
            border-radius: 5px;
            overflow: hidden;
            /*width: 730px;*/
            width: 270px;
            margin: 20px auto;
            position: relative;
            padding-bottom:5px;
        }
        #roll{
            width: 200%;
        }
        #roll dd{
            /*width: 98px;*/
            /*height: 98px;*/
            width: 270px;
            /*height: 330px;*/
            /*border: 1px solid #00EE00;*/
            float: left;
            margin-right: 5px;
        }
        .box span {position:absolute; bottom:45%; display:inline-block; width:50px; text-align: center; cursor:pointer; background:#1ab394; z-index:90; color:#fff; border-radius:0 4px 4px 0; padding:3px 5px;}
        .box .next {right:0; border-radius:4px 0 0 4px;}
        .span_img_width {display:block; text-align:left; padding-bottom:.2rem; font-size:.6rem; color:#333;}

        ,h2,h3,h4,h5,h6,form,fieldset,legend,img,input,textarea,section,th,td,hr,button {padding:0; margin:0;}
        body {font-family:"Microsoft Yahei"; font-size:.6rem; color:#333; /*background:url("http://www.ipanelonline.com/images/page_bg.jpg") repeat 0 0;*/ background-color:#CAE1FF; padding:5px 10px;}
        a {text-decoration:none;}
        img {border:0;}
    input, select, textarea {width:100%; border:solid 1px #ccc; background-color:#f9f9f9; border-radius:.15rem; padding:5px; font-size:.6rem; font-weight:500; margin-bottom:.5rem; /*input {-webkit-appearance:none;}*/}
        -webkit-tap-highlight-color:rgba(0,0,0,0);
        table {font-size:.6rem;}
        .logo {width:247px; margin:0px 0 5px 15px;}
        .logo img {width:206px;}
        .box_pic_top {width:100%; background-color:#fff;}
        .box_pic_center {width:100%; background-color:#fff; border-radius:5px;}
        .box_pic_bottom {width:100%; background-color:#fff;}
        .box_tab {margin:5px 20px 0;}
        .layout {width:100%; margin:0 auto; line-height:24px;}
        .layout h1 {font-size:.75rem; font-weight:bold; text-align:center; padding:20px 0; margin:0 20px;}
        .title_font {border-bottom:4px solid #666; line-height:30px;}
        .layout h2 {font-weight:normal; font-size:.6rem; line-height:30px; padding:10px; border:1px dashed #F97C00; border-radius:.25rem; background:#FFFCED; text-align:left; margin:10px 20px 15px 20px;}
        .layout h3 {font-size:.6rem; padding:1rem 0 .2rem; text-align:left; color:#0E4B83; border-bottom:solid 1px #CCC; margin-bottom:5px;}
        .layout h3 span {font-weight:normal; font-size:13px; font-style:italic;}
        .shuoming {margin:0 0 10px 0; padding-bottom:5px;}
        .shuoming p {color:#f00; font-size:.6rem; margin:0 20px;}
        .next_btn {margin:10px 0; padding-top:10px; text-align:center;}
        .btn {min-width:120px; text-align:center; font-size:.65rem; font-weight:400; color:white; border:0; cursor:pointer; background-color:#23c6c8; border-color:#23c6c8; border-radius:.2rem; padding:.3rem 1rem; margin-left:1rem; margin-top:.5rem;}
        .footer {font-weight:300; font-size:.5rem; color:#333; text-align:center; padding:10px 0;}
        .checd_box {display:block; padding:.3rem 0;}
        .checd_box input {vertical-align:middle;}
        .checd_box span {vertical-align:middle; padding-left:.3rem; font-size:.6rem;}
        .danger {background-color:#ed5565; border-color:#ed5565;}
        .success {background-color:#1c84c6; border-color:#1c84c6;}
        .primary {background-color:#1ab394; border-color:#1ab394;}
        .btn_row {width:100%; text-align: center;}
        .btn_row input {margin-bottom:.5rem; margin-left:0; max-width:300px;}
        .btn_bottom {padding-bottom:.5rem;}
        .textarea_border {border:solid 1px #ddd; background-color:#fff0f5; padding:.25rem; margin-top:.5rem; font-size:.6rem; width:100%;}
        .img_box {border:dashed 1px #1ab394; padding:.5rem; min-height:100px; text-align:center; margin-top:.5rem;}
        .img_dashed {width:20%; height:65px; line-height:65px; border:dashed 1px #1ab394; padding:.5rem; text-align:center; margin-top:.5rem; display:inline-block;}
        .img_left {margin-left:.3rem;}
        .img_box span {font-size:.65rem; color:#1ab394; display:block; margin:.2rem 0;}
        .img_dashed strong {font-weight:300; color:#ccc; font-size:.5rem;}
        .fa_plus {font-size:1rem; color:#ccc;}
        .fa_camera {font-style:normal; font-size:.7rem;}
        .i_text {margin-top:.3rem;}
        .qrcode {position:absolute; top:20px; right:20px;}
        .qrcode img {width:60px;}
        .prize_name {padding:5px 5px 5px 10px; text-align:left; overflow:hidden; text-overflow:ellipsis; display:-webkit-box; -webkit-line-clamp:2;}
        .pay {color:#8a6d3b; margin:0 .5rem .5rem; font-size:.6rem; background-color:#fcf8e3; border-radius:.2rem; border:solid 1px #faebcc; padding:.3rem .5rem;}
        .dName {padding:0 .5rem; text-align:center;}
        .proName {text-align:center; color:#fff; font-weight:300; padding:.3rem .5rem; border-radius:.2rem; border:solid 1px #1c84c6; background-color:#1c84c6; margin:0 .5rem .5rem;}
        .aError {margin:.5rem .75rem 0; font-size:.6rem; font-weight:400; color:#333; text-align:left;}
        .btn-primary:hover {background-color:#23c6c8!important;}
        .province select {float:left; width:100%;}
        #city_info, #district_info {width:100%; border-radius:.15rem; font-size:.6rem; font-weight:500;}
    </style>
    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(document.documentElement.getBoundingClientRect().width);
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + 'px';
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
<div class="layout">
        <div class="logo">
            <a href="javascript:;" target="_blank" title="drsay">
                <img src="/theme/admin/im/img/drsay_logo.jpg" title="drsay" alt="drsay" border="0" />
            </a>
            <div class="qrcode"><img src="/theme/admin/im/img/qrcode.jpg" alt="" /></div>
        </div>
        <div class="box_pic_top"></div>
        <div class="box_pic_center">
            <h1 class="title_font"><span class="STYLE1"><?php echo $lang_ary[LABEL_SURVEY_INFO];?></span></h1>
            <form action="/bk/act_get_address" class="form-horizontal" id="item_form" enctype="multipart/form-data"
                  onsubmit="return tqjs.form_sumbit(this,'show_msg','<?php echo $lang_ary[LABEL_SURVEY_IN_PROCESS];?>','','','','<?php echo CONFIRM_SUBMISSION ?>');">
                <div class="alert alert-error aError" style="display:none; margin:.5rem .75rem 0; font-size:.6rem; font-weight:400; color:#333; text-align:left; padding:.3rem .5rem;" id="show_msg_block">
                    <button data-dismiss="alert" class="close"></button>
                    <span id="show_msg"></span>
                </div>
                <table align="center" width="100%">
                    <tr>
                        <td>
                            <div class="box">
                                <span class="prev"><?php echo $lang_ary[LABEL_SURVEY_PRIZES_PREV];?></span>
                                <dl id="roll">
                                    <?php if($get_prizes_info){?>
                                        <?php foreach($get_prizes_info as $v){?>
                                            <dd id="prize_detail_<?php echo $v['id'];?>" onclick="get_prize_info('<?php echo $v['id'];?>');">
                                                <div><img width="270" height="270" src="<?php echo $v['prize_photo'];?>"  /></div>
                                                <div class="prize_name"><?php echo $v['prize_name'];?></div>
                                            </dd>
                                        <?php }?>
                                    <?php }?>
                                </dl>
                                <span class="next"><?php echo $lang_ary[LABEL_SURVEY_PRIZES_NEXT];?></span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="pay">
                                <?php echo $lang_ary[LABEL_SURVEY_EXCHANGE_INFO];?>
                            </div>
                        </td>
                    </tr>
                    <tbody id="prize" style="display: none;">
                        <tr>
                            <td>
                                <div class="proName">
                                    <span><?php echo $lang_ary[LABEL_SURVEY_PRIZES_SELECT];?></span>【<span id="prize_info"></span>】
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tr>
                        <td>
                            <div class="dName">
                                <span class="span_img_width"><?php echo $lang_ary[LABEL_SURVEY_PRIZES_CONSIGNEE];?>：</span><input type="text" name="consignee" id="consignee" />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="dName">
                                <span class="span_img_width"><?php echo $lang_ary[LABEL_AREA];?>：</span>
                                <div>
                                    <div id="province_info" class="province"><?php echo $province_info;?></div>
                                    <div id="city_info" style="float:left;"></div>
                                    <div id="district_info" style="float:left;"></div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="dName">
                                <span class="span_img_width"><?php echo $lang_ary[LABEL_SURVEY_PRIZES_RECEIVING_ADDRESS];?>：</span><input type="text" name="receiving_address" id="receiving_address" />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="dName">
                                <span class="span_img_width"><?php echo $lang_ary[LABEL_PHONE_NUMBER];?>：</span><input type="text" name="mobile" id="mobile" />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="dName">
                                <span class="span_img_width"><?php echo $lang_ary[LABEL_ID_NUMBER];?>：</span><input type="text" name="id_number" id="id_number" />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="center">
                            <input type="hidden" name="prize_id" id="prize_id" value="" />
                            <input type="hidden" name="bk_code" id="bk_code" value="<?php echo $bk_code;?>" />
                            <input type="hidden" name="survey_uid_code" id="survey_uid_code" value="<?php echo $survey_uid_code;?>" />
<!--                            <input id="submit_but" name="submit_but" class="btn btn-primary" type="submit" value="提交" />-->
                            <button id="submit_but" name="submit_but" class="btn btn-primary" style="margin-bottom:1rem;" type="submit" value="<?php echo $lang_ary[LABEL_SUBMIT_INFO];?>" /><?php echo $lang_ary[LABEL_SUBMIT_INFO];?></button>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <div class="box_pic_bottom"></div>
</div>
</div>
<script src="<?= base_url()?>theme/<?= TEMPLATE_DIR?>/assets/js/comm_init.js"></script>
<script src="<?= base_url()?>theme/<?= TEMPLATE_DIR?>/assets/js/jquery.form.js"></script>
<div class="footer" style="clear:both;">
    Copyright &copy; <?php echo date('Y');?> Powered by drsay.cn.
</div>
<script>
    //获取礼品
    function get_prize_info(id) {
        $("#prize_id").val(id);
        var prize_name = $("#prize_detail_"+id+" .prize_name").html();
        $("#prize_info").html(prize_name);
        $("#prize").show();
    }

    //获取城市
    function get_area_info(pid, div_sign)
    {
        if (div_sign == "city") {
            $("#district_info").hide();
        } else {
            $("#district_info").show();
        }
        $.ajax({
            type: "POST",
            url: "/bk/get_area_info",
            data: "pid="+pid+"&div_sign="+div_sign+"&bk_code=<?php echo $bk_code;?>"+"&survey_uid_code=<?php echo $survey_uid_code;?>",
            success: function(msg){

                var json_msg = jQuery.parseJSON( msg );
                if(json_msg.rs_code == "error"){
                    alert(json_msg.rs_msg);
                    return false;
                } else {
                    $("#"+div_sign+"_info").html(json_msg.rs_msg);
                }
            }
        });
    }
</script>
</body>
</html>
