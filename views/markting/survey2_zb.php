<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Faricimab治疗nAMD和DME全球三期临床研究两年数据发布</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .q2_sort {
            border: 1px solid black;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            margin-bottom: 2px;
            margin-right: 2px;
        }

        .float_left {
            float: left;
        }

        .continer {
            max-width: 800px;
            width: 100%;
            font-family: "宋体";
            margin: 0 auto;
        }

        .continer section {
            position: relative;
        }

        .continer section img {
            width: 100%;
            display: block;
        }

        .box .title {
            font-weight: 700;
            text-align: justify;
        }

        .question_border .checkbox div input {
            vertical-align: middle;
        }

        .question_border .checkbox div input[type="text"] {
            border: none;
            outline: none;
            border-bottom: 1px solid #333;
            padding-left: 5px;
            background: transparent;
        }

        /* pc */
        @media screen and (min-width: 750px) {
            .q2_sort {
                width: 22px;
                height: 22px;
                line-height: 22px;
                margin-top: 15px;
                margin-right: 10px;
            }

            .box {
                padding: 20px 30px;
                width: 100%;
                margin: 0 auto;
                background: #bbd6f3;
                color: #000;
            }

            .box .title {
                font-size: 28px;
                margin-bottom: 10px;
                line-height: 38px;
                text-align: justify;
            }

            .question_border .checkbox {
                font-size: 20px;
                padding-left: 40px;
                margin-bottom: 5px;
            }

            .question_border .float_info {
                font-size: 16px;
                float: left;
                cursor: pointer;
            }

            .question_border .word {
                font-size: 26px;
                margin-bottom: 10px;
                width: 90%;
            }

            .question_border .city {
                font-size: 26px;
                margin-bottom: 10px;
            }
        }

        /* 移动端 */
        @media screen and (max-width: 750px) {
            .q2_sort {
                width: 20px;
                height: 20px;
                line-height: 20px;
                margin-top: 8px;
                margin-right: 5px;
            }

            .box {
                padding: 10px 20px;
                width: 100%;
                margin: 0 auto;
                background: #bbd6f3;
                color: #000;
            }

            .box .title {
                font-size: 16px;
                margin-bottom: 10px;
                line-height: 26px;
            }

            .question_border .checkbox {
                font-size: 16px;
                margin-bottom: 5px;
            }

            .question_border .float_info {
                float: left;
                cursor: pointer;
            }

            .question_border .word {
                font-size: 16px;
                margin-bottom: 5px;
                width: 88%;
            }

            .question_border .city {
                font-size: 16px;
                margin-bottom: 5px;
            }
        }

        button.save {
            margin: 20px 0;
            border: none;
            outline: none;
            color: #fff;
            padding: 10px 0;
            background: #0f7dcf;
            width: 50%;
            font-size: 24px;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>

<body>
<form id="form" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
    <div class="continer">
        <!-- 1 -->
        <section>
            <div class="box box1">
                <div class="question_border">
                    <div class="title">
                        请问您接诊的眼底疾病患者包含以下哪些类型？【多选】
                    </div>
                    <div class="checkbox checkbox_two">
                        <div class="city">
                            <label><input class="g_info g_1" type="checkbox" name="edit[screen_out][]" value="A" />
                                老年性黄斑变性
                            </label>
                        </div>
                        <div class="city">
                            <label><input class="g_info g_1" type="checkbox" name="edit[screen_out][]" value="B" />
                                糖尿病视网膜病变
                            </label>
                        </div>
                        <div class="city">
                            <label><input class="g_info g_1" type="checkbox" name="edit[screen_out][]" value="C" />
                                视网膜血管阻塞
                            </label>
                        </div>
                        <div class="city">
                            <label><input class="g_info g_1" type="checkbox" name="edit[screen_out][]" value="D" />
                                葡萄膜炎
                            </label>
                        </div>
                        <div class="city">
                            <label><input class="g_info g_2" type="checkbox" name="edit[screen_out][]" value="E" />
                                以上皆无
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div align="center">
            <input type="hidden" name="s" value="<?php echo $s; ?>" />
            <input type="hidden" name="f" value="<?php echo $f; ?>" />
            <button type="submit" class="save">下一页</button>
<!--            <button type="button" class="save" onclick="jump_url();">提交</button>-->
        </div>
    </div>
</form>
    <script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>

    <script>
        function jump_url() {
            if ($(".g_info").is(":checked") == false) {
                alert("请至少选择一项");
            } else {
                if ($(".g_2").is(":checked") == true) {
                    window.location.href = '<?php echo $zb_link;?>';
                } else {
                    window.location.href = '/markting/s/survey2?s=<?php echo $s;?>&f=<?php echo $f;?>';
                }
            }
        }

        /************************** 表单提交 **************************/
        var common_js = {
            form_sumbit: function(item_form, msg_id) {
                $(item_form).ajaxSubmit({
                    target: "",
                    beforeSubmit: null,
                    success: common_js.show_response(msg_id),
                    url: "/markting/s/survey2_zb",
                    type: "post",
                    dataType: "text",
                    clearForm: false,
                    resetForm: false,
                    cache: false,
                });
                return false;
            },
            //接受PHP返回信息并重绘提示框
            show_response: function(msg_id) {
                return function(str) {
                    var res = jQuery.parseJSON(str);
                    if (res.rs_code == "error") {
                        alert(res.rs_msg);
                    }

                    if (res.rs_backurl != "" && res.rs_backurl != undefined) {
                        window.location.href = res.rs_backurl;
                    }
                };
            },
            //版本号
            tver: "1.0.1",
        };
        common_js;
        /************************** 表单提交end **************************/

        $(".g_2").click(function() {
            if ($(this).is(":checked") == true) {
                $(".g_1").prop("checked", false);
            }
        });
        $(".g_1").click(function() {
            if ($(this).is(":checked") == true) {
                $(".g_2").prop("checked", false);
            }
        });
    </script>
</body>

</html>