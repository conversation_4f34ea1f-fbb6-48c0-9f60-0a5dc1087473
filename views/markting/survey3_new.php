<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>南欣苏睿生物技术（成都）有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
        }


        .container {
            max-width: 800px;
            margin: 0 auto;
            min-height: 100%;
        }

        .index,
        .last {
            position: relative;
            max-width: 800px;
        }

        .index img,
        .last img {
            width: 100%;
            vertical-align: middle;
        }

        .index .save {
            position: absolute;
            left: 50%;
            bottom: 10%;
            transform: translateX(-50%);
        }

        .last .save {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        @media screen and (min-width:750px) {
            .last .save {
                /*bottom: 55px;*/
                bottom: 46px;/*第二期*/
            }
        }

        @media screen and (max-width:750px) {
            .last .save {
                /*bottom: 0.6%;*/
                bottom: 0.9%;/*第二期*/
            }
        }

        .subject {
            padding: 30px 20px;
            background: #faf6dc;
            min-height: 100%;
        }

        .subject .box {
            padding: 30px 20px 50px;
            background: #fff;
            box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
            margin-bottom: 20px;
        }

        .subject .box .title {
            font-size: 18px;
            margin-bottom: 10px;
        }

        .subject .box .title sup {
            color: red;
        }

        .subject .box .item {
            height: 40px;
            line-height: 40px;
            font-size: 16px;
        }

        .subject .box .item:not(:last-child) {
            border-bottom: 1px solid #eff1f1;
        }

        .subject .box .item label {
            display: block;
        }

        .subject .box .item input[type="radio"] {
            width: 18px;
            height: 18px;
            vertical-align: middle;
        }

        .subject .box textarea {
            font-size: 16px;
            width: 100%;
            height: 150px;
            background: #f1f1f0;
            border: none;
            outline: none;
            border-radius: 10px;
            resize: none;
            padding: 10px 20px;
        }

        .save {
            background: #fa8216;
            color: #fff;
            width: 81%;
            font-size: 22px;
            /*height: 50px;*/
            /*line-height: 50px;*/
            height: 69px;/*第二期*/
            line-height: 69px;/*第二期*/
            text-align: center;
            margin: 0 auto;
            margin-top: 50px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            box-shadow: 1px 1px 1px 1px #ccc;
        }

        @media screen and (max-width:750px) {
            .save {
                background: #fa8216;
                color: #fff;
                width: 81%;
                font-size: 22px;
                /*height: 50px;*/
                /*line-height: 50px;*/
                height: 40px;/*第二期*/
                line-height: 40px;/*第二期*/
                text-align: center;
                margin: 0 auto;
                margin-top: 50px;
                border-radius: 5px;
                cursor: pointer;
                border: none;
                box-shadow: 1px 1px 1px 1px #ccc;
            }
        }
    </style>
</head>

<body>
    <div class="container" style="position:relative;">
        <div <?php if ($page == 1) { ?>class="page_1" <?php } ?>>
            <form id="form" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
                <?php if ($page == 1) { ?>
                    <div class="index">
                        <img src="/theme/markting/7823/bg.jpg" alt="" />
                        <button type="submit" class="save">下一步</button>
                    </div>
                <?php } ?>


                <?php if ($page == 2) { ?>
                    <div class="subject">
                        <img style="width: 100%;" src="/theme/markting/7823/top.jpg" alt="">
                        <div class="box1 box">
                            <div class="title">
                                <span>01<sup>*</sup></span>
                                您是否计划（或正在）开展基础医学科研项目？
                            </div>

                            <div class="content">
                                <div class="item">
                                    <label>
                                        <input name="edit[q1]" type="radio" name="edit[q1]" value="A" />
                                        是
                                    </label>
                                </div>
                                <div class="item">
                                    <label>
                                        <input name="edit[q1]" type="radio" name="edit[q1]" value="B" />
                                        否
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="box2 box">
                            <div class="title">
                                <span>02</span>
                                您的研究方向或需要的支持？（选填)
                            </div>

                            <div class="content">
                                <textarea name="edit[q2]" placeholder="简单描述"></textarea>
                            </div>
                        </div>

                        <div class="box3 box">
                            <div class="title">
                                <span>03<sup>*</sup></span>
                                您是否需要申领小睿助手科研互助金？
                            </div>

                            <div class="content">
                                <div class="item">
                                    <label>
                                        <input type="radio" name="edit[q3]" value="A" />
                                        是
                                    </label>
                                </div>
                                <div class="item">
                                    <label>
                                        <input type="radio" name="edit[q3]" value="B" />
                                        否
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div align="center">
                            <button type="submit" class="save">
                                下一步
                            </button>
                        </div>
                    </div>

                <?php } ?>

                <?php if ($page == 3) { ?>
                    <div class="last">
                        <img src="/theme/markting/7823/3bg_n.jpg" alt="" />
                        <button type="submit" class="save">提交</button>
                    </div>

                <?php } ?>

                <input type="hidden" name="last_click_id" value="<?php echo $last_click_id; ?>" />
                <input type="hidden" name="page" value="<?php echo $page; ?>" />
                <input type="hidden" name="s" value="<?php echo $s; ?>" />
            </form>
        </div>
    </div>

    <script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <script>
        var last_click_id = <?php echo $last_click_id; ?>;
        setInterval(function() {
            $.ajax({
                type: 'post',
                url: '/markting/s/act3',
                data: {
                    last_click_id: last_click_id
                },
                success: function(res) {}
            })
        }, 5000)

        /************************** 表单提交 **************************/
        var common_js = {
            form_sumbit: function(item_form, msg_id) {
                // if (confirm("确认提交吗？")) {
                //
                // }
                $(item_form).ajaxSubmit({
                    target: "",
                    beforeSubmit: null,
                    success: common_js.show_response(msg_id),
                    url: "/markting/s/survey3",
                    type: "post",
                    dataType: "text",
                    clearForm: false,
                    resetForm: false,
                    cache: false,
                });
                return false;
            },
            //接受PHP返回信息并重绘提示框
            show_response: function(msg_id) {
                return function(str) {
                    var res = jQuery.parseJSON(str);
                    if (res.rs_code == "error") {
                        alert(res.rs_msg);
                        return false;
                    } else {
                        if (res.rs_backurl != "" && res.rs_backurl != undefined) {
                            window.location.href = res.rs_backurl;
                        }
                    }
                };
            },
            //版本号
            tver: "1.0.1",
        };
        common_js;
        /************************** 表单提交end **************************/

        // 点击切换城市
        $(".city input").click(function() {
            var name = $(this).data("name");
            $(".telphone_info ." + name)
                .show()
                .siblings()
                .hide();
        });

        // 量表题
        $(".scale_questions div").click(function() {
            $(this).addClass("active").siblings().removeClass("active");
            $(".scale_questions input").val($(this).text());
        });

        // 点击排序效果
        var sort = 0;
        $(".sort").click(function() {
            if ($(this).hasClass("sorted")) {
                return false;
            }
            $(this).addClass("sorted").siblings().removeClass("sorted");
            sort++;
            if (sort % 4 == 1) {
                sort = 1;
            }
            if (sort == 1) {
                $(this).siblings().children(".sort_item").text("");
            }
            $(this).children(".sort_item").text(sort);

            if (sort == 4) {
                $(this).removeClass("sorted");
                let arr = [];
                $(".sort")
                    .children(".sort_item")
                    .each(function() {
                        arr.push($(this).text());
                    });
                $("input[name='edit[q2]']").val(arr);
            }
        });


        // // 多选题最多选两项
        // $("input[name = 'edit[q1][]']").click(function() {
        //     let that = $(this);
        //     var count = 0;
        //     var arr = [];
        //     $("input[name = 'edit[q1][]']").each(function() {
        //         if ($(this).is(":checked") == true) {
        //             arr.push($(this));
        //             count++;
        //         }
        //     });
        //     if (count > 2) {
        //         alert("请选择2个选项");
        //         that.prop("checked", false);
        //     }
        // });
    </script>
</body>

</html>