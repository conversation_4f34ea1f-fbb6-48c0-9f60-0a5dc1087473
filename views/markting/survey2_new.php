<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Faricimab治疗nAMD和DME全球三期临床研究两年数据发布-知情同意书</title>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .q2_sort {
        border: 1px solid black;
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        margin-bottom: 2px;
        margin-right: 2px;
    }

    .float_left {
        float: left;
    }

    .continer {
        max-width: 800px;
        width: 100%;
        font-family: "宋体";
        margin: 0 auto;
    }

    .continer section {
        position: relative;
    }

    .continer section img {
        width: 100%;
        display: block;
    }

    .box .title {
        font-weight: 700;
        text-align: justify;
    }

    .question_border .checkbox div input {
        vertical-align: middle;
    }

    .question_border .checkbox div input[type="text"] {
        border: none;
        outline: none;
        border-bottom: 1px solid #333;
        padding-left: 5px;
        background: transparent;
    }

    /* pc */
    @media screen and (min-width: 750px) {
        .q2_sort {
            width: 22px;
            height: 22px;
            line-height: 22px;
            margin-top: 15px;
            margin-right: 10px;
        }

        .box {
            padding: 20px 30px;
            width: 100%;
            margin: 0 auto;
            background: #bbd6f3;
            color: #000;
        }

        .box .title {
            font-size: 28px;
            margin-bottom: 10px;
            line-height: 38px;
            text-align: justify;
        }

        .question_border .checkbox {
            font-size: 20px;
            padding-left: 40px;
            margin-bottom: 5px;
        }

        .question_border .float_info {
            font-size: 16px;
            float: left;
            cursor: pointer;
        }

        .question_border .word {
            font-size: 26px;
            margin-bottom: 10px;
            width: 90%;
        }

        .question_border .city {
            font-size: 26px;
            margin-bottom: 10px;
        }
    }

    /* 移动端 */
    @media screen and (max-width: 750px) {
        .q2_sort {
            width: 20px;
            height: 20px;
            line-height: 20px;
            margin-top: 8px;
            margin-right: 5px;
        }

        .box {
            padding: 10px 20px;
            width: 100%;
            margin: 0 auto;
            background: #bbd6f3;
            color: #000;
        }

        .box .title {
            font-size: 16px;
            margin-bottom: 10px;
            line-height: 26px;
        }

        .question_border .checkbox {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .question_border .float_info {
            float: left;
            cursor: pointer;
        }

        .question_border .word {
            font-size: 16px;
            margin-bottom: 5px;
            width: 88%;
        }

        .question_border .city {
            font-size: 16px;
            margin-bottom: 5px;
        }
    }

    button.save {
        margin: 20px 0;
        border: none;
        outline: none;
        color: #fff;
        padding: 10px 0;
        background: #0f7dcf;
        width: 50%;
        font-size: 24px;
        border-radius: 5px;
        cursor: pointer;
    }
</style>

<body>
    <form id="form" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
        <div class="continer">
            <!-- 1 -->
            <section>
                <img src="/theme/markting/7778/1.jpg?<?php echo time(); ?>" alt="" />
                <div class="box box1">
                    <div class="question_border">
                        <div class="title">
                            问题：真实世界Anti-VEGF治疗中，您最关注的是？（选2项）
                        </div>
                        <div class="checkbox checkbox_two">
                            <div class="city">
                                <label><input type="checkbox" name="edit[q1][]" value="A" />
                                    A、快速视力改善
                                </label>
                            </div>
                            <div class="city">
                                <label><input type="checkbox" name="edit[q1][]" value="B" />
                                    B、快速解剖结构改善（CST改善和积液消退）
                                </label>
                            </div>
                            <div class="city">
                                <label><input type="checkbox" name="edit[q1][]" value="C" />
                                    C、长期视力稳定
                                </label>
                            </div>
                            <div class="city">
                                <label><input type="checkbox" name="edit[q1][]" value="D" />
                                    D、更长的治疗间隔与更低的治疗负担
                                </label>
                            </div>
                            <div class="city">
                                <label><input type="checkbox" name="edit[q1][]" value="E" />
                                    E、其他，请补充<input class="q1_text" type="text" name="edit[q1_other]" />
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 2 -->
            <section>
                <img src="/theme/markting/7778/2.jpg?<?php echo time(); ?>" alt="" />
                <div class="box box2">
                    <div class="question_border">
                        <input type="hidden" name="edit[q2]" />
                        <div class="title">
                            问题：在Faricimab的DME全球三期临床试验YOSEMITE/RHINE
                            2年最新结果中，对您最具吸引力的疗效数据是？（对照组为阿柏西普Q8W给药）（排序，1为最具吸引力）
                        </div>
                        <div style="overflow: hidden">
                            <div class="float_info sort">
                                <div class="float_left q2_sort sort_item"></div>
                                <div class="float_left word">
                                    约60%的患者达到4个月的治疗间隔，近80%的患者达到至少3个月的治疗间隔
                                </div>
                            </div>

                            <div class="float_info sort">
                                <div class="float_left q2_sort sort_item"></div>
                                <div class="float_left word">
                                    Faricimab组CST的降低幅度更大，优势持续至第2年
                                </div>
                            </div>

                            <div class="float_info sort">
                                <div class="float_left q2_sort sort_item"></div>
                                <div class="float_left word">
                                    Faricimab组DME消退的患者比例更高，第二年可达80%以上
                                </div>
                            </div>

                            <div class="float_info sort">
                                <div class="float_left q2_sort sort_item"></div>
                                <div class="float_left word">
                                    Faricimab组无IRF患者比例更高，第二年超过50%的患者达到IRF的完全消退
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 3 -->
            <section>
                <img src="/theme/markting/7778/3.jpg?<?php echo time(); ?>" alt="" />
                <div class="box box3">
                    <div class="question_border">
                        <div class="title">
                            问题：
                            Faricimab作为全球首个用于眼内注射的抗Ang-2/VEGF-A双特异性抗体，
                            研究表明，抑制Ang-2可以显著增加血管稳定性，您会关注Faricimab带来的哪些患者获益？（不定项选择）
                        </div>

                        <div class="checkbox" style="padding-left:0">
                            <div class="city">
                                <label><input class="g_1" type="checkbox" name="edit[q3][]" value="A" />
                                    不了解
                                </label>
                            </div>
                            <div class="city">
                                <label><input class="g_2" type="checkbox" name="edit[q3][]" value="B" />
                                    减少炎症
                                </label>
                            </div>
                            <div class="city">
                                <label><input class="g_2" type="checkbox" name="edit[q3][]" value="C" />
                                    减少纤维化
                                </label>
                            </div>
                            <div class="city">
                                <label><input class="g_2" type="checkbox" name="edit[q3][]" value="D" />
                                    减少渗漏
                                </label>
                            </div>
                            <div class="city">
                                <label><input class="g_2" type="checkbox" name="edit[q3][]" value="E" />
                                    减少新生血管生成
                                </label>
                            </div>
                            <div class="city">
                                <label><input class="g_2" type="checkbox" name="edit[q3][]" value="F" />
                                    减少细胞损伤
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section><img src="/theme/markting/7778/4.jpg?<?php echo time(); ?>" alt="" /></section>

            <div align="center">
                <input type="hidden" name="last_click_id" value="<?php echo $last_click_id; ?>" />
                <button type="submit" class="save">提交</button>
            </div>
        </div>
    </form>

    <script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <script>
        var last_click_id = <?php echo $last_click_id; ?>;
        setInterval(function() {
            $.ajax({
                type: 'post',
                url: '/markting/s/act2',
                data: {
                    last_click_id: last_click_id
                },
                success: function(res) {}
            })
        }, 5000)

        /************************** 表单提交 **************************/
        var common_js = {
            form_sumbit: function(item_form, msg_id) {
                if (confirm("确认提交吗？")) {
                    $(item_form).ajaxSubmit({
                        target: "",
                        beforeSubmit: null,
                        success: common_js.show_response(msg_id),
                        url: "/markting/s/survey2",
                        type: "post",
                        dataType: "text",
                        clearForm: false,
                        resetForm: false,
                        cache: false,
                    });
                    return false;
                }
            },
            //接受PHP返回信息并重绘提示框
            show_response: function(msg_id) {
                return function(str) {
                    var res = jQuery.parseJSON(str);
                    alert(res.rs_msg);
                    if (res.rs_backurl != "" && res.rs_backurl != undefined) {
                        window.location.href = res.rs_backurl;
                    }
                };
            },
            //版本号
            tver: "1.0.1",
        };
        common_js;
        /************************** 表单提交end **************************/

        // 点击切换城市
        $(".city input").click(function() {
            var name = $(this).data("name");
            $(".telphone_info ." + name)
                .show()
                .siblings()
                .hide();
        });

        // 量表题
        $(".scale_questions div").click(function() {
            $(this).addClass("active").siblings().removeClass("active");
            $(".scale_questions input").val($(this).text());
        });

        // 点击排序效果
        var sort = 0;
        $(".sort").click(function() {
            if ($(this).hasClass("sorted")) {
                return false;
            }
            $(this).addClass("sorted").siblings().removeClass("sorted");
            sort++;
            if (sort % 4 == 1) {
                sort = 1;
                $("input[name='edit[q2]']").val("")
            }
            if (sort == 1) {
                $(this).siblings().children(".sort_item").text("");
            }
            $(this).children(".sort_item").text(sort);

            if (sort == 4) {
                $(this).removeClass("sorted");
                let arr = [];
                $(".sort")
                    .children(".sort_item")
                    .each(function() {
                        arr.push($(this).text());
                    });
                $("input[name='edit[q2]']").val(arr);
            }
        });


        // 多选题最多选两项
        $("input[name = 'edit[q1][]']").click(function() {
            let that = $(this);
            var count = 0;
            var arr = [];
            $("input[name = 'edit[q1][]']").each(function() {
                if ($(this).is(":checked") == true) {
                    arr.push($(this));
                    count++;
                }
            });
            if (count > 2) {
                alert("请选择2个选项");
                that.prop("checked", false);
            }
        });

        $(".g_2").click(function() {
            if ($(this).is(":checked") == true) {
                $(".g_1").prop("checked", false);
            }
        });
        $(".g_1").click(function() {
            if ($(this).is(":checked") == true) {
                $(".g_2").prop("checked", false);
            }
        });
    </script>
</body>

</html>