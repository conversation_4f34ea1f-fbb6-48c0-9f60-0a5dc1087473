<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>learning</title>
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .container {
            margin: 0;
            padding: 0;
            min-height: 100%;
            position: relative;
            text-align: center;
        }

        img {
            margin: 0;
            padding: 0;
            width: 100%;
            max-width: 480px;
        }
    </style>
</head>

<body>
    <div class="container">
        <img src="/theme/markting/learning.jpg" alt="" />
    </div>

    <script src="/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
    <script>
        var last_click_id = <?php echo $last_click_id; ?>
        // 页面刷新、关闭时触发onbeforeunload事件把停留时间记录到localStorage
        // window.onbeforeunload = function() {
        //     $.ajax({
        //         type: 'post',
        //         url: '/markting/s/act',
        //         data: {
        //             last_click_id: last_click_id
        //         },
        //         success: function(res) {}
        //     })
        // };

        setInterval(function() {
            $.ajax({
                type: 'post',
                url: '/markting/s/act',
                data: {
                    last_click_id: last_click_id
                },
                success: function(res) {}
            })
        }, 5000)
    </script>
</body>

</html>