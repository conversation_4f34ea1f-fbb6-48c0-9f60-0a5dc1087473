<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>南欣苏睿生物技术（成都）有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-size: 12px;
            line-height: 20px;
        }

        body,
        html {
            margin: 0;
            padding: 0;
            height: 100%;
            text-align: justify;
        }

        @media screen and (min-width: 750px) {
            .container {
                /*margin: 0;*/
                /*padding: 0;*/
                /*min-height: 100%;*/
                /*position: relative;*/
                /*text-align: center;*/

                width: 800px;
                height: 100%;
                margin: 0 auto;
                font-size: 14px;
                /*line-height: 24px;*/
                padding: 30px 10px 10px;
                background: #f5f5f5;
            }
        }

        @media screen and (max-width: 750px) {
            .container {
                /*margin: 0;*/
                /*padding: 0;*/
                /*min-height: 100%;*/
                /*position: relative;*/
                /*text-align: center;*/

                width: 100%;
                height: 100%;
                margin: 0 auto;
                font-size: 14px;
                /*line-height: 24px;*/
                padding: 30px 10px 10px;
                background: #f5f5f5;
            }
        }

        .img {
            margin: 0;
            padding: 0;
            width: 100%;
            /*max-width: 480px;*/
        }

        .first {
            /*padding: 5px 10px;*/
            border: 1px solid #000;
            float: left;
            margin-right: 5px;
            width: 30px;
            height: 30px;
            text-align: center;
            line-height: 30px;
        }

        .active {
            border: 1px solid blue;
            background-color: blue;
            color: #fff;
        }

        button.save {
            margin: 20px 0;
            border: none;
            outline: none;
            color: #fff;
            padding: 10px 0;
            background: #53a4f4;
            width: 50%;
            font-size: 24px;
            border-radius: 5px;
        }

        .telphone_info div {
            display: none;
            font-weight: 600;
        }

        .scale_questions div {
            cursor: pointer;
        }

        .intro {
            font-weight: 600;
            font-style: italic;
            padding: 10px;
            background: #d6e6f5;
            border-radius: 10px;
            margin: 10px;
            text-align: center;
        }


        .float_left {
            float: left;
        }

        .float_right {
            float: right;
        }

        .float_info::after {
            content: "";
            display: block;
            height: 0;
            visibility: hidden;
            clear: both;
        }

        .title_color {
            color: #3f75b1;
            font-weight: bold;
        }

        table {
            border-collapse: collapse;
        }

        table,
        td,
        th {
            border: 1px solid black;
        }

        #lineTd {
            width: 100px;
            background: #f5f5f5 url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxsaW5lIHgxPSIwIiB5MT0iMCIgeDI9IjEwMCUiIHkyPSIxMDAlIiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjEiLz48L3N2Zz4=) no-repeat 100% center;
        }

        .q2_sort {
            border: 1px solid black;
            width: 20px;
            height: 20px;
            line-height:20px;
            text-align: center;
            margin-bottom: 2px;
            margin-right: 2px;
        }

        .sort {
            cursor: pointer;
        }

        .superior{
            vertical-align: text-top;
            font-size: 12px;
            /*color: red;*/
        }

        .question_border{
            /*border: 1px solid #4a85c1;*/
            /*padding: 10px;*/
            /*border-radius: 4px;*/

            background-color:#bbd6f3;
            color:#000;
            border-radius: 10px;
            padding:10px;
        }

        .q2_text{
            margin-top:10px;
            width:100%;
            border: none;
            outline: none;
            border-bottom: 1px solid #333;
            padding-left: 5px;
            background: transparent;
        }

        .sign_info{
            background-color: black;
            width:5px;
            height:5px;
            display: inline-block;
            vertical-align: middle;
            margin-right:10px;
        }

        .page_1{
            position:absolute;left:50%;top:50%;transform: translate(-50%, -50%);
        }
        .page_2_title{
            font-size:16px;
            font-weight: bold
        }
        .city{
            line-height:28px;
        }
        .city label{
            font-size:14px;
        }

    </style>
</head>

<body>
    <div class="container" style="position:relative;">
        <div <?php if($page == 1){?>class="page_1"<?php }?>>
            <form id="form" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
            <?php if($page == 1){?>
            <div align="center" style="font-weight:bold; font-size:14px;">南欣苏睿欢迎您的到来，让我们开启基础医学科研互助旅程</div>
            <br />

            <div align="center">让我们彼此了解一下吧！</div>
            <br />
            <?php }?>

            <?php if($page == 2){?>
            <div class="box box1">
                <div class="question_border">
                    <div class="page_2_title">Q1：您是否计划（或正在）开展基础医学科研项目？</div>
                    <div>
                        <div class="city">
                            <label><input type="radio" name="edit[q1]" value="A" />
                                是
                            </label>
                        </div>
                        <div class="city">
                            <label><input type="radio" name="edit[q1]" value="B" />
                                否
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <br />

            <div class="question_border">
                <div class="page_2_title">Q2：您的研究方向或需要的支持？（选填）</div>
                <div>
                    <div><input class="q2_text" type="text" name="edit[q2]" /></div>
                </div>
            </div>
            <br />

            <div class="question_border">
                <div class="page_2_title">Q3：您是否需要申领小睿助手科研互助金？</div>
                <div>
                    <div class="city">
                        <label><input type="radio" name="edit[q3]" value="A" />
                            是
                        </label>
                    </div>
                    <div class="city">
                        <label><input type="radio" name="edit[q3]" value="B" />
                            否
                        </label>
                    </div>
                </div>
            </div>
            <br />

            <?php }?>

            <?php if($page == 3){?>
            <div>接下来，是小睿的自我介绍时间。</div>
            <br />

            <div style="text-indent:24px;">南欣苏睿生物技术（成都）有限公司，坐落于成都天府生命科技园，总部位于南京，属“国家高新技术企业”，与南京南欣医药技术研究院战略合作。自2014年成立以来，我们一直致力于基础医学研究领域的探索与服务。依托研究院强大科研技术平台和公司卓越运营创新能力，服务单位遍布全国30多个省、市、自治区，与众多科研院所和临床科室进行项目服务合作。</div>
            <br />
            <div style="text-indent:24px;">公司在南京、成都建有3个基础医学研究共享实验平台；拥有理论计算、人工智能、大数据分析及绿色合成等国家级人才团队；自主研发UDPS实验室数据单项处理系统，集数据采集、处理、加密储存、校验、交付为一体的全流程数据管理。实现数据溯源、准确、安全，保障科研成果质量及诚信。</div>
            <br />

            <div align="center"><img class="img" src="/theme/markting/7823/1.jpg" /></div>
            <br />

            <div style="text-indent:24px;">我们主要从事机理机制研究、药效学研究、纳米材料研究、蛋白组学研究、代谢组学研究、生物信息研究、组织工程研究、肠道菌群研究、META分析研究等全流程科研服务。</div>
            <br />

            <div align="center"><img class="img" src="/theme/markting/7823/2.jpg" /></div>
            <br />

            <div style="text-indent:24px;">小睿诚邀您参与基础医学科研小助手服务活动。我们愿以技术和服务改善您基础医学研究品质，使您的工作、科研和生活更加从容！</div>
            <br />

            <div style="text-indent:24px;">更多信息请您关注南欣苏睿公众号，我们期待与您的进一步交流！</div>
            <br />

            <div align="center"><img src="/theme/markting/7823/3.png" /></div>
            <br />

            <div style="text-indent:24px;">技术服务热线：4000878860 / 13376058080 (微信同号)</div>
            <br />

            <div style="text-indent:24px;">共享平台地址：成都天府生命科技园（成都市高新区科园南路88号）C1栋401</div>
            <br />

            <div style="text-indent:24px;">江苏生命科技创新园（南京市栖霞区玮地路9号）C6栋7层</div>
            <br />
            <?php }?>

            <div align="center"><button type="submit" class="save"><?php if($page != 3){echo "下一步";} else {echo "提交";}?></button></div>
            <input type="hidden" name="last_click_id" value="<?php echo $last_click_id; ?>" />
            <input type="hidden" name="page" value="<?php echo $page; ?>" />
            <input type="hidden" name="s" value="<?php echo $s; ?>" />
        </form>
        </div>
    </div>

    <script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <script>
        var last_click_id = <?php echo $last_click_id; ?>;
        setInterval(function() {
            $.ajax({
                type: 'post',
                url: '/markting/s/act3',
                data: {
                    last_click_id: last_click_id
                },
                success: function(res) {}
            })
        }, 5000)

        /************************** 表单提交 **************************/
        var common_js = {
            form_sumbit: function(item_form, msg_id) {
                // if (confirm("确认提交吗？")) {
                //
                // }
                $(item_form).ajaxSubmit({
                    target: "",
                    beforeSubmit: null,
                    success: common_js.show_response(msg_id),
                    url: "/markting/s/survey3",
                    type: "post",
                    dataType: "text",
                    clearForm: false,
                    resetForm: false,
                    cache: false,
                });
                return false;
            },
            //接受PHP返回信息并重绘提示框
            show_response: function(msg_id) {
                return function(str) {
                    var res = jQuery.parseJSON(str);
                    if (res.rs_code == "error") {
                        alert(res.rs_msg);
                        return false;
                    } else {
                        if (res.rs_backurl != "" && res.rs_backurl != undefined) {
                            window.location.href = res.rs_backurl;
                        }
                    }
                };
            },
            //版本号
            tver: "1.0.1",
        };
        common_js;
        /************************** 表单提交end **************************/

        // 点击切换城市
        $(".city input").click(function() {
            var name = $(this).data("name");
            $(".telphone_info ." + name)
                .show()
                .siblings()
                .hide();
        });

        // 量表题
        $(".scale_questions div").click(function() {
            $(this).addClass("active").siblings().removeClass("active");
            $(".scale_questions input").val($(this).text());
        });

        // 点击排序效果
        var sort = 0;
        $(".sort").click(function() {
            if ($(this).hasClass("sorted")) {
                return false;
            }
            $(this).addClass("sorted").siblings().removeClass("sorted");
            sort++;
            if (sort % 4 == 1) {
                sort = 1;
            }
            if (sort == 1) {
                $(this).siblings().children(".sort_item").text("");
            }
            $(this).children(".sort_item").text(sort);

            if (sort == 4) {
                $(this).removeClass("sorted");
                let arr = [];
                $(".sort")
                    .children(".sort_item")
                    .each(function() {
                        arr.push($(this).text());
                    });
                $("input[name='edit[q2]']").val(arr);
            }
        });


        // // 多选题最多选两项
        // $("input[name = 'edit[q1][]']").click(function() {
        //     let that = $(this);
        //     var count = 0;
        //     var arr = [];
        //     $("input[name = 'edit[q1][]']").each(function() {
        //         if ($(this).is(":checked") == true) {
        //             arr.push($(this));
        //             count++;
        //         }
        //     });
        //     if (count > 2) {
        //         alert("请选择2个选项");
        //         that.prop("checked", false);
        //     }
        // });
    </script>
</body>

</html>