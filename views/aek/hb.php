<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui"
    />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>首页</title>
    <link href="http://www.drsay.cn/theme/aek/css/index_mobile.css" rel="stylesheet" />
    <script src="http://www.drsay.cn/theme/aek/js/rem.js"></script>
    <script src="http://www.drsay.cn/theme/aek/js/jquery.min.js"></script>
    <link rel="stylesheet" href="http://www.drsay.cn/theme/aek/css/viewer.css" />
    <script src="http://www.drsay.cn/theme/aek/js/viewer.js"></script>
  </head>
  <body class="index_wrap_body">
    <div class="index_wrap_box">
      <div class="div_one_cont docs-pictures">
        <img src="http://www.drsay.cn/theme/aek/img/hb.jpg" />
      </div>
      <div class="index_wrap_tit">如果您有任何问题可联系爱尔康工作人员</div>
      <div class="index_cont_one">
        <h4><span>辽宁</span></h4>
        <p>蔡玲：139-9854-9227</p>
        <h4>
          <span>济南</span
          >，<span>淄博</span>，<span>滨州</span>，<span>东营</span>，<span
            >聊城</span
          >
        </h4>
        <p>王磊：186-6966-9090</p>
        <h4>
          <span>济南</span
          >，<span>临沂</span>，<span>泰安</span>，济宁，菏泽，枣庄，德州
        </h4>
        <p>邵明强：133-7569-8181</p>
        <h4>
          <span>青岛</span
          >，<span>烟台</span>，<span>威海</span>，<span>潍坊</span>，<span
            >日照</span
          >
        </h4>
        <p>刘君：186-5325-6097</p>
      </div>
      <div class="index_cont_two">
        <h4>您是否期望“爱尔康”工作人员对您进行线下讲解人工晶体内容？</h4>
        <input
          type="button"
          id="0"
          name="choose"
          value="非常"
          class="select_btn"
        />
        <input
          type="button"
          id="1"
          name="choose"
          value="一般"
          class="select_btn"
        />
        <!-- <input type="button" value="是" class="select_btn">
            <input type="button" value="否" class="select_btn"> -->
        <div class="btn_sure_div">
          <input type="submit" value="确认" class="btn_sure" id="tijiao" />
        </div>
      </div>
    </div>
    <script>
      var dianji;
      $(".select_btn").click(function () {
        $(this)
          .addClass("checked_class")
          .siblings()
          .removeClass("checked_class");
      });
      $(function () {
        // 图片放大
        var console = window.console || { log: function () {} };
        var $images = $(".docs-pictures");
        var $toggles = $(".docs-toggles");
        var $buttons = $(".docs-buttons");
        var options = {
          // inline: true,
          url: "data-original",
        };

        function toggleButtons(mode) {
          if (/modal|inline|none/.test(mode)) {
            $buttons
              .find("button[data-enable]")
              .prop("disabled", true)
              .filter('[data-enable*="' + mode + '"]')
              .prop("disabled", false);
          }
        }

        $images.on({}).viewer(options);

        toggleButtons(options.inline ? "inline" : "modal");

        $toggles.on("change", "input", function () {
          var $input = $(this);
          var name = $input.attr("name");

          options[name] =
            name === "inline" ? $input.data("value") : $input.prop("checked");
          $images.viewer("destroy").viewer(options);
          toggleButtons(options.inline ? "inline" : "modal");
        });

        $buttons.on("click", "button", function () {
          var data = $(this).data();
          var args = data.arguments || [];

          if (data.method) {
            if (data.target) {
              $images.viewer(data.method, $(data.target).val());
            } else {
              $images.viewer(data.method, args[0], args[1]);
            }

            switch (data.method) {
              case "scaleX":
              case "scaleY":
                args[0] = -args[0];
                break;

              case "destroy":
                toggleButtons("none");
                break;
            }
          }
        });
        $(".docs-pictures").click(function () {
          dianji = 1;
        });
      });
      var id = "3002";
      var qid = "1";
      $("#tijiao").click(function () {
        var answer = $(".checked_class").val();
        if (!answer) {
          alert("请选择后确认！");
          return false;
        }
        $.ajax({
          //   url: "/short_message/ShortMessage/tijiao",
          type: "POST",
          dataType: "json",
          data: {
            image_click: dianji,
            answer: answer,
            id: id,
            qid: qid,
          },
          success: function (res) {
            if (res.code == "success") {
              alert(res.msg);
              // window.location.href = "https://admin.eclin.cn/short_message/"+res.data;
            } else {
              alert(res.msg);
            }
          },
        });
      });
    </script>
  </body>
</html>
