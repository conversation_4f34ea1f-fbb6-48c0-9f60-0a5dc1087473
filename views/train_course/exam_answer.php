<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title><?php echo $title; ?></title>
    <script type="text/javascript">
      var json_question = <?php echo $question; ?>;
      var answer = <?php echo $answer_detail;?>;
      console.log(answer);
      var info = json_question.list;
      document.write(
        "<link rel='stylesheet' type='text/css' href='/theme/exam/css/common.css?v=" +
          Date.now() +
          "' />"
      );
      document.write(
        "<link rel='stylesheet' type='text/css' href='/theme/exam/css/media.css?v=" +
          Date.now() +
          "' />"
      );
      document.write(
        "<link rel='stylesheet' type='text/css' href='/theme/exam/css/analysis.css?v=" +
          Date.now() +
          "' />"
      );
    </script>
  </head>
  <body
    oncontextmenu="return false"
    onselectstart="return false"
    oncopy="return false"
  >
    <div class="container">
      <div class="header hd">
        <!-- 挖个坑 -->
      </div>
      <div class="content">
        <div id="main">
          <!-- 挖个坑 -->
        </div>
        <div class="neirong">
          <div class="answer">
            <span class="normal">您选择：<font color="#D0021B"></font></span>
            <span class="right" data-answer="{{ info.answer }}"
              >正确答案：<font color="#417505"></font>
            </span>
          </div>
          <div class="jx">
            <!-- 挖个坑 -->
          </div>
        </div>
        <div class="sy">
          <p><?php echo $water_mark; ?></p>
        </div>
      </div>
      <button id="btn">下一题</button>
      <div class="footer">
        <div class="dati" id="ft">
          <img src="/theme/exam/img/member_sel.png" alt="" /><span class="right"></span>
          <img src="/theme/exam/img/cuo.png" alt="" /><span class="error"></span>
          <img src="/theme/exam/img/duoxiangmufuwutai.png" alt="" id="answer" />
          <span class="answer">答题卡</span>
        </div>
        <div class="number clearfix">
          <!-- 挖个坑 -->
        </div>
      </div>
      <div class="borderl"></div>
      <div class="borderr"></div>
      <div class="mc"></div>
    </div>
    <!--  题号模板 -->
    <script type="text/html" id="headTpl">
      <p>第 {{info.sort}} 题</p>
    </script>
    <!-- 题目模板 -->
    <script type="text/html" id="mainTpl">
      <p class="title"  data-id="{{info.id}}">{{info.caption}}「1分」</p>
      {{ each option v i}}
      <p class="select">
       <label>
         <span class="radio"></span>
          <input type="radio" name="radio" value="{{ v.id }}" style="display: none" disabled/>{{v.q_num}}
        {{v.text}}
       </label>
      </p>
      {{/each}}
    </script>
    <!-- 解析模板 -->
    <script type="text/html" id="jxTpl">
      {{if info.remark}}
      <p class="dajx">答案解析：</p>
      <p class="detail">{{ info.remark }}</p>
      {{/if}}
    </script>
    <!-- 答题卡模板 -->
    <script type="text/html" id="numTpl">
      {{ each list v i }}
      <a data-index="{{i}}" data-id="{{v.info.id}}">{{i+1}}</a>
      {{ /each}}
    </script>
    <script src="/theme/admin/common/layui/jquery-1.9.1.min.js"></script>
    <script src="/theme/exam/js/template-web.js"></script>
    <script type="text/javascript">
      document.write(
        "<script type='text/javascript' src='/theme/exam/js/common.js?v=" +
          Date.now() +
          "'></s" +
          "cript>"
      );
      document.write(
        "<script type='text/javascript' src='/theme/exam/js/height.js?v=" +
          Date.now() +
          "'></s" +
          "cript>"
      );
      document.write(
        "<script type='text/javascript' src='/theme/exam/js/analysis.js?v=" +
          Date.now() +
          "'></s" +
          "cript>"
      );
    </script>
  </body>
</html>
