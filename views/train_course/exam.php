<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title><?php echo $title; ?></title>
    <script type="text/javascript">
      var json_question = <?php echo $question; ?>;
      var info = json_question.list;
      document.write(
        "<link rel='stylesheet' type='text/css' href='/theme/exam/css/common.css?v=" +
          Date.now() +
          "' />"
      );
      document.write(
        "<link rel='stylesheet' type='text/css' href='/theme/exam/css/media.css?v=" +
          Date.now() +
          "' />"
      );
      document.write(
        "<link rel='stylesheet' type='text/css' href='/theme/exam/css/exam.css?v=" +
          Date.now() +
          "' />"
      );
    </script>
  </head>
  <body
    oncontextmenu="return false"
    onselectstart="return false"
    oncopy="return false"
  >
    <div class="container">
      <div class="header">
        <span class="one">共:<font color="#417505"><?php echo $paper_info['item_count']; ?></font> 题</span>
        <span class="two">剩余:<font color="#D0021B"><?php echo $paper_info['item_count']; ?></font> </span>
        <span class="time" id="time">
          倒计时: <?php echo ($paper_info['total_minute'] - 1);?> : 59
        </span>
      </div>
      <div class="content">
        <!-- 提示信息 -->
        <p class="message">您已成功交卷</p>
        <div id="main">
          <!-- 挖个坑 -->
        </div>
        <button id="btn">下一题</button>
        <div class="sy">
          <p><?php echo $water_mark; ?></p>
        </div>
      </div>
      <div class="footer">
        <ul>
          <li><button type="submit" id="sub">交卷</button></li>
          <li id="ft">
            <img src="/theme/exam/img/duoxiangmufuwutai.png" alt="" id="answer" />
            <span class="answer">答题卡</span>
          </li>
        </ul>
        <div class="number clearfix">
          <!-- 挖个坑 -->
        </div>
      </div>
      <div class="borderl"></div>
      <div class="borderr"></div>
      <div class="mc"></div>
      <!-- 对话框 -->
      <div class="dialog">
        <div class="title">确定交卷</div>
        <button class="sure">确定</button>
        <button class="cancel">取消</button>
      </div>
    </div>
    <!-- 题目模板 -->
    <script type="text/html" id="mainTpl">
      <div class="main">
          <p class="title" data-id="{{info.id}}" data-answer="{{info.answer}}">{{info.sort}} {{info.caption}}</p>
              {{ each option v i}}
            <p class="select">
             <label>
               <span class="radio"></span>
                <input type="radio" name="radio" value="{{ v.id }}" style="display: none"/>{{v.q_num}}
              {{v.text}}
             </label>
            </p>
            {{/each}}
      </div>
    </script>
    <!-- 答题卡模板 -->
    <script type="text/html" id="numTpl">
      {{ each list v i }}
      <a data-index="{{i}}" data-id="{{v.info.id}}">{{i+1}}</a>
      {{ /each}}
    </script>
    <script src="/theme/admin/common/layui/jquery-1.9.1.min.js"></script>
    <script src="/theme/exam/js/template-web.js"></script>
    <script type="text/javascript">
      document.write(
        "<script type='text/javascript' src='/theme/exam/js/common.js?v=" +
          Date.now() +
          "'></s" +
          "cript>"
      );
      document.write(
        "<script type='text/javascript' src='/theme/exam/js/height.js?v=" +
          Date.now() +
          "'></s" +
          "cript>"
      );
      document.write(
        "<script type='text/javascript' src='/theme/exam/js/exam.js?v=" +
          Date.now() +
          "'></s" +
          "cript>"
      );
      function get_user_answer(){
        var formData = new FormData();
        var pid = '<?php echo $pid; ?>';
        var answer = sessionStorage.getItem("result");
        formData.append('pid',pid);
        formData.append('answer',answer);
        $.ajax({
            url: '/train_course/get_user_answer/',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
              var res = JSON.parse(res);
                if (res.rs_code == "success") {
                    alert(res.rs_msg);
                    location.reload();
                }else {
                    alert(res.rs_msg);
                    return false;
                }
            }
        });
      }
    </script>
  </body>
</html>
