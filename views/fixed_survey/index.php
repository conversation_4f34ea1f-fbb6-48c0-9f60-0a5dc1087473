<!DOCTYPE>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui"
    />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <link type="text/css" rel="styleSheet"  href="<?php echo base_url();?>theme/css/fixed_survey_style.css" />
    <title>优生优育</title>
    <script>
        (function (doc, win) {
            var docEl = doc.documentElement,
                resizeEvt = 'onorientationchange' in window ? 'onorientationchange' : 'resize',
                recalc = function () {
                    var clientWidth = docEl.clientWidth;
                    if (!clientWidth) return;
                    if (clientWidth >= 1920) {
                        docEl.style.fontSize = '100px';
                    } else {
                        docEl.style.fontSize = 100 * (clientWidth / 1920) + 'px';
                    }
                };

            if (!doc.addEventListener) return;
            win.addEventListener(resizeEvt, recalc, false);
            doc.addEventListener('DOMContentLoaded', recalc, false);
        })(document, window);

    </script>

</head>
<body>
    <!-- 头部信息 -->
    <div class="body">
    <div class="head">
        <h1>关于优生优育项目的调研</h1>
    </div>
    <form action="" id="item_form" enctype="multipart/form-data" method="post">
    <div class="header">
        <input type="hidden" name="member_uid" value="<?= $member_uid?>"/>
        <input type="hidden" name="pid" value="<?= $pid?>"/>
        <input type="hidden" name="uid" value="<?= $uid?>"/>
        <p>从医师数据库自动输出的目标医师基础信息</p>
        <div class="doctor-info">
            <label>所属医院</label>
            <input type="text" readonly="readonly" name="unit_name" id="hospital" value="<?= $user_info['unit_name']?>"/>
            <input type="hidden" name="unit_id" value="<?= $user_info['unit_id']?>"/>
        </div>
        <div class="doctor-info">
            <label>医生科室</label>
            <input type="text" readonly="readonly" name="department_name" id="offices" value="<?= $user_info['department_name']?>"/>
            <input type="hidden" name="department" value="<?= $user_info['department']?>"/>
        </div>
        <div class="doctor-info">
            <label>医生姓名</label>
            <input type="text" readonly="readonly" name="user_name" id="name" value="<?= $user_info['name']?>"/>
        </div>
        <input type="hidden" name="mobile" id="phone" value="<?= $user_info['mobile']?>"/>
    </div>
    <!-- 调查信息 -->
    <div class="survey-title">
        <p>医生你好，我是健康通的访问员，受中国医院协会委托正在进行关于优生优育项目和指南调研，有1-2个问题需要您给与支持，大概时间不超过2分钟，回答完我们会给您礼金作为酬谢！不知您是否有时间？</p>
    </div>
    <!-- 调查内容 -->
    <div class="survey-content">
        <div id="question">
            <p>
                    <i>S1、</i>请问您属于哪个科室？</p>
                <div class="office">
                    <input type="radio" value="a" name="office_radio" class="radiobox">
                    <label class="pay">a 产科（独立的，不包含新生儿科）
                        <span>只问Q1问题</span>
                    </label>
                </div>
                <div class="office">
                    <input type="radio" value="b" name="office_radio" class="radiobox">
                    <label class="pay">b 新生儿科（独立的，不归属于产科）
                        <span>只问Q2问题</span>
                    </label>
                </div>
                <div class="office">
                    <input type="radio" value="c" name="office_radio" class="radiobox">
                    <label class="pay">c 产科（综合，包含新生儿科）
                        <span>两题都问</span>
                    </label>
                </div>
            <div id="question1" style="display: none;">
                <p><i>Q1、</i>请问2017年您院全年的新生儿人数大约是多少？</p>
                <div class="urvey-content-info">
                    <label>2017年全院新生儿人数：</label>
                    <input type="text" name="number" id="number" onkeyup="value=value.replace(/[^\d]/g,'')" value="<?php echo $number?>"/><span>人</span>
                    <input type="hidden" id="init_a" value="<?= $number?>"/>
                </div>
            </div>
            <div id="question2" style="display: none;">
                <p><i>Q2、</i>2017年您院全年出生后有问题然后再咱院住过院的新生儿数大概是多少？（包括外院转来的）其中住过院的早产儿数是多少？（包括外院转来的）</p>
                <div class="urvey-content-info">
                    <label>a 2017年因“有问题住院”后出院的新生儿人数(包括外院转来的)：</label>
                    <input type="text" name="number_baby" id="number_baby" class="number_baby" onkeyup="value=value.replace(/[^\d]/g,'')" maxlength="10"><span>人</span>
                </div>
                <div class="urvey-content-info">
                    <label>b 2017年因“有问题住院”后出院的早产儿人数(包括外院转来的)：</label>
                    <input type="text" name="premature" id="premature" class="premature" onkeyup="value=value.replace(/[^\d]/g,'')" maxlength="10"><span>人</span>
                </div>
            </div>
        </div>
    </div>
    </form>
    <!-- 结束语 -->
    <div class="end-content">
            <p>
                <span>结束语</span>好的，本次调研全部结束了，再次感谢您的支持，谢谢！再见</p>
        </div>
    <!-- 底部按钮 -->
    <div class="sumbit">
        <div class="btn-info">
            <button class="sure" onclick="btnSubmit()">点击提交数据</button>
        </div>
        <div class="btn-info">
            <button class="cancel" onclick="btnCancel()">失败关闭</button>
        </div>
    </div>
</div>

</body>
</html>

<script type="text/javascript" src="<?php echo base_url();?>theme/<?php echo TEMPLATE_DIR;?>/js/jquery-3.1.1.min.js"></script>
<script src="<?= base_url()?>/theme/<?= TEMPLATE_DIR?>/assets/js/jquery.form.js"></script>
<script type="text/javascript">
    function btnSubmit(){
        var number = $.trim($('#number').val());
        var q2_a = $.trim($('#number_baby').val());
        var q2_b = $.trim($('#premature').val());
        var office = $("input[name='office_radio']:checked").val();
        
        if(office == ""){
            alert("请选择您属于科室！");
            return false;
        }
        //所有输入为数字
        var question = $("#question input[type=text]");
        for (var i = 0; i < question.length; i++) {
            var value = $.trim($(question[i]).val());
            if(value != "" && isNaN(value)){
                alert("请输入数字！");
                $(question[i]).val("");
                $(question[i]).focus();
                return;
            }
        }
        //Q1
        if(office == 'a' || office == 'c'){
            if(number == ""){
                alert("请输入【2017年全院新生儿人数】！");
                $('#number').focus();
                return;
            }
        }
        
        //Q2
        if(office == 'b' || office == 'c'){
            if(q2_a == ""){
                alert("请输入【2017年因“有问题住院”后出院的新生儿人数】！");
                $("#number_baby").focus();
                return;
            }
            if(q2_b == ""){
                alert("请输入【2017年因“有问题住院”后出院的早产儿人数】！");
                $("#premature").focus();
                return;
            }
            if(parseInt(q2_b) > parseInt(q2_a)){
                alert("请输入【早产儿人数】 <= 【新生儿人数】的数！");
                $("#premature").focus();
                return;
            }
        }
        // if(office == 'c'){
        //     if(parseInt(q2_a) > parseInt(number)){
        //         alert("请输入【2017年因“有问题住院”后出院的新生儿人数】 <= 【2017年全院新生儿人数】的数！");
        //         $("#number_baby").focus();
        //         return;
        //     }
        //     if(parseInt(q2_b) > parseInt(number)){
        //         alert("请输入【2017年因“有问题住院”后出院的早产儿人数】 <= 【2017年全院新生儿人数】的数！");
        //         $("#premature").focus();
        //         return;
        //     }
        // }

        $("#item_form").ajaxSubmit({
            target:'',
            url:'/fixed_survey/submit',
            type:'post',
            dataType:'json',
            clearForm:false,
            resetForm:false,
            cache:false,
            success:function(data) {
                if(data.rs_code=='success'){
                    alert(data.rs_msg);
                    window.location.href=data.rs_backurl;
                }
                if(data.rs_code=='error'){
                    alert(data.rs_msg);
                }
            },
        });
    }

    function btnCancel(){
        window.location.href="http://www.drsay.cn";
    }

    $("input[type=text]").focus(function(){
        this.select();
    });

    $(function(){
        $(":radio[name='office_radio']").click(function(){
            var index = $(":radio[name='office_radio']").index($(this));
            if(index == 0){
                $("#question1").show();
                $("#question2").hide();
                $("#number").focus();
            }else if(index == 1){
                $("#question1").hide();
                $("#question2").show();
                $("#number_baby").focus();
            }else if(index == 2){
                $("#question1").show();
                $("#question2").show();
                $("#number").focus();
            }
        })
    })

</script>

