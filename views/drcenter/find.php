<link href="/theme/drcenter/css/swiper.min.css" rel="stylesheet" />
<link href="/theme/drcenter/css/home.css" rel="stylesheet" />

<style>
    * {
        box-sizing: border-box;
    }

    input,
    textarea {
        -webkit-appearance: none;
        border-radius: 0px;
    }

    /* 清浮动 */
    .clearfix::before,
    .clearfix::after {
        content: "";
        display: table;
    }

    .clearfix:after {
        clear: both;
    }

    /* IE 6/7 */

    .clearfix {
        zoom: 1;
    }

    .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
    }

    .container {
        height: calc(100vh - 70px);
        overflow: hidden;
    }

    .container .content {
        /* height: calc(100vh - 50px); */
        overflow: auto;
        /* padding-bottom: 70px; */
    }

    .container .content .item {
        margin-bottom: 15px;
    }

    .container .content .item .share_content {
        padding: 15px 10px 0;
    }

    .item .head_portrait {
        width: 45px;
        height: 45px;
        margin-right: 5px;
        position: relative;
        border-radius: 5px;
        overflow: hidden;
    }

    .head_portrait img {
        margin-right: 10px;
        width: 100%;
    }

    .share_detail {
        width: 85%;
    }

    .share_detail h4 {
        font-size: 18px;
    }

    .share_detail p {
        text-align: justify;
        font-size: 16px;
    }

    .share_detail video {
        width: 100%;
        margin-top: 10px;

    }

    .item .comment_num {
        position: relative;
    }

    .main {
        height: calc(100vh - 50px);
        overflow: auto;
        padding-bottom: 70px;
    }

    .comment_num .img_box {
        position: relative;
        display: inline-block;
        min-width: 30px;
        height: 30px;
        margin-right: 20px;
        padding-left: 25px;
        cursor: pointer;
        margin-bottom: 10px;
    }

    .comment_num .img_box img {
        width: 20px;
        left: 0 !important;
        top: 10px !important;
        transform: none !important;
    }

    .comment_num .img_box font {
        display: inline-block;
        max-width: 50px;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .comment_list .input {
        width: 95%;
        height: 45px;
        background: #ffff;
        position: relative;
        margin: 0 auto;
        display: none;
    }

    .comment_list .input input[name='comment'] {
        width: 82%;
        height: 100%;
        background: none;
        outline: none;
        border: 1px solid #ccc;
        padding: 0 20px;
        font-size: 16px;
        box-sizing: border-box;
        position: absolute;
        left: 0;
    }

    .comment_list .input button {
        height: 100%;
        width: 20%;
        position: absolute;
        top: 0;
        right: 0;
        outline: none;
        background: #4AABE3;
        border: none;
        font-size: 16px;
        color: #fff;
    }

    .comment_list .comment_box {
        background: #eff1f3;
        padding: 0 10px;
    }

    .comment_list .comment_item:not(:first-child) {
        border-top: 1px solid #ccc;
    }

    .comment_list .comment_item {
        padding: 10px 5px;

        width: 95%;
        margin: 0 auto;
    }

    .comment_list .comment_item p:first-child {
        position: relative;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
    }

    .comment_list .comment_item p:first-child img {
        width: 25px;
        left: 0 !important;
        top: 0 !important;
        transform: none !important;
    }

    .comment_list .comment_item p:first-child span {
        margin-left: 30px;
        font-weight: 500;
    }

    .comment_list .comment_item .p_comment {
        margin-left: 30px;
        font-size: 12px;
    }

    .comment_list .comment_item p:first-child button {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        padding: 0 5px;
        border: none;
        outline: none;
        background: none;
        font-size: 14px;
    }

    .item .reply {
        position: relative;
        height: 40px;
        display: none;
        margin: 0 10px;
    }

    .item .reply input {
        width: 80%;
        height: 100%;
        border: 1px solid #ccc;
        outline: none;
        background: #fff;
        padding: 0 10px;
        position: absolute;
        left: 0;
    }

    .item .reply button {
        position: absolute;
        right: 0;
        height: 100%;
        width: 20%;
        background: #4AABE3;
        border: none;
        outline: none;
        color: #fff;
    }


    .swiper-container {
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        background: #000;
        display: none;
        z-index: 9999;
    }

    .swiper-container img {
        width: 100%;
        top: 50% !important;
        left: 0 !important;
        transform: translateY(-50%) !important;
    }

    .swiper-pagination-bullet {
        background: #fff;
    }

    .swiper-pagination-bullet-active {
        background: #4aabe3;
    }

    .preview {
        padding: 10px;
    }

    .preview_box {
        width: 30%;
        height: 100px;
        float: left;
        overflow: hidden;
        margin-right: 8px;
        position: relative;
        margin-bottom: 10px;
    }

    .preview_box img {
        width: 100%;
    }

    img {
        position: absolute;
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
    }

    .add_share {
        background: #4AABE3;
        height: 44px;
        position: relative;
    }

    .add_share img {
        width: 40px;
        left: 90% !important;
        cursor: pointer;
    }

    .share_dialog {
        display: none;
        width: 100%;
        height: 100%;
        padding: 0 20px;
        background: #fff;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 999;
    }

    .share_dialog .btn {
        padding: 20px 0;
    }

    .share_dialog .btn button {
        border: none;
        outline: none;
        background: none;
        height: 40px;
        width: 70px;
        border-radius: 5px;
        font-size: 16px;
        cursor: pointer;
    }

    .share_dialog .btn .cancel {
        text-align: left;
    }

    .share_dialog .btn button:last-child {
        float: right;
        background: #4AABE3;
        color: #fff;
    }

    .share_dialog textarea {
        width: 100%;
        outline: none;
        border: none;
        resize: none;
        height: 140px;
        font-size: 16px;
    }

    .share_dialog .share_img {
        flex-wrap: wrap;
    }

    .share_dialog .share_img label {
        position: relative;
        display: inline-block;
        width: 30%;
        height: 100px;
        border: 1px solid #cdcdcd;
        overflow: hidden;
        margin-bottom: 10px;
        margin-right: 10px;
    }

    .share_dialog .share_img label .add {
        width: 50px;
    }

    .share_dialog .share_img label .preview_img {
        height: 100%;
    }
</style>
<div class="container">
    <!-- 上传按钮 -->
    <div class="add_share">
        <img src="/theme/drcenter/images/survey/add_share.png" alt="">
    </div>
    <!-- 主要内容 -->
    <div class="main">
        <div class="content">
            <!-- 挖个坑 -->
        </div>
    </div>
    <!-- 上传分享模态框 -->
    <div class="share_dialog">
        <form id="uploadForm" action="" methods="post">
            <div class="btn">
                <button class="cancel">取消</button>
                <button type="submit" s class="send">发表</button>
            </div>
            <input type="hidden" name="uid">
            <input type="hidden" name="login_token">
            <input type="hidden" name="easy_address">
            <textarea placeholder="这一刻的想法" name="content"></textarea>
            <div class="share_img flex">
                <label>
                    <img class="preview_img" src="" alt="">
                    <input type="file" style="display:none">
                    <img class="add" src="/theme/drcenter/images/survey/add.png" alt="">
                </label>
            </div>
        </form>

    </div>
</div>

<!-- 朋友圈文章模版 -->
<script type="text/html" id="blogsTpl">
{{each rs_msg v i }}
<div class="item">

    <!-- 分享的内容 -->
    <div class="share_content flex">
        <!-- 头像 -->
        <div class="head_portrait">
            <img src="{{v.avatar ||'/theme/drcenter/images/default_auther.jpg'}}" alt="" style="margin-right: 10px;" />
        </div>
        <!-- 分享的细节 -->
        <div class="share_detail">
            <!-- 昵称 -->
            <h4>{{v.nickname || "健康通" }}</h4>
            <!-- 文字 -->
            <p>{{v.content}}</p>
            <!-- 图片 -->
            <ul class="preview clearfix">
                {{each v.imgs $value $index }}
                <li class="preview_box" data-index="{{$index+1}}">
                    <img src="{{$value}}" alt="" />
                </li>
                {{/each}}
            </ul>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    {{each v.imgs $value $index}}
                    <div class="swiper-slide"><img src="{{$value}}" alt="" /></div>
                    {{/each}}
                </div>
                <div class="swiper-pagination"></div>
            </div>
            <p class="flex" style="font-size: 12px;justify-content: space-between;margin-bottom: 15px;">
                <span>{{v.release_time}}</span>
                <span>浏览量:{{v.browser_num}}</span>
            </p>
            <p style="font-size: 12px;">{{v.easy_address}}</p>
        </div>
    </div>

    <!-- 点赞评论量 -->
    <div class="comment_num" data-id="{{v.id}}">
        <div style="text-align: right;">
            <span class="img_box {{v.is_praise==1?'active':''}}">
                <img src="{{ v.is_praise == 1 ? '/theme/drcenter/images/survey/gooded.png' :'/theme/drcenter/images/survey/good.png'}}" alt="">
                <font>{{v.praise_num}}</font>
            </span>
            <span class="img_box">
                <img src="/theme/drcenter/images/survey/comment.png" alt="">
                <font>{{v.criticism_num}}</font>
            </span>
        </div>
    </div>

    <!-- 评论列表 -->
    <div class="comment_list">
        <div class="input" data-index="{{v.id}}">
            <input type="text" name="comment"><button>评论</button>
        </div>
        <div class="comment_box">
            {{each v.criticism_list $value $index }}
            <div class="comment_item" data-id="{{$value.critic}}">
                <p>
                    <img src="{{$value.critic_avatar || '/theme/drcenter/images/default_auther.jpg'}}" alt="" />
                    <span>{{$value.critic_name || "昵称"}}</span>
                </p>
                <p class='p_comment'>{{ $value.target== '' ?  '' : '回复'+$value.target_name+':'}} {{$value.criticism}}</p>
            </div>
            {{/each}}
        </div>
    </div>
    <p class="reply">
        <input type="text">
        <button class="btn_reply">回复</button>
    </p>
</div>
{{/each}}
</script>

<script src="/theme/drcenter/js/jquery.form.min.js"></script>
<script src="/theme/drcenter/js/swiper.min.js"></script>
<!-- 获取当前地址 -->
<script src="https://pv.sohu.com/cityjson?ie=utf-8"></script>


<script>
    var data = JSON.parse(localStorage.getItem("data"))
    var token = data.login_token || ""
    var uid = data.uid || ""
    // 获取定位
    var easy_address = returnCitySN.cname
    $("input[name='easy_address']").val(easy_address)
    $('input[name="uid"]').val(uid)
    $('input[name="login_token"]').val(token)

    // 进入页面获取朋友圈文章详情
    request_back("api_blogs/blogs", {
            uid: uid,
            login_token: token,
        },
        true,
        function(info) {
            console.log(info)
            $(".content").html(template("blogsTpl", info))
        })
    // 点击图片预览
    $(".content ").on("click", ".preview li", function() {
        // $(".swiper-container").show();
        $(this).parent().next().show()
        var initialSlide = $(this).data("index") - 1;
        var mySwiper = new Swiper(".swiper-container", {
            initialSlide: initialSlide,
            grabCursor: true,
            pagination: {
                el: ".swiper-pagination",
            },
        });

        $(".swiper-container").click(function() {
            $(".swiper-container").hide();
        });
    });

    // 点赞
    $(".content").on("click", ".comment_num div .img_box:nth-child(1)", function() {
        var blog_id = $(this).parent().parent().data("id")
        $(this).toggleClass("active");
        if ($(this).hasClass("active")) {
            $(this).children("img").attr("src", "/theme/drcenter/images/survey/gooded.png");
            var num = $(this).children("font").text()
            $(this).children("font").text(+num + 1)
            // 点赞成功
            request_back("api_blogs/praise", {
                    uid: uid,
                    login_token: token,
                    blog_id: blog_id
                },
                true,
                function(info) {
                    if (info.rs_code == "success") {

                    }
                })
        } else {
            $(this).children("img").attr("src", "/theme/drcenter/images/survey/good.png");
            var num = $(this).children("font").text()
            $(this).children("font").text(+num - 1)
            // 取消点赞
            request_back("api_blogs/unpraise", {
                    uid: uid,
                    login_token: token,
                    blog_id: blog_id
                },
                true,
                function(info) {
                    if (info.rs_code == "success") {}
                })
        }
    });

    /****************************** 评论 ******************************/
    // 点击其他地方隐藏
    $(document).click(function() {
        $(".comment_list .input").hide()
    })
    // 阻止冒泡
    $(".content").on("click", ".input", function(e) {
        e.stopPropagation()
    })
    // 评论
    $(".content").on("click", ".comment_num div .img_box:nth-child(2)", function(e) {
        e.stopPropagation()
        $(".reply").hide()
        $(".input").hide()
        $(this).parent().parent().next().children(".input").show()
        $(this).parent().parent().next().children(".input").children("input").focus()
    })

    // 添加评论接口
    function add_blogs(comment, blog_id, target_id, ) {
        request_back("api_blogs/add_blogs_comment", {
                uid: uid,
                login_token: token,
                blog_id: blog_id,
                content: comment,
                target_id: target_id || ""
            },
            true,
            function(info) {
                console.log(info)
            })
    }

    // 回车发送
    $(".content").on("keyup", ".comment_list .input input", function(e) {
        var comment = $(this).val().trim()
        if (comment && e.keyCode == 13) {
            $(".comment_list .input button").trigger("click")
        }
    })

    // 按钮发送
    $(".content").on("click", ".comment_list .input button", function() {
        var comment = $(this).prev().val().trim()
        var blog_id = $(this).parent().data("index")
        if (comment) {
            $(this).prev().val("")
            $(this).parent().next().prepend(
                '<div class="comment_item">' +
                '<p>' +
                '<img src="/theme/drcenter/images/default_auther.jpg" alt="" />' +
                '<span>昵称</span>' +
                '</p>' +
                '<p class="p_comment">' + comment + '</p>' +
                '</div>'
            )
            var num = $(this).parent().parent().prev().find(".img_box:last").children("font").text()
            $(this).parent().parent().prev().find(".img_box:last").children("font").text(+num + 1)
            add_blogs(comment, blog_id)

        }

    })
    /****************************** 评论end ******************************/

    /****************************** 回复框 ******************************/
    // 显示回复框
    $(".content").on("click", ".comment_list .comment_box .comment_item", function(e) {
        e.stopPropagation()
        $(".input").hide()
        var name = $(this).children("p:first").children("span").text();
        var target_id = $(this).data("id");
        $(this).parent().parent().next().show();
        $(this).parent().parent().next().focus();
        $(this).parent().parent().next().attr("data-target", target_id);
        $(this).parent().parent().next().children("input").prop("placeholder", "回复" + name + "")
    })

    // 回车发送
    $(".content").on("keyup", ".item .reply input", function(e) {
        var content = $(this).val().trim()
        if (content && e.keyCode == 13) {
            $(".item .btn_reply").trigger("click")
        }
    })

    // 回复按钮发送
    $(".content").on("click", ".item .btn_reply", function() {
        var content = $(this).prev().val().trim()
        var blog_id = $(this).parent().prev().children(".input").data("index")
        var target_id = $(this).parent().data("target")
        var reply = $(this).prev().prop("placeholder")
        if (content) {
            $(this).parent().prev().children(".comment_box").append(
                '<div class="comment_item">' +
                '<p>' +
                '<img src="/theme/drcenter/images/default_auther.jpg" alt="" />' +
                '<span>昵称</span>' +
                '</p>' +
                '<p class="p_comment">' + reply + ':' + content + '</p>' +
                '</div>'
            )
            $(this).prev().val("")
            $(".reply").hide()
            var num = $(this).parent().prev().prev().children().children(".img_box:last").children("font").text()
            $(this).parent().prev().prev().children().children(".img_box:last").children("font").text(+num + 1)
            add_blogs(content, blog_id, target_id)
        }
    })

    // 点击其他地方隐藏
    $(document).click(function() {
        $(".reply").hide()
    })
    $(".content").on("click", ".item .reply", function(e) {
        e.stopPropagation()
    })

    /****************************** 回复框end ******************************/

    // 分享对话框的显示
    $(".add_share").click(function() {
        $(".share_dialog").show()
    })

    // 点击取消隐藏分享对话框
    $(".share_dialog .btn .cancel").click(function() {
        $(".share_dialog").hide()
    })

    // 上传图片预览
    $(".share_img").on("change", "label input", function(e) {
        $(this).attr("name", "img1")
        var objurl = $(this)[0].files[0]
        var windowURL = window.URL || window.webkitURL;
        var dataUrl = windowURL.createObjectURL(objurl)
        $(this).prev().attr("src", dataUrl)
        $(this).next().attr("src", "")
        if ($(".share_img label").length < 9) {
            $(this).parent().parent().append(
                '<label>' +
                '<img class="preview_img" src="" alt="">' +
                '<input type="file" style="display:none">' +
                '<img class="add" src="/theme/drcenter/images/survey/add.png" alt="">' +
                '</label>')
        }
    })

    // 添加朋友圈文章
    // ************************ from提交开始 *********************
    var options = {
        beforeSubmit: showRequest, // 提交前的回调函数
        success: showResponse, // 提交后的回调函数
        url: url + "api_blogs/add_blogs", // 默认是form的action， 如果申明，则会覆盖
        type: "post",
        timeout: 3000 // 限制请求的时间，当请求大于3秒后，跳出请求
    }

    $("#uploadForm").ajaxForm(options);

    function showRequest(formData, jqForm, options) {
        console.log(formData)
    };

    function showResponse(responseText, statusText) {
        console.log(responseText)
        console.log(statusText)
        window.location.reload()
    };
    // ************************ from提交结束 *********************
    $(function() {
        $("html").css("fontSize", "16px")
    })
</script>