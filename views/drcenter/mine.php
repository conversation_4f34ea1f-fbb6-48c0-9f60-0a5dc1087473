<link href="/theme/drcenter/css/main.css?<?php echo time(); ?>" rel="stylesheet" />
<style>
    .foot .word {
        top: 48px !important;
    }

    .foot li .img {
        top: 10px !important;
    }

    .alert-dialog {
        display: none;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 999;
        width: 80%;
        background: #fdfef9;
        border: 1px solid #2c5c84;
        border-radius: 5px;
    }

    .alert-dialog p {
        font-size: 0.2rem;
        color: #333333;
        line-height: 0.7rem;
        height: 0.7rem;
    }

    .alert-dialog p img {
        height: 0.3rem;
        margin-left: 0.25rem;
        margin-right: 0.2rem;
    }

    .alert-dialog span {
        display: block;
        margin: 0.37rem 0.25rem 0;
        font-size: 0.28rem;
        line-height: 0.4rem;
        color: #4a4a4a;
    }

    .alert-dialog button {
        height: 1.32rem;
        font-size: 0.32rem;
        margin-top: 0.58rem;
        width: 100%;
        color: #fff;
        text-align: center;
        background: #1faee6;
    }

    .alert-dialog-sec div {
        display: flex;
        margin-top: 0.58rem;
    }

    .alert-dialog-sec div p {
        flex: 1;
        height: 0.8rem;
        font-size: 0.32rem;
        text-align: center;
        color: #fff;
        background: #1faee6;
    }

    .first {
        display: none;
        position: fixed;
        z-index: 9999999;
    }

    .first img {
        width: 7.5rem;
    }

    .second {
        display: none;
        position: fixed;
        z-index: 9999999;
    }

    .second img {
        width: 7.5rem;
    }
</style>


<div id="first" class="first">
    <img src="/theme/drcenter/images/first.png" id="img-first" />
</div>
<div id="second" class="first">
    <img src="/theme/drcenter/images/second.png" id="img-first" />
</div>
<div class="mine-wrapper">
    <div class="mine-header">
        <p class="mine-header-title">我的</p>
        <img class="mine-header-avatar" src="/theme/drcenter/images/doctor-consult-avatar.png" />
        <p class="mine-header-name"></p>
    </div>
    <div class="mine-content">
        <ul>
            <li class="mine-content-item">
                <a href="/drcenter/dr/mine_auth">
                    <img src="/theme/drcenter/images/mine/mine-authentication.png" />
                    <span>个人资料认证</span>
                    <i></i>
                </a>
            </li>
            <li class="mine-content-item">
                <a href="/drcenter/dr/doctor_certify">
                    <img src="/theme/drcenter/images/mine/mine-doctor-auth.png" />
                    <span>职业认证</span>
                    <i></i>
                </a>
            </li>
            <li class="mine-content-item">
                <a href="/drcenter/dr/point">
                    <img src="/theme/drcenter/images/mine/mine-point.png" />
                    <span>积分</span>
                    <i></i>
                </a>
            </li>
            <li class="mine-content-item">
                <a href="/drcenter/dr/setting">
                    <img src="/theme/drcenter/images/mine/mine-setting.png" />
                    <span>设置</span>
                    <i></i>
                </a>
            </li>
            <li class="mine-content-item login_out">
                <a href="/drcenter/dr/login">
                    <img src="/theme/drcenter/images/mine/mine-logout.png" />
                    <span>退出</span>
                    <i></i>
                </a>
            </li>
            <!-- <li class="mine-content-item">
                    <a href="/drcenter/dr/store">
                        <img src="/theme/drcenter/images/mine/mine-logout.png" />
                        <span>我的收藏</span>
                        <i></i>
                    </a>
                </li> -->

            <!-- <li onClick="showFirst();" class="mine-content-item">
                    <img src="/theme/drcenter/images/mine/mine-point.png" />
                    <span>开机消息推送演示</span>
                    <i></i>
                </li>
                <li onClick="showSecond();" class="mine-content-item">
                    <img src="/theme/drcenter/images/mine/mine-point.png" />
                    <span>离线消息推送演示</span>
                    <i></i>
                </li>
                <li onClick="showAlertDialogSec();" class="mine-content-item">
                    <img src="/theme/drcenter/images/mine/mine-point.png" />
                    <span>站内提醒您的患者参与问卷</span>
                    <i></i>
                </li> -->
        </ul>
    </div>
</div>
<div id="alert" class="alert-dialog">
    <p><img src="/theme/drcenter/images/alert-logo.png" />5分钟前</p>
    <span>您有一个晚期/转移性肺癌治疗的研究项目等待您去完成，请快速参与吧。</span>
    <button id="close-alert">关闭</button>
</div>
<div id="alert-sec" class="alert-dialog alert-dialog-sec">
    <p><img src="/theme/drcenter/images/alert-logo.png" />5分钟前</p>
    <span>您的晚期/转移性肺癌治疗的研究项目已经完成80%啦，
        还有12个小时即将结束，赶快提醒未参与的患者去参与吧！</span>
    <div>
        <p id="close-alert-c1">关闭</p>
        <p id="close-alert-c2">好的</p>
    </div>
</div>
<script>
    // setTimeout(function() {
    //     if (sessionStorage.getItem("is-show-alert")) {
    //         return;
    //     }
    //     $("#alert").css("display", "block");
    //     sessionStorage.setItem("is-show-alert", true);
    // }, 3000);

    // $("#close-alert").click(function() {
    //     $("#alert").css("display", "none");
    // });

    function showFirst() {
        $("#first").css("display", "block");
    }

    $("#first").click(function() {
        $("#first").css("display", "none");
    });

    function showSecond() {
        $("#second").css("display", "block");
    }

    $("#second").click(function() {
        $("#second").css("display", "none");
    });

    $("#close-alert-c1").click(function() {
        $("#alert-sec").css("display", "none");
    });
    $("#close-alert-c2").click(function() {
        $("#alert-sec").css("display", "none");
        window.location.href = base_url + "active_report?answer_number=32&status=待答";
    });

    function showAlertDialogSec() {
        $("#alert-sec").css("display", "block");
    }

    var data = JSON.parse(localStorage.getItem("data"))
    var token = data.login_token || ""
    var uid = data.uid || ""

    // 信息填充
    $(".mine-header-name").text(data.name || "")
    $(".mine-header img").attr("src", data.avatar || '/theme/drcenter/images/default_auther.jpg')


    // 进入页面获取会员信息
    // request_back("api_user/user_detail", {
    //         uid: uid,
    //         login_token: token,
    //         type: 1,
    //     },
    //     true,
    //     function(info) {
    //         console.log(info)
    //         localStorage.setItem("user_detail", JSON.stringify(info.rs_msg))
    //     })

    // 退出
    $(".login_out").click(function() {
        localStorage.clear()
    })
</script>