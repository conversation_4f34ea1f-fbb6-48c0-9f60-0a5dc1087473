    <link href="/theme/drcenter/css/active-report.css" rel="stylesheet">
    <style>
        .remind-alert-dialog-back {
            display: none;
            position: fixed;
            z-index: 9999999;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .remind-alert-dialog-content {
            position: absolute;
            left: 50%;
            top: 20%;
            width: 6rem;
            height: 3.75rem;
            background: #fff;
            transform: translate(-50%, 0);
            border-radius: 3px;
        }

        .remind-alert-dialog-content textarea {
            width: calc(100% - 0.7rem);
            margin: 0 0.35rem;
            height: 2.5rem;
            padding: 0.2rem .15rem;
            font-size: 0.26rem;
            line-height: 0.4rem;
            color: #333333;
            background: #fefef2;
            border: 1px solid #e1e1e1;
            outline: none;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            resize: none;
            -webkit-appearance: none;
            border-radius: 0;
        }

        .remind-alert-dialog-title {
            margin: .15rem 0;
            position: relative;
            height: .6rem;
        }


        .remind-alert-dialog-title img {
            float: left;
            margin-left: .15rem;
            margin-top: .075rem;
            width: 0.45rem;
            height: 0.45rem;
        }

        .remind-alert-dialog-title span {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.32rem;
        }

        .remind-alert-dialog-title button {
            float: right;
            margin-right: .15rem;
            line-height: .6rem;
            height: .6rem;
            font-size: 0.28rem;
            color: #1faee6;
            background: #fff;
            border: none;
            outline: none;
        }

        .remind-alert-dialog-suc {
            text-align: center;
            display: none;
        }

        .remind-alert-dialog-suc img {
            display: block;
            margin: 0.8rem auto 0.3rem;
            width: 1rem;

        }

        .remind-alert-dialog-suc span {
            display: block;
            font-size: .32rem;
            color: #333333;
        }
    </style>
    <!-- 头部 -->
    <ul class="home-top-title">
        <li class="close" id="black_to_home">
            <img src="/theme/drcenter/images/home/<USER>">
        </li>
        <li>报表</li>
        <li>
        </li>
    </ul>
    <!-- 内容 -->
    <div class="active-content" style="padding-top: 1rem;">
        <p>已回答：<span id="answer_number_box"></span>人</p>
        <p>未回答：5人</p>
        <p>完成率：
            <span>75%</span>
        </p>
    </div>
    <!-- 列表 -->
    <ul class="active-people-list">
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people1.png">
            </div>
            <p class="active-people-name">白鸥</p>
            <p class="active-people-answer">已回答</p>
            <a href="/drcenter/dr/questionnaire_result" class="active-people-result">查看结果</a>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people3.png">
            </div>
            <p class="active-people-name">陈梦</p>
            <p class="active-people-answer">已回答</p>
            <a href="/drcenter/dr/questionnaire_result" class="active-people-result">查看结果</a>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people6.png">
            </div>
            <p class="active-people-name">胡安明</p>
            <p class="active-people-answer">已回答</p>
            <a href="/drcenter/dr/questionnaire_result" class="active-people-result">查看结果</a>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people5.png">
            </div>
            <p class="active-people-name">赫立</p>
            <p class="active-people-answer">已回答</p>
            <a href="/drcenter/dr/questionnaire_result" class="active-people-result">查看结果</a>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people2.png">
            </div>
            <p class="active-people-name">常乐</p>
            <p class="active-people-answer">已回答</p>
            <a href="/drcenter/dr//questionnaire_result" class="active-people-result">查看结果</a>
        </li>
    </ul>
    <ul class="active-people-list">
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people10.png">
            </div>
            <p class="active-people-name">Aurora</p>
            <p class="active-people-answer1">未回答</p>
            <p class="active-people-result1">发送邀请</p>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people4.png">
            </div>
            <p class="active-people-name">郭洁</p>
            <p class="active-people-answer1">未回答</p>
            <p class="active-people-result1">发送邀请</p>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people9.png">
            </div>
            <p class="active-people-name">Achen</p>
            <p class="active-people-answer1">未回答</p>
            <p class="active-people-result1">发送邀请</p>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people8.png">
            </div>
            <p class="active-people-name">Bei</p>
            <p class="active-people-answer1">未回答</p>
            <p class="active-people-result1">发送邀请</p>
        </li>
        <li>
            <div class="active-people-avatar">
                <img src="/theme/drcenter/images/people/people7.png">
            </div>
            <p class="active-people-name">万华</p>
            <p class="active-people-answer1">未回答</p>
            <p class="active-people-result1">发送邀请</p>
        </li>
    </ul>
    <!-- 提醒未回答者 -->
    <div class="active-people-remind">
        <span id="active-people">一键提醒未回答者</span>
    </div>

    <div id="remind-alert" class="remind-alert-dialog-back">
        <div class="remind-alert-dialog-content">
            <div class="remind-alert-dialog-info">
                <div class="remind-alert-dialog-title">
                    <img id="remind-alert-clsoe" src="/theme/drcenter/images/close_icon.png" />
                    <span>问卷邀请</span>
                    <button id="remind-alert-send">发送</button>
                </div>
                <textarea cols="4" placeholder="请输入提醒内容">我是X医生，您有一份问卷尚未回答，请抽时间去完成吧！</textarea>

            </div>
            <div class="remind-alert-dialog-suc">
                <img src="/theme/drcenter/images/alert-suc-icon.png" />
                <span>问卷提醒发送成功</span>
            </div>
        </div>

    </div>
    <script>
        //获取url中的参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg); //匹配目标参数
            if (r != null) return decodeURI(r[2]); //decodeURI参数内容。
            return null; //返回参数值
        }
        $(function() {
            // 回答人数
            var answer_number = getUrlParam('answer_number');
            $("#answer_number_box").html(answer_number)
            // 状态
            var status = getUrlParam('status')
            if (status == "待答") {
                $(".active-people-remind").show()
                $(".active-people-result1").show()
            }
            $("#active_menu_show").click(function() {
                $("#active_menu").toggle();
            })
            $("#black_to_home").click(function() {
                window.history.go(-1)
            })
            $(".active-people-avatar").click(function() {
                window.location.href = "/drcenter/dr/doctor_consult"
            })
        })

        $('#active-people').click(function() {
            $('#remind-alert').css('display', 'block')
            $('.remind-alert-dialog-info').css('display', 'block')
            $('.remind-alert-dialog-suc').css('display', 'none')

        })

        $('#remind-alert-send').click(function() {
            $('.remind-alert-dialog-info').css('display', 'none')
            $('.remind-alert-dialog-suc').css('display', 'block')
            setTimeout(() => {
                $('#remind-alert').css('display', 'none')
            }, 2000);

        })


        $('#remind-alert-clsoe').click(function() {
            $('#remind-alert').css('display', 'none')
        })


        remind - alert - clsoe
    </script>