<link rel="stylesheet" href="/theme/drcenter/css/point.css" />

<style>
    * {
        margin: 0;
        padding: 0
    }

    .Health-header {
        height: .88rem;
        background: #20537d;
        position: relative;
    }

    .Health-header-titile {
        font-size: .36rem;
        color: #FFFFFF;
        text-align: center;
        font-weight: 500;
        line-height: .88rem;
    }

    .Health-header-back {
        position: absolute;
        top: .26rem;
        left: .3rem;
        height: .36rem;

    }

    .point-wrapper {
        margin: 0;
        padding: 0;
        padding-top: 0.88rem;
        background: #FFFFFF;
    }

    span:nth-of-type(1) {
        display: block;
        width: 2rem;
        height: .56rem;
        color: #FFFFFF;
        font-size: .32rem;
        text-align: center;
        padding-top: .08rem;
        margin: 0 auto;
        background: #4face1;
        border-radius: .3rem;
    }

    .point-content-img {
        display: block;
        width: 3rem;
        text-align: center;
        margin: 0 auto;
        margin-top: 1.07rem;
    }

    .point-content {
        padding-top: 0.3rem;
    }

    span:nth-of-type(2) {
        display: block;
        text-align: center;
        margin: 0 auto;
        margin-top: .707rem;
        font-size: .36rem;
        color: #1D1D1D;
    }

    span:nth-of-type(3) {
        display: block;
        text-align: center;
        margin: 0 auto;
        margin-top: .2rem;
    }

    span:nth-of-type(4) {
        display: block;
        text-align: center;
        margin: 0 auto;
        margin-top: .1rem;
        color: #D0021B;
        font-size: .26rem;
    }

    button {
        margin-left: 5%;
        margin-top: 1rem;
        font-size: .36rem;
        color: #FFFFFF;
        text-align: center;
        width: 90%;
        background: #1faee6;
        padding-top: .2rem;
        padding-bottom: .2rem;
        border-radius: 8px;
        border: none;
    }

    span:nth-of-type(5) {
        display: block;
        text-align: center;
        margin: 0 auto;
        margin-top: .4rem;
        margin-bottom: 0.2rem;
        color: #368ECF;
        font-size: .28rem;
    }
</style>

<!-- 头部 -->
<ul class="home-top-title">
    <li class="close" id="black_to_home" onclick="window.history.go(-1)">
        <img src="/theme/drcenter/images/home/<USER>">
    </li>
    <li>积分</li>
    <li>
    </li>
</ul>
<div class="point-wrapper">
    <div class="point-content">
        <span onclick="toPointDetails()">积分明细</span>
        <img class="point-content-img" src="/theme/drcenter/images/point/point-icon.png" />
        <span>我的积分</span>
        <span>1000</span>
        <span>100积分=1¥</span>
        <button onclick="toPointWithdraw()">提取积分</button>
        <span onclick="toFrequeQue()">常见问题</span>
    </div>
</div>
<script>
    function toPointDetails() {
        window.location.href = base_url + "point_details"
    }

    function toPointWithdraw() {
        window.location.href = base_url + "point_withdraw"
    }

    function toFrequeQue() {
        window.location.href = base_url + "freque_question"
    }
</script>
