<link rel="stylesheet" type="text/css" href="/theme/drcenter/css/main.css">
<style>
    .consult-wrapper {
        padding-top: 0.88rem;
    }

    .msg {
        display: block;
        padding-bottom: 1rem;
        text-align: center;
    }

    .msg:after {
        content: '';
        clear: both;
        display: block;
    }

    .msg .time {
        display: inline-block;
        margin-top: 0.37rem;
        margin-bottom: 0.70rem;
        line-height: 0.3rem;
        padding: 0 0.15rem;
        font-size: 0.2rem;
        color: #fff;
        background: #d8d8d8;
        border-radius: 5px;
    }

    .left {
        float: left;
        width: 100%;
        margin-bottom: 0.78rem;
    }

    .content {
        display: flex;
        justify-content: flex-start;
    }

    .content img {
        margin-left: 0.27rem;
        margin-top: 0.05rem;
        width: 0.8rem;
        height: 0.8rem;
    }

    .content p {
        max-width: calc(100% - 2.5rem);
        word-wrap: break-word;
        word-break: normal;
        margin-left: .4rem;
        font-size: 0.3rem;
        text-align: left;
        color: #313131;
        background: #fff;
        position: relative;
        padding: 0.25rem 0.2rem;
        border-radius: 0.15rem;
        line-height: 0.4rem;
        white-space: pre-line;
    }

    .content p::after {
        content: '';
        width: 0;
        height: 0;
        position: absolute;
        top: 0.245rem;
        left: -0.28rem;
        border: solid 0.15rem;
        border-color: transparent #fff transparent transparent;
        font-size: 0;
    }

    .right {
        float: right;
        width: 100%;
        margin-bottom: 0.78rem;
    }

    .right .content {
        display: flex;
        justify-content: flex-end;
    }

    .right .content p {
        position: relative;
        margin-right: 0.4rem;
        float: right;
        background: #d4effe;
    }

    .right .content p::after {
        right: -0.28rem;
        left: auto;
        border: solid 0.15rem 1px;
        border-color: transparent transparent transparent #d4effe;

        font-size: 0;
    }

    .right .content img {
        margin-left: 0;
        margin-right: 0.27rem;
    }

    .msg-bottom {
        position: fixed;
        z-index: 1;
        bottom: 0;
        left: 0;
        right: 0;
        /* height: 0.98rem; */
        background: #f6f6f6;
        border: 1px solid #E1E1E1;
    }

    .msg-bottom div {
        margin: 0.2rem 0.2rem 0.2rem 0.3rem;
        display: flex;
    }

    .msg-bottom div input {
        flex: 1;
        line-height: 0.65rem;
        height: 0.65rem;
        padding: 0 0.2rem;
        font-size: 0.28rem;
        background: #fff;
        border-radius: 3px;

    }

    .msg-bottom div input::placeholder {
        color: #333333;
    }

    .send {
        width: 1.34rem;
        height: 0.65rem;
        line-height: 0.65rem;
        margin-left: 0.34rem;
        font-size: 0.28rem;
        text-align: center;
        color: #fff;
        background: #75cbff;
    }
</style>
<div class="Health-header">
    <h3 class="Health-header-titile">在线咨询</h3>
    <a onclick="window.history.go(-1)"><img src="/theme/drcenter/images/comm-back.png" alt="返回" class="Health-header-back"></a>
</div>
<div class="consult-wrapper">
    <div id="msg" class="msg">
        <!-- 挖个坑 -->
    </div>
    <div class="msg-bottom">
        <div>
            <input id="content" placeholder="请输入内容" />
            <span id="send" class="send">发送</span>
        </div>
    </div>
</div>

<!-- 意见反馈模板 -->
<script type="text/html" id="replyTpl">
{{each rs_msg v i }}
<div class="right">
    <span data-index="{{i}}" class="time">{{v.add_time}}</span>
    <div class="content">
        <p>{{v.feedback}}</p>
        <img src="/theme/drcenter/images/doctor-consult-avatar.png" />
    </div>
</div>

{{each v.reply_list $value}}
<div class="left">
    <div class="content">
        <img src="/theme/drcenter/images/mine/msg-left-icon.png" />
        <p>{{$value.reply}}</p>
    </div>
</div>
{{/each}}
{{/each}}
</script>

<script>
    $('#send').click(function() {
        add_feedback()
    })

    $("#content").keyup(function(e) {
        if (e.keyCode == 13) {
            add_feedback()
        }
    })

    // 新增意见反馈
    function add_feedback() {
        var content = $('#content').val()
        if (content == '') {
            alert('请输入内容')
            return
        }
        var lastHtml = $('#msg').html()
        $('#msg').html(lastHtml + createRightMsg(content))
        $('#content').val('')
        window.scrollTo(0, document.body.scrollHeight);
        // 新增意见反馈
        request_back("api_feedback/add_feedback", {
                uid: uid,
                login_token: token,
                content: content,
                data_source: 103,
                param_id: "",
            },
            true,
            function(info) {
                console.log(info)
                if (info.rs_code == "success") {}
            }
        )
    }

    function createRightMsg(content) {
        var str = "<div class='right'><div class='content'><p>" + content +
            "</p><img src='/theme/drcenter/images/doctor-consult-avatar.png'/></div></div>"
        return str
    }

    // 进入页面获取意见反馈列表
    var data = JSON.parse(localStorage.getItem("data"))
    var token = data.login_token || ""
    var uid = data.uid || ""

    request_back(
        "api_feedback/feedback_list", {
            uid: uid,
            login_token: token,
            data_source: 103,
        },
        true,
        function(info) {
            console.log(info)
            if (info.rs_code == "success") {
                var date = new Date();
                info.rs_msg.forEach(function(item, index) {
                    var add_time = item.add_time
                    var msgDate = new Date(1000 * add_time)
                    var year = msgDate.getFullYear();
                    var month = msgDate.getMonth() + 1;
                    var day = msgDate.getDate();
                    //当前年
                    if (date.getFullYear() == year) {
                        item.add_time = month + "月" + day + "日";
                    } else {
                        item.add_time = year + "年" + month + "月" + day + "日";
                    }
                });
                $("#msg").html(template("replyTpl", info))
                //如果是同一天，则当前不显示时间
                $(".time").each(function() {
                    var firstTime = $(this)[0].innerText
                    if ($(this).data("index") == 0) {
                        $(this).show()
                    } else {
                        if ($(this).text() == firstTime) {
                            $(this).hide()
                        }
                    }
                })
                window.scrollTo(0, document.body.scrollHeight)


            }
        })
</script>