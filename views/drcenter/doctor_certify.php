<link rel="stylesheet" type="text/css" href="/theme/drcenter/css/main.css">


<style>
    .search_input {
        padding: 0 !important;
        text-align: center;
        line-height: 0.92rem !important;
    }

    .search_input input {
        width: 95%;
        background: #fff;
        border: 1px solid #ccc;
        height: 80%;
        line-height: 0.92rem;
        padding: 0 10px;
    }

    .select {
        position: absolute;
        width: 100%;
        z-index: 7;
        display: none;
        background: aliceblue !important;
        overflow: auto;
    }

    .practice_ul,
    .job_title_ul {
        height: 4rem;
    }

    .department_ul {
        height: 8rem;
    }

    .select li {
        background: aliceblue !important;
    }

    .tips_dialog {
        display: none;
        position: fixed;
        z-index: 5;
        width: 100%;
        top: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
    }

    .tips_dialog_content {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 70%;
        transform: translate(-50%, -50%);
        background: #fdfef9;
        border: 1px solid #2c5c84;
        border-radius: 5px;
    }

    .tips_dialog_content span {
        display: block;
        margin: 0.45rem 0.25rem;
        font-size: 0.28rem;
        text-align: center;
        line-height: 0.4rem;
        color: #4a4a4a;
    }

    .tips_dialog_content button {
        display: block;
        height: 1rem;
        width: 100%;
        line-height: 1rem;
        font-size: 0.32rem;
        color: #fff;
        text-align: center;
        background: #1faee6;
    }
</style>


<div class="Health-header">
    <h3 class="Health-header-titile">医生认证</h3>
    <a onclick="window.history.go(-1)"><img src="/theme/drcenter/images/comm-back.png" alt="返回" class="Health-header-back"></a>
</div>
<div class="doctor-certification-wrapper">
    <p class="text-title real-name">真实姓名<input class="text-real" placeholder="请输入您的真实姓名" /></p>
    <p class="text-title">任职的医院及地址
        <!-- <img src="/theme/drcenter/images/mine/hospital-add.png" class="hospital-add" id="hospital-add" /> -->
    </p>
    <ul class="hospital-ul" id="hospital-ul">
        <li>
            <input class="text-input border-non unit_id_name" placeholder="添加医院名称" />
            <input class="text-input address" placeholder="添加医院地址" />
        </li>
    </ul>
    <ul class="detail-ul">
        <li>
            <p class="text-title">执业类别</p>
            <input class="text-input practice" readonly="readonly" placeholder="选择您的执业类别" />
            <ul class="select practice_ul">
                <!-- 挖个坑 -->
            </ul>
        </li>
        <li>
            <p class="text-title">职称</p>
            <input class="text-input job_title" readonly="readonly" placeholder="选择您的职位" />
            <ul class="select job_title_ul">
                <li class="text-input" data-val="1">住院医师</li>
            </ul>
        </li>
        <li>
            <p class="text-title">科室</p>
            <input class="text-input department" readonly="readonly" placeholder="选择您所在科室" />
            <ul class="select department_ul">
                <li class="search_input text-input" class="text-input"><input type="text" placeholder="请输入科室查询"></li>
                <li>
                    <ul class="department_list">
                        <!-- 挖个坑 -->
                    </ul>
                </li>
            </ul>
        </li>
        <li>
            <p class="text-title">医师资格证书编码</p>
            <input class="text-input credentials_no" placeholder="请输入医师资格证书编码" />
        </li>
        <li>
            <p class="text-title">医师执业证书编码</p>
            <input class="text-input license_no" placeholder="请输入医师执业证书编码" />
        </li>
    </ul>
    <div class="info-text">
        <p class="text-title">职业特长</p>
        <div>
            <textarea maxlength="200" id="special" class="text-input text-area remark" rows="3" placeholder="写出您的专长，患者更容易找到您"></textarea>
            <span id="special-length">0/200</span>
        </div>
    </div>
    <div class="info-text">
        <p class="text-title">执业经历</p>
        <div>
            <textarea class="text-input text-area last summary" placeholder="写出您的专长，患者更容易找到您"></textarea>
        </div>
    </div>
    <a class="doctor-certification-confirm">提交认证</a>
</div>

<!-- 提示框 -->
<div class="tips_dialog">
    <div class="tips_dialog_content">
        <span class="tips"></span>
        <button>确定</button>
    </div>
</div>


<!-- 执业类别的模板 -->
<script type="text/html" id="practiceTpl">
{{ each practice_sort v i }}
<li class="text-input" data-id="{{v.id}}">{{v.val}}</li>
{{ /each }}
</script>

<!-- 职称的模板 -->
<script type="text/html" id="job_titleTpl">
{{each list v i }}
<li class="text-input" data-id="{{v.id}}">{{v.val}}</li>
{{/each }}
</script>

<!-- 科室的模板 -->
<script type="text/html" id="departmentTpl">
{{ each department_list v i }}
<li class="text-input" data-id="{{v.id}}">{{v.department}}</li>
{{ /each }}
</script>

<script>
    $(function() {
        var data = JSON.parse(localStorage.getItem("data"))
        var token = data.login_token || ""
        var uid = data.uid || ""
        var unit_info_address = JSON.parse(data.unit_info_address) || ""
        // 添加医院
        // $('#hospital-add').click(function() {
        //     var hos_li = $('#hospital-ul').html()
        //     hos_li = hos_li +
        //         "<input class='text-input border-non' placeholder='添加医院名称'/><input class='text-input' placeholder='添加医院地址' />"
        //     $('#hospital-ul').append(hos_li)
        // })
        // 职业特长
        $('#special').bind('input propertychange', function() {
            $('#special-length').html($(this).val().length + '/200')
        })
        // 点击其他地方隐藏下拉框
        $(document).click(function() {
            $(".select").hide()
        })
        // 阻止冒泡
        $(".search_input").click(function(e) {
            e.stopPropagation()
        })
        // 获取字典数据
        var practice_sort;
        var practice_list;
        request_back("api_dic/get_dict", {
                uid: uid,
                login_token: token,
            },
            false,
            function(info) {
                console.log(info)
                practice_list = info.rs_msg
                practice_sort = info.rs_msg.practice_sort
            }
        )
        // 执业类别
        var practice_id;
        $(".practice").click(function(e) {
            e.stopPropagation()
            $(".select").hide()
            $(".practice_ul").html(template("practiceTpl", practice_list))
            $(this).next().show()
            // 选择执业类别
            $(".practice_ul").on("click", "li", function() {
                $(".select").hide()
                $(".practice").val($(this).text())
                $(".practice").attr("data-id", $(this).data("id"))
                practice_id = $(this).data("id")
            })
        })

        // 职称选择
        $(".job_title").on("click", function(e) {
            e.stopPropagation()
            $(".select").hide()
            var job_title;
            if (practice_id) {
                practice_sort.forEach(function(item) {
                    if (item.id == practice_id) {
                        job_title = item.job_title
                    }
                })
                $(".job_title_ul").html(template("job_titleTpl", {
                    list: job_title
                }))
                $(this).next().show()
                // 选择职位
                $(this).next().children("li").click(function() {
                    $(".select").hide()
                    $(".job_title").val($(this).text())
                    $(".job_title").attr("data-id", $(this).data("id"))
                })
            }
        })

        // 科室的选择
        $(".department").click(function(e) {
            e.stopPropagation()
            $(".select").hide()
            $(this).next().show()
            // 获取科室数据
            $(".search_input input").keyup(function() {
                var search_name = $(this).val()
                if (/^[\u2E80-\u9FFF]+$/.test(search_name)) {
                    request_back("api_dic/select_unit_department_n", {
                            uid: uid,
                            login_token: token,
                            type: 2,
                            search_name: search_name,
                        },
                        true,
                        function(info) {
                            $(".department_list").html(template("departmentTpl", info.rs_msg))
                        }
                    )
                }


            })
            // 选择科室
            $(".department_list").on("click", "li", function() {
                $(".select").hide()
                $(".department").val($(this).text())
                $(".department").attr("data-id", $(this).data("id"))
            })

        })

        // 信息填充
        $(".real-name input").val(data.name || "")
        $(".unit_id_name").val(unit_info_address[0].unit_name)
        $(".address").val(unit_info_address[0].unit_address)
        $(".practice").val(data.practice_sort_name || " ")
        $(".practice").attr("data-id", data.practice_sort || " ")
        $(".job_title").val(data.job_title_name || "")
        $(".job_title").attr("data-id", data.job_title || " ")
        $(".department").val(data.department_name || "")
        $(".department").attr("data-id", data.department || "")
        $(".credentials_no").val(data.credentials_no || "")
        $(".license_no").val(data.license_no || "")
        $(".remark").val(data.remark || "")
        $('.summary').val(data.summary || "")

        // 提交认证
        $('.doctor-certification-confirm').click(function() {
            var unit_info_address = [];
            $("#hospital-ul li").each(function() {
                unit_info_address.push({
                    unit_name: $(this).children(".unit_id_name").val(),
                    unit_address: $(this).children(".address").val(),
                })
            })
            var data = {
                uid: uid,
                login_token: token,
                data_from: 2,
                unit_info_address: unit_info_address,
                practice_sort: $(".practice").data("id"),
                job_title: $(".job_title").data("id"),
                department: $(".department").data("id"),
                credentials_no: $(".credentials_no").val(),
                license_no: $(".license_no").val(),
                remark: $(".remark").val(),
                summary: $(".summary").val()
            }
            request_back(
                "api_user/up_profile_n",
                data, true,
                function(info) {
                    console.log(info)
                    if (info.rs_code == "error") {
                        $(".tips_dialog_content .tips").text(info.rs_msg)
                        $(".tips_dialog").show()
                    } else if (info.rs_code == "success") {
                        localStorage.setItem("data", JSON.stringify(info.rs_msg))
                        window.history.go(-1)
                    }
                })
        })

        // 点击确定隐藏对话框
        $(".tips_dialog_content button").click(function() {
            $(".tips_dialog").hide()
        })
    })
</script>