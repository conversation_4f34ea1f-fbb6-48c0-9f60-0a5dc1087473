<link rel="stylesheet" type="text/css" href="/theme/drcenter/css/main.css">

<div class="Health-header">
    <h3 class="Health-header-titile">我的二维码</h3>
    <a onclick="window.history.go(-1)"><img src="/theme/drcenter/images/comm-back.png" alt="返回" class="Health-header-back"></a>
</div>
<div class="mine-qr-code-wrapper">
    <div class="mine-qr-code-content">
        <div class="mine-qr-code-info">
            <img class="mine-qr-code-info-avatar" src="/theme/drcenter/images/doctor-consult-avatar.png" />
            <div class="mine-qr-code-info-right">
                <p class="mine-qr-code-info-name">杜仲</p>
                <p class="mine-qr-code-info-address">上海</p>
            </div>
        </div>
        <img class="mine-qr-code-img" src="/theme/drcenter/images/mine/erweima1.png" />
        <p class="mine-qr-code-tip">扫一扫上面的二维码，加我微信</p>
    </div>
    <!-- <p class="mine-qr-code-scan">您也可以切换<span class="mine-qr-code-scan-detail">「扫一扫」</span></p> -->
</div>

<script>
    // 数据渲染
    var data = JSON.parse(localStorage.getItem("data"))
    var address = data.area_info.split("->")[0] || ""

    $(".mine-qr-code-info-avatar").attr("src", data.avatar || '/theme/drcenter/images/default_auther.jpg')
    $('.mine-qr-code-info-name').text(data.name || "")
    $(".mine-qr-code-info-address").text(address)
    $(".mine-qr-code-img").attr("src", data.doc_qrcode || '')
</script>