<link rel="stylesheet" type="text/css" href="/theme/drcenter/css/main.css">
<div class="Health-header">
    <h3 class="Health-header-titile">个人资料认证</h3>
    <a onclick="window.history.go(-1)"><img src="/theme/drcenter/images/comm-back.png" alt="返回" class="Health-header-back"></a>
</div>
<div class="mine-authentication-wrapper">
    <div class="mine-authentication-content">
        <ul>
            <li class="mine-authentication-item">
                <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>我的二维码</p><a href="/drcenter/dr/mine_qrcode"><img class="mine-authentication-erweima" src="/theme/drcenter/images/mine/erweima-icon.png" /></a>
            </li>
            <li class="mine-authentication-item mine-authentication-item-flex">
                <p id="proince-click" class="mine-authentication-left mine-authentication-left-flex"><span class="mine-authentication-left-tip">*</span>省份<span class="mine-authentication-country" id="proince">
                        <font color="#ccc">请选择</font>
                    </span></p>
                <p id="city-click" class="mine-authentication-left mine-authentication-left-flex mine-authentication-country-1">城市<span class="mine-authentication-country mine-authentication-country-2" id="city">
                        <font color="#ccc">请选择</font>
                    </span></p>
                <p id="district-click" class="mine-authentication-left mine-authentication-left-flex mine-authentication-country-2">区域<span class="mine-authentication-country mine-authentication-country-3" id="district">
                        <font color="#ccc">请选择</font>
                    </span></p>
            </li>
            <li class="mine-authentication-item mine-authentication-item-flex"">
                <p class=" mine-authentication-left"><span class="mine-authentication-left-tip">*</span>姓<i class="mine-authentication-two-space"></i>名</p>
                <input class="mine-authentication-right-flex mine-authentication-right-input" id="name" value="杜仲" />
                <i id="del-name" class="mine-authentication-del"></i>
            </li>
            <li class="mine-authentication-item">
                <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>性<i class="mine-authentication-two-space"></i>别</p>
                <div class="mine-authentication-right-sex"><input class="mine-authentication-sex" name="sex" checked type="radio" value="1" />男</input><input class="mine-authentication-sex mine-authentication-right-input" name="sex" type="radio" value="2" />女</input></div>
            </li>
            <li class="mine-authentication-item mine-authentication-item-flex">
                <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>出生日期</p>
                <p class="mine-authentication-right-flex" onfocus="this.blur();" id="birthday">1990-9-10</p><i class="mine-authentication-direction"></i>
            </li>
            <li class="mine-authentication-item mine-authentication-item-flex">
                <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>手机号码</p>
                <p class="mine-authentication-right-flex" id="mobile" contenteditable="true" style="outline:none;padding-right:15px;">15985866257</p>
                <!-- <span class="mine-authentication-edit">修改</span> -->
            </li>
            <li class="mine-authentication-item mine-authentication-item-flex">
                <p class="mine-authentication-left"><span class="mine-authentication-left-tip mine-authentication-left-tip-hide">*</span>邮<i class="mine-authentication-two-space"></i>箱</p>
                <input class="mine-authentication-right-flex mine-authentication-right-input" id="email" value="" placeholder="请填写" />
                <i id="del-email" class="mine-authentication-del"></i>
            </li>
            <li class="mine-authentication-item mine-authentication-item-flex">
                <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>身份证</p>
                <input class="mine-authentication-right-flex mine-authentication-right-input" id="idcard" value="" placeholder="请填写身份证号" />
                <i id="del-idcard" class="mine-authentication-del"></i>
            </li>
        </ul>
    </div>
    <p class="mine-authentication-info-tip"></p>
    <a class="mine-authentication-confirm">确定</a>
    <!-- 级联选择器盒子 -->
    <div class="mine-authentication-bottom-choose">
        <div class="mine-authentication-bottom-content">
            <ul class="mine-authentication-bottom-content-ul proince_list">
                <!-- 挖个坑 -->
            </ul>
        </div>
    </div>
</div>

<script type="text/html" id="proinceTpl">
{{ each rs_msg v i }}
<li class='mine-authentication-bottom-content-item' data-id="{{v.id}}"> {{v.val}} </li>
{{ /each }}
</script>
<script src="/theme/drcenter/js/dataPicker.js"></script>
<script>
    // 获取会员数据
    var data = JSON.parse(localStorage.getItem("data"))
    var token = data.login_token || ""
    var uid = data.uid || ""
    var area_info = data.area_info.split("->")

    // 信息填充
    $("#proince").text(area_info[0] || "")
    $("#proince").attr("data-id", data.province)
    $("#city").text(area_info[1] || "")
    $("#city").attr("data-id", data.city)
    $("#district").text(area_info[2] || "")
    $("#district").attr("data-id", data.district)
    $("#name").val(data.name || "")
    var gender = data.gender || ""
    $("input[value=" + gender + "][name='sex']").prop("checked", "checked")
    $("#birthday").text(data.birthday || "")
    $("#mobile").text(data.mobile || "")
    $("#email").val(data.email || "")
    $("#idcard").val(data.indentity_code || "")

    // 点击其他地方隐藏城市选择器
    $(document).click(function() {
        $('.mine-authentication-bottom-choose').hide()
    })

    // 获取地区数据
    function get_area_info(id) {
        request_back("api_dic/get_area_info", {
                uid: uid,
                login_token: token,
                id: id
            },
            true,
            function(info) {
                $(".proince_list").html(template("proinceTpl", info))
                $('.mine-authentication-bottom-choose').show()
            })
    }

    // 获取省份数据
    $("#proince").click(function() {
        get_area_info()
        // 选择省份
        $(".proince_list").off("click").on("click", "li", function() {
            // 设置省份
            var id = $(this).data('id')
            $("#proince").text($(this).text())
            $("#proince").attr("data-id", id)
            // 初始化城市和区域
            $("#city").html('<font color="#ccc">请选择</font>')
            $("#city").attr("data-id", "")
            $("#district").html('<font color="#ccc">请选择</font>')
            $("#district").attr("data-id", "")
            // 隐藏城市选择器
            $('.mine-authentication-bottom-choose').hide()
        })
    })

    // 获取城市数据
    $("#city").click(function() {
        var id = $("#proince").attr("data-id")
        if (id) {
            get_area_info(id)
        }
        // 选择城市
        $(".proince_list").off("click").on("click", "li", function() {
            // 设置城市
            var id = $(this).data('id')
            $("#city").text($(this).text())
            $("#city").attr("data-id", id)
            // 初始化区域
            $("#district").html('<font color="#ccc">请选择</font>')
            $("#district").attr("data-id", "")
            // 隐藏城市选择器 
            $('.mine-authentication-bottom-choose').hide()
        })

    })

    // 获取区域数据
    $("#district").click(function() {
        var id = $("#city").attr("data-id")
        if (id) {
            get_area_info(id)
        }
        // 选择城市
        $(".proince_list").off("click").on("click", "li", function() {
            // 设置区域
            var id = $(this).data('id')
            $("#district").text($(this).text())
            $("#district").attr("data-id", id)
            // 隐藏城市选择器 
            $('.mine-authentication-bottom-choose').hide()
        })

    })

    // 默认隐藏的元素
    $('#del-email').hide()
    $('#del-idcard').hide()
    $('.mine-authentication-bottom-choose').hide()

    //清除姓名
    $('#del-name').click(function() {
        $("#name").val('')
        $(this).hide()
        $("#name").focus()
    })
    //监听姓名输入
    $('#name').bind('input propertychange', function() {
        if ($(this).val() != '') {
            $('#del-name').show()
        } else {
            $('#del-name').hide()
        }
    })

    // //清除邮箱
    $('#del-email').click(function() {
        $("#email").val('')
        $(this).hide()
        $("#email").focus()
    })
    //监听邮箱输入
    $('#email').bind('input propertychange', function() {
        if ($(this).val() != '') {
            $('#del-email').show()
        } else {
            $('#del-email').hide()
        }
    })

    //清除身份证号
    $('#del-idcard').click(function() {
        $("#idcard").val('')
        $(this).hide()
        $("#idcard").focus()
    })
    //监听身份证号
    $('#idcard').bind('input propertychange', function() {
        if ($(this).val() != '') {
            $('#del-idcard').show()
        } else {
            $('#del-idcard').hide()
        }
    })

    // 日期选择器
    var calendar = new datePicker();
    calendar.init({
        'trigger': '#birthday',
        /*选择器，触发弹出插件*/
        'type': 'date',
        /*date 调出日期选择 datetime 调出日期时间选择 time 调出时间选择 ym 调出年月选择*/
        'minDate': '1900-1-1',
        /*最小日期*/
        'maxDate': new Date().getFullYear() + '-' + (new Date().getMonth() + 1) + '-' + new Date()
            .getDate(),
        /*最大日期*/
        'onSubmit': function() {
            /*确认时触发事件*/
            var theSelectData = calendar.value;
            $('#birthday').html(theSelectData)
        },
        'onClose': function() {
            /*取消时触发事件*/
        }
    });

    // 编辑个人信息
    $(".mine-authentication-confirm").click(function() {
        var data = {
            uid: uid,
            login_token: token,
            data_from: 1,
            province: $("#proince").data("id"),
            city: $("#city").data("id"),
            district: $("#district").data("id"),
            name: $("#name").val(),
            gender: $("input[name='sex']:checked").val(),
            birthday: $("#birthday").text(),
            mobile: $("#mobile").text(),
            email: $("#email").val(),
            identity_card: $("#idcard").val()
        }
        request_back(
            "api_user/up_profile_n",
            data, true,
            function(info) {
                console.log(info)
                if (info.rs_code == "error") {
                    $(".mine-authentication-info-tip").text(info.rs_msg)
                } else if (info.rs_code == "success") {
                    localStorage.setItem("data", JSON.stringify(info.rs_msg))
                    window.history.go(-1)
                }
            })

    })


    //获取省份数据
    // var proinceArr = []
    // var cityArr = ['上海市']
    // window.allProvice = provice
    // provice.map(function(item) {
    //     proinceArr.push(item.name)
    // })
    // $('#proince-click').click(function() {
    //     //选择省份
    //     //清除城市数据
    //     cityArr = []
    //     $('#city').html('')
    //     let liArr = ''
    //     proinceArr.map(function(item) {
    //         liArr = liArr + createListItem(item)
    //     })
    //     showBottomSelecItem(liArr)
    //     $('.mine-authentication-bottom-content-item').click(function() {
    //         //设置城市数据
    //         cityArr = []
    //         var cityDataArr = provice[$(this).index()]
    //         console.log(cityDataArr.name)
    //         //设置省份
    //         $('#proince').html(cityDataArr.name)
    //         cityDataArr.city.map(function(cityItem) {
    //             cityArr.push(cityItem.name)
    //         })

    //         $('.mine-authentication-bottom-choose').hide()
    //     })

    // })

    // $('#city-click').click(function() {
    //     //选择城市
    //     let liArr = ''
    //     cityArr.map(function(item) {
    //         liArr = liArr + createListItem(item)
    //     })
    //     showBottomSelecItem(liArr)
    //     $('.mine-authentication-bottom-content-item').click(function() {
    //         //设置城市数据
    //         $('#city').html(cityArr[$(this).index()])
    //         $('.mine-authentication-bottom-choose').hide()
    //     })

    // })

    //创建列表数据
    // function createListItem(item) {
    //     return "<li class='mine-authentication-bottom-content-item'>" + item +
    //         "</li>"
    // }
    // //显示列表数据
    // function showBottomSelecItem(html) {
    //     $('.mine-authentication-bottom-content-ul').html('')
    //     $('.mine-authentication-bottom-content-ul').html(html)
    //     $('.mine-authentication-bottom-choose').show()
    // }



    // 获取地区
    // 省份
    // request_back("api_dic/get_area_info", {
    //         uid: uid,
    //         login_token: token
    //     },
    //     false,
    //     function(info) {
    //         proince_val = info.rs_msg
    //     })

    // proince_val.forEach(function(item) {
    //     if (item.id == data.province) {
    //         $("#proince").text(item.val)
    //     }
    // })

    // // 城市
    // request_back("api_dic/get_area_info", {
    //         uid: uid,
    //         login_token: token,
    //         id: data.city
    //     },
    //     false,
    //     function(info) {
    //         city_val = info.rs_msg
    //     })
    // city_val.forEach(function(item) {
    //     if (item.id == data.city) {
    //         $("#city").text(item.val)
    //     }
    // })
</script>