<link rel="stylesheet" href="/theme/drcenter/css/Statistics.css" />
<script type="text/javascript" src="/theme/drcenter/js/Response.js"></script>

<div class="Statistics-bg">
	<div class="Statistics-header">
		<h3 class="Statistics-header-titile">统计报表</h3>
		<a onclick="window.history.go(-1)"><img src="/theme/drcenter/images/home/<USER>" alt="返回" class="Statistics-header-back"></a>
	</div>
	<div class="Statistics-warpper">
		<div class="Statistics-header-box">
			<h4>关于晚期/转移性肺癌治疗研究</h4>
			<ul class="header-box-cont">
				<li>已回答：<span>15人</span></li>
				<li>未回答：<span>5人</span></li>
				<li>完成率：<span>75%</span></li>
				<div class="Statistics-clearFix"></div>
			</ul>
			<div class="Statistics-down">
				<a><img src="/theme/drcenter/images/Statistics-down.png" alt="下载格式"></a>
				<div class="Statistics-down-format">
					<div class="Statistics-down-Tit">
						<h3>下载</h3>
						<a class="Statistics-down-colse">取消</a>
					</div>
					<div class="Statistics-down-Cont">
						<ul class="Statistics-down-Elex">
							<li>
								<h4>PDF格式</h4>
								<p>统计图表</p>
							</li>
							<li>
								<h4>XLS格式</h4>
								<p>统计图表和表格</p>
							</li>
						</ul>
					</div>
				</div>
				<div class="Statistics-down-mask"></div>
			</div>
		</div>
		<div class="Statistics-content" id="tabsTiao">
			<div class="Statistics-content-item">
				<div class="Statistics-content-item-tit">
					<h3>是否有非癌症合并症？是哪种？</h3>
					<a><img src="/theme/drcenter/images/Statistics-menu.png" alt="图形类别" class="Statistics-menu"></a>
					<div class="Statistics-menu-chart">
						<ul>
							<li><label><img src="/theme/drcenter/images/Statistics-chart01-check.png"></label>柱形图</li>
							<li class="scolTiao"><label><img src="/theme/drcenter/images/Statistics-chart02.png"></label>条形图</li>
							<li><label><img src="/theme/drcenter/images/Statistics-chart03.png"></label>雷达图</li>
							<li class="scolBing"><label><img src="/theme/drcenter/images/Statistics-chart04.png"></label>饼&nbsp;图</li>
						</ul>
					</div>
				</div>
				<ul class="Statistics-section-box">
					<li><label class="Statistics-section-item1">呼吸系统</label><span>1人</span></li>
					<li><label class="Statistics-section-item2">心脏</label><span>1人</span></li>
					<li><label class="Statistics-section-item3">高血压</label><span>3人</span></li>
					<li><label class="Statistics-section-item4">其他</label><span>5人</span></li>
					<li><label class="Statistics-section-item5">未见记录中有非癌症合并症</label><span>5人</span></li>
				</ul>
				<div class="Statistics-clearFix"></div>
			</div>
			<div class="Statistics-news-hide">
				<h3><label>选项</label><span>恢复情况</span></h3>
				<ul class="Statistics-news-list">
					<li><b class="Statistics-news-dot1"></b><label>呼吸系统</label><span>1人</span></li>
					<li><b class="Statistics-news-dot2"></b><label>心脏</label><span>1人</span></li>
					<li><b class="Statistics-news-dot3"></b><label>高血压</label><span>3人</span></li>
					<li><b class="Statistics-news-dot4"></b><label>其他</label><span>5人</span></li>
					<li><b class="Statistics-news-dot5"></b><label>未见记录中有非癌症合并症</label><span>5人</span></li>
				</ul>
			</div>
			<div class="Statistics-news-btn"><b></b><label>隐藏具体数据 <span><img src="/theme/drcenter/images/Statistics-hide.png" class="S-hide-dot"></span></label><b></b></div>
			<p class="Statistics-people-sum">共15人回答</p>
		</div>
		<div class="Statistics-content" id="tabsBing">
			<div class="Statistics-content-item">
				<div class="Statistics-content-item-tit">
					<h3>是否有非癌症合并症？</h3>
					<a><img src="/theme/drcenter/images/Statistics-menu.png" alt="图形类别" class="Statistics-menu"></a>
					<div class="Statistics-menu-chart2">
						<ul>
							<li><label><img src="/theme/drcenter/images/Statistics-chart01.png"></label>柱形图</li>
							<li class="scolTiao"><label><img src="/theme/drcenter/images/Statistics-chart02.png"></label>条形图</li>
							<li><label><img src="/theme/drcenter/images/Statistics-chart03.png"></label>雷达图</li>
							<li class="scolBing"><label><img src="/theme/drcenter/images/Statistics-chart04-check.png"></label>饼&nbsp;图</li>
						</ul>
					</div>
				</div>
				<div class="Statistics-news-biao">
					<img src="/theme/drcenter/images/Statistics-group.png" alt="表图" />
				</div>
				<div class="Statistics-clearFix"></div>
			</div>
			<div class="Statistics-news-hide">
				<h3><label>选项</label><span>恢复情况</span></h3>
				<ul class="Statistics-news-list">
					<li><b class="Statistics-news-dot1"></b><label>肺栓塞</label><span>5人</span></li>
					<li><b class="Statistics-news-dot2"></b><label>矽肺</label><span>10人</span></li>
					<li><b class="Statistics-news-dot3"></b><label>肺结核</label><span>11人</span></li>
					<li><b class="Statistics-news-dot4"></b><label>慢性柱塞性肺炎</label><span>15人</span></li>
					<li><b class="Statistics-news-dot5"></b><label>肺间</label><span>14人</span></li>
					<li><b class="Statistics-news-dot6"></b><label>质纤维化</label><span>8人</span></li>
					<li><b class="Statistics-news-dot7"></b><label>哮喘</label><span>12人</span></li>
					<li><b class="Statistics-news-dot2"></b><label>矽肺</label><span>7人</span></li>
				</ul>
			</div>
			<div class="Statistics-news-btn"><b></b><label>隐藏具体数据 <span><img src="/theme/drcenter/images/Statistics-hide.png" class="S-hide-dot"></span></label><b></b></div>
			<p class="Statistics-people-sum">共15人回答</p>
		</div>
	</div>
</div>

<script>
	//		下载数据格式
	$('.Statistics-down a').on('click', function() {
		$(this).next().slideDown(200);
		$('.Statistics-down-mask').fadeIn(100);
	})
	$('.Statistics-down-colse').on('click', function() {
		$('.Statistics-down-format').slideUp(200);
		$('.Statistics-down-mask').fadeOut(100);
	})
	$('.Statistics-down-mask').on('click', function() {
		$('.Statistics-down-format').slideUp(200);
		$('.Statistics-down-mask').fadeOut(100);
	})

	//		隐藏信息内容
	$(".Statistics-news-btn").click(function() {
		if ($(this).hasClass("ul_zan")) {

			$(this).removeClass("ul_zan")
			$(this).siblings('.Statistics-news-hide').slideDown(300);
			$('.S-hide-dot').css('transform', 'rotateX(0deg)');
		} else {

			$(this).addClass("ul_zan");
			$(this).siblings('.Statistics-news-hide').slideUp(300);
			$('.S-hide-dot').css('transform', 'rotateX(180deg)');
		}
	});

	//		报表菜单展开
	$('.Statistics-content-item-tit a').click(function() {
		$(this).next().slideToggle();
	})

	$('.Statistics-menu-chart li').eq(0).addClass('Statistics-active');
	$('.Statistics-menu-chart li').click(function() {
		$(this).addClass('Statistics-active').siblings().removeClass('Statistics-active');
		for (var j = 0; j < 4; j++) {
			$('.Statistics-menu-chart li').eq(j).find('img').attr('src', '/theme/drcenter/images/Statistics-chart0' + (j + 1) + '.png');
		}
		$(this).find('img').attr('src', '/theme/drcenter/images/Statistics-chart0' + (parseInt($(this).index()) + 1) + '-check.png');
	})


	$('.Statistics-menu-chart2 li').eq(3).addClass('Statistics-active');
	$('.Statistics-menu-chart2 li').click(function() {
		$(this).addClass('Statistics-active').siblings().removeClass('Statistics-active');
		for (var j = 0; j < 4; j++) {
			$('.Statistics-menu-chart2 li').eq(j).find('img').attr('src', '/theme/drcenter/images/Statistics-chart0' + (j + 1) + '.png');
		}
		$(this).find('img').attr('src', '/theme/drcenter/images/Statistics-chart0' + (parseInt($(this).index()) + 1) + '-check.png');
	})


	//	点击图表到指定位置
	$(function() {
		var Tiao_top = $('#tabsTiao').offset().top;
		$('.scolTiao').click(function() {
			$('html,body').animate({
				scrollTop: Tiao_top
			}, 500);
			$('.Statistics-menu-chart').slideUp(300);
			$('.Statistics-menu-chart2').slideUp(300);
		})


		var Bing_top = $('#tabsBing').offset().top;
		$('.scolBing').click(function() {
			$('html,body').animate({
				scrollTop: Bing_top
			}, 500);
			$('.Statistics-menu-chart2').slideUp(300);
			$('.Statistics-menu-chart').slideUp(300);
		})
	})
</script>