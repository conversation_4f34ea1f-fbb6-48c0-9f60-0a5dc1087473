<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>index</title>
    <style>
      /* 样式初始化 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      li {
        list-style: none;
      }
      /* 清浮动 */
      .clearfix::before,
      .clearfix::after {
        content: "";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }
      /* IE 6/7 */
      .clearfix {
        zoom: 1;
      }

      .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
      }

      body {
        background: #ccc;
      }

      .container {
        width: 800px;
        height: 1130px;
        margin: 0 auto;
        background: #fff;
        position: relative;
        overflow: hidden;
        color: #082e4d;
      }

      .header {
        position: absolute;
        top: -15px;
        width: 100%;
      }
      .left {
        float: left;
        width: 40%;
        padding-left: 60px;
        padding-top: 320px;
        font-size: 12px;
        font-weight: 600;
      }

      .skills,
      .language {
        margin-bottom: 50px;
      }
      .skills h4,
      .language h4,
      .hobbies h4 {
        position: relative;
        width: 230px;
        height: 45px;
        font-size: 22px;
        border-radius: 50px;
        background: #d9dcde;
        color: #fff;
        margin-left: 20px;
        margin-bottom: 30px;
      }
      .skills h4 span,
      .language h4 span,
      .hobbies h4 span {
        position: absolute;
        text-align: center;
        width: 230px;
        height: 45px;
        line-height: 45px;
        border-radius: 50px;
        background: #39a1d0;
        left: -5px;
        top: -5px;
      }

      .skills p,
      .language p {
        padding: 8px 0;
      }

      .skills p .desc,
      .language p .desc {
        display: inline-block;
        width: 85px;
      }

      .skills p .process,
      .language p .process {
        display: inline-block;
        height: 10px;
        width: 160px;
        border-radius: 20px;
        background: #39a1d0;
        overflow: hidden;
        position: relative;
      }
      .skills p .process .details,
      .language p .process .details {
        position: absolute;
        left: 0;
        height: 100%;
        border-radius: 20px;
        background: #04205b;
      }

      .hobbies p span {
        display: inline-block;
        position: relative;
        padding-left: 30px;
        width: 48%;
        height: 30px;
        line-height: 30px;
      }
      .hobbies p span::before {
        content: "";
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background: #39a1d0;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .right {
        float: right;
        width: 55%;
        height: 100%;
        padding-top: 60px;
      }

      .user_info {
        font-size: 12px;
        margin-top: 25px;
        margin-bottom: 50px;
      }

      .user_info p {
        margin-bottom: 20px;
      }
      .user_info p .info {
        display: inline-block;
        width: 48%;
      }

      .user_info p .info span {
        display: inline-block;
        width: 20px;
        height: 20px;
        color: #fff;
        background: #39a1d0;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
      }

      .profile,
      .education {
        margin-bottom: 20px;
      }
      .profile h4,
      .education h4,
      .experience h4 {
        position: relative;
        width: 110%;
        height: 35px;
        font-size: 22px;
        border-radius: 50px;
        background: #d9dcde;
        color: #fff;
        margin-bottom: 10px;
      }

      .profile h4 span,
      .education h4 span,
      .experience h4 span {
        position: absolute;
        padding-left: 35px;
        width: 110%;
        height: 35px;
        line-height: 35px;
        border-radius: 50px;
        background: #04205b;
        left: -5px;
        top: -8px;
      }
      .education .college,
      .experience .company {
        align-items: center;
        justify-content: center;
      }
      .education .college div:first-child,
      .experience .company div:first-child {
        width: 68%;
        margin-bottom: 10px;
      }

      .education .college div:last-child,
      .experience .company div:last-child {
        width: 20%;
        height: 60px;
        font-size: 12px;
        font-weight: 700;
        line-height: 60px;
        padding-left: 10px;
        border-left: 1px solid #39a1d0;
      }
      .profile p {
        font-size: 12px;
        padding: 0px 70px 10px 20px;
        line-height: 14px;
      }

      .footer {
        position: absolute;
        width: 100%;
        bottom: -8px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img style="width: 100%;" src="/theme/drcenter/invoice/img/header.png" alt="" />
      </div>
      <div class="left">
        <div class="skills">
          <h4>
            <span>SKILLS</span>
          </h4>
          <p>
            <span class="desc">Writing</span>
            <span class="process">
              <span style="width: 95%;" class="details"></span>
            </span>
          </p>
          <p>
            <span class="desc">SEO</span>
            <span class="process">
              <span style="width: 80%;" class="details"></span>
            </span>
          </p>
          <p>
            <span class="desc">Analytics</span>
            <span class="process">
              <span style="width: 70%;" class="details"></span>
            </span>
          </p>
          <p>
            <span class="desc">Creative</span>
            <span class="process">
              <span style="width: 80%;" class="details"></span>
            </span>
          </p>
          <p>
            <span class="desc">Open Mind</span>
            <span class="process">
              <span style="width: 85%;" class="details"></span>
            </span>
          </p>
        </div>

        <div class="language">
          <h4>
            <span style="background: #04205b;">LANGUAGE</span>
          </h4>
          <p>
            <span class="desc">English</span>
            <span class="process">
              <span style="width: 95%;" class="details"></span>
            </span>
          </p>
          <p>
            <span class="desc">Spanish</span>
            <span class="process">
              <span style="width: 80%;" class="details"></span>
            </span>
          </p>
          <p>
            <span class="desc">Indian</span>
            <span class="process">
              <span style="width: 70%;" class="details"></span>
            </span>
          </p>
        </div>

        <div class="hobbies">
          <h4>
            <span>HOBBIES</span>
          </h4>

          <p>
            <span>Travel</span>
            <span>Reading</span>
          </p>

          <p>
            <span>Music</span>
            <span>Photograph</span>
          </p>
        </div>
      </div>
      <div class="right">
        <div class="john">
          <h2 style="font-size: 52px;">JOHN</h2>
          <h2 style="font-size: 52px; margin-top: -20px;">SMITH</h2>
          <p style="font-size: 20px; margin-top: -10px;">Content Writer</p>
        </div>
        <div class="user_info">
          <p>
            <span class="info">
              <span>E</span>
              <EMAIL>
            </span>
            <span class="info">
              <span>A</span>
              + 098209283038
            </span>
          </p>
          <p>
            <span class="info">
              <span>T</span>
              North Street Avenue 90
            </span>
            <span class="info">
              <span>W</span>
              www.yourcompany.com
            </span>
          </p>
        </div>
        <div class="profile">
          <h4>
            <span>PROFILE</span>
          </h4>

          <p>
            Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam
            nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat
            volutpat.
          </p>
          <p>
            Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper
            suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis
            autem vel eum iriure dolor in hendrerit in vulputate velit esse
            molestie consequat
          </p>
        </div>

        <div class="education">
          <h4>
            <span style="background: #39a1d0;">EDUCATION</span>
          </h4>
          <div class="college flex">
            <div>
              <p style="font-size: 18px; font-weight: 600;">First College</p>
              <p style="font-size: 12px; color: #39a1d0; margin-bottom: 5px;">
                Bachelor of Linguistic
              </p>
              <p style="font-size: 12px;line-height: 12px;font-weight: 300;">
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed
                diam nonummy nibh euismod tincidunt ut laoreet volutpat. Ut wisi
                mimin veniam.
              </p>
            </div>

            <div class="year">
              2010 - 2014
            </div>
          </div>
          <div class="college flex">
            <div>
              <p style="font-size: 18px; font-weight: 600;">Second College</p>
              <p style="font-size: 12px; color: #39a1d0; margin-bottom: 5px;">
                Bachelor of Linguistic
              </p>
              <p style="font-size: 12px;line-height: 12px;font-weight: 300;">
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed
                diam nonummy nibh euismod tincidunt ut laoreet volutpat. Ut wisi
                mimin veniam.
              </p>
            </div>

            <div class="year">
              2010 - 2014
            </div>
          </div>
        </div>

        <div class="experience">
          <h4>
            <span>EXPERIENCE</span>
          </h4>
          <div class="company flex">
            <div>
              <p style="font-size: 18px; font-weight: 600;">First Company</p>
              <p style="font-size: 12px; color: #39a1d0; margin-bottom: 5px;">
                Bachelor of Linguistic
              </p>
              <p style="font-size: 12px;line-height: 12px;font-weight: 300;">
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed
                diam nonummy nibh euismod tincidunt ut laoreet volutpat. Ut wisi
                mimin veniam.
              </p>
            </div>

            <div class="year">
              2010 - 2014
            </div>
          </div>
          <div class="company flex">
            <div>
              <p style="font-size: 18px; font-weight: 600;">Second Company</p>
              <p style="font-size: 12px; color: #39a1d0; margin-bottom: 5px;">
                Content Writer
              </p>
              <p style="font-size: 12px;line-height: 12px;font-weight: 300;">
                Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed
                diam nonummy nibh euismod tincidunt ut laoreet volutpat. Ut wisi
                mimin veniam.
              </p>
            </div>

            <div class="year">
              2010 - 2014
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <img style="width: 100%;" src="/theme/drcenter/invoice/img/footer.png" alt="" />
      </div>
    </div>
  </body>
</html>
