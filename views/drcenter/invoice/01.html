<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <style>
      /* 样式初始化 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      li {
        list-style: none;
      }
      /* 清浮动 */
      .clearfix::before,
      .clearfix::after {
        content: "";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }
      /* IE 6/7 */
      .clearfix {
        zoom: 1;
      }

      .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
      }

      body {
        background: #f5f5f5;
      }

      .container {
        width: 800px;
        height: 1130px;
        margin: 0 auto;
        background: #fff;
        position: relative;
        overflow: hidden;
        padding-bottom: 35px;
        color: #082e4d;
      }

      /* 头部 */
      .container .header {
        height: 160px;
        position: relative;
      }
      .header::after {
        content: "";
        width: 85%;
        height: 25%;
        background: #082e4d;
        position: absolute;
        left: -12px;
        bottom: 0;
        transform: skew(-30deg);
        -ms-transform: skew(-30deg);
        -moz-transform: skew(-30deg);
        -webkit-transform: skew(-30deg);
        -o-transform: skew(-30deg);
      }

      .container .header .title {
        font-size: 60px;
        font-weight: 700;
        color: #082e4d;
        position: absolute;
        left: 50px;
        top: 40px;
        letter-spacing: 5px;
      }

      .container .header .brand {
        background: #f74912;
        height: 85%;
        width: 65%;
        color: #fff;
        border-bottom: 15px solid #fff;
        /* border-left: 15px solid #fff; */
        transform: skew(-30deg);
        -ms-transform: skew(-30deg);
        -moz-transform: skew(-30deg);
        -webkit-transform: skew(-30deg);
        -o-transform: skew(-30deg);
        position: absolute;
        left: 50%;
        top: 0;
        z-index: 1;


      }

      .container .header .brand::before {
        content: "";
        height: 100%;
        width: 21px;
        background: #fff;
        position: absolute;
        left: -1px;
        top: 0;
      }

      .container .header .brand::after {
        content: "";
        height: 100%;
        width: 20px;
        background: #fff;
        position: absolute;
        left: 60px;
        top: 0;
      }

      .container .header .brand img,
      .container .header .brand div {
        transform: skew(30deg);
        -ms-transform: skew(30deg);
        -moz-transform: skew(30deg);
        -webkit-transform: skew(30deg);
        -o-transform: skew(30deg);
      }
      .container .header .brand img {
        /*width: 200px;*/
        /*position: absolute;*/
        /*top: 15%;*/
        /*left: 25%;*/

        height: 70px;
        position: absolute;
        bottom: 20px;
        right: 40px;
      }

      /* 主要内容 */
      .container .content {
        padding: 10px 30px;
      }

      /* 票头 */
      .invoice_title ul li {
        float: left;
        width: 20%;
        font-size: 16px;
        font-weight: 600;
        height: 140px;
        flex-direction: column;
        justify-content: center;
      }

      .invoice_title ul li:first-child {
        width: 60%;
      }
      .invoice_title ul li:last-child {
        text-align: right;
      }

      /* 发票明细 */
      .detail {
        padding-top: 40px;
      }

      .detail ul li {
        font-size: 12px;
        height: 65px;
        line-height: 65px;
        text-align: center;
        border-bottom: 1px solid #ccc;
      }

      .detail ul .title {
        height: 55px;
        line-height: 55px;
        border: 2px solid #f74912;
        font-weight: 700;
        font-size: 14px;
      }

      .detail ul li p {
        width: 15%;
      }
      .detail ul li p:first-child {
        width: 10%;
      }

      .detail ul li .item {
        width: 40%;
        text-align: left;
      }

      /* 明细总结 */
      .epilogue ul {
        font-size: 12px;
        justify-content: space-between;
        padding: 40px 0 40px 10px;
      }
      .epilogue ul li:first-child div p {
        padding: 1px 0;
      }
      .epilogue ul li:first-child div span:first-child {
        display: inline-block;
        width: 80px;
      }

      .epilogue ul li:first-child div span:last-child {
        display: inline-block;
        min-width: 125px;
      }

      .epilogue ul li:last-child {
        font-weight: 700;
      }

      .epilogue ul li:last-child p {
        padding: 3px 10px 3px 0;
        font-size: 14px;
      }

      .epilogue ul li:last-child p span:first-child {
        display: inline-block;
        width: 90px;
      }

      .epilogue ul li:last-child p span:last-child {
        display: inline-block;
        min-width: 65px;
      }

      /* 签名确认 */
      .autograph ul {
        font-size: 12px;
        justify-content: space-between;
        padding: 10px 0 20px 10px;
      }
      .autograph ul li:last-child {
        font-weight: 700;
        margin-top: 40px;
      }
      .autograph ul li:last-child p {
        text-align: center;
      }
      .autograph ul li:last-child p:first-child {
        min-width: 180px;
        border-radius: 0;
        border: none;
        outline: none;
        border-bottom: 1px solid #000;
        padding: 5px 20px;
        margin-bottom: 5px;
        font-size: 16px;
        text-align: center;
      }
      /* 底部 */
      .container .footer {
        width: 100%;
        height: 60px;
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .container .footer::before {
        content: "";
        height: 70%;
        width: 48%;
        background: #082e4d;
        position: absolute;
        bottom: 0;
        left: 12px;
        transform: skew(-30deg);
        -ms-transform: skew(-30deg);
        -moz-transform: skew(-30deg);
        -webkit-transform: skew(-30deg);
        -o-transform: skew(-30deg);
      }
      .footer::after {
        content: "";
        height: 100%;
        width: 48%;
        background: #f74912;
        position: absolute;
        bottom: 0;
        right: -20px;
        transform: skew(-30deg);
        -ms-transform: skew(-30deg);
        -moz-transform: skew(-30deg);
        -webkit-transform: skew(-30deg);
        -o-transform: skew(-30deg);
      }

      div,
      p,
      span {
        outline: none;
      }
      .boss_qm {
        left: 50%;
        height: 120px;
        position: absolute;
        top: -90px;
        transform: translateX(-50%);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 头部 -->
      <div class="header">
        <span class="title">INVOICE</span>
        <div class="brand">
          <img src="/theme/drcenter/invoice/img/logo_01.png" alt="" />
        </div>
      </div>
      <!-- 主要内容 -->
      <div class="content">
        <!-- 票头 -->
        <div class="invoice_title">
          <ul class="clearfix">
            <li class="flex">
              <p style="font-size: 20px; font-weight: 700;">Invoice to:</p>
              <p
                contenteditable="true"
                style="font-size: 18px; font-weight: 700;"
              >
                Dwyane Clark
              </p>
              <p contenteditable="true">24 Dummy Street Area,</p>
              <p contenteditable="true">Location,Lorem Ipsum</p>
              <p contenteditable="true">570xx59xx</p>
            </li>
            <li class="flex">
              <p>Invoice#</p>
              <p>Date</p>
            </li>
            <li class="flex">
              <p contenteditable="true">52148</p>
              <p contenteditable="true">01 / 02 / 2020</p>
            </li>
          </ul>
        </div>
        <!-- 发票明细  -->
        <div class="detail">
          <ul>
            <li class="title flex">
              <p>SL.</p>
              <p class="item">Item Description</p>
              <p>Price</p>
              <p>Qty.</p>
              <p>Total</p>
            </li>
            <li class="flex">
              <p contenteditable="true">1</p>
              <p contenteditable="true" class="item">Lorem Ipsum Dolor</p>
              <p contenteditable="true">$50.00</p>
              <p contenteditable="true">1</p>
              <p contenteditable="true">$50.00</p>
            </li>
            <li class="flex">
              <p contenteditable="true">1</p>
              <p contenteditable="true" class="item">Lorem Ipsum Dolor</p>
              <p contenteditable="true">$50.00</p>
              <p contenteditable="true">1</p>
              <p contenteditable="true">$50.00</p>
            </li>
            <li class="flex">
              <p contenteditable="true">1</p>
              <p contenteditable="true" class="item">Lorem Ipsum Dolor</p>
              <p contenteditable="true">$50.00</p>
              <p contenteditable="true">1</p>
              <p contenteditable="true">$50.00</p>
            </li>
            <li class="flex">
              <p contenteditable="true">1</p>
              <p contenteditable="true" class="item">Lorem Ipsum Dolor</p>
              <p contenteditable="true">$50.00</p>
              <p contenteditable="true">1</p>
              <p contenteditable="true">$50.00</p>
            </li>
            <li class="flex"></li>
          </ul>
        </div>
        <!-- 明细总结 -->
        <div class="epilogue">
          <ul class="flex">
            <li>
              <div
                style="font-size: 16px; font-weight: 700; margin-bottom: 30px;"
              >
                Thank you for your business
              </div>
              <div>
                <p style="font-size: 14px; font-weight: 700;">
                  Payment Info&nbsp;:
                </p>
                <p>
                  <span>Account#:</span>
                  <span contenteditable="true">1234 5678 9012</span>
                </p>
                <p>
                  <span>A/C Name:</span>
                  <span contenteditable="true">Lorem Ipsum</span>
                </p>
                <p>
                  <span>Bank Deatils:</span>
                  <span contenteditable="true">Add your bank details</span>
                </p>
              </div>
            </li>
            <li>
              <p>
                <span>Sub Total:</span>
                <span contenteditable="true">$220.00</span>
              </p>
              <p
                style="
                  border-bottom: 1px solid #000;
                  padding-bottom: 8px;
                  margin-bottom: 8px;
                "
              >
                <span>Tax:</span>
                <span contenteditable="true">0.00%</span>
              </p>
              <p style="font-size: 16px;">
                <span>Total:</span>
                <span contenteditable="true">$220.00</span>
              </p>
            </li>
          </ul>
        </div>
        <!-- 签名确认 -->
        <div class="autograph">
          <ul class="flex">
            <li>
              <p style="font-size: 14px; font-weight: 700;">
                Terms & Conditions
              </p>
              <p contenteditable="true">Lorem ipsum dolor sit amet,</p>
              <p contenteditable="true">consectetur adipiscing elit Fusce</p>
              <p contenteditable="true">diginssim pretium consectetur</p>
            </li>
            <li >
              <p style="position: relative"><img class="boss_qm"  src="/theme/drcenter/invoice/img/boss_qm.png" /></p>
              <p>Authorised Sign</p>
            </li>
          </ul>
        </div>
      </div>
      <!-- 底部 -->
      <div class="footer"></div>
    </div>
  </body>
</html>
