<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <style>
      /* 样式初始化 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      li {
        list-style: none;
      }
      /* 清浮动 */
      .clearfix::before,
      .clearfix::after {
        content: "";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }
      /* IE 6/7 */
      .clearfix {
        zoom: 1;
      }

      .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
      }

      body {
        background: #ccc;
      }

      .container {
        width: 800px;
        height: 1130px;
        margin: 0 auto;
        background: #fff;
        position: relative;
        color: #024563;
        padding: 0 50px;
        overflow: hidden;
      }
      /* 头部 */
      .header .brand {
        padding: 30px 0px 20px 430px;
        height: 100px;
        line-height: 50px;
        font-size: 28px;
        font-weight: 700;
      }
      .header .brand img {
        width: 50px;
        float: left;
        margin-right: 10px;
      }
      .header .title {
        position: relative;
      }
      .header .title::before {
        content: "";
        width: 6%;
        height: 30px;
        background: #fdc45a;
        position: absolute;
        left: -60px;
        top: 10px;
        transform: skew(30deg);
        -ms-transform: skew(30deg);
        -moz-transform: skew(30deg);
        -webkit-transform: skew(30deg);
        -o-transform: skew(30deg);
      }
      .header .title::after {
        content: "";
        width: 83%;
        height: 30px;
        background: #fdc45a;
        position: absolute;
        right: -60px;
        top: 10px;
        transform: skew(30deg);
        -ms-transform: skew(30deg);
        -moz-transform: skew(30deg);
        -webkit-transform: skew(30deg);
        -o-transform: skew(30deg);
      }

      /* 票头 */
      .invoice_title {
        padding: 30px 0;
      }
      .invoice_title ul {
        justify-content: space-between;
        position: relative;
      }
      .invoice_title ul::before{
        content: "";
        width: 1px;
        height: 100%;
        position: absolute;
        background: #fdc45a;
        left: 50%;
      }
      .invoice_title ul li {
        width: 45%;
        font-size: 14px;
      }
      .invoice_title ul li p {
        height: 25px;
        line-height: 25px;
      }
      .invoice_title ul li .line {
        font-weight: 700;
        border-bottom: 1px solid #fdc45a;
      }

      .invoice_title ul li p span:first-child {
        font-weight: 700;
        display: inline-block;
        width: 80px;
      }

      /* 明细 */
      .detail ul li {
        font-size: 12px;
        height: 30px;
        line-height: 30px;
        text-align: center;
      }
      .detail ul li p {
        width: 12%;
      }
      .detail ul li .item {
        width: 26%;
      }
      .detail ul .title {
        background-color: #024563 !important;
        color: #fdc45a;
      }
      .detail ul li:nth-child(odd) {
        background: #f3f4f6;
      }
      .detail ul li:nth-child(even) {
        background: #fff;
      }
      .detail ul li:not(:first-child) {
        border-bottom: 1px solid #024563;
      }
      .detail ul li p:first-child {
        border-left: 1px solid #024563;
      }

      .detail ul li p {
        border-right: 1px solid #024563;
      }

      /* 明细总结 */
      .epilogue ul {
        font-size: 12px;
        justify-content: space-between;
      }
      .epilogue ul li:first-child {
        padding-top: 30px;
      }
      .epilogue ul li:first-child p {
        height: 20px;
        line-height: 20px;
      }

      .epilogue ul li:last-child p {
        height: 30px;
        line-height: 30px;
      }
      .epilogue ul li:last-child p span {
        display: inline-block;
        width: 84px;
        padding-left: 10px;
        height: 100%;
        vertical-align: bottom;
      }
      .epilogue ul li:last-child p span:first-child {
        background-color: #024563;
        color: #fdc45a;
      }
      .epilogue ul li:last-child p span:last-child {
        border-bottom: 1px solid #024563;
        border-right: 1px solid #024563;
      }

      /* 签名确认 */
      .autograph {
          padding-top: 80px;
      }
      .autograph ul {
          justify-content: space-between;
      }
      .autograph ul li {
          text-align: center;
          font-size: 14px;
      }
      .autograph ul li  input {
          border: none;
          background: none;
          outline: none;
          border-radius: 0;
          width: 200px;
          border-bottom: 1px solid  #fdc45a;
          margin-bottom: 10px;
          text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 头部 -->
      <div class="header">
        <div class="brand">
          <img src="/theme/drcenter/invoice/img/logo.png" alt="" />
          <p>CompanyName</p>
        </div>
        <div class="title">
          <p style="font-size: 38px; font-weight: 700;">INVOICE</p>
          <p style="font-size: 18px; font-weight: 700;">10 November 2019</p>
        </div>
      </div>
      <!-- 主要内容 -->
      <div class="content">
        <!-- 票头 -->
        <div class="invoice_title">
          <ul class="flex">
            <li>
              <p class="line">NAME</p>
              <p class="line">SALESPERSON</p>
              <p><span>PHONE</span><span>123.456.789</span></p>
              <p><span>FAX</span><span>123.456.789</span></p>
              <p><span>EMAIL</span><span><EMAIL></span></p>
              <p><span>WEBSITE</span><span>www.yourcompany.com</span></p>
            </li>
            <li>
              <p class="line">ADDRESS</p>
              <p class="line"></p>
              <p class="line">STATE</p>
              <p class="line">ZIP CODE</p>
              <p class="line">PHONE NUMBER</p>
            </li>
          </ul>
        </div>
        <!-- 发票明细  -->
        <div class="detail">
          <ul>
            <li class="title flex">
              <p>NO</p>
              <p class="item">MODEL</p>
              <p>QTY</p>
              <p class="item">DESCRIPTION</p>
              <p>PRICE</p>
              <p>TOTAL</p>
            </li>

            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
          </ul>
        </div>
        <!-- 明细总结 -->
        <div class="epilogue">
          <ul class="flex">
            <li>
              <p>Return Policy and Terms and Conditions</p>
              <p>1.Lorem ipsum dolor sit amet,consectetur adipiscing elit.</p>
              <p>2.Lorem ipsum dolor sit amet,consectetur adipiscing elit.</p>
              <p>3.Lorem ipsum dolor sit amet,consectetur adipiscing elit.</p>
            </li>
            <li>
              <p><span>SUBTOTAL</span><span></span></p>
              <p><span>TAX</span><span style="background: #f3f4f6;"></span></p>
              <p><span>DISCOUNT</span><span></span></p>
              <p>
                <span>DELIVERY</span><span style="background: #f3f4f6;"></span>
              </p>
              <p><span>TOTAL</span><span></span></p>
            </li>
          </ul>
        </div>
        <!-- 签名确认 -->
        <div class="autograph">
          <ul class="flex">
            <li>
              <p><input type="text" /></p>
              <p>Customern Signature</p>
            </li>
            <li>
              <p><input type="text" /></p>
              <p>Store Owner</p>
            </li>
          </ul>
        </div>
      </div>
      <!-- 底部 -->
      <div class="footer"></div>
    </div>
  </body>
</html>
