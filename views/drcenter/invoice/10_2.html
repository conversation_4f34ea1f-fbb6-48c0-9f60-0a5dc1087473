<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <style>
      /* 样式初始化 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      li {
        list-style: none;
      }
      /* 清浮动 */
      .clearfix::before,
      .clearfix::after {
        content: "";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }
      /* IE 6/7 */
      .clearfix {
        zoom: 1;
      }

      .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
      }

      body {
        background: #ccc;
      }

      .container {
        width: 800px;
        height: 1130px;
        margin: 0 auto;
        background: #fff;
        position: relative;
        padding: 100px 80px 0;
      }

      .header_10 {
        width: 500px;
        position: absolute;
        top: -30px;
        left: -50px;
      }

      .footer_10 {
        width: 500px;
        position: absolute;
        bottom: -30px;
        right: -50px;
      }

      /* 头部 */
      .header ul {
        justify-content: space-between;
        align-items: flex-end;
      }

      .header ul li:first-child {
        width: 200px;
        text-align: center;
      }

      .header ul li:first-child img {
        width: 50px;
      }

      /* 票头 */
      .invoice_title {
        padding-top: 20px;
        padding-left: 435px;
        font-size: 14px;
      }
      .invoice_title p span:first-child {
        display: inline-block;
        width: 100px;
      }

      /* 发票明细 */
      .detail {
        margin-top: 30px;
      }
      .detail ul li p {
        width: 20%;
        text-align: center;
        height: 50px;
        line-height: 50px;
      }
      .detail ul li .item {
        width: 40%;
        text-align: left;
        padding-left: 15px;
      }

      .detail ul li:nth-child(odd) {
        background: #eac0da;
      }
      .detail ul li:nth-child(2) {
        border-top: 2px solid #000;
        margin-top: 10px;
      }
      .detail ul li:last-child {
        border-bottom: 2px solid #000;
      }
      .detail ul li:not(:first-child) p:first-child {
        border-left: 2px solid #000;
      }
      .detail ul li:not(:first-child) p:last-child {
        border-right: 2px solid #000;
      }
      .detail ul .title {
        height: 50px;
        line-height: 50px;
        font-size: 18px;
        background: #670267 !important;
        color: #fff;
        font-weight: 700;
      }

      /* 明细总结 */
      .epilogue {
        padding: 30px 0 0 20px;
      }
      .epilogue ul {
        justify-content: space-between;
        font-size: 14px;
        font-weight: 400;
      }

      .epilogue ul li {
        position: relative;
      }

      .epilogue ul li:last-child p {
        height: 30px;
        line-height: 30px;
      }

      .epilogue ul li:last-child p:last-child {
        background: #670267;
        color: #fff;
        font-weight: 700;
        font-size: 16px;
        position: absolute;
        width: 120%;
        left: -25px;
        height: 40px;
        line-height: 40px;
        /* padding-left: 20px; */
        margin-top: 15px;
      }

      .epilogue ul li:last-child p span:first-child {
        display: inline-block;
        width: 100px;
      }

      /* 签名确认 */
      .autograph {
        margin-left: 20px;
        padding: 30px 0 20px 0;
        font-size: 14px;
        width: 200px;
        border-bottom: 2px solid #d24e83;
      }

      /* 底部 */
      .footer {
        padding: 20px 0 0 20px;
        font-size: 12px;
        font-weight: 300;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img src="/theme/drcenter/invoice/img/10-header.png" alt="" class="header_10" />
      <img src="/theme/drcenter/invoice/img/10-footer.png" alt="" class="footer_10" />

      <!-- 头部 -->
      <div class="header">
        <ul class="flex">
          <li>
            <img src="/theme/drcenter/invoice/img/logo.png" alt="" />
            <p style="font-size: 22px; color: #d24e83;">
              <strong>YOUR</strong> COMPANY
            </p>
            <p style="font-size: 12px; color: #666;">YOUR TAGLINE HERE</p>
          </li>
          <li>
            <p style="font-size: 64px; color: #d24e83; font-weight: 700;">
              Invoice
            </p>
            <p style="font-size: 16px; margin-top: -10px; padding-left: 4px;">
              Invoice No : #************
            </p>
          </li>
        </ul>
      </div>
      <!-- 主要内容 -->
      <div class="content">
        <!-- 票头 -->
        <div class="invoice_title">
          <p>
            <span>Invoice Date</span>
            <span>: 30 April 2020</span>
          </p>
          <p>
            <span>Issue Date</span>
            <span>: 30 April 2020</span>
          </p>
          <p>
            <span>Account No</span>
            <span>: **********</span>
          </p>
        </div>
        <!-- 发票明细  -->
        <div class="detail">
          <ul>
            <li class="title flex">
              <p class="item">Product Description</p>
              <p>Price</p>
              <p>Qty.</p>
              <p>Total</p>
            </li>
            <li class="flex">
              <p class="item">Product Description</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p class="item">Product Description</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p class="item">Product Description</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p class="item">Product Description</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p class="item">Product Description</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p class="item">Product Description</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
          </ul>
        </div>
        <!-- 明细总结 -->
        <div class="epilogue">
          <ul class="flex">
            <li>
              <p
                style="
                  color: #d24e83;
                  font-size: 22px;
                  font-weight: 700;
                  margin-bottom: 10px;
                "
              >
                Invoice to:
              </p>
              <p style="font-weight: 700; font-size: 16px; margin-bottom: 5px;">
                Name Surename
              </p>
              <p>Addrees</p>
              <p>+***********</p>
              <p><EMAIL></p>
            </li>
            <li>
              <p><span>Subtotal</span><span>$300.00</span></p>
              <p><span>Shipping</span><span>$15.00</span></p>
              <p><span>Tax Rate</span><span>$10.00</span></p>
              <p>
                <span style="text-align: center;">TOTAL</span
                ><span>$325.00</span>
              </p>
            </li>
          </ul>
        </div>
        <!-- 签名确认 -->
        <div class="autograph">
          <p
            style="
              color: #d24e83;
              font-size: 22px;
              font-weight: 700;
              margin-bottom: 10px;
            "
          >
            Payment Info:
          </p>
          <p><span>Account No</span><span>: **********</span></p>
          <p><span>Name</span><span>: Kate Lauren</span></p>
          <p><span>Bank Detail</span><span>: 0***********</span></p>
        </div>
      </div>
      <!-- 底部 -->
      <div class="footer">
        <p
          style="
            color: #d24e83;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
          "
        >
          Term & Conditions:
        </p>
        <p>Lorem ipsum dolor sit amet, consectetuer adipiscing</p>
        <p>elit, sed diam nonummy nibh euismod tincidunt ut</p>
        <p>laoreet dolore magna aliquam erat volutpat.</p>
      </div>
    </div>
  </body>
</html>
