<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <style>
      /* 样式初始化 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      li {
        list-style: none;
      }
      /* 清浮动 */
      .clearfix::before,
      .clearfix::after {
        content: "";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }
      /* IE 6/7 */
      .clearfix {
        zoom: 1;
      }

      .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
      }

      body {
        background: #ccc;
      }

      .container {
        width: 800px;
        height: 1130px;
        margin: 0 auto;
        background: #fff;
        position: relative;
        padding: 0 50px;
        color: #042f52;
        overflow: hidden;
      }

      /* 头部 */
      .header {
       padding-top: 50px;
      }

      .header .brand img {
        width: 60px;
        float: left;
        margin-right: 10px;
      }
      .header .brand .brand_name {
        font-size: 32px;
        font-weight: 700;
      }
      .header .brand .sub {
        font-size: 12px;
        margin-top: -10px;
      }
      .header .title {
        padding-left: 460px;
        font-size: 52px;
        position: relative;
      }
      .header .title::before {
        content: "";
        width: 10%;
        height: 30px;
        background-color: #f9ac21;
        position: absolute;
        right: -50px;
        bottom: 20px;
      }
      .header .title::after {
        content: "";
        width: 70%;
        height: 30px;
        background-color: #f9ac21;
        position: absolute;
        left: -50px;
        bottom: 20px;
      }

      /* 票头 */
      .invoice_title ul li {
        float: left;
        width: 20%;
        font-size: 16px;
        font-weight: 600;
        height: 140px;
        flex-direction: column;
        justify-content: center;
      }
      .invoice_title ul li:first-child {
        width: 60%;
        font-size: 12px;
        font-weight: normal;
      }

      /* 明细 */
      .detail {
        border: 1px solid #ccc;
        padding-bottom: 120px;
      }
      .detail ul .title {
        height: 50px;
        line-height: 50px;
        color: #fff;
        background: #042f52 !important;
      }

      .detail ul li {
        font-size: 12px;
        height: 60px;
        line-height: 60px;
        text-align: center;
      }
      .detail ul li:nth-child(odd) {
        background: #eaedf0;
      }
      .detail ul li p {
        width: 15%;
      }
      .detail ul li .item {
        width: 40%;
        text-align: left;
      }

      /* 明细总结 */
      .epilogue {
        padding-top: 30px;
      }
      .epilogue ul {
        justify-content: space-between;
        font-size: 12px;
      }
      .epilogue ul li:last-child {
        font-size: 14px;
        font-weight: 700;
      }
      .epilogue ul li:last-child span:first-child {
        display: inline-block;
        width: 100px;
      }
      .epilogue ul li {
        position: relative;
      }
      .epilogue ul li .total_num {
        font-size: 14px;
        margin-top: 10px;
        position: absolute;
        width: 230px;
        height: 30px;
        line-height: 30px;
        padding-left: 25px;
        background-color: #f9ac21;
        right: -50px;
      }

      /* 签名确认 */
      .autograph {
        padding-top: 30px;
        padding-left: 500px;
        position: relative;
      }
      .autograph::before {
        content: "";
        height: 2px;
        width: 5%;
        background-color: #f9ac21;
        position: absolute;
        right: -50px;
        bottom: 23px;
      }
      .autograph::after {
        content: "";
        height: 2px;
        width: 75%;
        background-color: #f9ac21;
        position: absolute;
        left: -50px;
        bottom: 23px;
      }
      .autograph p {
        width: 200px;
        text-align: center;

      }
      .autograph p input {
        border: none;
        outline: none;
        width: 180px;
        border-bottom: 1px solid #000;
        text-align: center;
      } 

      /* 底部 */
      .footer {
        padding-top: 10px;
      }
      .footer span {
        display: inline-block;
        font-size: 14px;
        font-weight: 700;
        width: 80px;
        height: 12px;
        line-height: 12px;
        text-align: center;
      }
      .footer span:not(:last-child){
          border-right: 2px solid #000;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 头部 -->
      <div class="header">
        <div class="brand">
          <img src="/theme/drcenter/invoice/img/logo.png" alt="" />
          <div class="brand_name">Brand Name</div>
          <div class="sub">TAGLINE SPACE HERE</div>
        </div>

        <div class="title">INVOICE</div>
      </div>

      <!-- 票头 -->
      <div class="invoice_title">
        <ul class="clearfix">
          <li class="flex">
            <p style="font-size: 20px; font-weight: 700;">Invoice to:</p>
            <p style="font-size: 18px; font-weight: 700;">Dwyane Clark</p>
            <p>24 Dummy Street Area,</p>
            <p>Location,Lorem Ipsum</p>
            <p>570xx59xx</p>
          </li>
          <li class="flex">
            <p>Invoice#</p>
            <p>Date</p>
          </li>
          <li class="flex" style="text-align: right;">
            <p>52148</p>
            <p>01 / 02 / 2020</p>
          </li>
        </ul>
      </div>
      <!-- 主要内容 -->
      <div class="content">
        <!-- 发票明细  -->
        <div class="detail">
          <ul>
            <li class="title flex">
              <p>SL.</p>
              <p class="item">Item Description</p>
              <p>Price</p>
              <p>Qty.</p>
              <p>Total</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
          </ul>
        </div>
        <!-- 明细总结 -->
        <div class="epilogue">
          <ul class="flex">
            <li>
              <div
                style="font-size: 16px; font-weight: 700; margin-bottom: 30px;"
              >
                Thank you for your business
              </div>

              <div style="margin-bottom: 30px;">
                <p style="font-size: 14px; font-weight: 700;">
                  Terms & Conditions
                </p>
                <p>Lorem ipsum dolor sit amet,consectetur adipiscing</p>
                <p>elit Fusce diginssim pretium consectetur</p>
              </div>
              <div>
                <p style="font-size: 14px; font-weight: 700;">
                  Payment Info&nbsp;:
                </p>
                <p>
                  <span>Account#:</span>
                  <span>1234 5678 9012</span>
                </p>
                <p>
                  <span>A/C Name:</span>
                  <span>Lorem Ipsum</span>
                </p>
                <p>
                  <span>Bank Deatils:</span>
                  <span>Add your bank details</span>
                </p>
              </div>
            </li>

            <li>
              <p>
                <span>Sub Total:</span>
                <span>$220.00</span>
              </p>
              <p>
                <span>Tax:</span>
                <span>0.00%</span>
              </p>
              <p class="total_num">
                <span>Total:</span>
                <span>$220.00</span>
              </p>
            </li>
          </ul>
        </div>
        <!-- 签名确认 -->
        <div class="autograph">
          <p><input type="text" /></p>
          <p>Authorised Sign</p>
        </div>
      </div>
      <!-- 底部 -->
      <div class="footer">
        <span>Phone #</span>
        <span>Adress</span>
        <span>Website</span>
      </div>
    </div>
  </body>
</html>
