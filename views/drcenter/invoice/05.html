<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <style>
      /* 样式初始化 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      li {
        list-style: none;
      }
      /* 清浮动 */
      .clearfix::before,
      .clearfix::after {
        content: "";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }
      /* IE 6/7 */
      .clearfix {
        zoom: 1;
      }

      .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
      }

      body {
        background: #ccc;
      }

      .container {
        width: 800px;
        height: 1130px;
        margin: 0 auto;
        background: #fff;
        position: relative;
      }

      /* 头部 */
      .header {
        padding: 40px 60px;
        border-bottom: 4px solid #ef1010;
        justify-content: space-between;
        align-items: flex-end;
      }
      .header .brand img {
        width: 60px;
        float: left;
        margin-right: 10px;
      }

      .header .brand {
        width: 50%;
      }

      .header .brand .brand_name {
        font-size: 32px;
        font-weight: 700;
      }
      .header .brand .sub {
        font-size: 12px;
        margin-top: -10px;
      }
      .header .title {
        font-size: 56px;
        color: #ef1010;
        font-weight: 700;
        letter-spacing: 5px;
      }

      /* 票头 */
      .invoice_title {
        padding: 0 60px;
      }
      .invoice_title ul {
        justify-content: space-between;
        align-items: baseline;
      }
      .invoice_title ul li {
        font-size: 16px;
        font-weight: 600;
        height: 160px;
        flex-direction: column;
        justify-content: center;
      }
      .invoice_title ul li:first-child {
        width: 60%;
      }
      .invoice_title ul li:first-child p:not(:first-child) {
        padding-left: 108px;
        font-size: 12px;
        font-weight: 400;
      }

      /* 明细 */
      .detail {
        border-bottom: 4px solid #ef1010;
      }
      .detail ul li {
        padding: 0px 60px;
        height: 65px;
        line-height: 65px;
        position: relative;
      }
      .detail ul .title {
        font-weight: 700;
      }
      .detail ul li:not(:first-child)::after {
        content: "";
        width: 85%;
        height: 1px;
        background: #ccc;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }
      .detail ul li:last-child::after {
        height: 0;
      }
      .detail ul li:nth-child(even) {
        background: #f5f6f6;
      }
      .detail ul li:nth-child(odd) {
        background: #fff;
      }

      .detail ul li p {
        width: 20%;
        text-align: center;
      }
      .detail ul li p:first-child {
        width: 10%;
      }
      .detail ul li p.item {
        width: 50%;
        text-align: left;
      }

      /* 明细总结 */
      .epilogue {
        padding: 40px 60px;
        position: relative;
      }

      .epilogue::after {
        content: "";
        width: 200px;
        height: 2px;
        background: #000;
        position: absolute;
        bottom: 0;
        left: 60px;
      }
      .epilogue ul {
        justify-content: space-between;
      }
      .epilogue ul li p {
        padding: 0 10px;
      }
      .epilogue ul li:first-child {
        font-size: 12px;
        width: 50%;
      }
      .epilogue ul li p span:first-child {
        display: inline-block;
        width: 100px;
      }
      .epilogue ul li:last-child {
        font-weight: 700;
      }

      /* 签名确认 */
      .autograph {
        padding: 40px 60px;
      }
      .autograph ul {
        justify-content: space-between;
        align-items: flex-end;
      }

      .autograph ul li:first-child {
        font-size: 12px;
      }
      .autograph ul li:last-child input {
        width: 100%;
        border-radius: 0;
        border: none;
        outline: none;
        border-bottom: 1px solid #000;
        padding: 5px 20px;
        margin-bottom: 5px;
        font-size: 16px;
        text-align: center;
      }
      .autograph ul li:last-child p {
        text-align: center;
        font-weight: 700;
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 头部 -->
      <div class="header flex">
        <div class="brand">
          <img src="/theme/drcenter/invoice/img/logo.png" alt="" />
          <div class="brand_name">Brand Name</div>
          <div class="sub">TAGLINE SPACE HERE</div>
        </div>
        <span class="title">INVOICE</span>
      </div>
      <!-- 主要内容 -->
      <div class="content">
        <!-- 票头 -->
        <div class="invoice_title ">
          <ul class="clearfix flex">
            <li class="flex">
              <p style="font-size: 20px; font-weight: 700;">
                Invoice to: Dwyane Clark
              </p>
              <p>24 Dummy Street Area,</p>
              <p>Location, Lorem Ipsum,</p>
              <p>570xx59x</p>
            </li>
            <li class="flex">
              <p>Invoice#</p>
              <p>Date:</p>
            </li>
            <li class="flex">
              <p>52148</p>
              <p>01 / 02 / 2020</p>
            </li>
          </ul>
        </div>
        <!-- 发票明细  -->
        <div class="detail">
          <ul>
            <li class="title flex">
              <p>SL.</p>
              <p class="item">Item Description</p>
              <p>Price</p>
              <p>Qty.</p>
              <p>Total</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>1</p>
              <p>$50.00</p>
            </li>
            <li class="flex"></li>
          </ul>
        </div>
        <!-- 明细总结 -->
        <div class="epilogue">
          <ul class="flex">
            <li>
              <div
                style="font-size: 18px; font-weight: 700; margin-bottom: 30px;"
              >
                Thank you for your business
              </div>
              <div>
                <p style="font-size: 16px; font-weight: 700; color: #ef1010;">
                  Payment Info:
                </p>
                <p>
                  <span>Account #:</span>
                  <span>1234 5678 9012</span>
                </p>
                <p>
                  <span>A/C Name:</span>
                  <span>Lorem Ipsum</span>
                </p>
                <p>
                  <span>Bank Deatils:</span>
                  <span>Add your bank details</span>
                </p>
              </div>
            </li>
            <li>
              <p>
                <span>Sub Total:</span>
                <span>$220.00</span>
              </p>
              <p
                style="
                  border-bottom: 1px solid #000;
                  padding-bottom: 20px;
                  margin-bottom: 8px;
                "
              >
                <span>Tax:</span>
                <span>0.00%</span>
              </p>
              <p style="font-size: 16px; color: #ef1010;">
                <span>Total:</span>
                <span>$220.00</span>
              </p>
            </li>
          </ul>
        </div>
        <!-- 签名确认 -->
        <div class="autograph">
          <ul class="flex">
            <li>
              <p style="font-size: 16px; font-weight: 700; color: #ef1010;">
                Terms & Conditions
              </p>
              <p>
                Lorem ipsum dolor sit amet,consectetur adipiscing elit Fusce
              </p>
              <p>
                diginssim pretium consectetur.Curabitur tempor posuere massa in
              </p>
              <p>
                varius.Pellentesque viverra nibh eu vehicula mattis.Nullam porta
              </p>
            </li>
            <li>
              <p><input type="text" /></p>
              <p>Authorised Sign</p>
            </li>
          </ul>
        </div>
      </div>
      <!-- 底部 -->
      <div class="footer"></div>
    </div>
  </body>
</html>
