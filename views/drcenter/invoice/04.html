<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <style>
      /* 样式初始化 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      li {
        list-style: none;
      }
      /* 清浮动 */
      .clearfix::before,
      .clearfix::after {
        content: "";
        display: table;
      }
      .clearfix:after {
        clear: both;
      }
      /* IE 6/7 */
      .clearfix {
        zoom: 1;
      }

      .flex {
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-box;
        display: -webkit-flex;
        display: box;
        display: flexbox;
        display: flex;
      }

      body {
        background: #ccc;
      }

      .container {
        width: 800px;
        height: 1130px;
        margin: 0 auto;
        background: #fff;
        position: relative;
        padding: 120px 60px;
        color: #0b3357;
        overflow: hidden;
      }

      .footer_04 {
        position: absolute;
        bottom: -220px;
        width: 120%;
        left: -40px;
      }

      /* 头部 */
      .header ul {
        justify-content: space-between;
      }
      .header ul .title {
        font-size: 66px;
        letter-spacing: 5px;
        width: 55%;
      }

      .header ul li:not(:first-child) {
        padding-top: 20px;
      }

      .header ul li:not(:first-child) p:first-child {
        margin-bottom: 10px;
      }
      .header ul li:last-child {
        text-align: right;
      }

      .content {
        padding: 30px 0;
      }
      /* 票头 */
      .invoice_title ul {
        justify-content: space-between;
        font-size: 14px;
      }

      .invoice_title ul li:first-child p:first-child {
        font-size: 18px;
        font-weight: 700;
      }

      .invoice_title ul li:first-child p:not(:first-child) {
        padding-left: 30px;
      }
      .invoice_title ul li:last-child {
        position: relative;
        padding-left: 50px;
        padding-top: 8px;
      }
      .invoice_title ul li:last-child p {
        margin-bottom: 1px;
      }
      .invoice_title ul li:last-child img {
        width: 40px;
        position: absolute;
        left: 0;
      }

      /* 明细 */
      .detail {
        margin-top: 30px;
        border: 1px solid #0b3357;
        font-size: 12px;
        border-bottom: none;
      }
      .detail ul li p {
        width: 15%;
        padding-left: 15px;
      }
      .detail ul li:not(:first-child) p:first-child {
        padding-left: 50px;
      }
      .detail ul li .item {
        width: 75%;
        text-align: left;
      }

      .detail ul li:not(:first-child) .item {
        padding-left: 30px;
      }

      .detail ul .title {
        height: 30px;
        line-height: 30px;
        color: #fff;
        background: #0b3357;
      }

      .detail ul li:not(:first-child) {
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #ccc;
      }

      .detail ul li:last-child {
        border-bottom: none;
      }
      .detail ul li:not(:first-child) p:not(:last-child) {
        border-right: 1px solid #ccc;
      }
      /* 明细总结 */
      .epilogue {
        height: 140px;
        background: #a3a5a8;
        padding: 30px 0 0 0;
        font-size: 14px;
        font-weight: 700;
        position: relative;
      }
      .epilogue ul li {
        padding-left: 490px;
      }
      .epilogue ul li span:first-child {
        display: inline-block;
        width: 100px;
        text-align: left;
      }
      .epilogue ul .total {
        width: 100%;
        height: 40px;
        line-height: 40px;
        background: #0b3357;
        color: #fff;
        font-size: 16px;
        position: absolute;
        bottom: 0;
      }

      /* 底部 */
      .footer {
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img src="/theme/drcenter/invoice/img/04.png" alt="" class="footer_04" />
      <!-- 头部 -->
      <div class="header">
        <ul class="flex">
          <li class="title">INVOICE</li>
          <li>
            <p>Invoice#</p>
            <p>Date</p>
          </li>
          <li>
            <p>52148</p>
            <p>01 / 02 / 2020</p>
          </li>
        </ul>
      </div>
      <!-- 主要内容 -->
      <div class="content">
        <!-- 票头 -->
        <div class="invoice_title">
          <ul class="flex">
            <li>
              <p>Bill to:</p>
              <p style="font-size: 14px; font-weight: 700;">Dwyane Clark</p>
              <p>24 Dummy Street Area,</p>
              <p>Location,Lorem Ipsum,</p>
              <p>570xx59xx</p>
            </li>
            <li>
              <img src="/theme/drcenter/invoice/img/logo.png" alt="" />
              <p style="font-size: 18px;">CREATIVE <strong>MEDIA</strong></p>
              <p style="font-size: 12px; margin-top: -8px;">
                YOUR COMPANY YAGLINE HERE
              </p>
              <p>A- Unkonwn Area,</p>
              <p>Lorem, Ipsum Dolor,</p>
              <p>845xx145</p>
            </li>
          </ul>
        </div>
        <!-- 发票明细  -->
        <div class="detail">
          <ul>
            <li class="title flex">
              <p>QTY</p>
              <p class="item">PRODUCT DESCRIPTION</p>
              <p>PRICE</p>
              <p>TOTAL</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p>1</p>
              <p class="item">Lorem Ipsum Dolor</p>
              <p>$50.00</p>
              <p>$50.00</p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
            <li class="flex">
              <p></p>
              <p class="item"></p>
              <p></p>
              <p></p>
            </li>
          </ul>
        </div>
        <!-- 明细总结 -->
        <div class="epilogue">
          <ul>
            <li>
              <span>Subtotal</span>
              <span>$415.00</span>
            </li>
            <li>
              <span>Tax Rate</span>
              <span>0.00%</span>
            </li>
            <li class="total">
              <span>TOTAL</span>
              <span>$415.00</span>
            </li>
          </ul>
        </div>
      </div>
      <!-- 底部 -->
      <div class="footer">
        <p>THANK YOU FOR YOUR BUSINESS</p>
        <p style="font-weight: 700;">
          Payment is due max 7days after invoice without deduction
        </p>
      </div>
    </div>
  </body>
</html>
