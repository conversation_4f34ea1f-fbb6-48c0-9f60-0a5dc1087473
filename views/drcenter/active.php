    <link href="/theme/drcenter/css/active.css" rel="stylesheet">
    <style>
        .active-content-list>li {
            word-break: break-word;
            width: 100%;
        }

        .active-content-list>li:nth-child(3) {
            font-size: 0.26rem;
            color: #4a4a4a;
            border: 1px dashed #ccc;
            padding: 0.15rem 0.28rem;
            border-radius: 3px;
            background: #fdeede;
            box-sizing: border-box;
            margin-bottom: 10px
        }

        .active-content-list>li:nth-child(4) {
            font-size: 0.26rem;
            color: #9b9b9b;
        }

        .active-content-list>li:nth-child(2) {
            color: #041d31;
            font-weight: 700;
        }

        .active-content-list-jump {
            position: relative;
            background: #d3eaf9;
            width: 87%;
            margin: 0 auto;
            border-radius: 5px;
            border: 1px solid #2d152d;
            text-align: center;
            font-size: 0.48rem;
            font-weight: 700;
            color: purple;
        }

        .active-content-list-jump::after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border: 20px solid;
            border-color: transparent transparent #d3eaf9 transparent;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
        }

        .active-content-list-jump::before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border: 17px solid;
            border-color: transparent transparent #2d152d transparent;
            top: -34px;
            left: 50%;
            transform: translateX(-50%)
        }

        .active-content {
            height: 0.98rem;
            line-height: 0.98rem;
            background: #d3eaf9;
            font-size: 0.32rem;
            font-weight: bold;
            color: #20537D;
            display: flex;
            position: fixed;
            width: 100%;
            z-index: 99;
            justify-content: space-between;
        }

        .active-title-list {
            margin-top: 0;
            right: 0;
            top: 2rem;
            border: 1px solid #e1e1e1;
        }

        .content {
            padding-top: 2rem;
        }
    </style>
    <!-- 头部 -->
    <ul class="home-top-title">
        <li class="close" id="black_to_home">
            <img src="/theme/drcenter/images/home/<USER>">
        </li>
        <li>全部活动</li>
        <li>
        </li>
    </ul>
    <!-- 内容 -->
    <div class="active-content" style="padding-top: 1rem;">
        <div style="margin-left: 0.2rem;">
            <span>全部活动</span>
            <span>(12)</span>
        </div>
        <div id="active_menu_show" style="margin-right:0.5rem">
            <img style="width: 0.4rem;
            position: relative;
            right: 0.05rem;top: 0.07rem" src="/theme/drcenter/images/active-icon.png">
        </div>
    </div>
    <ul class="active-title-list" id="active_menu">
        <li><a href="#">正序</a></li>
        <li><a href="#">倒序</a></li>
    </ul>

    <div class="content">
        <!-- 挖个坑 -->
        <!-- <div class="active-content-list-line">
            <a href="https://www.drsay.cn/survey/8?r=1591089163966&s=QCBPAWNJ">
                <ul class="active-content-list">
                    <li>
                        <span>2018年 9月 10日 星期四</span>
                        <span class="cash" style="font-weight: 600;float:right;font-size:14px;">
                            <font color="#dc3004">¥</font>
                            <font color="#000">500</font>
                        </span>
                    </li>
                    <li>
                        <span>肿瘤生存质量调查报告(一)</span>
                    </li>
                    <li>
                        <span>关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述关于项目的简单描述</span>
                    </li>
                    <li>

                    </li>
                </ul>
            </a>
            <div class="active-content-list-jump">
                点击此处进入答卷
            </div>
        </div> -->

    </div>

    <script type="text/html" id="projectTpl">
    {{each list v i }}
    <div class="active-content-list-line">
        <a href="{{v.partner_link}}">
            <ul class="active-content-list" style="background: #f7fcff">
                <li>
                    <span>{{v.pro_end_time}}</span>
                    <span class="cash" style="font-weight: 600;float:right;font-size:14px;">
                        <!-- <font color="#dc3004">¥</font> -->
                        <font color="#000">{{v.point}}</font>
                    </span>
                </li>
                <li>
                    <span>{{v.project_id}}</span>
                </li>
                <li>
                    <span>{{v.pro_name}}</span>
                </li>
            </ul>
        </a>
        <div class="active-content-list-jump">
            点击此处进入答卷
        </div>
    </div>
    {{/each}}
    </script>
    <script>
        $(function() {
            $("#active_menu_show").click(function() {
                $("#active_menu").toggle();
            })
            $("#black_to_home").click(function() {
                window.location.href = base_url
            })

            var data = JSON.parse(localStorage.getItem("data"))
            var token = data.login_token || ""
            var uid = data.uid || ""

            // 进入页面获取数据
            request_back(
                "api_project/investigate_survey", {
                    uid: uid,
                    login_token: token,
                    from_where: 2
                },
                true,
                function(info) {
                    console.log(info)
                    if (info.rs_code == "success") {
                        $(".content").html(template("projectTpl", {
                            list: info.rs_msg
                        }))
                    }
                })
        })
    </script>