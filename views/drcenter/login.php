<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0,user-scalable=no" />
    <meta name="keywords" content="" />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>登录</title>
    <script src="/theme/drcenter/js/jquery-3.3.1.min.js"></script>
    <script>
        document.write('<script src="/theme/drcenter/js/api.js?v=' + Date.now() + '"></s' + 'cript>')
    </script>
    <link rel="stylesheet" href="/theme/drcenter/css/login.css" />

    <style>
        * {
            margin: 0;
            padding: 0;
        }

        .login-wrapper {
            background: #ffffff;
            margin: 0;
            padding: 0 0;
            padding-bottom: 0.34rem;
            width: 100%;
        }

        .login-content-icon {
            width: 70%;
            text-align: center;
            margin-left: 15%;
            margin-top: 1.5rem;
        }

        .login-content {
            margin-top: 1.05rem;
        }

        .login_input {
            border-bottom: 1px solid #23547b;
            width: 90%;
            margin-left: 5%;
            line-height: 1.26rem;
        }

        .login_input>img {
            width: .35rem;
            margin-left: .3rem;
        }

        .login_input input {
            position: relative;
            bottom: .09rem;
            font-size: .32rem;
            color: #9B9B9B;
            outline: none;
        }

        .login_input>span {
            float: right;
            margin-top: .3rem;
            height: .72rem !important;
            line-height: .72rem;
            font-size: .34rem;
            color: #FFFFFF;
            background: #1daee5;
            border-radius: .1rem;
            padding-left: .12rem;
            padding-right: .12rem;
        }

        .login-third-login {
            display: flex;
            margin: 0 1.4rem;
        }

        .login-third-login::after {
            content: '';
            display: block;
            clear: both;
        }

        .login-third-login hr:nth-of-type(1) {
            flex: 1;
            margin-top: .78rem;
            height: 1px;
            border: none;
            border-top: 1px dashed #4A4A4A;
            width: 38% !important;
        }

        .login-third-login hr:nth-of-type(2) {
            flex: 1;
            margin-top: .78rem;
            height: 1px;
            border: none;
            border-top: 1px dashed #4A4A4A;
            width: 38% !important;
        }

        .login-third-login>span {
            float: left;
            text-align: center;
            margin: 0 auto;
            margin-top: .65rem;
            color: #9B9B9B;
            font-size: .28rem;
            line-height: .32rem;
        }

        .login-third-list {
            text-align: center;
            margin: 0 1.2rem;
            margin-top: 0.54rem;
            display: -webkit-flex;
            /* Safari */
            -webkit-justify-content: space-between;
            /* Safari 6.1+ */
            display: flex;
            justify-content: space-between;
        }

        .login-third-list-item {
            position: relative;
            width: .85rem;
            text-align: center;
        }

        .login-third-list-item>img {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            display: block;
            height: .54rem;
        }

        .login-third-list-item>span {
            position: absolute;
            top: 0.6rem;
            left: 50%;
            transform: translateX(-50%);
            /* margin-left: .2rem; */
            font-size: .22rem;
            height: .35rem;
            width: 100%;
        }

        input:disabled {
            opacity: 1;
            color: #333;
        }

        input[type="submit"],
        input[type="reset"],
        input[type="text"],
        input[type="password"],
        input[type="number"],
        input[type="button"],
        button,
        input[type="date"],
        textarea {
            -webkit-appearance: none;
            border: none;
            background: none;
        }

        textarea {
            resize: none;
        }

        .login_btn {
            margin-left: 5%;
            margin-top: 1.8rem;
            font-size: .36rem;
            color: #FFFFFF;
            text-align: center;
            width: 90%;
            background: #1faee6;
            padding-top: .2rem;
            padding-bottom: .2rem;
            border-radius: 8px;
        }

        .login-company-name {
            display: block;
            margin-top: 2.4rem;
            color: #4A4A4A;
            font-size: .22rem;
            text-align: center;
        }

        .login-third-alert {
            position: fixed;
            display: none;
            top: 0rem;
            left: 0;
            z-index: 9999;
            width: 100%;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.5);
        }

        .login-third-alert-content {
            background: #FFFFFF;
            border-radius: .15rem;
            width: 4.8rem;
            top: 50%;
            position: fixed;
            transform: translate(-50%), -50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .login-third-alert-content>span {
            display: block;
            text-align: center;
            margin: 0 auto;
            margin-top: .6rem;
            padding-bottom: .4rem;
            font-size: .36rem;
            color: #000000;
            border-bottom: 1px solid #E1E1E1;
        }

        .login-third-alert-btn {
            display: flex;
            width: 100%;
            height: .88rem;
        }

        .login-third-alert-btn span {
            flex: 1;
            text-align: center;
            vertical-align: center;
            padding-top: .26rem;
            font-size: .28rem;
            color: #4A90E2;
            border-right: 1px solid #E1E1E1;
        }

        .verification_code_dialog {
            display: none;
            position: fixed;
            z-index: 5;
            width: 100%;
            top: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .verification_code_dialog .verification_code_dialog_content {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 70%;
            transform: translate(-50%, -50%);
            background: #fdfef9;
            border: 1px solid #2c5c84;
            border-radius: 5px;
        }

        .verification_code_dialog_content span {
            display: block;
            margin: 0.45rem 0.25rem;
            font-size: 0.28rem;
            text-align: center;
            line-height: 0.4rem;
            color: #4a4a4a;
        }

        .verification_code_dialog_content button {
            display: block;
            height: 1rem;
            width: 100%;
            line-height: 1rem;
            font-size: 0.32rem;
            color: #fff;
            text-align: center;
            background: #1faee6;
        }
    </style>
</head>
<script>
    (function(doc, win) {
        var docEl = doc.documentElement,
            resizeEvt = 'onorientationchange' in window ? 'onorientationchange' : 'resize',
            recalc = function() {
                var clientWidth = docEl.clientWidth;
                if (!clientWidth) return;
                if (clientWidth >= 750) {
                    docEl.style.fontSize = '100px';
                } else {
                    docEl.style.fontSize = 100 * (clientWidth / 750) + 'px';
                }
            };

        if (!doc.addEventListener) return;
        win.addEventListener(resizeEvt, recalc, false);
        doc.addEventListener('DOMContentLoaded', recalc, false);
    })(document, window);

    // function toUserCertificate() {
    //     window.location.href = "http://www.drsay.cn/drcenter/dr/user_certificate"
    // }

    // function showThirdLogin() {
    //     $('.login-third-alert').css("display", "block")
    // }

    // function showThirdLoginSina() {
    //     $('#login-third-type').html("健康通想要打开微博")
    //     $('.login-third-alert').css("display", "block")
    // }

    // function showThirdLoginQQ() {
    //     $('#login-third-type').html("健康通想要打开QQ")
    //     $('.login-third-alert').css("display", "block")
    // }

    // function closeThirdLogin() {
    //     $('#login-third-type').html("健康通想要打开微信")
    //     $('.login-third-alert').css("display", "none")
    // }
</script>

<body>
    <div class="login-wrapper">
        <img class="login-content-icon" src="/theme/drcenter/images/login/login-icon.png" />
        <div class="login-content">
            <div class="login_input">
                <img src="/theme/drcenter/images/login/account-icon.png" />
                <input name="mobile" type="text" placeholder="邮箱/手机号" />
            </div>
            <div class="login_input">
                <img src="/theme/drcenter/images/login/password-icon.png" />
                <input name="verification_code" type="text" placeholder="输入验证码" />
                <span class="verification_code">获取验证码</span>
            </div>
        </div>

        <button class="login_btn">登 录</button>
        <!-- <div class="login-third-login">
            <hr>
            <span>第三方登录</span>
            <hr>
        </div>
        <div class="login-third-list">
            <div class="login-third-list-item" onClick="showThirdLogin()">
                <img src="/theme/drcenter/images/login/login-wx.png" />
                <span>微信</span>
            </div>
            <div class="login-third-list-item" onClick="showThirdLoginSina()">
                <img src="/theme/drcenter/images/login/login-sina.png" />
                <span>微博</span>
            </div>
            <div class="login-third-list-item" onClick="showThirdLoginQQ()">
                <img src="/theme/drcenter/images/login/login-qq.png" />
                <span>QQ</span>
            </div>
        </div>

        <div class="login-third-alert">
            <div class="login-third-alert-content">
                <span id="login-third-type">健康通想要打开微信</span>
                <div class="login-third-alert-btn">
                    <span onClick="closeThirdLogin()">取消</span>
                    <span onClick="toUserCertificate()">确认</span>
                </div>
            </div>
        </div> -->

        <span class="login-company-name">@2020 健康通 ALL Rights Reserved.</span>
    </div>

    <!-- 验证码弹框 -->
    <div class="verification_code_dialog">
        <div class="verification_code_dialog_content">
            <span class="tips"></span>
            <button>确定</button>
        </div>
    </div>
</body>

<script>
    $(function() {
        // 点击确定关闭对话框
        $(".verification_code_dialog button").click(function() {
            $(".verification_code_dialog").hide();
        });

        //   验证码的接口
        $(".verification_code").click(function() {
            var mobile = $("input[name='mobile']").val();
            if (!mobile) {
                $(".verification_code_dialog .tips").text("手机号码不能为空!");
                $(".verification_code_dialog").show();
            }
            if (mobile) {
                mobile = mobile.trim();
                // 验证成功发送请求
                if (/^1[0-9]{10}$/.test(mobile)) {
                    // 发送请求
                    request_back(
                        "api_comm/send_sms", {
                            from_where: 1,
                            mobile: mobile,
                            type: 7
                        },
                        true,
                        function(info) {
                            $(".verification_code_dialog .tips").text(info.rs_msg);
                            $(".verification_code_dialog").show();
                        }
                    );
                } else {
                    $(".verification_code_dialog .tips").text("您填写的手机格式有误!");
                    $(".verification_code_dialog").show();
                }
            }
        });

        // 登录的接口
        $(".login_btn").click(function() {
            var mobile = $("input[name='mobile']").val();
            var verification_code = $("input[name='verification_code']").val();
            if (!mobile || !verification_code) {
                $(".verification_code_dialog .tips").text("请您输入完整信息!");
                $(".verification_code_dialog").show();
            }
            if (mobile && verification_code) {
                request_back(
                    "api_comm/login", {
                        type: 1,
                        mobile: mobile.trim(),
                        verification_code: verification_code.trim(),
                    },
                    true,
                    function(info) {
                        if (info.rs_code == "success") {
                            localStorage.setItem("data", JSON.stringify(info.rs_msg));
                            window.location.href =
                                base_url + "user_certificate";
                        } else {
                            $(".verification_code_dialog .tips").text(info.rs_msg);
                            $(".verification_code_dialog").show();
                        }
                    }
                );
            }
        });
    })
</script>

</html>