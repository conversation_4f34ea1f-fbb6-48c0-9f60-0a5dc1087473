<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body,
    html {
        background: #f5f5f5;
        height: 100%;
        color: #4a4a4a;
        overflow: hidden;
    }

    input,
    button {
        outline: none;
        border: none;
        background: transparent;
        -webkit-appearance: none;
    }

    ol,
    ul {
        list-style: none;
    }

    .radius {
        border-radius: 5px;
    }


    input,
    textarea {
        -webkit-appearance: none;
        border-radius: 0px;
    }

    /* 清浮动 */
    .clearfix::before,
    .clearfix::after {
        content: "";
        display: table;
    }

    .clearfix:after {
        clear: both;
    }

    /* IE 6/7 */

    .clearfix {
        zoom: 1;
    }

    .container::-webkit-scrollbar {
        display: none;
    }

    .container {
        background: #fff;
        overflow: auto;
        height: 90%;
        padding: 10px;
        padding-top: 95px;
    }

    .container .head {
        position: fixed;
        z-index: 99999;
        background: #fff;
        padding: 10px;
    }

    .search {
        position: relative;
        height: 50px;
    }

    .search img {
        width: 20px;
        position: absolute;
        left: 10px;
        top: 10px;
    }

    .search input {
        border: 1px solid #ccc;
        width: 100%;
        height: 40px;
        padding-left: 40px;
        font-size: 14px;
    }

    .department {
        height: 30px;
        line-height: 30px;
        overflow: hidden;
        position: relative;
    }

    .department img {
        position: absolute;
        cursor: pointer;
        width: 18px;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    .department ul {
        height: 100%;
        white-space: nowrap;
        overflow-y: hidden;
        overflow-x: auto;
    }

    .department ul li {
        display: inline-block;
        padding: 0 5px;
    }

    .recommend {
        height: 80px;
        margin: 10px 0;
    }

    .recommend ul {
        height: 100%;
        white-space: nowrap;
        overflow-y: hidden;
        overflow-x: auto;
    }

    ul::-webkit-scrollbar {
        display: none;
    }

    .recommend ul li {
        display: inline-block;
        width: 100px;
        height: 100%;
        vertical-align: top;
        margin: 0 5px;
        text-align: center;
        position: relative;
    }

    .recommend ul li .img {
        display: inline-block;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 2px solid #4AABE3;
        position: relative;
        margin-bottom: 10px;
    }

    .recommend ul li .img .img_box {
        width: 40px;
        height: 40px;
        /* border: 2px solid #000; */
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        overflow: hidden;
    }

    .recommend ul li .img .status {
        background: #4AABE3;
        color: #fff;
        font-size: 12px;
        padding: 0 3px;
        border-radius: 3px;
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        border: 2px solid #fff;
    }

    .recommend ul li .explain {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        height: 20px;
        color: #000;
        z-index: 9;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .comment_num .comment_box {
        text-align: left;
        position: absolute;
        left: 0;
        top: 50px;
        width: 100%;
        overflow: auto;
        box-sizing: border-box;
        background: #fff;
        border: 1px solid #dee4ec;
        z-index: 999;
    }

    .comment_num .comment_box .input {
        height: 40px;
        position: relative;
    }

    .comment_num .comment_box .input input {
        width: 80%;
        height: 100%;
        border: 1px solid #ccc;
        padding: 0 20px;
        position: absolute;
    }

    .comment_num .comment_box .input button {
        width: 20%;
        height: 100%;
        background: #53a4f4;
        font-size: 16px;
        color: #fff;
        position: absolute;
        right: 0;
    }

    .comment_num .comment_box .comment_detail {
        padding: 20px 10px;
        min-height: 200px;

    }

    .comment_num .comment_box .comment_detail p {
        word-wrap: break-word;
        position: relative;
        padding-left: 30px;

    }

    .comment_num .comment_box .comment_detail p span {
        position: absolute;
        left: 0;
        cursor: pointer;
        color: #666;
        border: 1px solid #666;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        border-radius: 50%;
        font-size: 12px;
    }

    .project {
        margin-top: 5px;
    }

    .project .item {
        margin-bottom: 30px;
    }

    .project .item video {
        width: 100%;
    }

    .project .item .item_detail {
        padding: 10px;
        position: relative;
    }

    .project .item .item_detail .user {
        cursor: pointer;
        float: left;
        width: 70%;
    }

    .project .item .item_detail .user .user_img {
        float: left;
        width: 38px;
        height: 38px;
        vertical-align: top;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 10px;
    }

    .project .item .item_detail .user div {
        float: left;
        width: 70%;
    }

    .project .item .item_detail .user div p {
        width: 100%;
    }

    .project .item .item_detail .user div p span {
        float: left;
        width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .project .item .item_detail .comment_num {
        float: right;
        width: 30%;
        text-align: right;
    }

    .project .item .item_detail .comment_num span font {
        display: inline-block;
        font-size: 12px;
        max-width: 40px;
        margin-top: 0px;
        margin-left: -5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }


    .project .item .item_detail .comment_num .img_box {
        cursor: pointer;
        display: inline-block;
        min-width: 40px;
    }

    .project .item .item_detail .comment_num span img {
        height: 20px;
        width: 20px;
        vertical-align: middle;

    }

    .button {
        width: 49%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background: #f5f7f8;
        border-radius: 20px;
        font-size: 12px;
        margin-bottom: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .draggable-element {
        cursor: move;
    }

    video {
        width: 100% !important;
        height: auto !important;
    }

    .choosed {
        color: #4AABE3 !important;
    }

    .border_red {
        border: 1px dashed #4AABE3 !important;
        color: #4AABE3 !important;
    }

    .manage {
        display: none;
        height: 100%;
        background: #fff;
        position: fixed;
        width: 100%;
        top: 0;
        left: 0;
        z-index: 99999;
        padding-bottom: 10px;
        overflow: auto;
    }

    .manage .title {
        text-align: center;
        height: 60px;
        line-height: 60px;
        font-size: 16px;
        position: relative;
    }

    .manage .title span {
        position: absolute;
        top: 0px;
        right: 20px;
        width: 40px;
        height: 40px;
        margin-top: 10px;
        cursor: pointer;
    }

    .manage .content .my {
        padding: 0 20px;
    }

    .manage .content .my input {
        width: 60%;
    }

    .manage .content .my span:first-child {
        font-size: 14px;
    }

    .manage .content .my .edit {
        display: inline-block;
        padding: 3px 15px;
        background: #cfdff7;
        border-radius: 20px;
        font-size: 12px;
        color: #4AABE3;
    }

    .manage .content .detail {
        padding: 20px 20px;
    }

    .manage .more .detail span {
        background: #fff;
        border: 1px solid #f5f7f8;
    }

    .container {
        width: 100%;
    }

    .container .head {
        width: 100%;
        top: 0;
        left: 0;
    }

    .department ul {
        width: 93%;
    }

    .footer {
        width: 100%;
    }

    a {
        text-decoration: none;
        color: #666;
    }
</style>

<div class="container">
    <div class="head radius">
        <div class="search">
            <img src="/theme/drcenter/images/survey/search.png" alt="" />
            <input type="text" class="radius search_input" placeholder="请输入搜索内容" />
        </div>

        <div class="department">
            <img src="/theme/drcenter/images/survey/icon_add.png" alt="" />
            <ul class="clearfix dep_list">
                <!-- 挖个坑 -->
            </ul>
        </div>
    </div>

    <div class="recommend">
        <ul class="clearfix company_list">
            <!-- 挖个坑 -->
        </ul>
    </div>

    <div class="project">
        <!-- 挖个坑 -->
    </div>
</div>

<div class="manage">
    <div class="title">
        <h3>科室管理</h3>
        <span><img src="/theme/drcenter/images/survey/close.png" alt="" style="width: 100%;" /></span>
    </div>
    <div class="content my_manage">
        <div class="my">
            <span>我的科室</span>
            <!-- <input type="text" placeholder="点击进入我的科室" />
                <span class="edit">编辑</span> -->
        </div>

        <div class="detail my_list">
            <!-- 挖个坑 -->
        </div>
    </div>

    <div class="content more">
        <div class="my">
            <span>更多频道</span>
            <!-- <input type="text" placeholder="点击添加频道" /> -->
        </div>
        <div class="detail  more_list">
            <!-- 挖个坑 -->
        </div>
    </div>
</div>

<script src="/theme/drcenter/js/drag-arrange.js"></script>


<!-- 定考科目分类模版 -->
<script type="text/html" id="deplistTpl">
{{ each list v i }}
<li data-id="{{v.id}}">{{v.text}}</li>
{{ /each }}
</script>

<!--  公司客户列表模板 -->
<script type="text/html" id="companylistTpl">
{{ each rs_msg v i }}
<li data-id="{{v.kh}}">
    <span class="img">
        <span class="img_box"><img src="{{v.answer_label}}" style="height:100%" alt="" /></span>
        <span class="status">{{ v.status == 2 ? "进行中" : "暂停"}}</span>
    </span>
    <span class="explain">{{v.client_name}}</span>
</li>
{{ /each }}
</script>

<!-- 项目列表模板 -->
<script type="text/html" id="projectTpl">
{{ each rs_msg v i }}
<div style="font-weight:700;margin-bottom:10px">
    {{@v.brief}}
</div>
<div class="item">
    {{@v.home_info}}
    <!-- <video src="/theme/drcenter/images/zb/01.mp4" controls="controls"></video> -->
    <div class="item_detail clearfix">
        <a href="{{v.link}}" target="_blank">
            <div class="user clearfix">
                <span class="user_img">
                    <img src="{{v.logo}}" alt="" style="height:100%" />
                </span>
                <div>
                    <p>
                        <span> {{v.title}} </span>
                        <strong>·</strong>
                        <font color="#4AABE3">关注</font>
                    </p>
                    <p style="font-size: 12px; color: #939aa3;">66万粉丝</p>
                </div>
            </div>
        </a>
        <div class="comment_num clearfix">
            <span class="img_box {{v.my_praise==1?'active':''}}" data-id="{{v.id}}">
                <img src="{{v.my_praise==1?'/theme/drcenter/images/survey/gooded.png':'/theme/drcenter/images/survey/good.png'}}" alt="" />
                <font>{{v.praise_num}}</font>
            </span>
            <span class="img_box" data-id="{{v.id}}">
                <img src="/theme/drcenter/images/survey/comment.png" alt="" />
                <font>{{v.comment_num}}</font>
            </span>
            <div class="comment_box" style="display: none;">
                <p class="input">
                    <input type="text" />
                    <button data-id="{{v.id}}">发送</button>
                </p>
                <div class="comment_detail">
                    <!-- 挖个坑 -->
                </div>
            </div>
        </div>
    </div>
</div>
{{ /each }}
</script>


<!-- 评论列表模板 -->
<script type="text/html" id="commentlistTpl">
{{each rs_msg v i }}
<p><span data-id="{{v.id}}">X</span>{{v.message}}</p>
{{/each}}
</script>

<!-- 我的科室列表 -->
<script type="text/html" id="mylistTpl">
{{ each list v i }}
<button data-id="{{v.id}}" class="button draggable-element choosed">{{v.text}}</button>
{{/each}}
</script>



<!-- 科室列表 -->
<script type="text/html" id="morelistTpl">
{{each rs_msg v i }}
<button data-id="{{v.id}}" class="button draggable-element">{{v.label_cn}}</button>
{{/each}}
<button class="button border_red">更多</button>
</script>

<script>
    $(function() {
        $("html").css("fontSize", "16px")
        var data = JSON.parse(localStorage.getItem("data"))
        var token = data.login_token || ""
        var uid = data.uid || ""
        // 获取我的科目数据
        var choosed_arr = JSON.parse(localStorage.getItem("choosed_arr")) || []
        $(".dep_list").html(template("deplistTpl", {
            list: choosed_arr
        }))

        // 获取公司客户列表数据
        request_back('api_study/company_list', {
                uid: uid,
                login_token: token,
            },
            true,
            function(info) {
                console.log(info)
                $(".company_list").html(template("companylistTpl", info))
            })


        // 获取项目请求列表数据
        function get_survey_list(search_key, subject_id, company_id) {
            request_back('api_study/survey_list', {
                    uid: uid,
                    login_token: token,
                    search_key: search_key,
                    subject_id: subject_id,
                    company_id: company_id
                },
                true,
                function(info) {
                    console.log(info)
                    $(".project").html(template("projectTpl", info))
                    // video
                    $("video").attr("poster", "/theme/drcenter/images/zb/poster.jpg");
                    // $("video").attr("autoplay", "autoplay")
                    // $("video").attr("preload", "auto");
                    $("video").css({
                        "object-fit": "fill"
                    })
                    $("video").attr("webkit-playsinline", "true");
                    $("video").attr("playsinline", "true");
                })
        }

        // 默认显示
        get_survey_list()
        // 输入内容搜索
        $(".search_input").keyup(function(e) {
            var search_key = $(this).val().trim()
            if (e.keyCode == 13) {
                get_survey_list(search_key, "", "")
            }
        })

        // 点击科室搜索
        $(".dep_list").on("click", "li", function() {
            var subject_id = $(this).data("id")
            get_survey_list("", subject_id, "")
        })

        //  点击公司搜索
        $(".company_list").on("click", "li", function() {
            var company_id = $(this).data("id")
            get_survey_list("", "", company_id)
        })

        // 评论题型
        // 点击其他地方隐藏
        $(".project ").on("click", ".input", function(e) {
            e.stopPropagation();
        });
        $(document).click(function() {
            $(".comment_box").hide();
            $(".comment_num span:nth-child(2)").removeClass("active")
        });

        // 点赞
        $(".project").on("click", ".comment_num .img_box:nth-child(1)", function() {
            var shumb_up;
            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                shumb_up = 1
                $(this).children("img").attr("src", "/theme/drcenter/images/survey/gooded.png");
                $(this)
                    .children("font")
                    .text(+$(this).text() + 1);
            } else {
                shumb_up = 0
                $(this).children("img").attr("src", "/theme/drcenter/images/survey/good.png");
                $(this)
                    .children("font")
                    .text(+$(this).text() - 1);
            }

            // 点赞接口
            request_back('api_study/shumb_up', {
                    uid: uid,
                    login_token: token,
                    pid: $(this).data("id"),
                    shumb_up: shumb_up,
                },
                true,
                function(info) {
                    console.log(info)
                })

        });


        // 评论 
        $(".project").on("click", ".comment_num span:nth-child(2)", function(e) {
            e.stopPropagation();
            $(".comment_box").hide()
            $(this).next().show();
            $(this).addClass("active");
            $(this).next().children().children("input").focus()

            // if ($(this).hasClass("active")) {
            //     $(this).children("img").attr("src", "/theme/drcenter/images/survey/commented.png");
            // } else {
            //     $(this).children("img").attr("src", "/theme/drcenter/images/survey/comment.png");
            // }

            var that = $(this)
            if ($(this).hasClass("active")) {
                //  获取评论列表
                request_back('api_study/comment_list', {
                        uid: uid,
                        login_token: token,
                        pid: $(this).data("id")
                    },
                    false,
                    function(info) {
                        console.log(info)
                        $(".comment_detail").html(template("commentlistTpl", info))
                    })
            }
        });

        // 回车发送
        $(".project").on("keyup", " .comment_box .input input", function(e) {
            var text = $(this).val();
            if (text && e.keyCode == "13") {
                $(".comment_box button").trigger("click")
            }
        })

        // 发送按钮
        $(".project").on("click", ".comment_box button", function() {
            var text = $(this).prev().val();
            if (text.trim() != "") {
                var num = $(this).parent().parent().prev().children("font").text()
                $(this).parent().parent().prev().children("font").text(+num + 1)
                $(this).parents().next(".comment_detail").prepend(
                    "<p>" +
                    "<span>X</span>" +
                    text +
                    "</p>"
                );
                $(this).prev().val("");
                // 添加评论
                request_back('api_study/add_comment', {
                        uid: uid,
                        login_token: token,
                        pid: $(this).data("id"),
                        message: text
                    },
                    true,
                    function(info) {
                        console.log(info)
                    })
            }
        });

        // 删除评论
        $(".project").on("click", ".comment_box span", function() {
            var that = $(this)
            // 删除评论接口的对接
            request_back('api_study/del_comment', {
                    uid: uid,
                    login_token: token,
                    id: $(this).data("id")
                },
                true,
                function(info) {
                    // console.log(info)
                    var num = that.parent().parent().parent().prev().children("font").text()
                    that.parent().parent().parent().prev().children("font").text(+num - 1)
                    that.parent().remove();
                })


        });

        // 我的科室
        $(".my_list").html(template("mylistTpl", {
            list: choosed_arr
        }))
        // manage显示
        $(".department img").click(function() {
            $(".manage").show();
            // 显示所有的科室
            request_back('api_study/dep_list', {
                    uid: uid,
                    login_token: token,
                },
                false,
                function(info) {
                    $(".more_list").html(template("morelistTpl", info))
                    $(".more_list .draggable-element").each(function() {
                        var id = $(this).data("id")
                        for (var i = 0; i < choosed_arr.length; i++) {
                            if (id == choosed_arr[i].id) {
                                $(this).addClass("choosed")
                            }
                        }
                    })
                })
            // manage的拖拽效果
            $(".draggable-element").arrangeable();
        });

        // 点击切换颜色
        $(".manage").on("click", "button", function() {
            $(this).toggleClass("choosed")
            if ($(this).hasClass("choosed")) {
                choosed_arr.push({
                    id: $(this).data("id"),
                    text: $(this).text()
                })
                // 复杂数组去重
                function uniqObjInArray(objarray) {
                    let len = objarray.length;
                    let tempJson = {};
                    let res = [];
                    for (let i = 0; i < len; i++) {
                        //取出每一个对象
                        tempJson[JSON.stringify(objarray[i])] = true;
                    }
                    let keyItems = Object.keys(tempJson);
                    for (let j = 0; j < keyItems.length; j++) {
                        res.push(JSON.parse(keyItems[j]));
                    }
                    return res;
                }
                localStorage.setItem("choosed_arr", JSON.stringify(uniqObjInArray(choosed_arr)))
                $(".my_list").html(template("mylistTpl", {
                    list: uniqObjInArray(choosed_arr)
                }))
            } else {
                var id = $(this).data("id")
                for (var i = 0; i < choosed_arr.length; i++) {
                    if (choosed_arr[i].id == id) {
                        choosed_arr.splice(i, 1)
                        localStorage.setItem("choosed_arr", JSON.stringify((choosed_arr)))
                        $(".my_list").html(template("mylistTpl", {
                            list: choosed_arr
                        }))
                    }
                }
            }
        })

        // 我的科室点击删除
        $(".manage").on("click", ".my_list button", function() {
            var id = $(this).data("id")
            $(this).remove()
            $(".manage .more_list .draggable-element").each(function() {
                console.log($(this))
                console.log($(this).data("id"))
                if ($(this).data("id") == id) {
                    $(this).removeClass("choosed")
                }
            })

            for (var i = 0; i < choosed_arr.length; i++) {
                if (choosed_arr[i].id == id) {
                    choosed_arr.splice(i, 1)
                    localStorage.setItem("choosed_arr", JSON.stringify(choosed_arr))
                }
            }


        })

        // manage隐藏
        $(".manage .title span").click(function() {
            $(".manage").hide();
            $(".dep_list").html(template("deplistTpl", {
                list: choosed_arr
            }))
        });
    });
</script>