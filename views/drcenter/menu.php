<style>
  .foot {
    height: 70px !important;
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    background: #fff;
    border-top: 1px solid #cecece;
    box-sizing: border-box;
  }

  .foot li {
    float: left;
    font-size: 14px;
    height: 100%;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
    width: 20%;
    text-align: center;
  }

  .foot li .word {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 45px;
  }

  .foot li .img {
    position: absolute;
    width: 26px;
    height: 26px;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
  }

  .foot li .img img {
    height: 100% !important;
    width: 100% !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
</style>
<ul class="foot">
  <li data-html="index" style="color: #4AABE3;">
    <span class="img">
      <img src="/theme/drcenter/images/tab/indexed.png" alt="" />
    </span>
    <span class="word">首页</span>
  </li>
  <li data-html="study">
    <span class="img">
      <img src="/theme/drcenter/images/tab/study.png" alt="" />
    </span>
    <span class="word">学习</span>
  </li>
  <li data-html="find">
    <span class="img">
      <img src="/theme/drcenter/images/tab/find.png" alt="" />
    </span>
    <span class="word">发现</span>
  </li>
  <li data-html="fans">
    <span class="img">
      <img src="/theme/drcenter/images/tab/fans.png" alt="" />
    </span>
    <span class="word">粉丝</span>
  </li>
  <li data-html="mine">
    <span class="img">
      <img src="/theme/drcenter/images/tab/mine.png" alt="" />
    </span>
    <span class="word">我的</span>
  </li>
</ul>

<script>
  $(function() {
    // tab栏的点击效果
    $(".foot li").click(function() {
      var html = $(this).data("html");
      window.location.href = base_url + html;
    });

    // 获取地址栏参数
    var pathname = window.location.pathname
    var pathname_array = pathname.split("/")
    var path = pathname_array[pathname_array.length - 1]
    $(".foot li").each(function() {
      var html = $(this).data("html")
      if (html == path) {
        $(this)
          .children(".img")
          .children()
          .attr("src", "/theme/drcenter/images/tab/" + html + "ed.png");
        $(this).css("color", "#4AABE3");

        $(this)
          .siblings()
          .each(function() {
            $(this)
              .children(".img")
              .children()
              .attr("src", "/theme/drcenter/images/tab/" + $(this).data("html") + ".png");
          });
        $(this).siblings().css("color", "#686868");
      }
    })
  });
</script>