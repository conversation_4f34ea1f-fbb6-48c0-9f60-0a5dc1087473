<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>关于中国医师职业状况和心理健康问题的调研</title>
<!--    <link href="/theme/management/css/style.css" rel="stylesheet">-->
    <style>
        *{
            margin: 0;padding: 0;
        }
        body{
            font-size: 100px;
        }
        .head{
            font-size: 2.4rem;
            text-align: center;
        }
        .body{
            width: 90%;
            margin: 1.5rem auto;
            font-size: 1.6rem;
        }
        header>p:nth-child(2){
            color: #8c8c8c;
            margin-top: 1rem;
            font-size: 1.4rem;
            text-align: center;
        }
        .content{
            font-size: 1.4rem;
            margin-top: 2rem;
            line-height: 2.5rem;
            border-radius: 0.5rem;
            height: auto;
            box-shadow: rgb(221, 221, 221) 5px 5px 8px;
        }
        .content>p{
            padding: 1rem;
        }
        .content-title{
            margin-top: 2rem;
            font-size: 1.4rem;
            line-height: 2.5rem;
            /*text-align: center;*/
        }
        .content-title>a{
            text-decoration: none;
            color: #607fa6;}

        .content-title>a>span{
            display: inline-block;
            height: 1.5rem;
            width: 1.5rem;
            border-radius: 50%;
            background: rgba(177, 0, 0, 0.61);
            text-align: center;
            color: #ffffff;
            margin-right: 0.5rem;
            line-height: 1.5rem;
            font-size: 1.2rem;
        }
        .btn {
            border-radius: 3px;
        }
        .btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .btn-w-m {
            min-width: 120px;
        }
        .btn-info {
            background-color: #23c6c8;
            border-color: #23c6c8;
            color: #FFFFFF;
        }

    </style>
</head>
<script>
    // “()()”表示自执行函数
    (function (doc, win) {
        var docEl = doc.documentElement,
            // 手机旋转事件,大部分手机浏览器都支持 onorientationchange 如果不支持，可以使用原始的 resize
            resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
            recalc = function () {
                //clientWidth: 获取对象可见内容的宽度，不包括滚动条，不包括边框
                var clientWidth = docEl.clientWidth;
                if (!clientWidth) return;
                docEl.style.fontSize = 10*(clientWidth / 320) + 'px';
            };

        recalc();
        //判断是否支持监听事件 ，不支持则停止
        if (!doc.addEventListener) return;
        //注册翻转事件
        win.addEventListener(resizeEvt, recalc, false);

    })(document, window);
</script>

<body>
<div class="body">
    <header>
        <p class="head">关于中国医师职业状况和心理健康问题的调研</p>
    </header>
    <aside class="back">
        <article class="content">
            <p>为更好的了解中国医师的职业状况与心理健康问题，推进健康中国建设，从2016年开始开展《关于中国医师职业状况和心理健康问题的调研报告》项目，以全国医师为调查对象，广泛调研全国医师的职业现状，全面分析其心理健康和精神需求状况，深入了解医师群体的思想状况和利益诉求，从而改善医师职业环境，形成尊医重卫社会风气，为实现健康中国梦做出贡献！</p>
        </article>
    </aside>
    <br />
    <br />
    <div align="center">
        <button type="button" class="btn btn-w-m btn-info" onclick="javascript:location.href='http://survey.euro.confirmit.com/wix/p1863423685.aspx'">
            进入问卷
        </button>
    </div>
</div>
</body>


</html>