<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo !empty($title) ? '上医说-' . $title : '上医说' ?></title>
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/animate.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/style.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/sumoselect.min.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/plugins/select2/select2.min.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/jquery.datetimepicker.css" rel="stylesheet">
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/plugins/toastr/toastr.min.css" rel="stylesheet">


    <!--    <script src="/theme/admin/common/layui/jquery-1.9.1.min.js"></script>-->
    <!--    <script src="/theme/admin/jquery.datetimepicker.js"></script>-->
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/umeditor/third-party/jquery.min.js"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/js/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/js/jquery.sumoselect.min.js"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/umeditor/third-party/template.min.js"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/js/plugins/toastr/toastr.min.js"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/umeditor/umeditor.config.js" charset="utf-8"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/umeditor/umeditor.min.js" charset="utf-8"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/datetimepicker/js/bootstrap-datetimepicker.js" charset="UTF-8"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/datetimepicker/js/locales/bootstrap-datetimepicker.fr.js" charset="UTF-8"></script>
    <!--    项目子菜单导航-->
    <script src="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/jquery-js/public.js"></script>

    <!--提交loading样式-->
    <link href="/theme/<?php echo TEMPLATE_DIR_NEW; ?>/css/plugins/ladda/ladda-themeless.min.css" rel="stylesheet">

    <!--富文本编辑器-->
    <!--    <link href="/theme/--><?php //echo TEMPLATE_DIR_NEW;
    ?>
    <!--/css/plugins/summernote/summernote.css" rel="stylesheet">-->
    <!--    <link href="/theme/--><?php //echo TEMPLATE_DIR_NEW;
    ?>
    <!--/css/plugins/summernote/summernote-bs3.css" rel="stylesheet">-->
    <style>
        .footer{
            position: relative !important;
        }
    </style>
</head>
<html>
<!--不发送Referrer信息-->
<!--<meta name="referrer" content="no-referrer">-->
<div id="wrapper">
    <div  class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight" style="margin-top: -20px;">
        <div class="row">
            <div class="col-lg-12" style="padding-right: 0px;padding-left: 0px; border:0px solid #FFFFFF;">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">

                        <h5>总量：<font color="red"><?php echo $tongji_data['total'];?></font>      已经清洗：<font color="red"><?php echo $tongji_data[1];?></font></h5>
                    </div>
                    <div class="ibox-content">
                        <form method="get" id="search_from">
                            <div class="col-sm-12 m-b-xs">
                                <div class="input-group pull-right" style="margin-right: -12px">

                                    <input type="text" placeholder="医院名称" name="search_unit" value="<?php echo !empty($param['search_unit']) ? $param['search_unit'] : ''; ?>" class=" form-control m-b " style="float: left;width: 160px;font-size:11px;">
                                    <input type="text" placeholder="医师名称" name="search_doctor" value="<?php echo !empty($param['search_doctor']) ? $param['search_doctor'] : ''; ?>" class=" form-control m-b " style="float: left;width: 160px;font-size:11px;">
                                    <span style="float: left;" class="input-group-btn">
                                        <input type="hidden" name="code" value="<?php echo $code;?>" />
                                        <button type="submit" class="btn btn-success">搜 索</button>
                                        <!--<button type="button" class="btn btn-warning" onclick="location.href='/baidu_data_clean/qkc_data_import'">导出</button>-->
                                    </span>
                                </div>
                            </div>
                        </form>
                        <div style="width:100%;overflow-x: scroll;">
                            <form id="the_from" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th data-toggle="true" style="width:20px;">
                                                <input type="checkbox" name="" class="i-checks select_modify">
                                            </th>
                                            <th style="text-align:center;">ID</th>
                                            <th style="text-align:center;">UID</th>
                                            <th style="text-align:center;">批次</th>
                                            <th style="text-align:center;min-width:60px;">省份</th>
                                            <th style="text-align:center;min-width:60px;">城市</th>
                                            <th style="text-align:center;min-width:60px;">城市ID</th>
                                            <th style="text-align:center;min-width:150px;">医院名称</th>
                                            <th style="text-align:center;min-width:80px;">科室</th>
                                            <th style="text-align:center;min-width:80px;">医生姓名</th>
                                            <th style="text-align:center;min-width:60px;">资格证号</th>
                                            <th style="text-align:center;min-width:60px;">性别</th>
                                            <th style="text-align:center;min-width:80px;">职称</th>
                                            <th style="text-align:center;min-width:80px;">职务</th>
                                            <th style="text-align:center;min-width:50px;">教学职称</th>
                                            <th style="text-align:center;min-width:50px;">专长</th>
                                            <!--<th style="text-align:center;min-width:30px;">百度查状态</th>-->
    <!--                                        <th style="text-align:center;min-width:50px;">简介</th>-->

                                            <th style="text-align:center;min-width:100px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        if ($list) {
                                            foreach ($list as $v) {
                                                if (isset($default_province_link[$v['province']])) {
                                                    $province_info_link = $default_province_link[$v['province']]['link'];
                                                } else {
                                                    $province_info_link = 'https://www.baidu.com/s?wd='.$v['province']."卫生健康委员会";
                                                }
                                                if (isset($default_province_link[$v['city']])) {
                                                    $city_info_link = $default_province_link[$v['city']]['link'];
                                                } else {
                                                    $city_info_link = 'https://www.baidu.com/s?wd='.$v['city']."卫生健康委员会";
                                                }
                                                $baidu_link = "https://www.baidu.com/s?wd={$v['unit_name']}-{$v['dr_name']}医师";
                                                ?>
                                                <tr>
                                                    <td style="text-align:center;">
                                                        <input type="checkbox" name="" class="i-checks ids" value="<?php echo $v['id'];?>">
                                                    </td>
                                                    <td><?php echo $v['id'];?></td>
                                                    <td><?php echo $v['uid'];?></td>
                                                    <td><?php echo $v['pc'];?></td>
                                                    <td><?php echo "<a href='{$province_info_link}' target='_blank'>".$v['province']."</a>";?></td>
                                                    <td><?php echo "<a href='{$city_info_link}' target='_blank'>".$v['city']."</a>";?></td>
                                                    <td><?php echo $v['city_id'];?></td>
                                                    <td><?php echo $v['unit_name'];?></td>
                                                    <td><input id="department_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][department]" value="<?php echo $v['department'];?>" /></td>
                                                    <td><input id="dr_name_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][dr_name]" value="<?php echo $v['dr_name'];?>" /></td>
                                                    <td><input id="zgz_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][zgz]" value="<?php echo $v['zgz'];?>" /></td>
                                                    <td><input id="dr_gender_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][dr_gender]" value="<?php echo $v['dr_gender'];?>" /></td>
                                                    <td><input id="dr_job_title_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][dr_job_title]" value="<?php echo $v['dr_job_title'];?>" /></td>
                                                    <td><input id="dr_position_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][dr_position]" value="<?php echo $v['dr_position'];?>" /></td>
                                                    <td><input id="dr_lable_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][dr_lable]" value="<?php echo $v['dr_lable'];?>" /></td>
                                                    <td><input id="dr_skill_<?php echo $v['id'];?>" class="form-control" type="text" name="edit[<?php echo $v['id'];?>][dr_skill]" value="<?php echo $v['dr_skill'];?>" /></td>

                                                    <!--<td><a onclick="change_baidu_status(<?php echo $v['id'];?>);" id="baidu_status_<?php echo $v['id'];?>"><?php echo $v['baidu'];?></a></td>-->
                                                    <!--<td><?php echo $v['dr_brief'];?></td>-->

                                                        <td style="text-align:center;">
                                                            <div class="btn-group">
                                                                <a class="btn btn-outline btn-success btn-xs" href="<?php echo $baidu_link;?>" target="_blank">百度查</a>
                                                                <a class="btn btn-outline btn-success btn-xs" onclick="save_qkc_data(<?php echo $v['id'];?>);">保存</a>
                                                            </div>
                                                        </td>
                                                </tr>
                                            <?php } ?>
                                        <?php } else { ?>
                                            <tr style="text-align: center">
                                                <td colspan="23">暂无数据</td>
                                            </tr>
                                            <?php
                                        }
                                        ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="20">
                                                <div class="form-group row">
                                                    <div class="col-sm-1 "><label class="checkbox iCheck-helper">
                                                            <input name="selAll" id="selAll" class="i-checks select_modify" onclick="selAll(this)" type="checkbox" /><span style="margin-left:10px;">全选</span></label>
                                                    </div>

                                                    <div class="col-sm-2 " style="margin-top: 3px;">
                                                        <span class="input-group-btn">
                                                            <input type="hidden" name="code" value="<?php echo $code;?>" />
                                                            <input type="hidden" name="back_url" value="<?php echo $_SERVER['REQUEST_URI'];?>" />
                                                            <button type="submit" style="margin-top: -1px;" class="ladda-button btn btn-primary" data-style="slide-up">批量编辑</button>
                                                        </span>
                                                    </div>

                                                    <div class="col-sm-5 "></div>
                                                    <div class="col-sm-4" style="margin-top: 8px;"><div style="float:right;"><?php echo $pagination; ?></div></div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



<!-- 详情-->
<div class="modal inmodal fade" id="source_data" aria-hidden="true" data-backdrop="static" tabindex="-1">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">精准匹配<span id="source_type"></span></h4>
                <small class="font-bold" style="color:#FF0000;">精准匹配的列表.</small>
            </div>
            <div class="modal-body source_info">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<!-- 点击培训Btn 模态弹出框 End-->

<script type="text/javascript">
    $(document).ready(function () {
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });

    //单条保存
    function save_qkc_data(id){
        swal({
            title: "Are you sure?",
            text: "You will not be able to recover this imaginary file!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, update it!",
            closeOnConfirm: false
        }, function () {
            $.ajax({
                url: "/baidu_data_clean/save_qkc_data",
                type: 'post',
                dataType: 'json',
                data: {
                    id: id,
                    department: $("#department_" + id).val(),
                    dr_name: $("#dr_name_" + id).val(),
                    zgz: $("#zgz_" + id).val(),
                    dr_gender: $("#dr_gender_" + id).val(),
                    dr_job_title: $("#dr_job_title_" + id).val(),
                    dr_position: $("#dr_position_" + id).val(),
                    dr_lable: $("#dr_lable_" + id).val(),
                    dr_skill: $("#dr_skill_" + id).val(),
                    back_url: '<?php echo $_SERVER['REQUEST_URI']?>',
                    code: '<?php echo $code;?>',
                },
                success: function (res) {
                    if (res.rs_code == 'success') {
                        swal("operation!", "Your operation success.", "success");
                        setTimeout('location.href = "<?php echo $_SERVER['REQUEST_URI']?>";', 2000);
                    } else {
                        swal("operation!", res.rs_msg, "error");
                    }
                }
            });
        });
    }
</script>




<div class="bottom">
    <div class="footer">

        <div>
            <strong>Copyright</strong> © 2014 - <?php echo date('Y')?> Gooddr.com Limited All Rights Reserved.
        </div>
    </div>
</div>

</div>
</div>


<script type="text/javascript" src="/theme/admin/jquery-js/jquery-nav.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/jquery-3.1.1.min.js"></script>
<!--树形下拉js-->
<!--<script src="/theme/admin/tree1/js/jquery-git1.min.js"></script>-->
<script src="/theme/admin/tree1/js/jquery-sortable-lists.min.js"></script>
<!-- Mainly scripts -->

<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/bootstrap.min.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/inspinia.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/pace/pace.min.js"></script>

<!-- SUMMERNOTE -->
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/summernote/summernote.min.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/assets/js/comm_init.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/assets/js/comm_init_swal.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/assets/js/jquery.form.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/select2/select2.full.min.js"></script>


<!-- Sweet alert -->
<!--登陆后的首页图表-->
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/flot/jquery.flot.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/flot/jquery.flot.tooltip.min.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/flot/jquery.flot.spline.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/flot/jquery.flot.resize.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/flot/jquery.flot.pie.js"></script>
<!-- ChartJS-->
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/chartJs/Chart.min.js"></script>
<!--登陆后的首页图表-->
<!-- Nestable List -->
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/nestable/jquery.nestable.js"></script>

<!--自动完成-->
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/typehead/bootstrap3-typeahead.min.js"></script>
<script src="/theme/admin/select-js/jquery.contextmenu.r2.js"></script>
<!--地区选择-->
<script src="/theme/jquery-city/js/city-picker.data.js"></script>
<script src="/theme/jquery-city/js/city-picker.js"></script>



<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/iCheck/icheck.min.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/metisMenu/jquery.metisMenu.js"></script>

<!-- Ladda -->
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/ladda/spin.min.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/ladda/ladda.min.js"></script>
<script src="/theme/<?php echo TEMPLATE_DIR_NEW;?>/js/plugins/ladda/ladda.jquery.min.js"></script>

<!--富文本编辑器-->
<!--<script src="/theme/--><?php //echo TEMPLATE_DIR_NEW;?><!--/js/plugins/summernote/summernote.min.js"></script>-->


<script type="text/javascript">
    //每隔十分钟是否存在未回复的咨询数
    function realtime_consulting_number(){
        $.ajax({
            url: "/consulting/realtime_consulting_number",type: "post",dataType: "json",
            success: function(data) {
                var has = $('.consulting_index_new').children().hasClass('remind');
                if(has){
                    if(data.rs_msg==0){
                        $('.consulting_index_new').children('.remind').remove('.remind');
                    }else{
                        $('.consulting_index_new').children('.remind').text(data.rs_msg);
                    }
                }else{
                    if(data.rs_msg!=0){
                        $('.consulting_index_new').append('<span class="remind">'+data.rs_msg+'</span>');
                    }
                }
            }
        });
    }
    $(document).ready(function() {
        setInterval(function() {
            realtime_consulting_number();
        }, 600000);

        $(window).resize(function () {          //当浏览器大小变化时
            thimble();
        });
        thimble();
    });
    function thimble(){
        var bw = $("#top_bar").height()-35;
        $("#page_thimble").height(bw);
    }
</script>
<script>
    $(function(){
        $(".MenuList_title_27 h1").hover(function  () {
            var index=$(this).index(".MenuList_title_27 h1");
            $(this).addClass("ml27click").siblings().removeClass("ml27click");
            $($(".MenuList_list_27")[index]).show().siblings().hide();
        })
    })
</script>

<?php
$debug = $this->input->get('debug');
if ($debug == '1') {
    $this->output->enable_profiler(true); ?>
    <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/jquery/2.2.0/jquery.min.js"></script>
    <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.1.0/highlight.min.js"></script>
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.1.0/styles/github.min.css">
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css">
    <?php
} ?>
</body>
</html>
