<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>抽奖</title>
    <link rel="stylesheet" href="/theme/reward_2020/css/index.css" />
    <script type="text/javascript" src="/theme/reward_2020/js/jquery.min.js"></script>
    <script type="text/javascript" src="/theme/reward_2020/js/easing.js"></script>

</head>

<body>
    <!-- 主体盒子-->
    <div class="main-box">
        <div class="main">
            <!-- 数字背景盒子-->
            <div class="num-bg-box">
                <!-- 数字盒子-->
                <div class="num_box">
                    <div class="num firstNumber"></div>
                    <div class="num secondNumber"></div>
                    <div class="num thirdNumber"></div>
                </div>
            </div>
        </div>
        <!-- 操作按钮-->
        <div class="btn-box">
            <div class="btn btn_click"><img src="/theme/reward_2020/images/start_btn.png"></div>
        </div>
    </div>
    <!--js-->
    <script>
        var u = 100;
        var n = 1;
        var timer; //定义滚动的定时器
        let arr = [];
        for (var i = 1; i < 151; i++) {
            if (i < 10) {
                arr.push('00' + i);
            } else if (i < 100) {
                arr.push('0' + i);
            } else {
                arr.push(i);
            }
        }
        var isBegin = false; //标识能否开始抽奖

        $(".num").css('backgroundPositionY', 0); //开始13888888888
        $(".num").eq(0).css('backgroundPositionY', 0)
        $(".num").eq(1).css('backgroundPositionY', 0)

        //执行数字滚动
        function firstNumber() {
            n++;
            $(".firstNumber").each(function (index) {
                var _num = $(this);
                _num.animate({
                    backgroundPositionY: ((u + 1) * n * (index + 1))
                }, 100);

            });
            firstNumbertimer = window.setTimeout(firstNumber, 100);
            isBegin = true;

        }

        function secondNumber() {
            n++;
            $(".secondNumber").each(function (index) {
                var _num = $(this);
                _num.animate({
                    backgroundPositionY: ((u + 2) * n * (index + 2))
                }, 100);

            });
            secondNumbertimer = window.setTimeout(secondNumber, 100);
            isBegin = true;
        }

        function thirdNumber() {
            n++;
            $(".thirdNumber").each(function (index) {
                var _num = $(this);
                _num.animate({
                    backgroundPositionY: ((u + 3) * n * (index + 3))
                }, 100);

            });
            thirdNumbertimer = window.setTimeout(thirdNumber, 100);
            isBegin = true;
        }
        $(function () {
            var count = 1;
            $(".btn_click").click(function () {
                if (count % 2 == 0) { // 偶数次
                    stop();
                    count++;
                    $(this).html('<img src="/theme/reward_2020/images/start_btn.png">');
                } else {
                    start();
                    count++;
                    $(this).html('<img src="/theme/reward_2020/images/stop_btn.png">');
                }
            })
            //开始抽奖
            function start() {
                if (isBegin) {
                    return false;
                } else {
                    firstNumber();
                    secondNumber();
                    thirdNumber();

                }
            }
            $.ajaxSetup({ async: false});
            //停止抽奖
            function stop() {
                // var result = arr[rand(arr.length)]; //指定中奖结果,可以抽取指定数组中的某一个
                var result = '000'; //指定中奖结果,可以抽取指定数组中的某一个
                $.post('/reward_2020/lottery',{},function (t) {
                    if(t.rs_code == 'success'){
                        result = t.rs_msg;
                    } else {
                        alert(t.rs_msg);
                    }
                },'json');
                var removeItem = result;
                arr = jQuery.grep(arr, function (value) {
                    return value != removeItem;
                });
                var num_arr = (result + '').split('');
                $(".firstNumber").each(function (index) {
                    var _num = $(this);
                    setTimeout(function () {
                        _num.animate({
                            backgroundPositionY: (u * 60) - (u * num_arr[0])
                        }, {
                            duration: 0,
                            easing: "easeInOutCirc",
                            complete: function () {
                                if (index == 10) {
                                    isBegin = false;
                                }
                            }
                        });
                        window.clearTimeout(firstNumbertimer);
                        isBegin = false;
                    }, 100);
                });
                $(".secondNumber").each(function (index) {

                    var _num = $(this);
                    setTimeout(function () {
                        _num.animate({
                            backgroundPositionY: (u * 60) - (u * num_arr[1])
                        }, {
                            duration: 0,
                            easing: "easeInOutCirc",
                            complete: function () {
                                if (index == 10) {
                                    isBegin = false;
                                }
                            }
                        });
                        window.clearTimeout(secondNumbertimer);
                        isBegin = false;
                    }, 700);

                });
                $(".thirdNumber").each(function (index) {

                    var _num = $(this);
                    setTimeout(function () {
                        _num.animate({
                            backgroundPositionY: (u * 60) - (u * num_arr[2])
                        }, {
                            duration: 0,
                            easing: "easeInOutCirc",
                            complete: function () {
                                if (index == 10) {
                                    isBegin = false;
                                }
                            }
                        });
                        window.clearTimeout(thirdNumbertimer);
                        isBegin = false;
                    }, 1400);
                });
            }
        });

        function rand(number) {
            return Math.floor(Math.random() * number);
        }
    </script>

</body>

</html>
