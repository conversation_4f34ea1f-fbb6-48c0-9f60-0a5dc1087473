<?php if($next_q){?>
<form method="post">
    <div>
        <?php if($default_error_info){?>
            <span style="color:red;"><?php echo $default_error_info;?></span><br />
        <?php }?>
        <?php if($error_info){?>
            <span style="color:red;"><?php echo $error_info;?></span><br />
        <?php }?>
        <?php echo $next_q['question_another_name'];?><br />
        <?php if(in_array($next_q['question_type'], ['single','multi'])){?>
            <?php foreach ($q_option as $k => $v) {?>
                <label>
                    <input type="<?php echo $this->default_input[$next_q['question_type']];?>" name="<?php echo $next_q['id_json'].($next_q['question_type'] == "multi" ? "[]" : "");?>" value="<?php echo $k;?>" /><?php echo $v;?>
                </label><br />
            <?php }?>
        <?php } else {?>
            <label>
                <input type="<?php echo $this->default_input[$next_q['question_type']];?>" name="<?php echo $next_q['id_json'].($next_q['question_type'] == "multi" ? "[]" : "");?>" value="<?php echo $k;?>" /><?php echo $v;?>
            </label><br />
        <?php }?>
    </div>
    <input type="hidden" name="sid" value="<?php echo md5(random_int(1,100));?>" />
    <?php if(!$answer_is_exist){?><!--不存在选题答案，说明是第一题-->
        <br /><input type="submit" value=">>" />
    <?php } else {?>
        <?php if($is_next_btn){?><!--不是最后一题-->
            <br />
            <input type="submit" value="<<" />
            <input type="submit" value=">>" />
        <?php } else {?>
            <br /><input type="submit" value="完成" />
        <?php }?>
    <?php }?>

</form>
<?php } else {?>
    <span style="color:red;"><?php echo $error_info;?></span><br />
<?php }?>
