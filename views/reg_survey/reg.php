<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0,user-scalable=no"
    />
    <title>信息确认</title>
    <script src="/theme/bk/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body,
        html {
            height: 100%;
        }
        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
        }
        input {
            -webkit-appearance: none;
        }
        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }
        .title {
            text-align: center;
            padding-top: 50px;
            font-weight: 700;
            font-size: 30px;
        }
        .content {
            text-align: center;
            padding-top: 50px;
        }
        .content .select {
            position: relative;
            color: #666;
            margin: 0 auto;
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            height: 36px;
            text-align: left;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }
        .content select {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            opacity: 0;
        }
        .content input[type="text"] {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 36px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }
        .msg {
            margin-top: 15px;
            color: red;
        }
        .btn {
            text-align: center;
            width: 90%;
            margin: 0 auto;
            margin-top: 40px;
        }
        .btn button {
            cursor: pointer;
            font-size: 24px;
            width: 45%;
            padding: 10px;
            border: none;
            outline: none;
            border-radius: 2px;
        }
        .btn .confirm {
            color: #fff;
            background: #f85691;
        }
        .btn .cancel {
            color: #000;
            background: transparent;
            border: 1px solid #fd5592;
        }

        .content {
            text-align: center;
            padding-top: 50px;
        }
        .content input {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 70%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .content .tips {
            font-size: 12px;
            text-align: left;
            width: 70%;
            margin: 0 auto;
            margin-top: 5px;
        }

        .content .code {
            width: 80%;
            height: 46px;
            margin: 0 auto;
            position: relative;
        }
        .content .code input {
            width: 100%;
        }
        .content .code .get_code {
            position: absolute;
            right: 0;
            height: 36px;
            line-height: 36px;
            background: #f85691;
            color: #fff;
            padding: 0 10px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="title">信息确认</div>

    <form action="">
        <div class="content">
            <p class="select">
                <span>请选择省份[必选]</span>
                <select name="" id="">
                    <option value="">请选择省份</option>
                    <option value="上海">上海</option>
                </select>
            </p>
            <p class="select">
                <span>请选择城市[必选]</span>
                <select name="" id="">
                    <option value="">请选择城市</option>
                    <option value="黄浦区">黄浦区</option>
                </select>
            </p>
            <input type="text" placeholder="请输入姓名[必填]" />
            <input type="text" placeholder="请输入医院[必填]" />
            <input type="text" placeholder="请输入身份证号[必填]" />
            <input type="text" id="phone" name="verify_mobile" placeholder="请输入手机号码" />
            <p class="code">
                <input type="text" id="code" name="verify_code" placeholder="请输入验证码" />
                <span class="get_code">获取验证码</span>
            </p>
            <p class="tips">为确保调研礼金支付给答卷者，请获取输入验证码，证明是您本人操作，谢谢！</p>

            <div class="msg">身份证与姓名不匹配，请确认后重新输入！</div>
        </div>

        <div class="btn">
            <button class="confirm submit" type="submit">确 认</button>
            <button class="cancel" type="reset">重 置</button>
        </div>
    </form>
</div>

<script src="https://www.to-pay.cn/theme/web/js/jquery-3.1.1.min.js"></script>
<script>
    $(function () {
        // 下拉框的选中效果
        $(".container").on("change", "select", function () {
            var text = $(this).children("select option:selected").text();
            $(this).parent().children("span").text(text);
        });

        // 清空表单内容
        $(".cancel").click(function () {
            $("form")[0].reset();
        });
    });
</script>
</body>
</html>
