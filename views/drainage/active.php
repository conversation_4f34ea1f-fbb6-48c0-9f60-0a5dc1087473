<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>健康通-活动</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body,
            html {
                height: 100%;
                width: 100%;
            }
            .container {
                max-width: 420px;
                margin: 0 auto;
                min-height: 100%;
                background: linear-gradient(#1cb9e8, #22f2f6);
                position: relative;
            }

            .container .bg_box {
                width: 100%;
                background: linear-gradient(#1cb9e8, #22f2f6);
                position: absolute;
                text-align: center;
                top: 50%;
                transform: translateY(-50%);
            }
            .container .bg_box .bg {
                width: 375px;
                object-fit: cover;
            }

            .container .bg_box .button {
                cursor: pointer;
                position: absolute;
                bottom: 40px;
                width: 60%;
                left: 50%;
                transform: translateX(-50%);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="bg_box">
                <img class="bg" src="/theme/drainage/index.jpg" alt="" />
                <img class="button" onclick="invite()" src="/theme/drainage/button.jpg" alt="" />
            </div>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);

            function invite() {
                location.href = "<?php echo $authen; ?>";
            }
        </script>
    </body>
</html>
