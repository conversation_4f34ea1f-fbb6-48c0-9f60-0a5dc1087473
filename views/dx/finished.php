

<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="上医说" />
    <meta name="description" content="上医说" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>健康通-上医说</title>
    <link rel="shortcut icon" href="/theme/images/favicon.ico" type="image/x-icon" />
    <!--    <link rel="stylesheet" href="http://medical.drrenew.com/theme/3sbioinc/font-awesome/css/font-awesome.css" />-->
    <link rel="stylesheet" href="/theme/management/font-awesome/css/font-awesome.css" />
    <script src="/theme/management/js/jquery-3.1.1.min.js"></script>
    <link href="/theme/css/web_style.css" rel="stylesheet">
    <!--防止被网络广告劫持弹出广告-->
    <style>
        html {
            display: none;
        }

        body,
        html {
            overflow-x: hidden;
        }

        .logo {
            width: 100%;
            margin: 0px 0 5px 15px;
        }

        .logo .web {
            float: right;
            font-size: 24px;
            margin-right: 30px;
            margin-top: 20px;
            color: #0b3c82;

        }

        .logo img {
            width: auto;
            height: 45px;
        }

        .line {
            width: 100%;
            height: 1px;
            background: #a2bde8;
            margin: 15px 0;
        }

        input {
            -webkit-appearance: none;
            outline: none;
        }

        button {
            cursor: pointer;
        }

        .payment_style label {
            text-align: left;
            display: block;
            padding: 5px 15px;
        }

        /* .payment_style label input {
            width: 20px !important;
        } */

        .alpay {
            padding: 20px 0;
        }

        .alpay label {
            display: block;
        }

        .alpay label span {
            display: inline-block;
            margin-bottom: 10px;
        }

        .alpay label input {
            width: 100% !important;
            height: 44px;
        }

        .single .input {
            display: inline-block;
            width: 22px;
            height: 22px;
            border: 1px solid #ccc;
            border-radius: 50%;
            box-sizing: border-box;
            margin-right: 10px;
            vertical-align: middle;
        }

        .submit {
            width: 90%;
            padding: 10px;
            border-radius: 5px;
            background: #fff;
            border: none;
            font-size: 22px;
            color: #fff;
            outline: none;
            letter-spacing: 10px;
            background: #53a4f4;
        }

        .box_pic_center {
            padding-bottom: 30px;
        }

        /* 对话框 */
        .dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            position: fixed;
            left: 0;
            top: 0;
        }

        .dialog {
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            background: #CAE1FF;
            text-align: center;
            width: 86%;
            color: #fff;
            min-height: 260px;
            font-size: 24px;
            border-radius: 3px;
            border: 1px solid #010810;
            box-sizing: border-box;
        }

        .dialog .title {
            background: #103e80;
            padding: 15px 0;
            border-bottom: 1px solid #072a56;
            color: #fff;
        }

        .dialog .word {
            padding: 40px 0;
            color: #041b3d;
            word-break: break-word;
        }

        .dialog .button {
            width: 100%;
        }

        .dialog .button button {
            word-break: break-word;
            box-sizing: border-box;
            width: 100%;
            border: none;
            outline: none;
            padding: 10px;
            background: #53a4f4;
            font-size: 22px;
            color: #fff;
            border-top: 1px solid #CAE1FF;
        }

        .dialog .last button {
            border-bottom: 1px solid #406eb0;
        }

        .dialog .first button {
            border-top: 1px solid #406eb0;
        }

        .dialog .bg {
            height: 20px;
            background: #3e6fb7;
            margin-top: 30px;
        }


        .dialog .button button:hover {
            background: #eda124;
        }

        .confirm_dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            position: fixed;
            top: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .confirm_dialog {
            width: 80%;
            min-height: 150px;
            margin: 0 auto;
            background: #fff;
            border: 1px solid #252424;
            border-radius: 5px;
            overflow: hidden;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .confirm_dialog .notice_title {
            padding: 10px 0;
            border-bottom: 1px solid #252424;
            background: #aa90da;
            font-size: 20px;
        }

        .confirm_dialog .notice_info {
            padding: 50px 0;
            font-size: 18px;
            border-bottom: 1px solid #252424;
        }

        .confirm_dialog .button {
            height: 44px;
            padding: 0px 30px;
        }

        .confirm_dialog .button button {
            height: 100%;
            min-width: 75px;
            border: 1px solid #ccc;
            outline: none;
            border-bottom: none;
            border-top: none;
            border-radius: 15px;
            font-size: 16px;
        }

        .confirm_dialog .button .cancel {
            background: #efefef;
            float: left;
        }

        .confirm_dialog .button .confirm {
            background: #aa90da;
            float: right;
        }

        .code {
            padding-top: 20px;
        }

        .code p {
            height: 40px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            border-radius: 0.15rem;
        }

        .code p input {
            height: 100%;
            padding: 0 10px;
            border: none;
        }

        /*.code p button {*/
        /*height: 100%;*/
        /*padding: 0 10px;*/
        /*border: none;*/
        /*outline: none;*/
        /*background: #a6ebcd;*/
        /*float: right;*/
        /*}*/

        .code p button {
            height: 100%;
            width: 120px;
            border: none;
            outline: none;
            background: #a6ebcd;
            float: right;
        }

        .code input[name="verify_code"] {
            width: 40%;
            float: left;
        }

        .disable {
            pointer-events: none;
        }
    </style>
    <script>
        if (self == top) {
            document.documentElement.style.display = 'block';
        } else {
            top.location = self.location;
        }
    </script>
    <!--防止被网络广告劫持弹出广告-->
    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(document.documentElement.getBoundingClientRect().width);
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + 'px';
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
<div class="layout">
    <div class="logo">
        <a href="javascript:;" title="drsay">
            <img src="/theme/admin/im/img/jkt_logo.png" title="drsay" alt="drsay" border="0" />
        </a>
        <!--<a style="float: right; margin-right: 30px;font-size: 20px;margin-top: 8px;" href="">关闭</a>-->
    </div>
    <div class="box_pic_top"></div>
    <div class="box_pic_center">
        <input type="hidden" name="scene" value="">
        <h1 class="title_font">
            <span class="STYLE1">提交完成</span>
        </h1>
        <h2 style="text-align: center;">

            <p class="status" style="font-size:22px;padding: 10px 0; text-align: left;">
            尊敬的张老师：
              <span style="display:block;padding-top: 30px;">
                恭喜老师，您已完成本次调研问卷！
            </span>

             <span style="display:block;padding-top: 30px;"> 健康通再次感谢您的支持！</span>
               
            </p>

                </div>
    <div class="box_pic_bottom"></div>
</div>

<!-- 提交成功提示框 -->
<div class="dialog_container">
    <div class="dialog">
        <p class="title">提交成功!!!</p>
        <p class="word">7个工作日内支付</p>
        
        <!--赛小欧访问员活动页，是否显示互联网活动-->
                
        <p class="button first">
            <button></button>
        </p>
        <p class="button last">
            <a href="http://www.gooddr.com?code=" target="_blank"><button>健康通官网</button></a>
        </p>
        <p class="bg"></p>
    </div>
</div>

<!-- 提交确认框 -->
<div class="confirm_dialog_container">
    <div class="confirm_dialog">
        <p class="notice_title">确认提醒</p>
        <p class="notice_info">您确认要选择这种支付方式吗?</p>
        <p class="button">
            <button class="cancel">取消</button>
            <button class="confirm">确认</button>
        </p>
    </div>
</div>


<div class="footer" style="clear:both;">
    Copyright &copy; 2020 Powered by 健康通
</div>

<script>
    // 单选题点击效果
    $('.payment_style input').click(function() {
        // 清空提示信息
        $(".submit_confirm").text("")
        // 点击样式
        if ($(this).prop("checked")) {
            $(this).prev().css({
                border: "7px solid #600ca9",
            });
            $(this).parent().parent().siblings(".single").children().children(".input").css({
                border: "1px solid #ccc"
            })
        }
        // 点击联动效果
        // 支付宝
        if ($(this).data("name") == "alpay") {
            $(".alpay").show()
            $(".wechat").hide()
            $(".phone").hide()
        }
        // 微信
        if ($(this).data("name") == "wechat") {
            $(".wechat").show()
            $(".alpay").hide()
            $(".phone").hide()

            // 微信支付即时检查
            // 定时请求
                        if ($("#wechat .wechat_msg").html() != '(成功)') {
                timeId = setInterval("check_order_info();", 2000);
            }
                    }
        // 积分
        if ($(this).data("name") == "point") {
            $(".alpay").hide()
            $(".wechat").hide()
            $(".phone").hide()
        }
        // 手机充值
        if ($(this).data("name") == "phone") {
            $(".phone").show()
            $(".alpay").hide()
            $(".wechat").hide()
        }
        // 慈善捐赠
        if ($(this).data("name") == "donation") {
            $(".alpay").hide();
            $(".wechat").hide();
            $(".phone").hide();
        }
    })

    var timeId;
    var scene = $("input[name='scene']").val()
    // 提交按钮
    $(".submit").click(function() {
        // 选择的支付方式支付的方式
        var payment = $('.payment_style input:checked').data("name")
        // 如果没有选择提示选择支付方式
        if (!payment) {
            $(".submit_confirm").text("请选择")
        } else {
            // 如果支付宝支付没有填写支付宝信息 提示填写完整信息
                        if (payment == "alpay" && (!$("input[name='payment_name']").val() || !$("input[name='payment_account']").val())) {
                $(".submit_confirm").text("请填写支付宝的完整信息")
                if (!$("input[name='payment_name']").val()) {
                    $("input[name='payment_name']").trigger("focus")
                } else {
                    $("input[name='payment_account']").trigger("focus")
                }
            }
            
            // 如果手机充值没有填写信息 提示填写完整信息
            

            // 完整填写信息显示确认框
            if (!$(".submit_confirm").text()) {
                $(".confirm_dialog_container").show()
            }

            // 确认框的取消
            $(".confirm_dialog_container .cancel").click(function() {
                $(".confirm_dialog_container").hide()
            })
            // 确认框的确定
            $(".confirm_dialog_container .confirm").off("click").on("click", function() {
                $(".confirm_dialog_container").hide()
                // 如果积分支付
                if (payment == "point") {
                    $(".submit_confirm").text("")
                }

                var payment_type = $(".single input:checked").val()
                var payment_name = $("input[name='payment_name']").val()
                var payment_account = $("input[name='payment_account']").val()
                var mobile_payment_account = $("input[name='mobile_payment_account']").val()

                var data = {
                    scene: scene,
                    payment_type: payment_type,
                    // AMY 2020-10-26 已经增加短信验证流程
                    bk_code: '',
                    survey_uid_code: '',
                    // AMY 2020-10-26 已经增加短信验证流程
                }

                if (payment_type == 206) {
                    data.payment_name = payment_name
                    data.payment_account = payment_account
                }
                if (payment_type == 209) {
                    data.mobile_payment_account = mobile_payment_account
                }

                if (!$(".submit_confirm").text()) {
                    $.ajax({
                        type: "post",
                        url: "/project_exchange/payment_sub",
                        data: data,
                        dataType: "json",
                        success: function(info) {
                            $(".submit_confirm").text(info.rs_msg)
                            if (info.rs_code == "re_account") {
                                $(".alpay").remove()
                                $(".wechat").remove()
                            }

                            // 提交成功显示对话框
                            if (info.rs_code == "success") {
                                $(".submit_confirm").text("")
                                $(".dialog_container").show()
                                if (!info.rs_msg) {
                                    $(".dialog .button.first").hide()
                                    $(".dialog .button.last").css({
                                        "border-top": "1px solid #406eb0"
                                    })
                                } else {
                                    $(".dialog .button.first button").text("继续参与下一个问卷[" + info.rs_msg.point + "]")
                                    $(".dialog .button.first button").click(function() {
                                        window.location.href = info.rs_msg.partner_link
                                    })
                                }
                            }
                        }
                    })
                }
            })
        }
    })

    // 支付宝姓名的验证
    $(".zfb_name input").blur(function() {
        var zfb_name = $(this).val();
        if (zfb_name) {
            var reg = /^([\u4e00-\u9fa5][·][\u4e00-\u9fa5]){2,20}|([\u4e00-\u9fa5][.][\u4e00-\u9fa5]){2,20}|([\u4e00-\u9fa5]){2,10}$/;
            if (!reg.test(zfb_name)) {
                $(this).val("");
                $(this).attr("placeholder", "请输入正确的姓名");
            }
        }
    });

    // 支付宝账号的验证
    $(".zfb_num input").blur(function() {
        var zfb_num = $(this).val();
        if (zfb_num) {
            var reg_num = /^1[3456789]\d{9}$/;
            var reg_email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            if (!reg_num.test(zfb_num) && !reg_email.test(zfb_num)) {
                $(this).val("");
                $(this).attr("placeholder", "请输入正确的支付宝账号");
            }
        }
    });

    // 手机验证
    $(".phone_num input").blur(function() {
        var phone_num = $(this).val();
        if (phone_num) {
            var reg_num = /^1[3456789]\d{9}$/;
            if (!reg_num.test(phone_num)) {
                $(this).val("");
                $(this).attr("placeholder", "您输入的手机号不合法");
            }
        }
    });


    // 微信支付请求检测是否已做支付
    function check_order_info() {
        $.ajax({
            type: "POST",
            url: "/bk/check_order_info",
            data: "scene=",
            success: function(str) {
                var json_msg = $.parseJSON(str);
                if (json_msg.rs_code == "success") {
                    $(".wechat").remove()
                    // $("#wechat").append("<font color='red'>(成功)<font>")
                    $("#wechat .wechat_msg").html("(成功)")
                    clearInterval(timeId)
                }
            }
        });
    }
</script>
</body>

</html>
