<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="Cache-Control" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>定性定量受访信息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        li {
            list-style: none;
        }

        .container {
            background: #fff;
            padding: 0 10px 0 10px;
            max-width: 500px;
            margin: 0 auto;
        }

        .logo {
            padding: 25px 0 5px 0;
            border-bottom: 1px solid #dee4ec;
        }

        .logo #logo_img {
            height: 30px;
            float: left;
        }

        .logo #logo_name {
            height: 30px;
            line-height: 30px;
            margin-left: 3px;
        }

        .title {
            font-size: 18px;
            text-align: center;
            font-weight: 700;
            height: 60px;
            line-height: 60px;
            position: relative;
            margin-bottom: 20px;
        }

        .title::after {
            content: "";
            position: absolute;
            width: 100%;
            height: 2.5px;
            background: #53a4f4;
            bottom: 0px;
            left: 0;
        }

        .sm {
            margin-bottom: 20px;
            background: #ebeef2;
            padding: 20px 10px;
            text-align: justify;
        }

        .sure_job {
            padding: 30px 0;
            background: #eaf0f1;
        }

        .sure_job .job li {
            text-align: center;
        }

        .sure_job .job li span {
            display: inline-block;
            width: 25%;
            text-align: left;
        }

        .sure_job .job li input,.sure_job .job li select {
            width: 65%;
        }

        input,select {
            outline: none;
            /* -webkit-appearance: none;
            -webkit-tap-highlight-color: rgba(255, 255, 255, 255); */
            border: 1px solid #dee4ec;
            padding-left: 5px;
            font-size: 14px;
            box-sizing: border-box;
            margin: 10px 0;
            border-radius: 0;
            height: 50px;
            color: rgb(102, 102, 102);
            border-color: rgb(222, 228, 236);
        }

        .content {
            margin-top: 20px;
            padding: 30px 0;
            background: #eaf0f1;
        }

        .content .head {
            height: 90px;
            padding: 0 20px;
        }

        .content .head img {
            width: 25px;
            margin-top: 10px;
        }

        .content .head .add {
            float: right;
            text-align: center;
            cursor: pointer;
        }

        .content ul li {
            text-align: center;
            border: 1px solid #ccc;
            margin: 0 10px;
        }

        .content ul li:not(:last-child) {
            border-bottom: none;
        }

        .content ul li .disease_name,
        .content ul li .operation_name {
            width: 70% !important;
        }

        .content ul li .disease_num,
        .content ul li .operation_num {
            width: 18% !important;
        }

        .content ul li .del {
            cursor: pointer;
        }

        .content ul li .del img {
            width: 18px;
            vertical-align: middle;
        }

        button.save {
            margin: 20px 0;
            border: none;
            outline: none;
            color: #fff;
            padding: 10px 0;
            background: #53a4f4;
            width: 50%;
            font-size: 24px;
            border-radius: 5px;
        }

        .search_box {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f5f5f5;
            padding: 10px;
            width: 80%;
            box-sizing: border-box;
            border-radius: 3px;
            border: 1px solid #ccc;
            font-size: 16px;
            z-index: 9;
            max-width: 400px;
        }

        .search_box input {
            height: 50px;
            line-height: 50px;
            width: 100%;
            padding: 0 10px;
        }

        .search_box ul {
            height: 300px;
            padding: 10px;
            overflow: auto;
        }

        .search_box ul li {
            padding-top: 10px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 搜索弹框 -->
        <div class="search_box">
            <input type="text" placeholder="请输入搜索内容" />
            <ul>
                <li>暂无数据</li>
                <!-- 挖个坑 -->
            </ul>
        </div>

        <!-- logo -->
        <div class="logo">
            <img id="logo_img" src="http://admin.drsay.cn/theme/dk/img/logo.png" />
            <span id="logo_name"></span>
        </div>

        <!-- 标题 -->
        <p class="title">定性定量受访信息表</p>

        <!-- 描述 -->
        <div class="sm">请仔细认真填写以下信息，确保准确无误。</div>

        <!-- form表单 -->
        <form id="form" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
            <!-- 确认你的职业信息 -->
            <div class="sure_job">
                <p style="
              font-weight: 700;
              text-align: left;
              margin-bottom: 10px;
              padding-left: 12px;
            ">请确认或更改您的执业信息</p>
                <ul class="job">
                    <li>
                        <span>省份</span>
                        <input name="edit[province]" type="text" placeholder="请输入所在省份" />
                    </li>
                    <li>
                        <span>城市</span>
                        <input name="edit[city]" type="text" placeholder="请输入所在城市" />
                    </li>
                    <li>
                        <span>疾病领域</span>
                        <input name="edit[disease]" type="text" placeholder="请输入疾病领域" />
                    </li>
                    <li>
                        <span>受访者类型</span>
                        <select name="edit[interviewee_type]">
                            <option>请选择受访者类型</option>
                            <option value="临床科室普通医生"> 临床科室普通医生</option>
                            <option value="临床科室科（副）主任"> 临床科室科（副）主任</option>
                            <option value="非临床科室普通技师/科员"> 非临床科室普通技师/科员</option>
                            <option value="非临床科室科（副）主任"> 非临床科室科（副）主任</option>
                            <option value="医院行政管理层"> 医院行政管理层</option>
                            <option value="KOL（省市级）"> KOL（省市级）</option>
                            <option value="KOL（国家级）"> KOL（国家级）</option>
                            <option value="护士"> 护士</option>
                            <option value="护士长"> 护士长</option>
                            <option value="患者（非肿瘤常见病）"> 患者（非肿瘤常见病）</option>
                            <option value="患者（肿瘤及罕见病）"> 患者（肿瘤及罕见病）</option>
                            <option value="患者家属（非肿瘤常见病）"> 患者家属（非肿瘤常见病）</option>
                            <option value="患者家属（肿瘤及罕见病）"> 患者家属（肿瘤及罕见病）</option>
                            <option value="患者组织/医疗平台负责人"> 患者组织/医疗平台负责人</option>
                            <option value="制药/器械厂家（经理级）"> 制药/器械厂家（经理级）</option>
                            <option value="制药/器械厂家（总监级及以上）"> 制药/器械厂家（总监级及以上）</option>
                            <option value="学术专家/学者（省市级）"> 学术专家/学者（省市级）</option>
                            <option value="学术专家/学者（国家级）"> 学术专家/学者（国家级）</option>
                            <option value="药店店长"> 药店店长</option>
                            <option value="药店店员"> 药店店员</option>
                            <option value="普通消费者"> 普通消费者</option>
                            <option value="特殊消费者"> 特殊消费者</option>
                            <option value="其他，请注明"> 其他，请注明</option>
                        </select>
                    </li>
                    <li>
                        <span>其他</span>
                        <input name="edit[interviewee_type_other]" type="text" placeholder="请输入其他受访者类型" />
                    </li>
                    <li>
                        <span>FW执行方法</span>
                        <select name="edit[fw_type]">
                            <option>请选择受访者类型</option>
                            <option value="定性IDI（面对面访问）"> 定性IDI（面对面访问）</option>
                            <option value="定性IDI（电话访问）"> 定性IDI（电话访问）</option>
                            <option value="定性FGD（线下座谈会/专家研讨会）"> 定性FGD（线下座谈会/专家研讨会）</option>
                            <option value="定性FGD（线上座谈会/专家研讨会）"> 定性FGD（线上座谈会/专家研讨会）</option>
                            <option value="定量F2F（线下面对面访问）"> 定量F2F（线下面对面访问）</option>
                            <option value="定量CATI"> 定量CATI</option>
                            <option value="定量Online（3rd-Party）"> 定量Online（3rd-Party）</option>
                            <option value="定量Online（IQVIA WeChat）"> 定量Online（IQVIA WeChat）</option>
                            <option value="定量Retail查店"> 定量Retail查店</option>
                            <option value="其他，请注明"> 其他，请注明</option>
                        </select>
                    </li>
                    <li>
                        <span>其他</span>
                        <input name="edit[fw_type_other]" type="text" placeholder="请输入其他FW执行方法" />
                    </li>
                    <li>
                        <span>受访者单位名称</span>
                        <input name="edit[interviewee_unit]" type="text" placeholder="请输入受访者所在单位名称" />
                    </li>
                    <li>
                        <span>医院级别</span>
                        <select name="edit[unit_level]">
                            <option>请选择医院级别</option>
                            <option value="三级"> 三级</option>
                            <option value="二级"> 二级</option>
                            <option value="一级"> 一级</option>
                            <option value="未评级"> 未评级</option>
                        </select>
                    </li>
                    <li>
                        <span>受访者科室/岗位</span>
                        <input name="edit[interviewee_department]" type="text" placeholder="请输入受访者所在科室/岗位" />
                    </li>
                    <li>
                        <span>受访者姓名</span>
                        <input name="edit[interviewee_name]" type="text" placeholder="请输入受访者姓名" />
                    </li>
                    <li>
                        <span>技术职称</span>
                        <select name="edit[interviewee_job_title]">
                            <option>请选择技术职称</option>
                            <option value="住院医师"> 住院医师</option>
                            <option value="主治医师"> 主治医师</option>
                            <option value="副主任医师"> 副主任医师</option>
                            <option value="主任医师"> 主任医师</option>
                        </select>
                    </li>
                    <li>
                        <span>行政职务</span>
                        <select name="edit[interviewee_position]">
                            <option>请选择行政职务</option>
                            <option value="科主任"> 科主任</option>
                            <option value="科副主任"> 科副主任</option>
                        </select>
                    </li>
                    <li>
                        <span>受访者座机</span>
                        <input name="edit[interviewee_landline]" type="text" placeholder="请输入受访者座机" />
                    </li>
                    <li>
                        <span>受访者手机</span>
                        <input name="edit[interviewee_phone]" type="text" placeholder="请输入受访者手机" />
                    </li>
                    <li>
                        <span>访问日期</span>
                        <input name="edit[visit_date]" type="text" placeholder="请输入访问日期" />
                    </li>
                    <li>
                        <span>访问时间</span>
                        <input name="edit[visit_time]" type="text" placeholder="请输入访问时间" />
                    </li>
                    <li>
                        <span>访问地点</span>
                        <input name="edit[visit_location]" type="text" placeholder="请输入访问地点" />
                    </li>
                    <li>
                        <span>礼金支付方</span>
                        <select name="edit[payer]">
                            <option>请选择礼金支付方</option>
                            <option value="IQVIA平台支付(研究生成二维码)"> IQVIA平台支付(研究生成二维码)</option>
                            <option value="IQVIA平台支付(代理生成二维码)"> IQVIA平台支付(代理生成二维码)</option>
                            <option value="由代理支付礼金"> 由代理支付礼金</option>
                        </select>
                    </li>
                    <li>
                        <span>礼金支付数额(元)</span>
                        <input name="edit[cash_amount]" type="text" placeholder="请输入礼金支付数额(元)" onkeyup="clearNoNum(this)" />
                    </li>
                    <li>
                        <span>礼金支付方式</span>
                        <select name="edit[payment_type]">
                            <option>请选择礼金支付方式</option>
                            <option value="现金"> 现金</option>
                            <option value="银行卡转账（公对私）"> 银行卡转账（公对私）</option>
                            <option value="支付宝转账（公对私）"> 支付宝转账（公对私）</option>
                            <option value="支付宝转账（私对私）"> 支付宝转账（私对私）</option>
                            <option value="微信转账（公对私）"> 微信转账（公对私）</option>
                            <option value="微信转账（私对私）"> 微信转账（私对私）</option>
                            <option value="积分"> 积分</option>
                            <option value="其他，请注明"> 其他，请注明</option>
                        </select>
                    </li>
                    <li>
                        <span>其他</span>
                        <input name="edit[payment_type_other]" type="text" placeholder="请输入其他礼金支付方式" />
                    </li>
                    <li>
                        <span>支付宝/微信账号</span>
                        <input name="edit[payment_account]" type="text" placeholder="代理为私对私支付时必填，无账号请注明已截图字样" />
                    </li>
                    <li>
                        <span>招募途径</span>
                        <input class="recruit_channel_name" id="recruit_channel" type="text" placeholder="请选择招募途径" readonly />
                        <input class="recruit_channel_id" name="edit[recruit_channel_id]" type="hidden" value="" />
                        <!--<select class="recruit_channel" name="recruit_channel_name">
                            <option>请选择招募途径</option>
                            <option value="IQVIA内部资源（包括微信平台或FW团队）">IQVIA内部资源（包括微信平台或FW团队）</option>
                            <option value="IQVIA研究团队自行招募（如PI，MC等）">IQVIA研究团队自行招募（如PI，MC等）</option>
                            <option value="北京奥力博市场咨询有限公司">北京奥力博市场咨询有限公司</option>
                            <option value="北京博华智信信息咨询有限公司">北京博华智信信息咨询有限公司</option>
                            <option value="北京缔五度管理咨询有限公司">北京缔五度管理咨询有限公司</option>
                            <option value="北京恒申博源信息咨询有限公司">北京恒申博源信息咨询有限公司</option>
                            <option value="北京捷峰联合市场咨询有限公司">北京捷峰联合市场咨询有限公司</option>
                            <option value="北京美利德科技有限公司">北京美利德科技有限公司</option>
                            <option value="北京仁盈通智咨询有限公司">北京仁盈通智咨询有限公司</option>
                            <option value="北京医脉互通科技有限公司">北京医脉互通科技有限公司</option>
                            <option value="北跃（天津）企业管理咨询有限公司">北跃（天津）企业管理咨询有限公司</option>
                            <option value="成都励精企业管理咨询有限公司">成都励精企业管理咨询有限公司</option>
                            <option value="成都智信迈市场调研有限公司">成都智信迈市场调研有限公司</option>
                            <option value="成都卓讯市场调查有限公司">成都卓讯市场调查有限公司</option>
                            <option value="福州奥通营销咨询服务有限公司">福州奥通营销咨询服务有限公司</option>
                            <option value="福州千询市场调查有限公司">福州千询市场调查有限公司</option>
                            <option value="上海超正科技">上海超正科技</option>
                            <option value="甘肃意通市场研究有限公司">甘肃意通市场研究有限公司</option>
                            <option value="广州成凯市场调研有限公司">广州成凯市场调研有限公司</option>
                            <option value="广州勤越信息咨询有限公司">广州勤越信息咨询有限公司</option>
                            <option value="广州市晶协信息咨询有限公司">广州市晶协信息咨询有限公司</option>
                            <option value="广州协博市场信息咨询有限公司">广州协博市场信息咨询有限公司</option>
                            <option value="哈尔滨远星信息咨询有限公司">哈尔滨远星信息咨询有限公司</option>
                            <option value="海鄞信息咨询(上海)有限公司">海鄞信息咨询(上海)有限公司</option>
                            <option value="杭州邦略信息技术有限公司">杭州邦略信息技术有限公司</option>
                            <option value="杭州佳盟商务咨询有限公司">杭州佳盟商务咨询有限公司</option>
                            <option value="杭州朗顿商务咨询有限公司">杭州朗顿商务咨询有限公司</option>
                            <option value="吉林省广深市场调查顾问有限责任公司">吉林省广深市场调查顾问有限责任公司</option>
                            <option value="济南瑞恒市场调查有限公司">济南瑞恒市场调查有限公司</option>
                            <option value="济南万达信息咨询有限公司">济南万达信息咨询有限公司</option>
                            <option value="健康通（北京）网络科技有限公司">健康通（北京）网络科技有限公司</option>
                            <option value="久远谦长（上海）企业管理咨询有限公司">久远谦长（上海）企业管理咨询有限公司</option>
                            <option value="昆明希杰信息咨询有限公司">昆明希杰信息咨询有限公司</option>
                            <option value="六晟信息科技（杭州）有限公司">六晟信息科技（杭州）有限公司</option>
                            <option value="南京国创市场信息咨询有限公司">南京国创市场信息咨询有限公司</option>
                            <option value="南京鹤儒市场研究有限公司">南京鹤儒市场研究有限公司</option>
                            <option value="南京科佰伊市场研究有限公司">南京科佰伊市场研究有限公司</option>
                            <option value="南京欧迈市场研究有限公司">南京欧迈市场研究有限公司</option>
                            <option value="太原市齐天恒">太原市齐天恒</option>
                            <option value="青岛爱斯特市场研究有限公司">青岛爱斯特市场研究有限公司</option>
                            <option value="青岛瑞格大公企业管理咨询有限公司">青岛瑞格大公企业管理咨询有限公司</option>
                            <option value="厦门玮施信息咨询有限公司">厦门玮施信息咨询有限公司</option>
                            <option value="山西同达信息咨询服务中心">山西同达信息咨询服务中心</option>
                            <option value="上海铂嘉医疗管理有限公司">上海铂嘉医疗管理有限公司</option>
                            <option value="上海博度市场营销咨询有限公司">上海博度市场营销咨询有限公司</option>
                            <option value="上海精微市场信息咨询有限公司">上海精微市场信息咨询有限公司</option>
                            <option value="上海掘瀚企业管理有限公司">上海掘瀚企业管理有限公司</option>
                            <option value="上海库润信息技术有限公司">上海库润信息技术有限公司</option>
                            <option value="上海遴闻商务信息咨询有限公司">上海遴闻商务信息咨询有限公司</option>
                            <option value="上海钤津咨询管理有限公司">上海钤津咨询管理有限公司</option>
                            <option value="上海商霖华通投资咨询有限公司">上海商霖华通投资咨询有限公司</option>
                            <option value="上海叙岑商务咨询有限公司">上海叙岑商务咨询有限公司</option>
                            <option value="上海勋康市场营销策划事务所">上海勋康市场营销策划事务所</option>
                            <option value="上海真睿商务信息咨询有限公司">上海真睿商务信息咨询有限公司</option>
                            <option value="上海智谱信息科技有限公司">上海智谱信息科技有限公司</option>
                            <option value="深圳市海航市场营销策划有限公司">深圳市海航市场营销策划有限公司</option>
                            <option value="深圳市依玛思创管理咨询有限公司">深圳市依玛思创管理咨询有限公司</option>
                            <option value="沈阳艾思博市场研究有限公司">沈阳艾思博市场研究有限公司</option>
                            <option value="沈阳明志致远市场研究有限公司">沈阳明志致远市场研究有限公司</option>
                            <option value="沈阳中宇行销顾问有限公司">沈阳中宇行销顾问有限公司</option>
                            <option value="石家庄知汇企业管理咨询有限公司">石家庄知汇企业管理咨询有限公司</option>
                            <option value="天津行天市场信息咨询服务有限公司">天津行天市场信息咨询服务有限公司</option>
                            <option value="武汉大视野市场研究咨询有限公司">武汉大视野市场研究咨询有限公司</option>
                            <option value="武汉新景市场信息咨询有限公司">武汉新景市场信息咨询有限公司</option>
                            <option value="西安佳进商务信息咨询有限公司">西安佳进商务信息咨询有限公司</option>
                            <option value="西安睿思商务信息咨询有限公司">西安睿思商务信息咨询有限公司</option>
                            <option value="西安源信市场研究有限责任公司">西安源信市场研究有限责任公司</option>
                            <option value="新疆瑞孚森信息咨询有限责任公司">新疆瑞孚森信息咨询有限责任公司</option>
                            <option value="壹信咨询（南京）有限公司">壹信咨询（南京）有限公司</option>
                            <option value="易贸信息科技（上海）有限公司">易贸信息科技（上海）有限公司</option>
                            <option value="郑州汉邦商务信息服务有限公司">郑州汉邦商务信息服务有限公司</option>
                            <option value="郑州卓亚企业管理咨询有限公司">郑州卓亚企业管理咨询有限公司</option>
                            <option value="重庆品则鑫企业管理咨询有限公司">重庆品则鑫企业管理咨询有限公司</option>
                            <option value="北京金鹏天宇">北京金鹏天宇</option>
                            <option value="灼识人才咨询管理（上海）有限公司">灼识人才咨询管理（上海）有限公司</option>
                            <option value="其他，请注明"> 其他，请注明</option>
                        </select>-->
                    </li>
                    <li>
                        <span>其他</span>
                        <input name="edit[recruit_channel_other]" type="text" placeholder="请输入其他招募途径" />
                    </li>
                    <li>
                        <span>S1</span>
                        <input name="edit[s1]" type="text" placeholder="请输入S1" />
                    </li>
                    <li>
                        <span>S2</span>
                        <input name="edit[s2]" type="text" placeholder="请输入S2" />
                    </li>
                    <li>
                        <span>S3</span>
                        <input name="edit[s3]" type="text" placeholder="请输入S3" />
                    </li>
                    <li>
                        <span>S4</span>
                        <input name="edit[s4]" type="text" placeholder="请输入S4" />
                    </li>
                    <li>
                        <span>S5</span>
                        <input name="edit[s5]" type="text" placeholder="请输入S5" />
                    </li>
                    <li>
                        <span>IQVIA微信平台支付金额</span>
                        <input name="edit[iqvia_amount]" type="text" placeholder="请输入IQVIA微信平台支付金额" onkeyup="clearNoNum(this)" />
                    </li>
                    <li>
                        <span>招募费</span>
                        <input name="edit[recruit_fee]" type="text" placeholder="请输入招募费" onkeyup="clearNoNum(this)" />
                    </li>
                    <li>
                        <span>样本总金额</span>
                        <input name="edit[sample_amount]" type="text" placeholder="请输入样本总金额" onkeyup="clearNoNum(this)" />
                    </li>
                    <li>
                        <span>访谈时长</span>
                        <input name="edit[interview_duration]" type="text" placeholder="请输入访谈时长" />
                    </li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 10px">
                <button type="submit" class="save">提交</button>
            </div>
        </form>

        <iframe name="hidden" style="display: none"></iframe>

        <!-- form表单 -->
    </div>

    <!-- 模版编译 -->
    <script type="text/html" id="searchTpl">
    {{ each list v i }}
    <li data-id="{{v.id}}">{{v.text}}</li>
    {{ /each }}
    </script>

    <script src="/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
    <script src="/theme/drcenter/js/template-web.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>

    <script>
        /************************** 表单提交 **************************/
        var common_js = {
            form_sumbit: function(item_form, msg_id) {
                $(item_form).ajaxSubmit({
                    target: "",
                    beforeSubmit: null,
                    success: common_js.show_response(msg_id),
                    url: "/dx/save",
                    type: "post",
                    dataType: "text",
                    clearForm: false,
                    resetForm: false,
                    cache: false,
                });
                return false;
            },
            //接受PHP返回信息并重绘提示框
            show_response: function(msg_id) {
                return function(str) {
                    var res = jQuery.parseJSON(str);
                    alert(res.rs_msg);
                    if (res.rs_backurl != "" && res.rs_backurl != undefined) {
                        window.location.href = res.rs_backurl;
                    }
                };
            },
            //版本号
            tver: "1.0.1",
        };
        common_js;
        /************************** 表单提交end **************************/
        $(function() {
            /************************** 诊量内容 **************************/

            //  添加
            add_item("disease");
            // 删除诊疗
            delete_item("disease");
            // 搜索
            search_item("disease", "/dk/diagnosis_code", "diagnosis_name");

            /**************************手术量内容**************************/

            add_item("operation");
            // 删除诊疗
            delete_item("operation");
            // 搜索
            search_item("recruit_channel", "/dx/recruit_channel", "recruit_channel_name");

            /********************************** 公共方法 **********************************/

            // 禁用选择输入历史
            $("input").attr("autocomplete", "off");

            // 删除
            function delete_item(type) {
                $(".content").on("click", "." + type + "_list .del", function() {
                    $(this).parent().remove();
                });
            }

            // 添加
            function add_item(type) {
                $(".content .head ." + type + "_add").click(function() {
                    var list_length = $("." + type + "_list").children().length + 1;
                    change_name(type, list_length);
                    $("." + type + "_list").append($(".append_" + type + "").html());
                });
            }

            // 搜索
            function search_item(type, url, name) {
                $(".container").on("click", "." + type + "_name", function(e) {
                    e.stopPropagation();
                    init_search_box();
                    var that = $(this);
                    var timeOut = null;
                    $(".search_box ").off("input").on("input", "input", function() {
                        if (timeOut) {
                            clearTimeout(timeOut)
                        }
                        timeOut = setTimeout(function() {
                            var val = $(".search_box input").val()
                            getRsMsg(url, {
                                [name]: val,
                            });
                            timeOut = null
                        }, 500)
                    });
                    $(".search_box")
                        .off("click")
                        .on("click", "ul li", function() {
                            that.val($(this).text());
                            that.next().attr("value", $(this).data("id"));
                            $(".search_box").hide();
                        });
                });
            }

            // 改变name
            function change_name(type, num) {
                $(".append_disease ." + type + "_name").attr(
                    "name",
                    type + "_name[" + num + "]"
                );
                $(".append_disease ." + type + "_num").attr(
                    "name",
                    type + "_num[" + num + "]"
                );
                $(".append_disease ." + type + "_id").attr(
                    "name",
                    type + "_id[" + num + "]"
                );
            }

            // 初始化search_box
            function init_search_box() {
                $(".search_box input").val("");
                $(".search_box ul").text("暂无数据!");
                $(".search_box").show();
            }

            // 发送ajax请求获取数据
            function getRsMsg(url, data) {
                $.ajax({
                    async: false,
                    type: "post",
                    url: url,
                    dataType: "json",
                    data: data,
                    beforeSend: function() {
                        $(".search_box ul").text("搜索中....");
                    },
                    success: function(res) {
                        if (!res.rs_msg.length) {
                            $(".search_box ul").text("暂无数据!");
                            return false
                        }
                        if (res.rs_code == "success") {
                            $(".search_box ul").html(
                                template("searchTpl", {
                                    list: res.rs_msg,
                                })
                            );
                        }
                    },
                });
            }

            // 点击其他地方隐藏搜索框
            $(document).click(function() {
                $(".search_box").hide();
            });
            $(".search_box input").on("click", function(e) {
                e.stopPropagation();
            });
            /********************************** 公共方法end **********************************/
        });

        //保留两位小数
        function clearNoNum(obj) {
            obj.value = obj.value.replace(/[^\d.]/g, "");//清除“数字”和“.”以外的字符
            obj.value = obj.value.replace(/^\./g, "");//验证第一个字符是数字而不是.
            obj.value = obj.value.replace(/\.{2,}/g, ".");//只保留第一个. 清除多余的.
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
        }
    </script>
</body>

</html>