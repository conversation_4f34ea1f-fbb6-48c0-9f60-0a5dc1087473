<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>wenjuan</title>
    <link rel="stylesheet" href="/theme/new_manage/css/plugins/select2/select2.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body,
        html {
            width: 100%;
            background-color: #f5f5f5;
            color: #666;
            font-size: 16px;
        }

        #loading {
            position: fixed;
            z-index: 400;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0%;
            text-align: center;
            padding-top: 100px;
            color: #595758;
            background-color: #ffffff;
        }

        ul li {
            list-style: none;
        }

        .container {
            margin: 0px auto;
            background: #fff;
        }

        .container .head {
            padding: 25px 0px 20px 0px;
            margin-left: 10px;
            margin-right: 10px;
            position: relative;
            margin-bottom: 30px;
        }

        .container .head h2 {
            font-size: 18px;
            text-align: center;
        }

        .container .head p {
            margin-top: 20px;
            line-height: 25px;
            text-align: justify;
        }

        .container .head .line {
            position: absolute;
            width: 100%;
            height: 2.5px;
            background: #53a4f4;
            bottom: 0px;
            left: 0;
        }

        .container .content,
        .container .tips {
            padding-left: 10px;
            padding-right: 10px;
        }

        .container .content div {
            margin-bottom: 15px;
        }

        .container .content p {
            margin-bottom: 10px;
        }

        .container .content input[type="text"] {
            background: none;
            outline: none;
            border: 1px solid #dee4ec;
            padding-left: 10px;
            border-radius: 3px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .container .content .label {
            display: block;
            padding: 5px;
            box-sizing: border-box;
            margin-bottom: 5px;
            position: relative;
            padding-left: 30px;
            font-size: 14px;
            cursor: pointer;
            border-radius: 3px;
        }

        .container .content .label input {
            display: none;
        }

        .container .content .label .outer {
            display: inline-block;
            border: 1px solid #dee4ec;
            position: absolute;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            left: 4px;
            box-sizing: border-box;
        }

        .container .content .multi input {
            margin-left: 10px;
            margin-right: 10px;
        }

        .container .content .multi span {
            display: inline-block;
            position: absolute;
            width: 16px;
            height: 16px;
            line-height: 13px;
            text-align: center;
            top: 50%;
            transform: translateY(-50%);
            left: 5px;
            color: #fff;
            font-weight: 700;
            background: url("/theme/survey/img/check.png") no-repeat;
            background-position: 0px 0px;
        }

        .container .content textarea {
            border: 1px solid #dee4ec;
            border-radius: 3px;
            outline: none;
            max-width: 100%;
            padding-left: 10px;
            box-sizing: border-box;
        }

        .container .select ul {
            border: 1px solid #dee4ec;
            border-top: none;
            width: 295px;
            height: 150px;
            padding-top: 10px;
            overflow: auto;
            position: absolute;
            background: #fff;
            z-index: 9;
        }

        .container .select ul li {
            cursor: pointer;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
        }

        .container .select ul li:hover {
            background-color: #53a4f4;
            color: #fff;
        }

        .select2-container--default .select2-selection--single {
            height: 25px;
            width: 300px;
            border: 1px solid #dee4ec;
            font-size: 14px;
            outline: none;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 25px;
        }

        .container .submit {
            padding: 0 10px 20px 10px;
            text-align: center;
        }

        .container .submit button {
            border: none;
            background: #53a4f4;
            color: #fff;
            text-align: center;
            border-radius: 5px;
            font-size: 24px;
        }

        .footer ul {
            margin: 0 auto;
            font-size: 12px;
            box-sizing: border-box;
        }

        .footer ul li {
            float: left;
            list-style: none;
            font-size: 10px;
            margin-top: 10px;
            color: #666;
        }

        .input {
            padding-left: 10px;
            padding-right: 10px;
            border: 1px solid #dee4ec;
            font-size: 14px;
            display: inline-block;
            box-sizing: border-box;
            border-radius: 3px;
            overflow-x: auto;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #dee4ec;
            outline: none;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            color: #666;
        }
    </style>
</head>


<body>
    <div id="loading">加载中...</div>
    <div class="container">
        <div class="logo" style="
          padding: 25px 0 5px 0;
          height: 30px;
          border-bottom: 1px solid #dee4ec;
          margin: 0 10px 0 10px;
        ">
            <img src="" alt="" />
            <span style="
            float: left;
            height: 30px;
            line-height: 30px;
            margin-left: 3px;
          ">xxxxxxxxx</span>
            <span style="float: right; height: 30px; line-height: 30px;">时间：2020-03-8
            </span>
        </div>
        <div class="head">
            <h2>标题</h2>
            <p>
                xxxxxxxxx
            </p>
            <div class="line"></div>
        </div>
        <div class="content" style="margin-bottom: 25px;">
            <!-- 单选题 -->
            <div class="single">
                <p class="title">1、单选题 <font color="red">*</font>
                </p>
                <label class="label">
                    <span class="outer"></span>
                    <input type="radio" name="adress" value="1" />选项一
                </label>
                <label class="label">
                    <span class="outer"></span>
                    <input type="radio" name="adress" value="2" />
                    选项二
                </label>
            </div>

            <!-- 多选题 -->
            <div class="multi">
                <p class="title">2、多选题 <font color="red">*</font>
                </p>
                <label class="label"><span></span> <input type="checkbox" />选择一
                </label>
                <label class="label"><span></span> <input type="checkbox" />选择二
                </label>
                <label class="label"><span></span> <input type="checkbox" />选择三
                </label>
                <label class="label"><span></span> <input type="checkbox" />选择四</label>
            </div>

            <!-- 年 -->
            <div class="yyyy">
                <p class="title">3、年 <font color="red">*</font>
                </p>
                <span class="input" id="year"></span>
            </div>

            <!-- 年月题 -->
            <div class="yyyymm">
                <p class="title">4、年月 <font color="red">*</font>
                </p>
                <span class="input" id="month"></span>
            </div>

            <!-- 年月日题 -->
            <div class="yyyymmdd">
                <p class="title">5、年月日题 <font color="red">*</font>
                </p>
                <span class="input" id="day"></span>
            </div>

            <!-- 数字输入框题 -->
            <div class="numericlist">
                <p class="title">6、数字输入框题 <font color="red">*</font>
                </p>
                <input type="text" onkeyup="value=value.replace(/[^\d]/g,'')" />
            </div>

            <!-- 年月日时分题 -->
            <div class="yyyymmddhm">
                <p class="title">7、年月日时分题 <font color="red">*</font>
                </p>
                <span class="input" id="datetime"></span>
            </div>

            <!-- 单行文本输入框题 -->
            <div class="opentext">
                <p class="title">8、单行文本输入框题 <font color="red">*</font>
                </p>
                <input type="text" />
            </div>

            <!-- 多行文本输入框题 -->
            <div class="opentextlist">
                <p class="title">9、多行文本输入框题 <font color="red">*</font>
                </p>
                <textarea name="" id="" cols="30" rows="10"></textarea>
            </div>

            <!-- 下拉单选题 -->
            <div class="select" style="position: relative;">
                <p class="title">9、下拉单选题 <font color="red">*</font>
                </p>
                <span class="input">
                    <font color="#ccc">请选择</font>
                    <font color="#ccc" style="float: right;">∨</font>
                </span>
                <ul style="display: none;">
                    <li>option1</li>
                    <li>option2</li>
                    <li>option3</li>
                    <li>option4</li>
                    <li>option5</li>
                </ul>
            </div>

            <!-- 输入下拉题 -->
            <div class="input_select" style="position: relative;">
                <p class="title">11、输入下拉题 <font color="red">*</font>
                </p>
                <span class="input" id="name">
                    <font color="#ccc">请选择</font>
                    <font color="#ccc" style="float: right;">∨</font>
                </span>
                <select id="select2">
                    <option value="">请选择</option>
                    <option value="张伟">张伟</option>
                    <option value="李娜">李娜</option>
                    <option value="李静">李静</option>
                    <option value="张丽">张丽</option>
                    <option value="王艳">王艳</option>
                    <option value="张涛">张涛</option>
                    <option value="刘芳">刘芳</option>
                </select>
            </div>

            <!-- 音频题 -->
            <div class="audio">
                <p class="title">12、音频题 <font color="red">*</font>
                </p>
                <label class="input">
                    <input name="audio" type="file" style="display: none;" />
                </label>
            </div>

            <!-- 视频题 -->
            <div class="video">
                <p class="title">13、视频题 <font color="red">*</font>
                </p>
                <label class="input">
                    <input name="video" type="file" style="display: none;" />
                </label>
            </div>

            <!-- 文字描述题 -->
            <div class="describe">
                <p class="title">14、文字描述题 <font color="red">*</font>
                </p>
                <textarea name="" id="" cols="30" rows="10"></textarea>
            </div>

            <!-- 图片题 -->
            <div class="picture">
                <p class="title">15、图片题 <font color="red">*</font>
                </p>
                <label class="input">
                    <input name="picture" type="file" style="display: none;" />
                </label>
            </div>

            <!-- 预览图片 -->
            <div class="previewPicture">
                <p class="title">16、预览图片<font color="red">*</font>
                </p>
                <label style="display: block; height: 30px;">
                    请选择预览图片
                    <input id="uploadId" name="sourceFile" type="file" style="display: none;" />
                </label>
                <img id="imageview" width="100%" height="240px"  />
            </div>

            <!-- 播放音频 -->
            <div class="previewPicture">
                <p class="title">17、播放音频<font color="red">*</font>
                </p>
                <audio controls loop style="width: 100%">
                    您的浏览器版本太低了。
                    <source src="/theme/survey/img/test.mp3" type="audio/ogg" />
                    <source src="/theme/survey/img/test.mp3" type="audio/mpeg" />
                </audio>
            </div>

            <!-- 播放视频 -->
            <div class="previewPicture">
                <p class="title">18、播放视频<font color="red">*</font>
                </p>
                <video src="/theme/survey/img/test.mp4" width="100%" height="240" controls loop style="outline: none;">
                    您的浏览器版本太低了。
                </video>
            </div>

            <!-- 省市区三级联动 -->
            <div class="citys">
                <p class="title">19、省市区三级联动<font color="red">*</font>
                </p>

                <div id="citysBox">
                    <span>
                        省：<select id="city_1">
                            <option>—请选择—</option>
                        </select></span>

                    <span>
                        市：<select id="city_2">
                            <option>—请选择—</option>
                        </select></span>
                    <span class="qucity" style="display: none;">
                        区：<select id="city_3">
                            <option>—请选择—</option>
                        </select>
                    </span>
                </div>
                <span style="display: none;" class="input" id="citys"></span>
            </div>

        </div>
        <div class="submit">
            <button>提交</button>
        </div>

    </div>
    <div class="footer">
        <ul>
            <li style="text-align: center;">
                Copyright © 2020 健康通(上海)网络科技有限公司.
            </li>
            <li style="text-align: right;">京ICP备13039326号-8</li>
        </ul>
    </div>
</body>

<script src="/theme/survey/js/juqery-1.11.3.js"></script>
<script src="/theme/survey/js/laydate/laydate.js"></script>
<script src="/theme/new_manage/js/plugins/select2/select2.full.min.js"></script>
<script src="/theme/survey/js/picker.js"></script>
<script src="/theme/survey/js/city.js"></script>
<script src="/theme/survey/js/index.js"></script>

</html>