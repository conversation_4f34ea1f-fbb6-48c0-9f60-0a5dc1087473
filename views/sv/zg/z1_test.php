<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title><?php echo $user_info['short_name'] . $project_info['pro_type_name']; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body,
        html {
            width: 100%;
            background-color: #f5f5f5;
            color: #666;
            font-size: 16px;
        }

        #loading {
            position: fixed;
            z-index: 400;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0%;
            text-align: center;
            padding-top: 100px;
            color: #595758;
            background-color: #ffffff;
        }

        .container {
            margin: 0px auto;
            background: #fff;
        }

        .container .head {
            padding: 25px 0px 20px 0px;
            margin-left: 10px;
            margin-right: 10px;
            position: relative;
            margin-bottom: 30px;
        }

        .container .head h2 {
            font-size: 18px;
            text-align: center;
        }

        .container .head p {
            margin-top: 20px;
            line-height: 25px;
            text-align: justify;
        }

        .container .head .line {
            position: absolute;
            width: 100%;
            height: 2.5px;
            background: #53a4f4;
            bottom: 0px;
            left: 0;
        }

        .container .content,
        .container .tips {
            padding-left: 10px;
            padding-right: 10px;
        }

        .container .content div {
            margin-bottom: 30px;
        }

        .container .content p {
            margin-bottom: 10px;
        }

        .container .content input[type="text"] {
            border: 1px solid #dee4ec;
            height: 26px;
            width: 300px;
            outline: none;
            padding-left: 3px;
            border-radius: 3px;
        }

        .container .content label {
            display: block;
            padding: 5px;
            box-sizing: border-box;
            margin-bottom: 5px;
            position: relative;
            padding-left: 30px;
            font-size: 14px;
            cursor: pointer;
            border-radius: 3px;
        }

        .container .content label input {
            display: none;
        }

        .container .content label .outer {
            display: inline-block;
            border: 1px solid #dee4ec;
            position: absolute;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            left: 4px;
            box-sizing: border-box;
        }

        .container .content .checkbox input {
            margin-left: 10px;
            margin-right: 10px;
        }

        .container .content .checkbox span {
            display: inline-block;
            position: absolute;
            width: 16px;
            height: 16px;
            line-height: 13px;
            text-align: center;
            top: 50%;
            transform: translateY(-50%);
            left: 5px;
            color: #fff;
            font-weight: 700;
            background: url("/theme/survey/img/check.png") no-repeat;
            background-position: 0px 0px;
        }

        .container .content .text textarea {
            border: 1px solid #dee4ec;
            border-radius: 3px;
            outline: none;
        }

        .container .submit {
            padding: 0 10px 20px 10px;
            text-align: center;
        }

        .container .submit button {
            border: none;
            background: #53a4f4;
            color: #fff;
            text-align: center;
            border-radius: 5px;
            font-size: 24px;
        }

        .footer ul {
            margin: 0 auto;
            font-size: 12px;
            box-sizing: border-box;
        }

        .footer ul li {
            float: left;
            list-style: none;
            font-size: 10px;
            margin-top: 10px;
            color: #666;
        }

        .dialog {
            position: fixed;
            z-index: 999;
            top: 200px;
            left: 50%;
            transform: translateX(-50%);
            background: #fff;
            width: 300px;
            height: 200px;
            border: 1px solid #dee4ec;
            box-shadow: 10px 10px 5px #dee4ec;
            text-align: center;
            box-sizing: border-box;
        }

        .dialog .title {
            position: relative;
            height: 50px;
        }

        .dialog .title p {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: red;
            font-weight: 700;
        }

        .dialog .title span {
            position: absolute;
            right: 10px;
            color: #666;
            cursor: pointer;
        }

        .dialog .content {
            color: #666;
            margin-top: 30px;
        }
    </style>

</head>

<body>
    <div id="loading">Loading...</div>
    <div class="container">
        <div class="logo" style="padding:25px 0 5px 0;height: 30px;border-bottom: 1px solid #dee4ec;margin:0 10px 0 10px">
            <img src="/theme/survey/img/logoh.png" alt="" style="height: 30px;float: left;" />
            <span style="float: left;height: 30px;line-height: 30px;margin-left: 3px;"> <?php echo $project_info['pro_type_name']; ?> </span>

            <span style="float: right;height: 30px;line-height: 30px;"><?php echo date("Y/m/d") ?></span>
        </div>
        <div class="head">
            <h2>
                <!--调研标题-->
                <?php echo $user_info['short_name'] . $project_info['title']; ?><?php echo "【" . $zhgh . "】"; ?>
                <!--调研标题-->
            </h2>


            <p>
                <!--调研概述-->
                <?php echo $project_info['info']; ?>

                <!--调研概述-->
            </p>


            <div class="line"></div>
        </div>
        <form method="post" onsubmit="return checking()">
            <div class="content" style="margin-bottom: 25px;">
                <?php if ($q_info) { ?>
                    <?php foreach ($q_info as $k => $v) {
                        $question_type = strtolower($v['type']);
                    ?>
                        <div class="three" id="question_<?php echo $v['question_id']; ?>" style="<?php if ($question_trigger && in_array($v['question_id'], $question_trigger)) {
                                                                                                        echo 'display:none;';
                                                                                                    } ?>">
                            <p class="title"><?php echo $v['question_label']; ?> <?php if ($v['question_must'] == 1) { ?><font color="red">*</font><?php } ?><?php echo "【" . $default_question_type[$question_type] . "】"; ?></p>
                            <?php if ($v['list']) { ?>
                                <?php foreach ($v['list'] as $v_option) {
                                    //触发题型
                                    $event_info = "";
                                    if ($v_option['answer_jump_question_id']) { //显示某道题
                                        $event_info = ' onclick = "show_question(' . $v_option['answer_jump_touch'] . ', \'' . $v_option['answer_jump_question_id'] . '\')"';
                                    }
                                ?>
                                    <!--单选题开始-->
                                    <?php if ($question_type === "single") { ?>
                                        <label><span class="outer" <?php if (isset($post_data[$v_option['variable_id']]) && $post_data[$v_option['variable_id']] == $v_option['answer_code']) {
                                                                        echo "style='border: 5px solid rgb(83, 164, 244);'";
                                                                    } ?>></span><input <?php echo $event_info; ?> type="radio" name="<?php echo $v_option['variable_id']; ?>" <?php if (isset($post_data[$v_option['variable_id']]) && $post_data[$v_option['variable_id']] == $v_option['answer_code']) {
                                                                                                                                                                                    echo "checked";
                                                                                                                                                                                } ?> value="<?php echo $v_option['answer_code']; ?>" />
                                            <?php echo $v_option['answer_label']; ?></label>
                                    <?php } ?>
                                    <!--单选题开始-->
                                <?php } ?>
                            <?php } ?>
                        </div>
                    <?php } ?>
                <?php } ?>

            </div>
            <div class="submit">
                <input type="hidden" name="pid_turn" id="pid_turn" value="<?php echo $pid_turn; ?>" />
                <input type="hidden" name="zhgh" id="zhgh" value="<?php echo $zhgh; ?>" />
                <input type="hidden" name="val_id" id="val_id" value="<?php echo $val_id; ?>" />
                <input type="hidden" name="val_encode_str" id="val_encode_str" value="<?php echo $val_encode_str; ?>" />
                <button type="submit">提交</button>
            </div>
        </form>
    </div>
    <div class="footer">
        <ul>
            <li style="text-align: center">
                <!--公司名称输出-->
                Copyright © <?php echo date("Y"); ?> <?php echo $user_info['company_name']; ?>.
                <!--公司名称输出-->
            </li>
            <li style="text-align: center;">京ICP备13039326号-8</li>
        </ul>
    </div>
    <div class="dialog" style="display: none;">
        <div class="title">
            <p>提示!</p>
            <span>X</span>
        </div>
        <div class="content">您还有必填题未作答,请答完再提交。</div>
    </div>

</body>

<script src="/theme/survey/js/juqery-1.11.3.js"></script>
<script src="/theme/survey/js/laydate/laydate.js"></script>
<script>
    $(document).ready(function() {
        $(window).load(function() {
            $("#loading").hide();
        });

        // 时间选择器
        laydate.render({
            elem: "#day" //指定元素
        });

        laydate.render({
            elem: "#month",
            type: "month"
        });
        // 设置input和textarea的边框颜色
        $(" .container input[type='text'], .container textarea").focus(
            function() {
                $(this).css("border-color", "#53a4f4");
            }
        );
        $(" .container input[type='text'], .container textarea").blur(function() {
            $(this).css("border-color", "#dee4ec");
        });

        // 单选题效果
        $(".container .content label input[type='radio']").click(function() {
            if ($(this).prop("checked")) {
                // 选中的效果
                if ($(window).width() < 750) {
                    $(this)
                        .parent()
                        .css({
                            "border-color": "#53a4f4",
                            background: "#d6e2fc"
                        });
                }
                $(this)
                    .prev()
                    .css({
                        border: "5px solid #53a4f4"
                    });
                // 未被选中的效果
                $(this)
                    .parent()
                    .siblings()
                    .css({
                        "border-color": "#dee4ec",
                        background: "#fff"
                    });
                $(this)
                    .parent()
                    .siblings("label")
                    .children()
                    .css("border", "1px solid #dee4ec");
            }
        });

        // 多选题效果
        $(".container .content label input[type='checkbox']").click(function() {
            // 选中的效果
            if ($(this).prop("checked")) {
                if ($(window).width() < 750) {
                    $(this)
                        .parent()
                        .css({
                            "border-color": "#53a4f4",
                            background: "#d6e2fc"
                        });
                }
                $(this)
                    .prev()
                    .css("background-position", "0 -17px");
            } else {
                $(this)
                    .parent()
                    .css({
                        "border-color": "#dee4ec",
                        background: "#fff"
                    });
                $(this)
                    .prev()
                    .css("background-position", "0 0");
            }
        });

        // 适应
        $(window)
            .resize(function() {
                if ($(window).width() > 750) {
                    $("body").css("color", "#666");
                    $(".container").css({
                        "margin-top": "20px",
                        width: "758px",
                        padding: "30px 30px 0 30px"
                    });
                    $("h2").css({
                        "font-weight": "400",
                        color: "#4c4c4c"
                    });
                    $(".content .title").css("font-weight", "400");
                    $("input[type='text']").css({
                        width: "300px",
                        height: "25px",
                        color: "#666"
                    });
                    $(".container .content label").css({
                        border: "none",
                        "padding-top": "0px",
                        "padding-bottom": "0px"
                    });
                    $(".container .content label .outer").css("margin-right", "5px");
                    $(".container .content textarea").css({
                        width: "100%",
                        color: "#666"
                    });
                    $(".submit button").css({
                        width: "100px",
                        height: "36px",
                        "line-height": "36px"
                    });
                    $(".footer ul").css({
                        width: "818px",
                        height: "70px",
                        "line-height": "55px",
                        padding: "0px 30px 0px"
                    });
                    $(".footer ul li:nth-child(1)").css({
                        width: "70%",
                        "text-align": "left"
                    });
                    $(".footer ul li:nth-child(2)").css({
                        width: "30%",
                        "text-align": "right"
                    });
                } else {
                    $("body").css("color", "#000");
                    $(".container").css({
                        "margin-top": "0",
                        width: "100%",
                        padding: "0"
                    });

                    $("h2").css({
                        "font-weight": "700",
                        color: "#000"
                    });
                    $(".content .title").css("font-weight", "700");
                    $("input[type='text']").css({
                        width: "98%",
                        height: "40px",
                        color: "#000"
                    });
                    $(".container .content label").css({
                        border: "1px solid #dee4ec",
                        "padding-top": "10px",
                        "padding-bottom": "10px"
                    });
                    $(".container .content label .outer").css("margin-right", "0");
                    $(".container .content textarea").css({
                        width: "98%",
                        color: "#000"
                    });
                    $(".submit button").css({
                        width: "100%",
                        height: "50px",
                        "line-height": "50px"
                    });
                    $(".footer ul").css({
                        width: "90%",
                        height: "70px",
                        "line-height": "15px"
                    });
                    $(".footer ul li").css({
                        width: "100%",
                        "text-align": "center"
                    });
                }
            })
            .trigger("resize");
    });

    function checking() {
        // 必填题的数组
        var text_input_val = [];
        var radio_input_val = [];
        var checkbox_input_val = [];
        var textarea_val = [];
        // 必填题的个数
        var text_input_length = $("input[type='text'].question_must").length;
        var radio_input_length = $("input[type='radio'].question_must")
            .parent()
            .parent().length;
        var checkbox_input_length = $("input[type='checkbox'].question_must")
            .parent()
            .parent().length;
        var textarea_length = $("textarea.question_must").length;
        $(".content .question_must").each(function() {
            if ($(this).context.type == "text" && $(this).val()) {
                text_input_val.push(
                    $(this)
                    .val()
                    .trim()
                );
            }
            if ($(this).context.type == "radio" && $(this).prop("checked")) {
                radio_input_val.push(
                    $(this)
                    .val()
                    .trim()
                );
            }
            if ($(this).context.type == "checkbox" && $(this).prop("checked")) {
                checkbox_input_val.push(
                    $(this)
                    .val()
                    .trim()
                );
            }
            if ($(this).context.type == "textarea" && $(this).val()) {
                textarea_val.push(
                    $(this)
                    .val()
                    .trim()
                );
            }
        });
        if (
            text_input_val.length < text_input_length ||
            radio_input_val.length < radio_input_length ||
            checkbox_input_val.length < checkbox_input_length ||
            textarea_val.length < textarea_length
        ) {
            $(".dialog").show();
            return false;
        } else {
            // 提交到后台
            return true;
        }

        return false;
    }
    $(".dialog .title span").click(function() {
        $(".dialog").hide();
    });

    // 必答题
    $("input[type='radio'][name='Q1']").addClass("question_must")

    var question_type = <?php echo json_encode($question_and_type, JSON_UNESCAPED_UNICODE); ?>
    //显示或者隐藏某道题
    function show_question(answer_jump_touch, answer_jump_question_id) {
        var q_info = answer_jump_question_id.split(',');
        for (var i = 0; i < q_info.length; i++) {
            if (answer_jump_touch == 0) { //显示
                $("#question_" + q_info[i]).show();
                $("input:radio[name='" + q_info[i] + "']").addClass("question_must")
            }
            if (answer_jump_touch == 1) { //不显示
                $("#question_" + q_info[i]).hide();
                $("input:radio[name='" + q_info[i] + "']").attr("checked", false)
                $("input:radio[name='" + q_info[i] + "']").removeClass("question_must")
            }
        }
    }
</script>

</html>