<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>九宫格</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body,
        html {
            width: 100%;
            background-color: #f5f5f5;
            color: #666;
            font-size: 16px;
        }

        #loading {
            position: fixed;
            z-index: 400;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0%;
            text-align: center;
            padding-top: 100px;
            color: #595758;
            background-color: #ffffff;
        }

        .squared {
            margin: 50px auto;
            background: #fff;
            width: 818px;
            height: 100%;
            padding-top: 50px;
        }

        #dc-luck-draw {
            position: relative;
            text-align: center;
            margin-top: 20px;
        }

        #dc-luck-draw #draw-chance {
            height: 70px;
            line-height: 50px;
        }

        .dc-hide {
            display: none;
        }

        #dc-luck-draw #luck-dial {
            margin: 0 auto;
            width: 614px;
            height: 365px;
            position: relative;
            background: #f5f5f5;
            box-sizing: border-box;
        }

        #dc-luck-draw #luck-dial-content {
            height: 100%;
            width: 100%;
        }

        #dc-luck-draw #luck-dial-content .squared-list {
            width: 55%;
            height: 90%;
            padding-top: 10px;
            box-sizing: border-box;
            margin: 0 auto;
            position: relative;
        }

        #dc-luck-draw #luck-dial-content .squared-list-item {
            list-style: none;
            position: absolute;
            width: 30%;
            height: 100px;
            text-align: center;
            background: #fff;
            font-size: 14px;
            border: 3px solid #f0f0f0;
            border-radius: 10px;
            box-sizing: border-box;
        }

        #dc-luck-draw #luck-dial-content .squared-list-item .squared-list-item-content {
            position: relative;
            display: block;
            width: 94px;
            margin: 0 auto;
            text-align: center;
            font-size: 12px;
            -webkit-transform-origin: 50% 135px;
            -ms-transform-origin: 50% 135px;
            transform-origin: 50% 135px;
        }

        #dc-luck-draw #luck-dial-content .squared-list-item .squared-list-item-content-title {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            line-height: 12px;
            padding-top: 20px;
            padding-bottom: 10px;
            word-break: break-all;
            word-wrap: break-word;
        }

        #dc-luck-draw #luck-dial-content .squared-list-item img {
            height: 30px !important;
        }

        #dc-luck-draw #draw-btn {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-56%, -58%);
            font-size: 22px;
            color: #ffffff;
            background: #ff9d00;
            width: 90px;
            height: 95px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            z-index: 5;
            padding-top: 15px;
            box-sizing: border-box;

        }

        .squared .create_question {
            margin-top: 60px;
            padding-bottom: 30px;
        }

        .squared .create_question a {
            display: inline-block;
            width: 200px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            color: #fff;
            background: #53a4f4;
            border-radius: 5px;
        }

        .squared .erweima {
            display: none;
            text-align: center;
            margin-top: 30px;
            padding-bottom: 30px;
        }

        .squared .erweima .img {
            display: inline-block;
            width: 100px;
            height: 100px;
            background: url("/theme/survey/img/erweima.png");
            background-size: 120px;
            background-position: -10px -8px;
        }

        .dialog {
            position: fixed;
            z-index: 999;
            top: 200px;
            left: 50%;
            transform: translateX(-50%);
            background: #fff;
            width: 300px;
            height: 200px;
            border: 1px solid #dee4ec;
            box-shadow: 10px 10px 5px #dee4ec;
            text-align: center;
            box-sizing: border-box;
        }

        .dialog .title {
            position: relative;
            height: 50px;
        }

        .dialog .title img {
            width: 200px;
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .dialog .title p {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
        }

        .dialog .title span {
            position: absolute;
            right: 10px;
            color: #666;
            cursor: pointer;
        }

        .dialog .content {
            color: #666;
        }

        .get_prize {
            margin-top: 15px;
        }

        .thanks {
            line-height: 100px;
        }
    </style>
</head>

<body>
<div id="loading">加载中...</div>
<div class="squared">
    <div class="consorry">
        <p style="text-align: center;font-size: 18px;">
            该问卷已停止收集
        </p>
        <div class="wj_luck_draw" id="wj_luck_draw">
            <div id="dc-luck-draw">
                <div id="draw-chance">
                    感谢您的参与！送您 <span style="color:#FF6D56; font-size: 24px;">1</span> 次抽奖机会
                </div>
                <div id="luck-dial">
                    <div id="luck-dial-content" class="rowinfinite">
                        <ul class="squared-list">
                            <li class="squared-list-item" style="color:#FF6D56; top: 10px ;left: 0;">
                                <p class="get_prize">
                                    <font size="6">100</font>万
                                </p>
                                <p>出行险</p>
                            </li>
                            <li class="squared-list-item" style="color:#aaa;top: 10px; left: 33%;">
                                <p class="thanks">谢谢惠顾</p>
                            </li>
                            <li class="squared-list-item" style="color:#FF6D56;top: 10px; left: 66%;">
                                <p class="get_prize"">
                                <font size=" 6">2</font>元
                                </p>
                                <p>微信红包</p>
                            </li>
                            <li class="squared-list-item" style="color:#aaa;top: 38%; left: 66%;">
                                <p class="thanks">谢谢惠顾</p>
                            </li>
                            <li class="squared-list-item" style="color:#FF6D56;top: 74%; left: 66%;">
                                <p class="get_prize"">
                                <font size=" 6">5</font>元
                                </p>
                                <p>微信红包</p>
                            </li>
                            <li class="squared-list-item" style="color:#aaa;top: 74%; left: 33%;">
                                <p class="thanks">谢谢惠顾</p>
                            </li>
                            <li class="squared-list-item" style="color:#FF6D56;top: 74%; left: 0;">
                                <p class="get_prize"">
                                <font size=" 6">1</font>元
                                </p>
                                <p>微信红包</p>
                            </li>
                            <li class="squared-list-item" style="color:#aaa;top: 38%;left: 0;">
                                <p class="thanks">谢谢惠顾</p>
                            </li>
                        </ul>
                    </div>
                    <div id="draw-btn">
                        <p>开始</p>
                        <p>抽奖</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="create_question" style="text-align: center;">
        <a target="_blank" style="cursor: pointer;">我也要免费创建问卷</a>
    </div>
    <div class="erweima">
        <p>微信：18616252228</p>
        <span class="img"></span>
        <p>扫码领取红包</p>
    </div>
</div>
<!-- 模态框 -->
<div class="dialog" style="display: none;">
    <div class="title">
        <img src="/theme/survey/img/choujiang.png" alt="" />
        <p>抽奖结果</p>
        <span>X</span>
    </div>
    <div class="content">谢谢惠顾</div>
</div>
<script src="/theme/survey/js/juqery-1.11.3.js"></script>
<script>
    $(function () {
        $(window).load(function () {
            $('#loading').hide()
        })

        // 抽奖
        var end = Math.random() * 8   //表示抽到的位子
        end = Math.ceil(end)
        var round = 8  //round表示转几圈后开始抽奖
        var ms = 20 //控制抽奖转圈速度
        var i = 0;   //防止重复点击
        $("#draw-btn").click(function (e) {
            i++
            if (i < 2) {
                getPrize(0, end, round, ms);
            }
        });
        // 递归抽奖
        function getPrize(index, end, round, time) {
            squared();
            function squared() {
                if (round > 0) {
                    setTimeout(function () {
                        index++;
                        if (index > 8) {
                            index = 0;
                            round--;
                        }
                        $("#dc-luck-draw #luck-dial-content .squared-list-item:nth-child(" + index + ")").css({
                            "border-color": "#ff8533",
                            "background-color": "#fff799"
                        }).siblings().css({
                            "border-color": "#f0f0f0",
                            "background-color": "#fff"
                        })
                        squared();
                    }, time);
                }
                else if (round === 0) {  //最后一圈的时候
                    setTimeout(function () {
                        $(".squared-list-item:nth-child(" + index + ")").css({
                            "border-color": "#ff8533",
                            "background-color": "#fff799"
                        }).siblings().css({
                            "border-color": "#f0f0f0",
                            "background-color": "#fff"
                        })
                        if (index != end) {
                            index++;
                            squared();  //在最后一圈，但是还没有到达想要的位置，继续递归。
                        }
                        if (index == end) {
                            setTimeout(function () {
                                $('#draw-chance span').text("0")
                                $(".dialog .content").text($(".squared-list-item:nth-child(" + index + ")").text())
                                $('.dialog').fadeIn(100)
                            }, 1000);
                            setTimeout(function () {
                                $(".dialog").hide()
                                $(".create_question").hide()
                                $(".erweima").show()
                            }, 3000)

                            $("body").click(function () {
                                if ($('.dialog').is(":visible")) {
                                    $(".dialog").hide()
                                    $(".create_question").hide()
                                    $(".erweima").show()
                                }
                            })
                        }
                    }, 100)
                }
            }
        }

        // 适应
        $(window)
            .resize(function () {
                if ($(window).width() > 750) {
                    $('body').css('color', '#666')
                    $('.squared').css({
                        margin: '50px auto',
                        width: '818px'
                    })
                    $("#luck-dial").width("614px")
                    $(".squared-list").width("55%")

                } else {
                    $('body').css('color', '#000')
                    $('.squared').css({
                        margin: '0px auto',
                        width: '100%'
                    })
                    $("#luck-dial").width("300px")
                    $(".squared-list").width("90%")
                }
            })
            .trigger('resize')
    })
</script>
</body>

</html>
