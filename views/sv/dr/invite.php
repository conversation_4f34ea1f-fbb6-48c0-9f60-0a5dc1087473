<head>
    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
    <link href="/theme/new_manage/css/sumoselect.min.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/select2/select2.min.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/new_manage/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <link href="/theme/new_manage/datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
    <link href="/theme/new_manage/jquery.datetimepicker.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/toastr/toastr.min.css" rel="stylesheet">

</head>
<!-- Ladda style -->
<script type="text/javascript" src="http://lib.sinaapp.com/js/jquery/1.7.2/jquery.min.js"></script>
<link href="css/plugins/ladda/ladda-themeless.min.css" rel="stylesheet">
<div class="wrapper wrapper-content animated fadeInRight" style="">
    <div class="row">
        <div class="col-lg-12"  style="padding-right: 0px;padding-left: 0px; border:0px solid #FFFFFF;">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>“腾讯医生”邀请列表【共：<?=$total?>】</h5>
                </div>
                <div class="ibox-content" style="padding-left:5px; padding-right:5px; margin-left:0px; margin-right:0px; font-size:12px;">
                     <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th data-hide="all">省</th>
                            <th data-hide="all">市</th>
                            <th data-hide="all">医院</th>
                            <th data-hide="all">科室</th>
                            <th data-hide="all">姓名</th>
                            <th data-hide="all">职务</th>
                            <th data-hide="all">职称</th>
                            <th data-hide="all">手机</th>
                            <th data-hide="all">执业证</th>
                            <th data-hide="all">执业类别</th>
                            <th data-hide="all">授权</th>
                        </tr>
                        </thead>
                        <tbody>

                        <?php if (!empty($list)) {$i = $offset;foreach ($list as $row) {$i++;?>
                        <tr>
                            <td><?=$row['province']?></td>
                            <td><?=$row['city']?></td>
                            <td><?=$row['hospital']?></td>
                            <td><?=$row['department']?></td>
                            <td><?=$row['name']?></td>
                            <td><?=$row['duty']?></td>
                            <td><?=$row['title']?></td>
                            <td><?=$row['mobile']?></td>
                            <td><?=$row['pro_num']?></td>
                            <td><?=$row['type']?></td>
                            <td><?=$row['auth']?></td>
                        </tr>
                        <?php }} else {?>
                            <tr class="text-center">
                                <td id="empty_td" colspan="13">暂无数据...</td>
                            </tr>
                        <?php }?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="20">
                                <div class="col-sm-4" style="margin-top: 8px; float:right;"><?php echo $pages ?></div>
                            </td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>
 <!-- Ladda -->
    <script src="js/plugins/ladda/spin.min.js"></script>
    <script src="js/plugins/ladda/ladda.min.js"></script>
    <script src="js/plugins/ladda/ladda.jquery.min.js"></script>
