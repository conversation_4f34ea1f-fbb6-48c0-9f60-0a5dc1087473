<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name = "format-detection" content = "telephone=no">
    <meta http-equiv="content-type" content="no-cache, must-revalidate" />
    <META HTTP-EQUIV="Cache-Control" CONTENT="no-cache, must-revalidate"/> 
    <META HTTP-EQUIV="expires" CONTENT="0"/>
    <title><?=$info['label_cn']?></title>
    <link rel="stylesheet" href="/theme/new_manage/css/plugins/select2/select2.min.css">
    <link rel="stylesheet" href="/theme/survey/css/index.css?v=<?=rand(10000,99999)?>">
    <!-- <script src="/theme/survey/js/juqery-1.11.3.js"></script> -->
    <script src="/theme/survey/js/Recorder-master/recorder.wav.min.js"></script>
    <script src="/theme/survey/js/Recorder-master/recorder-core.js"></script>
    <script src="/theme/survey/js/Recorder-master/wav.js"></script>
    <script src="/theme/survey/js/jquery-3.4.1.min.js"></script>
    <script src="/theme/survey/js/laydate/laydate.js"></script>
    <script src="/theme/new_manage/js/plugins/select2/select2.full.min.js"></script>
    <script src="/theme/survey/js/picker.js"></script>
    <script src="/theme/survey/js/pickerData.js"></script>
    <script src="/theme/survey/js/jSignature.js"></script>
    <script src="/theme/survey/js/index.js?v=<?=rand(10000,99999)?>"></script>
    <script>
        var question_list = <?=$question_list?>;
    </script>
    <style>
        .lable-title {
            margin-top: 5px;
        }
        textarea {
            outline: none;
            resize: none;
        }
        .html5buttons a{
            background: #1d84c6;
        }
        .ibox-tools a{
            color: #f5f5f5;
        }
        .jump_box {
            padding-top: 65px;
        }
        .prev_btn,.next_btn{
            border: none;
            background: #53a4f4 !important;
            color: #fff;
            text-align: center;
            border-radius: 5px;
            padding: 8px 20px !important;
            outline: none;
            cursor: pointer;
            font-size: 16px !important;
            min-width: 88px;
        }
        .prev_btn{
            margin-right:20px;
        }
        .record_open_btn {
            background: #51bf51;
        }
        .record_stop_btn{
            background: #e10e27;
        }
        .record_open_btn,.record_stop_btn{
            border: none;
            color: #fff;
            text-align: center;
            outline: none;
            cursor: pointer;
            font-size: 12px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
        }
        .record_box {
            float: left;
            width: 70%;
            height: 100%;
            padding-left: 5px;
            box-sizing: border-box;
            text-align: center;
            line-height: 65px;
            background: #fff;     
        }
        .record_box audio {
            width: 100%;
            outline: none;
            vertical-align: middle;
        }
        .btn_box {
            float: left;
            vertical-align: top;
            width: 26%;
            height: 100%;
            border-left: 1px solid #ccc;
            line-height: 65px;
            box-sizing: border-box;
            text-align: center;
        }
        .height_80 {
            min-height: 80px!important;
        }
    </style>
</head>

<body>
    <!-- <div id="loading"><p>加载中</p></div> -->
    <div class="container">
        <div class="logo" style="padding: 25px 0 5px 0;border-bottom: 1px solid #dee4ec;margin: 0 10px 0 10px;">
            <img id="logo_img" style="height: 30px; float: left;" src="" />
            <span id="logo_name" style="height: 30px;line-height: 30px;margin-left: 3px;"></span>
            <?php if($info['timmer_st']==1):?>
            <span style="float: right;height: 30px;line-height: 30px;color: #1667fe;font-weight: 600;">
                计时：<span class="timer" data-question_id="">00:00:00</span>
            </span>
            <?php endif;?>
        </div>
        <div class="head">
            <h2><?=$info['title']?></h2>
            <p style="white-space: pre-wrap;"><?=$info['brief']?></p>
            <div class="line"></div>
        </div>
        <div class="content" style="margin-bottom: 25px;">
            <?php foreach (json_decode($question_list,true) as $key => $val) : ?>
                <?php switch ($val['type']):
                    case 'single': ?>
                        <!-- 单选题 -->
                        <div id="<?=$val['question_id']?>" class="single question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php if($answer['type']=='single'):?>
                                    <label class="label">
                                        <span class="outer"></span>
                                        <input tabindex="<?=$index?>" id="<?=$val['question_id'].'_'.$answer['answer_code']?>" name="single<?=$val['question_id']?>" value="" type="radio" class="noshow single<?=$val['question_id']?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"/><?=$answer['answer_label']?>
                                        <?php
                                            $key = array_search($val['question_id'].'_'.$answer['answer_code'].'_other', array_column($val['list'], 'variable_id'));
                                        ?>
                                        <?php if($key>-1):?>
                                            <?php
                                                $config = explode('@', $val['list'][$key]['answer_label'])[1];
                                                $msg = explode('_', $config)[0]??'';
                                                $min = explode('_', $config)[1]??'';
                                                $max = explode('_', $config)[2]??'';
                                            ?>
                                            <input tabindex="<?=$key?>" id="<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>" name="single<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>" type="text" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" value="<?=$val['list'][$key]['answer_value']??''?>">
                                            <script>
                                                $('#<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>').on('blur input',function(){
                                                    var text = $(this).val().length;
                                                    var min = parseInt($(this).data('min'));
                                                    var max = parseInt($(this).data('max'));
                                                    if(text<min || text>max){
                                                        $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                                    }else{
                                                        var index = $(this).attr('tabindex');
                                                        question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                                        $('.msgbox<?=$val['question_id']?>').html('*');
                                                        if($(this).parent().find('input[name="single<?=$val['question_id']?>"]:checked').data('jump')=="1"){
                                                            var answer_jump_question_id = $(this).parent().find('input[name="single<?=$val['question_id']?>"]:checked').data('jump_id');
                                                            $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                        }
                                                    }
                                                });
                                            </script>
                                        <?php endif;?>
                                    </label>
                                    <script>
                                        $(document).ready(function() {
                                            <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                                if ($(window).width() < 750) {
                                                    $('#<?=$val['question_id'].'_'.$answer['answer_code']?>').parent().css({
                                                        "border-color": "#53a4f4",
                                                        background: "#d6e2fc",
                                                    });
                                                }
                                                $('#<?=$val['question_id'].'_'.$answer['answer_code']?>').prev().css({border: "5px solid #53a4f4"});

                                                $('#<?=$val['question_id'].'_'.$answer['answer_code']?>').prop('checked',true);
                                                $('#<?=$val['question_id'].'_'.$answer['answer_code']?>').attr('value',1);
                                                $('#<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>').css({
                                                    display: "inline-block",
                                                    width: "260px",
                                                    "margin-bottom": "0px",
                                                });
                                                // 逻辑跳转
                                                <?php if($answer['answer_jump_touch']==1):?>
                                                    var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                <?php endif;?>
                                            <?php else:?>
                                                $('.single').find('input[name="single<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>"]').hide();
                                            <?php endif;?>
                                        });
                                    </script>
                                <?php endif;?>
                            <?php endforeach;?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                    if(item.answer_value==1){
                                        question_list.<?=$val['question_id']?>.list[i].old_answer = 1;
                                    }
                                });
                                $('.single<?=$val['question_id']?>').change(function(){
                                    $('.single<?=$val['question_id']?>').each(function(){
                                        var index = $(this).attr('tabindex');
                                        if($(this).prop('checked')==true){
                                            $(this).attr('value','1');
                                            if(question_list.<?=$val['question_id']?>.list[index].old_answer == 1){
                                                question_list.<?=$val['question_id']?>.list[index].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[index].count_size="+1";
                                            }
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 1;
                                        }else{
                                            $(this).attr('value','0');
                                            if(question_list.<?=$val['question_id']?>.list[index].old_answer == 1){
                                                question_list.<?=$val['question_id']?>.list[index].count_size="-1";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[index].count_size="";
                                            }
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 0;
                                        }
                                    });
                                    var obj = $(this).closest('label').find('input[type="text"]');
                                    if($(this).prop('checked')==true&&obj.length>0){
                                        $(this).next().css({
                                            display: "inline-block",
                                            width: "260px",
                                            "margin-bottom": "0px",
                                        });
                                        obj.focus();
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id','');
                                        return false;
                                    }else{
                                        $(this).closest('label').find('input[type="text"]').hide();
                                        $('.msgbox<?=$val['question_id']?>').html('*');
                                    }

                                    // 逻辑跳转
                                    if($('.single<?=$val['question_id']?>:checked').data('jump')=="1"){
                                        var answer_jump_question_id = $('.single<?=$val['question_id']?>:checked').data('jump_id');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'multi': ?>
                        <!-- 多选题 -->
                        <div id="<?=$val['question_id']?>" class="multi question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                                $config = explode('_', $title[1]);
                                $msg = $config[0]??'';
                                $min = $config[1]??'';
                                $max = $config[2]??'';
                            ?>
                            <p class="title"><?=$title[0]?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                            <?php if($answer['type']=='multi'):?>
                                <?php
                                    $title_answer = explode('@', $answer['answer_label']);
                                ?>
                                <label class="label">
                                    <span></span>
                                    <input tabindex="<?=$index?>" class="noshow multi<?=$val['question_id'].'_'.$answer['answer_code']?>" name="<?=$val['question_id']?>" value="0" type="checkbox" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"/><?=$title_answer[0]?>
                                    <?php
                                        $key = array_search($val['question_id'].'_'.$answer['answer_code'].'_other', array_column($val['list'], 'variable_id'));
                                    ?>
                                    <?php if($key>-1):?>
                                        <?php
                                            $config_other = explode('@', $val['list'][$key]['answer_label'])[1];
                                            $msg_other = explode('_', $config_other)[0]??'';
                                            $min_other = explode('_', $config_other)[1]??'';
                                            $max_other = explode('_', $config_other)[2]??'';
                                        ?>
                                        <input tabindex="<?=$key?>" id="<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>" name="<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>" type="text" style="width: auto !important; height: 32px; color: rgb(102, 102, 102); border-color: rgb(222, 228, 236);margin-bottom:0px;display:inline-block;" data-msg="<?=$msg_other ?>" data-min="<?=$min_other ?>" data-max="<?=$max_other ?>" value="<?=$val['list'][$key]['answer_value']??''?>">
                                        <script>
                                            $('input[name="<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>"]').on('blur input',function(){
                                                var text = $(this).val().length;
                                                var msg_other = $(this).data('msg');
                                                var min_other = parseInt($(this).data('min'));
                                                var max_other = parseInt($(this).data('max'));
                                                if(text<min_other || text>max_other){
                                                    $('.msgbox<?=$val['question_id']?>').html('*'+msg_other);
                                                    return false;
                                                }else{
                                                    var index = $(this).attr('tabindex');
                                                    question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                                    if($(this).parent().find('input[name="<?=$val['question_id']?>"]:checked').data('jump')=="1"){
                                                        var answer_jump_question_id = $(this).parent().find('input[name="<?=$val['question_id']?>"]:checked').data('jump_id');
                                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                    }
                                                }
                                            });
                                        </script>
                                    <?php endif;?>
                                </label>
                                <script>
                                    $(document).ready(function() {
                                        <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                            if ($(window).width() < 750) {
                                                $('.multi<?=$val['question_id'].'_'.$answer['answer_code']?>').parent().css({
                                                    "border-color": "#53a4f4",
                                                    background: "#d6e2fc",
                                                });
                                            }
                                            $('.multi<?=$val['question_id'].'_'.$answer['answer_code']?>').prev().css("background-position", "0 -17px");

                                            $('.multi<?=$val['question_id'].'_'.$answer['answer_code']?>').prop('checked',true);
                                            $('.multi<?=$val['question_id'].'_'.$answer['answer_code']?>').attr('value',1);

                                            $('input[name="<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>"]').css({
                                                display: "inline-block",
                                                width: "260px",
                                                "margin-bottom": "0px"
                                            });

                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            $('input[name="<?=$val['question_id'].'_'.$answer['answer_code'].'_other'?>"]').hide();
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endif;?>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                    if(item.answer_value==1){
                                        question_list.<?=$val['question_id']?>.list[i].old_answer = 1;
                                    }
                                });
                                $('.multi').find('input[type="text"]').hide();
                                $('input[name="<?=$val['question_id']?>"]').change(function(){
                                    $('input[name="<?=$val['question_id']?>"]').each(function(){
                                        var index = $(this).attr('tabindex');
                                        if($(this).prop('checked')==true){
                                            $(this).attr('value','1');
                                            if(question_list.<?=$val['question_id']?>.list[index].old_answer == 1){
                                                question_list.<?=$val['question_id']?>.list[index].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[index].count_size="+1";
                                            }
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 1;
                                        }else{
                                            $(this).attr('value','0');
                                            if(question_list.<?=$val['question_id']?>.list[index].old_answer == 1){
                                                question_list.<?=$val['question_id']?>.list[index].count_size="-1";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[index].count_size="";
                                            }
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 0;
                                        }
                                    });

                                    // 判断选择个数
                                    var num = $('input[name="<?=$val['question_id']?>"]:checked').length;
                                    if(num<1||num>parseInt('<?=$max?>')){
                                        $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id','');
                                        $(this).closest('label').find('input[type="text"]').hide();
                                        return false;
                                    }else{
                                        $('.msgbox<?=$val['question_id']?>').html('*');
                                    }

                                    // 判断其他
                                    var obj = $(this).closest('label').find('input[type="text"]');
                                    if(obj.length>0&&obj.prev().prop('checked')==true){
                                        obj.css({
                                            display: "inline-block",
                                            width: "260px",
                                            "margin-bottom": "0px"
                                        });
                                        obj.focus();
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id','');
                                        return false;
                                    }else{
                                        obj.hide();
                                        $('.msgbox<?=$val['question_id']?>').html('*');
                                    }

                                    var arr = [];
                                    $('input[name="<?=$val['question_id']?>"]:checked').each(function(){
                                        if($(this).data('jump')==1){
                                            var answer_jump_question_id = $(this).data('jump_id');
                                            if(arr.indexOf(answer_jump_question_id)<0){
                                                arr.push(answer_jump_question_id);
                                            }
                                        }
                                    });
                                    answer_jump_question_id = arr.join();
                                    $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'yyyy': ?>
                        <!-- 年 -->
                        <div id="<?=$val['question_id']?>" class="yyyy question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: block;margin-bottom:10px;"><?=$title?> <span class="red yyyymsg<?=$answer['answer_code']?>"></span></span>
                                <span tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="input yearbox<?=$answer['answer_code']?>" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"><?=$answer['answer_value']??''?></span>
                                <script>
                                    if ($(window).width() > 750) {
                                        // 年
                                        laydate.render({
                                            elem: ".yearbox<?=$answer['answer_code']?>",
                                            type: "year",
                                            done: function(value, date){
                                                var cent = parseInt(date.year);
                                                var max = parseInt('<?=$max?>');
                                                var min = parseInt('<?=$min?>');
                                                if(cent<min||cent>max){
                                                    $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                                }else{
                                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                                }

                                                var index = $('.yearbox<?=$answer['answer_code']?>').attr('tabindex');
                                                question_list.<?=$val['question_id']?>.list[index].answer_value = cent;

                                                // 逻辑跳转
                                                if($('.yearbox<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                    var answer_jump_question_id = $('.yearbox<?=$answer['answer_code']?>').attr('data-jump_id');
                                                    $('.yearbox<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                }
                                            }
                                        });
                                    } else {
                                        // picker的年
                                        $(".yearbox<?=$answer['answer_code']?>").on("click", function() {
                                            picker<?=$val['question_id'].'_'.$answer['answer_code']?>.show();
                                        });
                                        var picker<?=$val['question_id'].'_'.$answer['answer_code']?> = new Picker({
                                            data: [yyyyData],
                                            selectedIndex: [thisYear - 1930],
                                            title: "请选择年",
                                        });
                                        picker<?=$val['question_id'].'_'.$answer['answer_code']?>.on("picker.select", function(selectedVal, selectedIndex) {
                                            var cent = parseInt(yyyyData[selectedIndex[0]].text);
                                            var max = parseInt('<?=$max?>');
                                            var min = parseInt('<?=$min?>');
                                            if(cent<min||cent>max){
                                                $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                            }else{
                                                $('.msgbox<?=$val['question_id']?>').html('*');
                                            }
                                            $(".yearbox<?=$answer['answer_code']?>").text(yyyyData[selectedIndex[0]].text);
                                            var index = $('.yearbox<?=$answer['answer_code']?>').attr('tabindex');
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = cent;

                                            // 逻辑跳转
                                            if($('.yearbox<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                var answer_jump_question_id = $('.yearbox<?=$answer['answer_code']?>').attr('data-jump_id');
                                                $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            }
                                        });
                                    }

                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'yyyymm': ?>
                        <!-- 年月题 -->
                        <div id="<?=$val['question_id']?>" class="yyyymm question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: block;margin-bottom:10px;"><?=$title?> <span class="red yyyymmmsg<?=$answer['answer_code']?>"></span></span>
                                <span tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="input monthbox<?=$answer['answer_code']?>" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"><?=$answer['answer_value']??''?></span>
                                <script>
                                    if ($(window).width() > 750) {
                                        // 年月
                                        laydate.render({
                                            elem: ".monthbox<?=$answer['answer_code']?>",
                                            type: "month",
                                            done: function(value, date){
                                                if(date.month<10){
                                                    var cent = parseInt(date.year+'0'+date.month);
                                                }else{
                                                    var cent = parseInt(''+date.year+date.month);
                                                }
                                                var max = parseInt('<?=$max?>');
                                                var min = parseInt('<?=$min?>');
                                                if(cent<min||cent>max){
                                                    $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                                }else{
                                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                                }
                                                var index = $('.monthbox<?=$answer['answer_code']?>').attr('tabindex');
                                                question_list.<?=$val['question_id']?>.list[index].answer_value = value;

                                                // 逻辑跳转
                                                if($('.monthbox<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                    var answer_jump_question_id = $('.monthbox<?=$answer['answer_code']?>').attr('data-jump_id');
                                                    $('.monthbox<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                }
                                            }
                                        });
                                    } else {
                                        // picker的年月
                                        var picker<?=$val['question_id'].'_'.$answer['answer_code']?> = new Picker({
                                            data: [yyyyData, yyyymmData],
                                            selectedIndex: [thisYear - 1930, thisMonth],
                                            title: "请选择年月",
                                        });
                                        $(".monthbox<?=$answer['answer_code']?>").on("click", function() {
                                            picker<?=$val['question_id'].'_'.$answer['answer_code']?>.show();
                                        });
                                        picker<?=$val['question_id'].'_'.$answer['answer_code']?>.on("picker.select", function(selectedVal, selectedIndex) {
                                            $(".monthbox<?=$answer['answer_code']?>").text(yyyyData[selectedIndex[0]].text + "-" + yyyymmData[selectedIndex[1]].text);
                                            var index = $('.monthbox<?=$answer['answer_code']?>').attr('tabindex');
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = yyyyData[selectedIndex[0]].text + "-" + yyyymmData[selectedIndex[1]].text;
                                            // 逻辑跳转
                                            if($('.monthbox<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                var answer_jump_question_id = $('.monthbox<?=$answer['answer_code']?>').attr('data-jump_id');
                                                $('.monthbox<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            }
                                        });
                                    }
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'date': ?>
                        <!-- 日期题 -->
                        <div id="<?=$val['question_id']?>" class="yyyymmdd question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: block;margin-bottom:10px;"><?=$title?> <span class="red datemsg<?=$answer['answer_code']?>"></span></span>
                                <span tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="input daybox<?=$answer['variable_id']?>" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"><?=$answer['answer_value']??''?></span>
                                <script>
                                    if ($(window).width() > 750) {
                                        // 日期题
                                        laydate.render({
                                            elem: ".daybox<?=$answer['variable_id']?>",
                                            done: function(value, date){
                                                var year = date.year;
                                                if(date.month<10){
                                                    var month = '0'+date.month;
                                                }else{
                                                    var month = date.month;
                                                }
                                                if(date.date<10){
                                                    var date = '0'+date.date;
                                                }else{
                                                    var date = date.date;
                                                }
                                                var cent = parseInt(''+year+month+date);
                                                var max = parseInt('<?=$max?>');
                                                var min = parseInt('<?=$min?>');
                                                if(cent<min||cent>max){
                                                    $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                                }else{
                                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                                }
                                                var index = $('.daybox<?=$answer['variable_id']?>').attr('tabindex');
                                                question_list.<?=$val['question_id']?>.list[index].answer_value = value;
                                                // 逻辑跳转
                                                if($('.daybox<?=$answer['variable_id']?>').attr('data-jump')=="1"){
                                                    var answer_jump_question_id = $('.daybox<?=$answer['variable_id']?>').attr('data-jump_id');
                                                    $('.daybox<?=$answer['variable_id']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                }
                                            }
                                        });
                                    } else {
                                        // picker的年月日
                                        var picker<?=$val['question_id'].'_'.$answer['answer_code']?> = new Picker({
                                            data: [yyyyData, yyyymmData, yyyymmddData],
                                            selectedIndex: [thisYear - 1930, thisMonth, thisDay],
                                            title: "请选择年月日",
                                        });
                                        $(".daybox<?=$answer['variable_id']?>").on("click", function() {
                                            picker<?=$val['question_id'].'_'.$answer['answer_code']?>.show();
                                        });
                                        var selectedArr = [thisYear, thisMonth, thisDay];
                                        // 日期联动
                                        picker<?=$val['question_id'].'_'.$answer['answer_code']?>.on("picker.change", function(index, selectedIndex) {
                                            var days;
                                            if (index == 0) {
                                                // 年
                                                selectedArr[0] = selectedIndex + 1930;
                                            } else if (index == 1) {
                                                var yyyymmddData = [];
                                                // 月
                                                selectedArr[1] = selectedIndex + 1;
                                                // 获取当月总天数
                                                days = new Date(selectedArr[0], selectedArr[1], 0).getDate();
                                                for (var i = 1; i <= days; i++) {
                                                    if (i < 10) {
                                                        yyyymmddData.push({
                                                            text: '0' + i,
                                                            value: '0' + i,
                                                        });
                                                    } else {
                                                        yyyymmddData.push({
                                                            text: i,
                                                            value: i,
                                                        });
                                                    }
                                                }
                                                picker<?=$val['question_id'].'_'.$answer['answer_code']?>.refillColumn(2, yyyymmddData);
                                                picker<?=$val['question_id'].'_'.$answer['answer_code']?>.scrollColumn(2, 0);
                                            }
                                        });
                                        picker<?=$val['question_id'].'_'.$answer['answer_code']?>.on("picker.select", function(selectedVal, selectedIndex) {
                                            var cent = parseInt(yyyyData[selectedIndex[0]].text + yyyymmData[selectedIndex[1]].text + selectedVal[2]);
                                            var max = parseInt('<?=$max?>');
                                            var min = parseInt('<?=$min?>');
                                            if(cent<min||cent>max){
                                                $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                            }else{
                                                $('.msgbox<?=$val['question_id']?>').html('*');
                                            }
                                            $(".daybox<?=$answer['variable_id']?>").text(yyyyData[selectedIndex[0]].text + "-" + yyyymmData[selectedIndex[1]].text + "-" + selectedVal[2]);
                                            // 逻辑跳转
                                            if($('.daybox<?=$answer['variable_id']?>').attr('data-jump')=="1"){
                                                var answer_jump_question_id = $('.daybox<?=$answer['variable_id']?>').attr('data-jump_id');
                                                $('.daybox<?=$answer['variable_id']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            }
                                        });
                                    }
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'time': ?>
                        <!-- 时间题 -->
                        <div id="<?=$val['question_id']?>" class="yyyymmddhm question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: block;margin-bottom:10px;"><?=$title?> <span class="red timemsg<?=$answer['answer_code']?>"></span></span>
                                <span tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="input datetime timebox<?=$answer['answer_code']?>" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"><?=$answer['answer_value']??''?></span>
                                <script>
                                    if ($(window).width() > 750) {
                                        // 年月日时分题
                                        laydate.render({
                                            elem: ".timebox<?=$answer['answer_code']?>",
                                            type: "time",
                                            done: function(value, date){
                                                var cent = parseInt(value.replace(/:/g,''));
                                                var max = parseInt('<?=$max?>'.replace(/:/g,''));
                                                var min = parseInt('<?=$min?>'.replace(/:/g,''));
                                                if(cent<min||cent>max){
                                                    $('.timemsg<?=$answer['answer_code']?>').html('*<?=$msg?>');
                                                    $('.timebox<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id','');
                                                    return false;
                                                }else{
                                                    $('.timemsg<?=$answer['answer_code']?>').html('');
                                                }
                                                var index = $('.timebox<?=$answer['answer_code']?>').attr('tabindex');
                                                question_list.<?=$val['question_id']?>.list[index].answer_value = value;
                                                // 逻辑跳转
                                                if($('.timebox<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                    var answer_jump_question_id = $('.timebox<?=$answer['answer_code']?>').attr('data-jump_id');
                                                    $('.timebox<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                }
                                            }
                                        });
                                    } else {
                                        // picker的年月日时分
                                        var picker<?=$val['question_id'].'_'.$answer['answer_code']?> = new Picker({
                                            data: [
                                                yyyymmddhData,
                                                yyyymmddhmData,
                                                yyyymmddhmsData,
                                            ],
                                            selectedIndex: [
                                                thisHour,
                                                thisMinutes,
                                                thisSeconds,
                                            ],
                                            title: "请选择年月日时分",
                                        });
                                        $(".timebox<?=$answer['answer_code']?>").on("click", function() {
                                            picker<?=$val['question_id'].'_'.$answer['answer_code']?>.show();
                                        });

                                        picker<?=$val['question_id'].'_'.$answer['answer_code']?>.on("picker.select", function(selectedVal, selectedIndex) {
                                            // var cent = parseInt(yyyymmddhData[selectedIndex[0]].text);

                                            var cent = parseInt(''+getzf(yyyymmddhData[selectedIndex[0]].text)+getzf(yyyymmddhmData[selectedIndex[1]].text)+getzf(yyyymmddhmData[selectedIndex[2]].text));
                                            var max = parseInt('<?=$max?>'.replace(/:/g,''));
                                            var min = parseInt('<?=$min?>'.replace(/:/g,''));

                                            if(cent<min||cent>max){
                                                $('.timemsg<?=$answer['answer_code']?>').html('*<?=$msg?>');
                                                $('.timebox<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id','');
                                                // return false;
                                            }else{
                                                $('.timemsg<?=$answer['answer_code']?>').html('');
                                            }
                                            $(".timebox<?=$answer['answer_code']?>").text(
                                                getzf(yyyymmddhData[selectedIndex[0]].text) +
                                                ":" +
                                                getzf(yyyymmddhmData[selectedIndex[1]].text) +
                                                ":" +
                                                getzf(yyyymmddhmData[selectedIndex[2]].text)
                                            );

                                            var index = $('.timebox<?=$answer['answer_code']?>').attr('tabindex');
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = getzf(yyyymmddhData[selectedIndex[0]].text)+":"+getzf(yyyymmddhmData[selectedIndex[1]].text)+":"+getzf(yyyymmddhmData[selectedIndex[2]].text);

                                            // 逻辑跳转
                                            if($('.timebox<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                var answer_jump_question_id = $('.timebox<?=$answer['answer_code']?>').attr('data-jump_id');
                                                $('.timebox<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            }
                                        });
                                    }
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach;?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'quantity': ?>
                        <!-- 量题 -->
                        <div id="<?=$val['question_id']?>" class="quantity question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <input tabindex="0" class="<?=$val['question_id']?>" name="<?=$val['question_id']?>" value="" type="text" onKeyUp="value=value.replace(/[^\d]/g,'')" style="width: 300px; height: 32px; color: rgb(102, 102, 102);">
                            <script>
                                $(document).ready(function(){
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(item.answer_value){
                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                        }
                                    });
                                })
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'opentext': ?>
                        <!-- 开放文本题 -->
                        <div id="<?=$val['question_id']?>" class="opentext question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <input tabindex="<?=$index?>" id="<?=$val['question_id'].'_'.$answer['variable_id']?>" name="<?=$val['question_id']?>" class="opentext<?=$val['question_id']?>" type="text" data-label="<?=$answer['answer_label']?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" value="<?=$answer['answer_value']??''?>"/>
                                <script>
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                $('.opentext<?=$val['question_id']?>').on('blur input',function(){
                                    var text = $(this).val().length;
                                    var min = parseInt('<?=$min?>');
                                    var max = parseInt('<?=$max?>');
                                    if(text<min || text>max){
                                        $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                    }else{
                                        $('.msgbox<?=$val['question_id']?>').html('*');
                                    }
                                    var index = $(this).attr('tabindex');
                                    question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();

                                    // 逻辑跳转
                                    if($(this).attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $('.opentext<?=$val['question_id']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                                // $('.opentext<?=$val['question_id']?>').trigger('blur');
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'opentextlist': ?>
                        <!-- 文本列表 -->
                        <div id="<?=$val['question_id']?>" class="opentextlist question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font></p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0]??'';
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: inline-block; margin-bottom: 10px;"><?=$title?></span>
                                <span class="red"></span>
                                <input tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="opentextlist<?=$val['question_id']?> fullwidth" type="text" class="fullwidth" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" value="<?=$answer['answer_value']??''?>"/>
                                <script>
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach;?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                $('.opentextlist<?=$val['question_id']?>').on('blur input',function(){
                                    var text = $(this).val().length;
                                    var min = parseInt($(this).data('min'));
                                    var max = parseInt($(this).data('max'));
                                    var msg = $(this).data('msg');
                                    if(text<min || text>max){
                                        $(this).prev().html('*'+msg);
                                    }else{
                                        $(this).prev().html('');
                                    }
                                    var index = $(this).attr('tabindex');
                                    question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                    // 逻辑跳转
                                    if($(this).attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                                // $('.opentextlist<?=$val['question_id']?>').trigger('blur');
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'opentextarea': ?>
                        <!-- 开放文本域 -->
                        <div id="<?=$val['question_id']?>" class="opentextarea question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <textarea tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="opentextarea<?=$val['question_id']?>" rows="6" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" style="margin-bottom:15px;"><?=$answer['answer_value']??''?></textarea>
                                <script>
                                    $('.opentextarea<?=$val['question_id']?>').on('blur input',function(){
                                        var text = $(this).val().length;
                                        var min = parseInt('<?=$min?>');
                                        var max = parseInt('<?=$max?>');
                                        if(text<min || text>max){
                                            $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                        }else{
                                            $('.msgbox<?=$val['question_id']?>').html('*');
                                        }
                                        var index = $(this).attr('tabindex');
                                        question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();

                                        // 逻辑跳转
                                        if($('.opentextarea<?=$val['question_id']?>').attr('data-jump')=="1"){
                                            var answer_jump_question_id = $('.opentextarea<?=$val['question_id']?>').attr('data-jump_id');
                                            $('.opentextarea<?=$val['question_id']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        }
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'opentextarealist': ?>
                        <!-- 开放的文本区域列表题 -->
                        <div id="<?=$val['question_id']?>" class="opentextarealist question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: inline-block; margin-bottom: 10px;"><?=$title?></span>
                                <span class="red"></span>
                                <textarea tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="opentextarealist<?=$val['question_id']?>" rows="6" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" style="margin-bottom:15px;"><?=$answer['answer_value']??''?></textarea>
                                <script>
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach;?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                $('.opentextarealist<?=$val['question_id']?>').on('blur input',function(){
                                    var text = $(this).val().length;
                                    var min = parseInt($(this).data('min'));
                                    var max = parseInt($(this).data('max'));
                                    var msg = $(this).data('msg');
                                    if(text<min || text>max){
                                        $(this).prev().html('*'+msg);
                                    }else{
                                        $(this).prev().html('');
                                    }
                                    var index = $(this).attr('tabindex');
                                    question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                    // 逻辑跳转
                                    if($(this).attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'numeric': ?>
                        <!-- 数字题 -->
                        <div id="<?=$val['question_id']?>" class="numeric question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <input tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" type="text" class="numeric<?=$val['question_id']?>" onKeyUp="value=value.replace(/[^\d]/g,'')" data-min="<?=$min?>"  data-max="<?=$max?>" data-msg="<?=$msg?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" value="<?=$answer['answer_value']??''?>"/>
                                <script>
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                $('.numeric<?=$val['question_id']?>').on('blur input', function() {
                                    var cent = parseInt($(this).val());
                                    var min = parseInt($(this).data('min'));
                                    var max = parseInt($(this).data('max'));
                                    if ($(this).val()=='' || min > 0 && cent < min || max > 0 && cent > max) {
                                        $('.msgbox<?=$val['question_id']?>').html('*'+$(this).data('msg'));
                                    } else {
                                        $('.msgbox<?=$val['question_id']?>').html('*');
                                    }
                                    var index = $(this).attr('tabindex');
                                    question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                    // 逻辑跳转
                                    if($(this).attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'numericlist': ?>
                        <!-- 数字域题 -->
                        <div id="<?=$val['question_id']?>" class="numeric question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label'])[0];
                                    $config = explode('@', $answer['answer_label'])[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: inline-block;margin-bottom:10px;margin-right:10px;"><?=$title?></span><span class="red"></span>
                                <input tabindex="<?=$index?>" id="<?=$answer['variable_id']?>" name="<?=$val['question_id']?>" class="numericlist<?=$val['question_id']?>" type="text" onKeyUp="value=value.replace(/[^\d]/g,'')" data-min="<?=$min?>"  data-max="<?=$max?>" data-msg="<?=$msg?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" value="<?=$answer['answer_value']??''?>"/>
                                <script>
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;padding-bottom:80px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                $('.numericlist<?=$val['question_id']?>').on('blur input', function() {
                                    var cent = parseInt($(this).val());
                                    var min = parseInt($(this).data('min'));
                                    var max = parseInt($(this).data('max'));
                                    if ($(this).val()==''||min > 0 && cent < min || max > 0 && cent > max) {
                                        $(this).prev().html($(this).data('msg'));
                                    } else {
                                        $(this).prev().html('');
                                    }
                                    var index = $(this).attr('tabindex');
                                    question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                    // 逻辑跳转
                                    if($(this).attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'describe': ?>
                        <!-- 文字描述题 -->
                        <div id="<?=$val['question_id']?>" class="describe question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?></p>
                            <?php foreach ($val['list'] as $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0]??'';
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <input type="hidden" class="describe<?=$answer['answer_code']?>" data-min="<?=$min?>"  data-max="<?=$max?>" data-msg="<?=$msg?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"/>
                                <script>
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <!-- <button class="gray_btn" data-question_id="" style="display:none;"></button> -->
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                $(document).ready(function() {
                                    question_list.<?=$val['question_id']?>.list[0].answer_value = 1;
                                    if($('.question<?=$val['question_id']?>').find('input').attr('data-jump')=='1'){
                                        var answer_jump_question_id = $('.question<?=$val['question_id']?>').find('input').attr('data-jump_id');
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'select': ?>
                        <!-- 下拉单选题 -->
                        <div id="<?=$val['question_id']?>" class="select question<?=$val['question_id']?> question_box" style="position: relative;<?php if($val['question_hide']==1):?>display:none;<?php endif;?>">
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <select id="select" class="select<?=$val['question_id']?>">
                                <option value="">请选择</option>
                                <?php foreach ($val['list'] as $index => $answer) : ?>
                                <option tabindex="<?=$index?>" value="<?=$answer['answer_code']?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>selected<?php endif;?>><?=$answer['answer_label']?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                if ($(window).width() > 750) {
                                    $(".select<?=$val['question_id']?>").select2({
                                        width: "300px",
                                        height: "32px",
                                        minimumResultsForSearch: -1,
                                    });
                                }else{
                                    $(".select<?=$val['question_id']?>").select2({
                                        width: "100%",
                                        height: "40px",
                                        minimumResultsForSearch: -1,
                                    });
                                }
                                $('.select<?=$val['question_id']?>').change(function(){
                                    $('.select<?=$val['question_id']?> option').each(function(){
                                        var index = $(this).attr('tabindex');
                                        if(index>=0&&$(this).prop('selected')==true){
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 1;
                                        }else if(index>=0&&$(this).prop('selected')==false){
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 0;
                                        }
                                    });
                                    if($(this).find('option:selected').attr('data-jump')=='1'){
                                        var answer_jump_question_id = $(this).find('option:selected').attr('data-jump_id');
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else{
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                    }
                                });
                                $(document).ready(function(){
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(item.answer_value){
                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                        }
                                    });
                                    $('.select<?=$val['question_id']?>').trigger('change');
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'inputselect': ?>
                        <!-- 输入下拉题 -->
                        <div id="<?=$val['question_id']?>" class="input_select question<?=$val['question_id']?> question_box" style="position: relative;<?php if($val['question_hide']==1):?>display:none;<?php endif;?>">
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <!-- <span class="input" id="name" class="inputselectbox<?=$val['question_id']?>">
                                <font color="#ccc">请选择</font>
                            </span> -->
                            <select class="inputselect<?=$val['question_id']?>">
                                <option value="">请选择</option>
                                <?php foreach ($val['list'] as $index => $answer) : ?>
                                    <option tabindex="<?=$index?>" value="<?=$answer['answer_code']?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>selected<?php endif;?>><?=$answer['answer_label']?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="jump_box" style="text-align: center; padding-top:80px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                if ($(window).width() > 750) {
                                    $(".inputselect<?=$val['question_id']?>").show();
                                    // select2的配置
                                    $(".inputselect<?=$val['question_id']?>").select2({
                                        width: "300px",
                                        height: "32px",
                                        tags: true,
                                    });
                                }else{
                                    $(".inputselect<?=$val['question_id']?>").select2({
                                        width: "100%",
                                        height: "40px",
                                        tags: true,
                                    });
                                }
                                $('.inputselect<?=$val['question_id']?>').change(function(){
                                    $('.inputselect<?=$val['question_id']?> option').each(function(){
                                        var index = $(this).attr('tabindex');
                                        if(index>=0&&$(this).prop('selected')==true){
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 1;
                                        }else if(index>=0&&$(this).prop('selected')==false){
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 0;
                                        }
                                    });
                                    if($(this).find('option:selected').attr('data-jump')=='1'){
                                        var answer_jump_question_id = $(this).find('option:selected').attr('data-jump_id');
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else if($(this).find('option:selected').attr('data-select2-tag')=='true'){
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',$(this).find('option:eq(1)').attr('data-jump_id'));
                                        var value = $(this).find('option:selected').html();
                                        question_list.<?=$val['question_id']?>.list.push({answer_label:value,answer_value:1,type: "inputselect",variable_id: "<?=$val['question_id']?>",answer_jump_touch:1,answer_jump_question_id:$(this).find('option:eq(1)').attr('data-jump_id')});
                                    }else{
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                    }
                                });
                                $(document).ready(function(){
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(item.answer_value){
                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                        }
                                    });
                                    $('.inputselect<?=$val['question_id']?>').trigger('change');
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'picture': ?>
                        <!-- 图片显示题 -->
                        <div id="<?=$val['question_id']?>" class="picture question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                            ?>
                            <p class="title"><?=$title[0]?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <div class="imgBox">
                                <?php foreach ($val['list'] as $index => $answer) : ?>
                                    <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                        <img tabindex="<?=$index?>" class="picture<?=$val['question_id'].'_'.$answer['answer_code']?> active" height="200px" src="<?=$answer['answer_label']?>" style="border: 1px solid red; outline: none; box-sizing: border-box; width: 49%; height: 202px;" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>">
                                        <script>
                                            $(document).ready(function() {
                                                question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                    if(item.answer_value){
                                                        question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                    }else{
                                                        question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                    }
                                                });
                                                // 逻辑跳转
                                                <?php if($answer['answer_jump_touch']==1):?>
                                                    var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                <?php endif;?>
                                            });
                                        </script>
                                    <?php else:?>
                                        <img tabindex="<?=$index?>" class="picture<?=$val['question_id'].'_'.$answer['answer_code']?>" height="200px" src="<?=$answer['answer_label']?>" style="border: 1px solid #dee4ec;outline:none; box-sizing: border-box;width:48%" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"/>
                                    <?php endif;?>
                                <?php endforeach; ?>
                            </div>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                img_width(parseInt(<?=$title[1]?>));
                                // 图片显示题
                                $(".question<?=$val['question_id']?> .imgBox img").click(function () {
                                    $(this).addClass('active').css("border", "1px solid red").siblings().removeClass('active').css("border", "1px solid #dee4ec");
                                    $(".question<?=$val['question_id']?> .imgBox img").each(function(){
                                        var index = $(this).attr('tabindex');
                                        if($(this).hasClass('active')){
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 1;
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 0;
                                        }
                                    });

                                    if($(this).attr('data-jump')=='1'){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else{
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'video': ?>
                        <!-- 播放视频题 -->
                        <div id="<?=$val['question_id']?>" class="video question<?=$val['question_id']?> question_box" style="position: relative;<?php if($val['question_hide']==1):?>display:none;<?php endif;?>">
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']);
                                    $config = explode('_', $title[1]);
                                    $area = $config[0]??'';
                                    $msg = $config[1]??'';
                                    $min = $config[2]??'';
                                    $max = $config[3]??'';
                                ?>

                                <video class="videobox<?=$answer['answer_code']?>" src="<?=$title[0]?>" width="100%" height="240" controls style="outline: none; background: #000;">
                                    您的浏览器版本太低了。
                                </video>
                                <div class="option" style="display: none;width: 100%;position: absolute;background-color: #fff;left: 50%;bottom: 0;transform: translateX(-50%);z-index: 999;margin-bottom:115px;">
                                    <h4><?=$config[0]?></h4>
                                    <textarea tabindex="<?=$index?>" class="video<?=$answer['answer_code']?>" style="color: rgb(102, 102, 102);margin-top: 10px;padding-top: 5px;resize: none;height: 80px;" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"><?=$answer['answer_value']??''?></textarea>
                                </div>

                                <script>
                                    // 监听视频播放结束
                                    $(".videobox<?=$answer['answer_code']?>")[0].addEventListener("ended", function () {
                                        $(".question<?=$val['question_id']?> .option").show();
                                        $(".question<?=$val['question_id']?> .jump_box").show();
                                        $(".videobox<?=$answer['answer_code']?>").css({"margin-bottom":"150px"});
                                    });

                                    $('.video<?=$answer['answer_code']?>').on('blur input',function(){
                                        var text = $(this).val().length;
                                        var min = parseInt('<?=$min?>');
                                        var max = parseInt('<?=$max?>');
                                        if(text<min || text>max){
                                            $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                        }else{
                                            $('.msgbox<?=$val['question_id']?>').html('*');
                                        }
                                        var index = $(this).attr('tabindex');
                                        question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                        if($(this).attr('data-jump')=='1'){
                                            var answer_jump_question_id = $(this).attr('data-jump_id');
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        }else{
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                        }
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            $(".question<?=$val['question_id']?> .option").show();
                                            $(".question<?=$val['question_id']?> .jump_box").show();
                                            $(".videobox<?=$answer['answer_code']?>").css({"margin-bottom":"150px"});
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;display: none;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'audio': ?>
                        <!-- 播放音频题 -->
                        <div id="<?=$val['question_id']?>" class="audio question<?=$val['question_id']?> question_box" style="position: relative;<?php if($val['question_hide']==1):?>display:none;<?php endif;?>">
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']);
                                    $config = explode('_', $title[1]);
                                    $area = $config[0]??'';
                                    $msg = $config[1]??'';
                                    $min = $config[2]??'';
                                    $max = $config[3]??'';
                                ?>
                                <audio class="audiobox<?=$val['question_id'].'_'.$answer['answer_code']?>" controls style="width: 100%;">
                                    您的浏览器版本太低了。
                                    <source src="<?=$title[0]?>" type="audio/ogg" />
                                    <source src="<?=$title[0]?>" type="audio/mpeg" />
                                </audio>
                                <div class="option" style="display: none;width: 100%;position: absolute;background-color: #fff;left: 50%;bottom: 0;transform: translateX(-50%);z-index: 999;margin-bottom: 115px;">
                                    <h4><?=$config[0]?></h4>
                                    <textarea tabindex="<?=$index?>" class="audio<?=$answer['answer_code']?>" style="color: rgb(102, 102, 102);margin-top: 10px;padding-top: 5px;resize: none;height: 80px;" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"><?=$answer['answer_value']??''?></textarea>
                                </div>
                                <script>
                                    // 监听音频播放结束
                                    $(".audiobox<?=$val['question_id'].'_'.$answer['answer_code']?>")[0].addEventListener("ended", function () {
                                        $(".question<?=$val['question_id']?> .option").show();
                                        $(".question<?=$val['question_id']?> .jump_box").show();
                                        $(".audiobox<?=$val['question_id'].'_'.$answer['answer_code']?>").css({ "margin-bottom": "150px" });
                                    });

                                    $('.audio<?=$answer['answer_code']?>').on('blur input',function(){
                                        var text = $(this).val().length;
                                        var min = parseInt('<?=$min?>');
                                        var max = parseInt('<?=$max?>');
                                        if(text<min || text>max){
                                            $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                        }else{
                                            $('.msgbox<?=$val['question_id']?>').html('*');
                                        }
                                        var index = $(this).attr('tabindex');
                                        question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                        if($(this).attr('data-jump')=='1'){
                                            var answer_jump_question_id = $(this).attr('data-jump_id');
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        }else{
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                        }
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            $(".question<?=$val['question_id']?> .option").show();
                                            $(".question<?=$val['question_id']?> .jump_box").show();
                                            $(".audiobox<?=$val['question_id'].'_'.$answer['answer_code']?>").css({ "margin-bottom": "150px" });
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;display: none;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'signname': ?>
                        <!-- 签名题 -->
                        <div id="<?=$val['question_id']?>" class="signname question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title" style="font-weight: 400;"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <div class="button" style="text-align: right;border: 1px solid #ccc;border-bottom: none;background: #f5f5f5;padding: 10px 20px 2px; margin-bottom: 0px">
                                    <button class="clearSig<?=$answer['answer_code']?>" style="background-color: #ccc;">重置</button>
                                </div>
                                <?php if(isset($answer['answer_value'])):?>
                                    <div tabindex="<?=$index?>" class="signature<?=$val['question_id'].'_'.$answer['answer_code']?>" style="border: 1px solid #ccc;background:#f5f5f5;border-top:none;overflow: hidden;display:none;" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"></div>
                                    <img width="100%" class="view_signature<?=$val['question_id']?>" style="border: 1px solid #ccc;border-top:none;background:#f5f5f5;overflow: hidden;box-sizing:border-box;" src="<?=$answer['answer_value']??''?>">
                                    <script>
                                        $(document).ready(function() {
                                            question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                if(item.answer_value){
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                }else{
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                }
                                            });
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        });
                                    </script>
                                <?php else:?>
                                    <div tabindex="<?=$index?>" class="signature<?=$val['question_id'].'_'.$answer['answer_code']?>" style="border: 1px solid #ccc;background:#f5f5f5;border-top:none;overflow: hidden;" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"></div>
                                    <img style="border: 1px solid #ccc;border-top:none;display:none;" src="">
                                <?php endif;?>
                                <input type="hidden" name="signature<?=$val['question_id'].'_'.$answer['answer_code']?>" value="">
                                <script>
                                    // 签名题
                                    $(".signature<?=$val['question_id'].'_'.$answer['answer_code']?>").jSignature({
                                        height: "200",
                                        "background-color": "#f5f5f5",
                                        color: "#666",
                                    });

                                    //重置画布
                                    $(".clearSig<?=$answer['answer_code']?>").click(function () {
                                        $(".signature<?=$val['question_id'].'_'.$answer['answer_code']?>").show().jSignature("clear");
                                        $('.view_signature<?=$val['question_id']?>').hide();
                                        $('input[name="signature<?=$val['question_id'].'_'.$answer['answer_code']?>"]').val('');
                                    });
                                    $(".signature<?=$val['question_id'].'_'.$answer['answer_code']?>").bind('change', function(e){
                                        var base64 = $(this).jSignature("getData");

                                        var base64 = $(this).jSignature("getData", "image");
                                        if(!base64){
                                            return false;
                                        }

                                        var src = "data:" + base64[0] + "," + base64[1];
                                        var index = $(this).attr('tabindex');
                                        $('input[name="signature<?=$val['question_id'].'_'.$answer['answer_code']?>"]').val(base64[1]);

                                        // 逻辑跳转
                                        if($('.signature<?=$val['question_id'].'_'.$answer['answer_code']?>').attr('data-jump')=="1"){
                                            var answer_jump_question_id = $('.signature<?=$val['question_id'].'_'.$answer['answer_code']?>').attr('data-jump_id');
                                            $('.signature<?=$val['question_id'].'_'.$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        }
                                    });
                                    $(document).ready(function() {
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="" onClick="up_sign('signature<?=$val['question_id'].'_'.$answer['answer_code']?>');">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'takeaudio': ?>
                        <!-- 上传视频 -->
                        <div id="<?=$val['question_id']?>" class="takeaudio question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title" style="font-weight: 400;"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']);
                                    $config = explode('_', $title[1]);
                                    $msg = $config[0]??'';
                                    $min = $config[1]??'';
                                    $max = $config[2]??'';
                                ?>
                                <p><?=$title[0]?> <span class="red msgbox<?=$answer['answer_code']?>"></span></p>
                                <label>
                                    <input tabindex="<?=$index?>" class="takeaudio<?=$val['question_id'].'_'.$answer['answer_code']?>" name="takeaudio<?=$val['question_id'].'_'.$answer['answer_code']?>" multiple="multiple" type="file" style="display: none;" accept="video/*" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"<?php if(!isset($answer['answer_value'])):?>data-file="0"<?php endif;?>>
                                    <span></span>
                                </label>
                                
                                <?php if(isset($answer['answer_value'])):?>
                                    <div class="takeaudioBox takeaudioBox<?=$val['question_id'].'_'.$answer['answer_code']?>" style="height: 240px;border: 1px solid #ccc; position: relative;margin-bottom:30px;">
                                        <video controls="" width="100%" height="240" style="background: rgb(0, 0, 0);" src="<?=$answer['answer_value']??''?>"></video>
                                        <span style="font-size: 40px;font-weight: 100;position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);color: #ccc;"></span>
                                    </div>
                                <?php else:?>
                                    <div class="takeaudioBox takeaudioBox<?=$val['question_id'].'_'.$answer['answer_code']?>" style="height: 240px;border: 1px solid #ccc; position: relative;margin-bottom:30px;">
                                        <video controls="" width="100%" height="240" style="display: none; background: #000;"></video>
                                        <span style="font-size: 40px;font-weight: 100;position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);color: #ccc;">+</span>
                                    </div>
                                <?php endif;?>
                                <script>
                                    $('input[name="takeaudio<?=$val['question_id'].'_'.$answer['answer_code']?>"]').change(function() {
                                        var content = $(this)[0].files[0];
                                        //获取录音时长
                                        var url = URL.createObjectURL(content);
                                        //audio获取视频的时长
                                        var audioElement = new Audio(url);
                                        audioElement.addEventListener("loadedmetadata", function (_event) {
                                            var length = audioElement.duration;
                                            var min = parseInt("<?=$config[1]?>");
                                            var max = parseInt("<?=$config[2]?>");
                                            if (min > 0 && length < min || max > 0 && length > max) {
                                                $('.msgbox<?=$answer['answer_code']?>').html('<?=$config[0]?>');
                                                return false;
                                            }else{
                                                $('.msgbox<?=$answer['answer_code']?>').html('');
                                                var objUrl = getObjectURL(content);
                                                $('.takeaudioBox<?=$val['question_id'].'_'.$answer['answer_code']?> video').attr("src", objUrl);

                                                // 上传
                                                var formData = new FormData();
                                                formData.append("file",content);
                                                $.ajax({
                                                    url: "/sv/dr/upload",
                                                    type: "post",
                                                    data: formData,
                                                    async: true,
                                                    contentType: false,
                                                    processData: false,
                                                    mimeType: "multipart/form-data",
                                                    dataType: 'json',
                                                    success: function (res) {
                                                        var index = $('input[name="takeaudio<?=$val['question_id'].'_'.$answer['answer_code']?>"]').attr('tabindex');
                                                        question_list.<?=$val['question_id']?>.list[index].answer_value = res.result;
                                                    },
                                                    error: function (e) {
                                                        console.log(e);
                                                    }
                                                });


                                                // $(".recording audio")[0].play();
                                                $('.takeaudioBox<?=$val['question_id'].'_'.$answer['answer_code']?> video').show();
                                                if($('input[name="takeaudio<?=$val['question_id'].'_'.$answer['answer_code']?>"]').attr('data-jump')=='1'){
                                                    var answer_jump_question_id = $('input[name="takeaudio<?=$val['question_id'].'_'.$answer['answer_code']?>"]').attr('data-jump_id');
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                }else{
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                                }
                                            }
                                        });
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'recording': ?>
                        <!-- 上传音频 -->
                        <div id="<?=$val['question_id']?>" class="recording question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title" style="font-weight: 400;"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']);
                                    $config = explode('_', $title[1]);
                                    $msg = $config[0]??'';
                                    $min = $config[1]??'';
                                    $max = $config[2]??'';
                                ?>
                                <p> <?=$title[0]?></p>
                                <label>
                                    <input tabindex="<?=$index?>" id="recording<?=$val['question_id'].'_'.$answer['answer_code']?>" name="recording<?=$val['question_id'].'_'.$answer['answer_code']?>" multiple="multiple" type="file" style="display: none;" accept="audio/*" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" <?php if(!isset($answer['answer_value'])):?>data-file="0"<?php endif;?>>
                                </label>
                                
                                <?php if(isset($answer['answer_value'])):?>
                                    <div class="recordingBox recordingBox<?=$val['question_id'].'_'.$answer['answer_code']?>" style="height: 54px; border: none; position: relative; margin-bottom: 30px;">
                                        <span style="font-size: 40px;font-weight: 100;position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);color: #ccc;"></span>
                                        <audio controls="" style="width: 100%;" src="<?=$answer['answer_value']??''?>"></audio>
                                    </div>
                                <?php else:?>
                                    <div class="recordingBox recordingBox<?=$val['question_id'].'_'.$answer['answer_code']?>" style="height: 54px;border: 1px solid #ccc; position: relative;margin-bottom:30px;">
                                        <span style="font-size: 40px;font-weight: 100;position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);color: #ccc;">+</span>
                                        <audio controls="" style="width: 100%; display: none;"></audio>
                                    </div>
                                <?php endif;?>
                                <script>
                                    $('input[name="recording<?=$val['question_id'].'_'.$answer['answer_code']?>"]').change(function() {
                                        var content = $(this)[0].files[0];
                                        //获取录音时长
                                        var url = URL.createObjectURL(content);
                                        //audio获取视频的时长
                                        var audioElement = new Audio(url);
                                        audioElement.addEventListener("loadedmetadata", function (_event) {
                                            var length = audioElement.duration;
                                            var min = parseInt("<?=$config[1]?>");
                                            var max = parseInt("<?=$config[2]?>");
                                            if (min > 0 && length < min || max > 0 && length > max) {
                                                $('.msgbox<?=$val['question_id']?>').html('*<?=$config[0]?>');
                                                return false;
                                            }else{
                                                $('.msgbox<?=$val['question_id']?>').html('*');
                                                var objUrl = getObjectURL(content);
                                                $('.recordingBox<?=$val['question_id'].'_'.$answer['answer_code']?> audio').attr("src", objUrl);
                                                $('.recordingBox<?=$val['question_id'].'_'.$answer['answer_code']?> audio').show();
                                                $('.recordingBox<?=$val['question_id'].'_'.$answer['answer_code']?>').css("border", "none");


                                                // 上传
                                                var formData = new FormData();
                                                formData.append("file",content);
                                                $.ajax({
                                                    url: "/sv/dr/upload",
                                                    type: "post",
                                                    data: formData,
                                                    async: true,
                                                    contentType: false,
                                                    processData: false,
                                                    mimeType: "multipart/form-data",
                                                    dataType: 'json',
                                                    success: function (res) {
                                                        var index = $('input[name="recording<?=$val['question_id'].'_'.$answer['answer_code']?>"]').attr('tabindex');
                                                        question_list.<?=$val['question_id']?>.list[index].answer_value = res.result;
                                                    },
                                                    error: function (e) {
                                                        console.log(e);
                                                    }
                                                });

                                                if($('input[name="recording<?=$val['question_id'].'_'.$answer['answer_code']?>"]').attr('data-jump')=='1'){
                                                    var answer_jump_question_id = $('input[name="recording<?=$val['question_id'].'_'.$answer['answer_code']?>"]').attr('data-jump_id');
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                }else{
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                                }
                                            }
                                        });
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'photograph': ?>
                        <!-- 拍摄照片 -->
                        <div id="<?=$val['question_id']?>" class="photograph question<?=$val['question_id']?> question_box" style="margin-bottom:0;<?php if($val['question_hide']==1):?>display:none;<?php endif;?>">
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label']??'')[0];
                                    $config = explode('@', $answer['answer_label']??'')[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $max = explode('_', $config)[1]??'';
                                ?>
                                <?php if(isset($answer['answer_value'])):?>
                                    <div class="photographBox">
                                        <span class="plus"></span>
                                        <span class="type"><?=$title?></span>
                                        <label>
                                            +<input tabindex="<?=$index?>" name="photograph<?=$val['question_id'].'_'.$answer['answer_code']?>" type="file" accept="image/*" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>"/>
                                        </label>
                                        <img width="240px" height="240px" src="<?=$answer['answer_value']??''?>"/>
                                    </div>
                                <?php else:?>
                                    <div class="photographBox">
                                        <span class="plus">+</span>
                                        <span class="type"><?=$title?></span>
                                        <label>
                                            +<input tabindex="<?=$index?>" name="photograph<?=$val['question_id'].'_'.$answer['answer_code']?>" type="file" accept="image/*" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" data-file="0"/>
                                        </label>
                                        <img width="240px" height="240px"/>
                                    </div>
                                <?php endif;?>
                                <script>
                                    // 上传图片
                                    $("input[name='photograph<?=$val['question_id'].'_'.$answer['answer_code']?>']").change(function () {
                                        var content = $(this)[0].files[0];
                                        var size = content.size;
                                        var max = parseInt('<?=$max?>')*1024*1024;
                                        if(size>max){
                                            $('.msgbox<?=$val['question_id']?>').html('*<?=$msg?>');
                                            return false;
                                        }else{
                                            $('.msgbox<?=$val['question_id']?>').html('*');
                                        }
                                        var windowURL = window.URL || window.webkitURL;
                                        var objUrl = windowURL.createObjectURL(content);
                                        $(this).parent().next().attr("src", objUrl);
                                        $(this).parent().next().css("display", "inline-block");
                                        $(this).parent().prev().css("border-bottom", "1px solid #ccc");
                                        $(this).parent().prev().prev().hide();


                                        // 上传
                                        var formData = new FormData();
                                        formData.append("file",content);
                                        $.ajax({
                                            url: "/sv/dr/upload",
                                            type: "post",
                                            data: formData,
                                            async: true,
                                            contentType: false,
                                            processData: false,
                                            mimeType: "multipart/form-data",
                                            dataType: 'json',
                                            success: function (res) {
                                                var index = $('input[name="photograph<?=$val['question_id'].'_'.$answer['answer_code']?>"]').attr('tabindex');
                                                question_list.<?=$val['question_id']?>.list[index].answer_value = res.result;
                                            },
                                            error: function (e) {
                                                console.log(e);
                                            }
                                        });


                                        if($(this).attr('data-jump')=='1'){
                                            var answer_jump_question_id = $(this).attr('data-jump_id');
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        }else{
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                        }
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'union2': ?>
                        <!-- 省市二级联动题 -->
                        <div id="<?=$val['question_id']?>" class="union3 question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                                $config = explode('_', $title[1]);
                                $msg = $config[0]??'';
                                $min = $config[1]??'';
                                $max = $config[2]??'';
                            ?>
                            <p class="title"><?=$title[0]?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <div class="citysBox">
                                <?php $arr = []; ?>
                                <?php foreach ($val['list'] as $index => $answer) : ?>
                                    <?php
                                        $province = explode('_', $answer['answer_label'])[0];
                                        $city = explode('_', $answer['answer_label'])[1];
                                        $arr[$province]['name'] = $province;
                                        $arr[$province]['list'][$city]['name'] = $city;
                                        $arr[$province]['list'][$city]['tabindex'] = $index;
                                        $arr[$province]['list'][$city]['answer_jump_touch'] = $answer['answer_jump_touch'];
                                        $arr[$province]['list'][$city]['answer_jump_question_id'] = $answer['answer_jump_question_id'];
                                    ?>
                                    <script>
                                        $(document).ready(function() {
                                            question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                if(item.answer_value){
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                }else{
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                }
                                            });
                                            <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                                $('#province<?=$val['question_id']?>').find('option[value="<?=$province?>"]').prop('selected',true).trigger('change');
                                                $('#city<?=$val['question_id']?>').find('option[value="<?=$city?>"]').prop('selected',true).trigger('change');
                                                $("#union2<?=$val['question_id']?>").text('<?=$province.' '.$city?>');

                                                // 逻辑跳转
                                                <?php if($answer['answer_jump_touch']==1):?>
                                                    var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                <?php endif;?>
                                            <?php else:?>
                                                
                                            <?php endif;?>
                                        });
                                    </script>
                                <?php endforeach; ?>
                                <span style="display: inline-block; width: 48%;">
                                    <?=$config[0]?>：<select id="province<?=$val['question_id']?>" style="height: 32px">
                                        <option>—请选择—</option>
                                        <?php foreach ($arr as $k => $v) : ?>
                                            <option value="<?=$v['name']?>"><?=$v['name']?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </span>
                                <span style="display: inline-block; width: 48%;">
                                    <?=$config[1]?>：<select id="city<?=$val['question_id']?>" style="height: 32px">
                                        <option>—请选择—</option>
                                    </select>
                                </span>
                            </div>
                            <span style="display: none;width: 100%;height: 40px;line-height: 40px;color: rgb(0, 0, 0);" class="input union_select" id="union2<?=$val['question_id']?>"></span>
                            <script>
                                var arr<?=$val['question_id']?> = JSON.parse('<?= json_encode($arr, true) ?>');
                                // 改变第1级
                                $('#province<?=$val['question_id']?>').change(function() {
                                    var province = $(this).val();
                                    $('#city<?=$val['question_id']?>').html('<option>—请选择—</option>');
                                    if (arr<?=$val['question_id']?>.hasOwnProperty(province)) {
                                        for (var item in arr<?=$val['question_id']?>[province]['list']) {
                                            var element = arr<?=$val['question_id']?>[province]['list'][item];
                                            $('#city<?=$val['question_id']?>').append('<option value="' + element.name + '" data-tabindex="'+element.tabindex+'" data-jump="'+element.answer_jump_touch+'" data-jump_id="'+element.answer_jump_question_id+'">' + item + '</option>');
                                        }
                                    }
                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                });
                                // 改变第2级
                                $('#city<?=$val['question_id']?>').change(function(){
                                    var index = $(this).find('option:selected').attr('data-tabindex');
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(i==index){
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=1;
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=0;
                                        }
                                    });

                                    if($(this).find('option:selected').attr('data-jump')=='1'){
                                        var answer_jump_question_id = $(this).find('option:selected').attr('data-jump_id');
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else{
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                    }
                                });

                                // 第1级默认index
                                var firstIndex = Object.keys(arr<?=$val['question_id']?>)[0];
                                // 第2级默认index
                                var secondIndex = Object.keys(arr<?=$val['question_id']?>[firstIndex].list)[0];
                                // 移动端picker的三联
                                var first<?=$val['question_id']?> = []; //第1级
                                var second<?=$val['question_id']?> = []; //第2级
                                var selectedIndex<?=$val['question_id']?> = [0, 0]; // 默认选中的地区
                                var checked<?=$val['question_id']?> = [firstIndex, secondIndex]; // 已选选项

                                creatList(arr<?=$val['question_id']?>, first<?=$val['question_id']?>);

                                // 默认的第1级有子集
                                if (arr<?=$val['question_id']?>[firstIndex].hasOwnProperty("list")) {
                                    creatList(arr<?=$val['question_id']?>[firstIndex].list, second<?=$val['question_id']?>);
                                } else {
                                    second<?=$val['question_id']?> = [{
                                        text: "",
                                        value: 0
                                    }];
                                }

                                var picker<?=$val['question_id']?> = new Picker({
                                    data: [first<?=$val['question_id']?>, second<?=$val['question_id']?>],
                                    selectedIndex: selectedIndex<?=$val['question_id']?>,
                                    title: "请选择",
                                });
                                picker<?=$val['question_id']?>.on("picker.select", function(selectedVal, selectedIndex<?=$val['question_id']?>) {
                                    var text1 = first<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[0]].text;
                                    var text2 = second<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[1]].text;
                                    $("#union2<?=$val['question_id']?>").text(text1 + " " + text2);

                                    var index = second<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[1]].tabindex;
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(i==index){
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=1;
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=0;
                                        }
                                    });

                                    if(second<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[1]].answer_jump_touch=='1'){
                                        var answer_jump_question_id = second<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[1]].answer_jump_question_id;
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else{
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                    }
                                });
                                picker<?=$val['question_id']?>.on("picker.change", function(index, selectedIndex<?=$val['question_id']?>) {
                                    if (index === 0) {
                                        selectedIndex<?=$val['question_id']?> = Object.keys(arr<?=$val['question_id']?>)[selectedIndex<?=$val['question_id']?>];
                                        second<?=$val['question_id']?> = [];
                                        checked<?=$val['question_id']?>[0] = selectedIndex<?=$val['question_id']?>;
                                        var firstList = arr<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>];
                                        if (firstList.hasOwnProperty("list")) {
                                            creatList(firstList.list, second<?=$val['question_id']?>);
                                        } else {
                                            second<?=$val['question_id']?> = [{
                                                text: "",
                                                value: 0
                                            }];
                                            checked<?=$val['question_id']?>[1] = 0;
                                        }
                                        picker<?=$val['question_id']?>.refillColumn(1, second<?=$val['question_id']?>);
                                        picker<?=$val['question_id']?>.scrollColumn(1, 0);
                                    }
                                });

                                $("#union2<?=$val['question_id']?>").click(function() {
                                    picker<?=$val['question_id']?>.show();
                                });
                            </script>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'union3': ?>
                        <!-- 省市区三级联动题 -->
                        <div id="<?=$val['question_id']?>" class="union3 question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                                $config = explode('_', $title[1]);
                                $msg = $config[0]??'';
                                $min = $config[1]??'';
                                $max = $config[2]??'';
                            ?>
                            <p class="title"><?=$title[0]?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <div class="citysBox">
                                <?php $arr = []; ?>
                                <?php foreach ($val['list'] as $index => $answer) : ?>
                                    <?php
                                    $province = explode('_', $answer['answer_label'])[0];
                                    $city = explode('_', $answer['answer_label'])[1];
                                    $dict = explode('_', $answer['answer_label'])[2];
                                    $arr[$province]['name'] = $province;
                                    $arr[$province]['list'][$city]['name'] = $city;
                                    $arr[$province]['list'][$city]['list'][$dict]['name'] = $dict;
                                    $arr[$province]['list'][$city]['list'][$dict]['tabindex'] = $index;
                                    $arr[$province]['list'][$city]['list'][$dict]['answer_jump_touch'] = $answer['answer_jump_touch'];
                                    $arr[$province]['list'][$city]['list'][$dict]['answer_jump_question_id'] = $answer['answer_jump_question_id'];
                                    ?>
                                    <script>
                                        $(document).ready(function() {
                                            question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                if(item.answer_value){
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                }else{
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                }
                                            });
                                            <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                                $('#province<?=$val['question_id']?>').find('option[value="<?=$province?>"]').prop('selected',true).trigger('change');
                                                $('#city<?=$val['question_id']?>').find('option[value="<?=$city?>"]').prop('selected',true).trigger('change');
                                                $('#dict<?=$val['question_id']?>').find('option[value="<?=$dict?>"]').prop('selected',true).trigger('change');
                                                $("#union3<?=$val['question_id']?>").text('<?=$province.' '.$city.' '.$dict?>');

                                                // 逻辑跳转
                                                <?php if($answer['answer_jump_touch']==1):?>
                                                    var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                <?php endif;?>
                                            <?php else:?>
                                                
                                            <?php endif;?>
                                        });
                                    </script>
                                <?php endforeach; ?>
                                <span style="display: inline-block; width: 30%;">
                                    <?=$config[0]?>：<select id="province<?=$val['question_id']?>" style="height: 32px">
                                        <option>—请选择—</option>
                                        <?php foreach ($arr as $k => $v) : ?>
                                            <option value="<?=$v['name']?>"><?=$v['name']?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </span>

                                <span style="display: inline-block; width: 30%;">
                                    <?=$config[1]?>：<select id="city<?=$val['question_id']?>" style="height: 32px">
                                        <option>—请选择—</option>
                                    </select>
                                </span>
                                <span class="qucity" style="display: inline-block; width: 30%;">
                                    <?=$config[2]?>：<select id="dict<?=$val['question_id']?>" style="height: 32px">
                                        <option>—请选择—</option>
                                    </select>
                                </span>
                            </div>
                            <span style="display: none;width: 100%;height: 40px;line-height: 40px;color: rgb(0, 0, 0);" class="input union_select" id="union3<?=$val['question_id']?>"></span>
                            <script>
                                var arr<?=$val['question_id']?> = JSON.parse('<?= json_encode($arr, true) ?>');
                                $('#province<?=$val['question_id']?>').change(function() {
                                    var province = $(this).val();
                                    $('#city<?=$val['question_id']?>').html('<option>—请选择—</option>');
                                    $('#dict<?=$val['question_id']?>').html('<option>—请选择—</option>');
                                    if (arr<?=$val['question_id']?>.hasOwnProperty(province)) {
                                        for (var item in arr<?=$val['question_id']?>[province]['list']) {
                                            $('#city<?=$val['question_id']?>').append('<option value="' + item + '">' + item + '</option>');
                                        }
                                    }
                                });
                                $('#city<?=$val['question_id']?>').change(function() {
                                    var province = $('#province<?=$val['question_id']?>').val();
                                    var city = $(this).val();
                                    $('#dict<?=$val['question_id']?>').html('<option>—请选择—</option>');
                                    if (arr<?=$val['question_id']?>[province]['list'].hasOwnProperty(city)) {
                                        for (var item in arr<?=$val['question_id']?>[province]['list'][city]['list']) {
                                            var element = arr<?=$val['question_id']?>[province]['list'][city]['list'][item];
                                            $('#dict<?=$val['question_id']?>').append('<option value="' + element.name + '" data-tabindex="'+element.tabindex+'" data-jump="'+element.answer_jump_touch+'" data-jump_id="'+element.answer_jump_question_id+'">' + item + '</option>');
                                        }
                                        if ($('#dict<?=$val['question_id']?> option').length > 1) {
                                            $('#dict<?=$val['question_id']?>').parent().show();
                                        }
                                    }
                                });
                                $('#dict<?=$val['question_id']?>').change(function() {
                                    var index = $('#dict<?=$val['question_id']?>').find('option:selected').attr('data-tabindex');
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(i==index){
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=1;
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=0;
                                        }
                                    });

                                    if($(this).find('option:selected').attr('data-jump')=='1'){
                                        var answer_jump_question_id = $(this).find('option:selected').attr('data-jump_id');
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else{
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                    }
                                });

                                // 第1级默认index
                                var firstIndex = Object.keys(arr<?=$val['question_id']?>)[0];
                                // 第2级默认index
                                var secondIndex = Object.keys(arr<?=$val['question_id']?>[firstIndex].list)[0];
                                // 第3级默认index
                                var thirdIndex = Object.keys(arr<?=$val['question_id']?>[firstIndex].list[secondIndex].list)[0];
                                // 移动端picker的三联
                                var first<?=$val['question_id']?> = []; //第1级
                                var second<?=$val['question_id']?> = []; //第2级
                                var third<?=$val['question_id']?> = []; //第3级
                                var selectedIndex<?=$val['question_id']?> = [0, 0, 0]; // 默认选中的地区
                                var checked<?=$val['question_id']?> = [firstIndex, secondIndex, thirdIndex]; // 已选选项


                                creatList(arr<?=$val['question_id']?>, first<?=$val['question_id']?>);

                                // 默认的第1级有子集
                                if (arr<?=$val['question_id']?>[firstIndex].hasOwnProperty("list")) {
                                    creatList(arr<?=$val['question_id']?>[firstIndex].list, second<?=$val['question_id']?>);
                                } else {
                                    second<?=$val['question_id']?> = [{
                                        text: "",
                                        value: 0
                                    }];
                                }

                                // 默认的第2级有子集
                                if (arr<?=$val['question_id']?>[firstIndex].list[secondIndex].hasOwnProperty("list")) {
                                    creatList(arr<?=$val['question_id']?>[firstIndex].list[secondIndex].list, third<?=$val['question_id']?>);
                                } else {
                                    third<?=$val['question_id']?> = [{
                                        text: "",
                                        value: 0
                                    }];
                                }
                                var picker<?=$val['question_id']?> = new Picker({
                                    data: [first<?=$val['question_id']?>, second<?=$val['question_id']?>, third<?=$val['question_id']?>],
                                    selectedIndex: selectedIndex<?=$val['question_id']?>,
                                    title: "请选择",
                                });
                                picker<?=$val['question_id']?>.on("picker.select", function(selectedVal, selectedIndex<?=$val['question_id']?>) {
                                    var text1 = first<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[0]].text;
                                    var text2 = second<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[1]].text;
                                    var text3 = third<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[2]].text;
                                    $("#union3<?=$val['question_id']?>").text(text1 + " " + text2 + " " + text3);

                                    var index = third<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[2]].tabindex;
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(i==index){
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=1;
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].answer_value=0;
                                        }
                                    });

                                    if(third<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[2]].answer_jump_touch=='1'){
                                        var answer_jump_question_id = third<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>[2]].answer_jump_question_id;
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else{
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id','');
                                    }

                                });
                                picker<?=$val['question_id']?>.on("picker.change", function(index, selectedIndex<?=$val['question_id']?>) {
                                    if (index === 0) {
                                        selectedIndex<?=$val['question_id']?> = Object.keys(arr<?=$val['question_id']?>)[selectedIndex<?=$val['question_id']?>];
                                        second<?=$val['question_id']?> = [];
                                        third<?=$val['question_id']?> = [];
                                        checked<?=$val['question_id']?>[0] = selectedIndex<?=$val['question_id']?>;
                                        var firstList = arr<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>];
                                        if (firstList.hasOwnProperty("list")) {
                                            creatList(firstList.list, second<?=$val['question_id']?>);
                                            var secondList = arr<?=$val['question_id']?>[selectedIndex<?=$val['question_id']?>];
                                            if (secondList.hasOwnProperty("list")) {
                                                secondIndex = Object.keys(secondList.list)[0];
                                                creatList(secondList.list[secondIndex].list, third<?=$val['question_id']?>);
                                            } else {
                                                third<?=$val['question_id']?> = [{
                                                    text: "",
                                                    value: 0
                                                }];
                                                checked<?=$val['question_id']?>[2] = 0;
                                            }
                                        } else {
                                            second<?=$val['question_id']?> = [{
                                                text: "",
                                                value: 0
                                            }];
                                            third<?=$val['question_id']?> = [{
                                                text: "",
                                                value: 0
                                            }];
                                            checked<?=$val['question_id']?>[1] = 0;
                                            checked<?=$val['question_id']?>[2] = 0;
                                        }
                                        picker<?=$val['question_id']?>.refillColumn(1, second<?=$val['question_id']?>);
                                        picker<?=$val['question_id']?>.refillColumn(2, third<?=$val['question_id']?>);
                                        picker<?=$val['question_id']?>.scrollColumn(1, 0);
                                        picker<?=$val['question_id']?>.scrollColumn(2, 0);
                                    } else if (index === 1) {
                                        third<?=$val['question_id']?> = [];
                                        checked<?=$val['question_id']?>[1] = selectedIndex<?=$val['question_id']?>;
                                        var first_index = checked<?=$val['question_id']?>[0];

                                        selectedIndex<?=$val['question_id']?> = Object.keys(arr<?=$val['question_id']?>[first_index].list)[selectedIndex<?=$val['question_id']?>];
                                        if (arr<?=$val['question_id']?>[first_index].list[selectedIndex<?=$val['question_id']?>].hasOwnProperty("list")) {
                                            var secondList = arr<?=$val['question_id']?>[first_index].list[selectedIndex<?=$val['question_id']?>];
                                            creatList(secondList.list, third<?=$val['question_id']?>);
                                            picker<?=$val['question_id']?>.refillColumn(2, third<?=$val['question_id']?>);
                                            picker<?=$val['question_id']?>.scrollColumn(2, 0);
                                        } else {
                                            third<?=$val['question_id']?> = [{
                                                text: "",
                                                value: 0
                                            }];
                                            checked<?=$val['question_id']?>[2] = 0;
                                            picker<?=$val['question_id']?>.refillColumn(2, third<?=$val['question_id']?>);
                                            picker<?=$val['question_id']?>.scrollColumn(2, 0);
                                        }
                                    }
                                });

                                $("#union3<?=$val['question_id']?>").click(function() {
                                    picker<?=$val['question_id']?>.show();
                                });
                            </script>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'gridsingle': ?>
                        <!-- 表格单选 -->
                        <div id="<?=$val['question_id']?>" class="gridsingle question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                                $config = explode('_', $title[1]);
                                $msg = $config[0]??'';
                                $min = $config[1]??'';
                                $max = $config[2]??'';
                            ?>
                            <p class="title"><?=$title[0]?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            <div class="table_scroll">
                                <table>
                                    <thead>
                                    <tr>
                                        <th><div></div></th>
                                        <?php $col_num = 0;?>
                                        <?php foreach ($val['list'] as $answer) : ?>
                                            <?php if($answer['variable_id']==$val['list'][0]['variable_id']):?>
                                                <th><?=explode('@@',$answer['answer_label'])[1]?></th>
                                                <?php $col_num++;?>
                                            <?php endif;?>
                                            <script>
                                                $(document).ready(function() {
                                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                        if(item.answer_value){
                                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                        }else{
                                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                        }
                                                    });
                                                    <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                                        // 逻辑跳转
                                                        <?php if($answer['answer_jump_touch']==1):?>
                                                            var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                        <?php endif;?>
                                                    <?php else:?>
                                                        
                                                    <?php endif;?>
                                                });
                                            </script>
                                        <?php endforeach; ?>
                                    </tr>
                                    </thead>
                                    <tbody>
                                        <?php $current_row = 0;?>
                                        <?php for ($i=0;$i<(count($val['list'])/$col_num);$i++) : ?>
                                        <?php $current_index = $current_row*$col_num;?>
                                        <tr>
                                            <td><?=explode('@@',$val['list'][$current_index]['answer_label'])[0]?></td>
                                            <?php for ($j=0;$j<$col_num;$j++) : ?>
                                            <td>
                                                <label>
                                                    <?php if(isset($val['list'][$current_index+$j]['answer_value'])&&$val['list'][$current_index+$j]['answer_value']==1):?>
                                                        <span class="outer" style="margin-right: 5px;border: 5px solid rgb(83, 164, 244);"></span>
                                                        <input tabindex="<?=$current_index+$j?>" id="<?=$val['list'][$current_index+$j]['variable_id']?>" class="gridsingle<?=$val['question_id']?>" name="gridsingle<?=$val['question_id'].'_'.$current_index?>" value="<?=$val['list'][$current_index]['answer_code']?>" type="radio" style="display: none;" checked/>
                                                    <?php else:?>
                                                        <span class="outer" style="margin-right: 5px;"></span>
                                                        <input tabindex="<?=$current_index+$j?>" id="<?=$val['list'][$current_index+$j]['variable_id']?>" class="gridsingle<?=$val['question_id']?>" name="gridsingle<?=$val['question_id'].'_'.$current_index?>" value="<?=$val['list'][$current_index]['answer_code']?>" type="radio" style="display: none;" />
                                                    <?php endif;?>
                                                </label>
                                            </td>
                                            <?php endfor; ?>
                                        </tr>
                                        <script>
                                            $('input[name="gridsingle<?=$val['question_id'].'_'.$current_index?>"]').change(function(){
                                                if ($(this).prop("checked")) {
                                                    $(this).prev().css({
                                                        border: "5px solid #53a4f4",
                                                    });
                                                    $(this).parent().parent().siblings().children("label").children("span").css("border", "1px solid #dee4ec");
                                                }
                                                var check = true;
                                                $(this).closest('tbody').find('tr').each(function(){
                                                    if($(this).find('input[type="radio"]:checked').length==0){
                                                        check = false;
                                                    }
                                                });
                                                if(check){
                                                    $('.gridsingle<?=$val['question_id']?>').each(function(){
                                                        var index = $(this).attr('tabindex');
                                                        if($(this).prop('checked')==true){
                                                            question_list.<?=$val['question_id']?>.list[index].answer_value=1;
                                                        }else{
                                                            question_list.<?=$val['question_id']?>.list[index].answer_value=0;
                                                        }
                                                    });

                                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                                    if('<?=$val['list'][0]['answer_jump_question_id']?>'=='1'){
                                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',"<?=$val['list'][0]['answer_jump_question_id']?>");
                                                    }
                                                }
                                            });
                                        </script>
                                        <?php $current_row++;?>
                                        <?php endfor; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="" onClick="checkgridsingle(this,'<?=$msg?>');">下一题</button>
                            </div>
                            <script>
                                $(function(){
                                    // 逻辑跳转
                                    if("<?=$val['list'][0]['answer_jump_touch']?>"=="1"){
                                        // jump_set(question_id,jump_question_id);
                                        var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'gridmulti': ?>
                        <!-- 表格多选 -->
                        <div id="<?=$val['question_id']?>" class="gridmulti question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                                $config = explode('_', $title[1]);
                                $msg = $config[0]??'';
                                $min = $config[1]??'';
                                $max = $config[2]??'';
                            ?>
                            <p class="title"><?=$title[0]?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            <div class="table_scroll">
                                <table>
                                    <thead>
                                    <tr>
                                        <th><div></div></th>
                                        <?php $col_num = 0;?>
                                        <?php foreach ($val['list'] as $answer) : ?>
                                            <?php $col_id = explode('_',$val['list'][0]['variable_id'])[1];?>
                                            <?php if(explode('_',$answer['variable_id'])[1]==$col_id):?>
                                                <th><?=explode('@@',$answer['answer_label'])[1]?></th>
                                                <?php $col_num++;?>
                                            <?php endif;?>
                                            <script>
                                                $(document).ready(function() {
                                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                        if(item.answer_value){
                                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                        }else{
                                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                        }
                                                    });
                                                    <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                                        // 逻辑跳转
                                                        <?php if($answer['answer_jump_touch']==1):?>
                                                            var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                        <?php endif;?>
                                                    <?php else:?>
                                                        
                                                    <?php endif;?>
                                                });
                                            </script>
                                        <?php endforeach; ?>
                                    </tr>
                                    </thead>
                                    <tbody>
                                        <?php $current_row = 0;?>
                                        <?php for ($i=0;$i<(count($val['list'])/$col_num);$i++) : ?>
                                        <?php $current_index = $current_row*$col_num;?>
                                        <tr>
                                            <td><?=explode('@@',$val['list'][$current_index]['answer_label'])[0]?></td>
                                            <?php for ($j=0;$j<$col_num;$j++) : ?>
                                            <td>
                                                <label>
                                                    <?php if(isset($val['list'][$current_index+$j]['answer_value'])&&$val['list'][$current_index+$j]['answer_value']==0):?>
                                                        <span></span>
                                                        <input tabindex="<?=$current_index+$j?>" id="<?=$val['list'][$current_index+$j]['variable_id']?>" class="gridmulti<?=$val['question_id']?>" name="gridmulti<?=$val['question_id'].'_'.$current_index?>" value="<?=$val['list'][$current_index]['answer_code']?>" type="checkbox" style="display: none;" />
                                                    <?php else:?>
                                                        <span style="background-position: 0px -17px;"></span>
                                                        <input tabindex="<?=$current_index+$j?>" id="<?=$val['list'][$current_index+$j]['variable_id']?>" class="gridmulti<?=$val['question_id']?>" name="gridmulti<?=$val['question_id'].'_'.$current_index?>" value="<?=$val['list'][$current_index]['answer_code']?>" type="checkbox" style="display: none;" checked/>

                                                    <?php endif;?>
                                                </label>
                                            </td>
                                            <?php endfor; ?>
                                        </tr>
                                        <script>
                                            $('input[name="gridmulti<?=$val['question_id'].'_'.$current_index?>"]').change(function(){
                                                // 选中的效果
                                                if ($(this).prop("checked")) {
                                                    $(this).prev().css("background-position", "0 -17px");
                                                } else {
                                                    $(this).prev().css("background-position", "0 0");
                                                }
                                                var check = true;
                                                $(this).closest('tbody').find('tr').each(function(){
                                                    var leng = $(this).find('input[type="checkbox"]:checked').length;
                                                    var min = parseInt('<?=$min?>');
                                                    var max = parseInt('<?=$max?>');
                                                    if(leng<min || leng>max){
                                                        check = false;
                                                    }
                                                });
                                                if(check){
                                                    $('.gridmulti<?=$val['question_id']?>').each(function(){
                                                        var index = $(this).attr('tabindex');
                                                        if($(this).prop('checked')==true){
                                                            question_list.<?=$val['question_id']?>.list[index].answer_value=1;
                                                        }else{
                                                            question_list.<?=$val['question_id']?>.list[index].answer_value=0;
                                                        }
                                                    });

                                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                                    if('<?=$val['list'][0]['answer_jump_question_id']?>'=='1'){
                                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',"<?=$val['list'][0]['answer_jump_question_id']?>");
                                                    }
                                                }
                                            });
                                        </script>
                                        <?php $current_row++;?>
                                        <?php endfor; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="" onClick="checkgridmulti(this,'<?=$msg?>','<?=$min?>','<?=$max?>');">下一题</button>
                            </div>
                            <script>
                                $(function(){
                                    // 逻辑跳转
                                    if("<?=$val['list'][0]['answer_jump_touch']?>"=="1"){
                                        // jump_set(question_id,jump_question_id);
                                        var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'gridopen': ?>
                        <!-- 表格文本 -->
                        <div id="<?=$val['question_id']?>" class="gridtext question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                                $config = explode('_', $title[1]);
                                $msg = $config[0]??'';
                                $min = $config[1]??'';
                                $max = $config[2]??'';
                            ?>
                            <p class="title"><?=$title[0]?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            <div class="table_scroll">
                                <table>
                                    <thead>
                                    <tr>
                                    <th><div></div></th>
                                        <?php $col_num = 0;?>
                                        <?php foreach ($val['list'] as $answer) : ?>
                                            <?php $col_id = explode('_',$val['list'][0]['variable_id'])[1];?>
                                            <?php if(explode('_',$answer['variable_id'])[1]==$col_id):?>
                                                <th><?=explode('@@',$answer['answer_label'])[1]?></th>
                                                <?php $col_num++;?>
                                            <?php endif;?>
                                            <script>
                                                $(document).ready(function() {
                                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                        if(item.answer_value){
                                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                        }else{
                                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                        }
                                                    });
                                                    <?php if(isset($answer['answer_value'])):?>
                                                        // 逻辑跳转
                                                        <?php if($answer['answer_jump_touch']==1):?>
                                                            var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                        <?php endif;?>
                                                    <?php else:?>
                                                        
                                                    <?php endif;?>
                                                });
                                            </script>
                                        <?php endforeach; ?>
                                    </tr>
                                    </thead>
                                    <tbody>
                                        <?php $current_row = 0;?>
                                        <?php for ($i=0;$i<(count($val['list'])/$col_num);$i++) : ?>
                                        <?php $current_index = $current_row*$col_num;?>
                                        <tr>
                                            <td><?=explode('@@',$val['list'][$current_index]['answer_label'])[0]?></td>
                                            <?php for ($j=0;$j<$col_num;$j++) : ?>
                                            <td>
                                                <input tabindex="<?=$current_index+$j?>" id="<?=$val['list'][$current_index+$j]['variable_id']?>" class="gridopen<?=$val['question_id']?>" name="gridopen<?=$val['question_id'].'_'.$current_index?>" type="text" value="<?=$val['list'][$current_index+$j]['answer_value']??''?>"/>
                                            </td>
                                            <?php endfor; ?>
                                        </tr>
                                        <script>
                                            $('input[name="gridopen<?=$val['question_id'].'_'.$current_index?>"]').on('blur input',function(){
                                                var check = true;
                                                $(this).closest('tbody').find('input').each(function(){
                                                    if($(this).val()==''){
                                                        check = false;
                                                    }
                                                });
                                                if(check){
                                                    $('.gridopen<?=$val['question_id']?>').each(function(){
                                                        var index = $(this).attr('tabindex');
                                                        question_list.<?=$val['question_id']?>.list[index].answer_value=$(this).val();
                                                    });
                                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                                    if('<?=$val['list'][0]['answer_jump_question_id']?>'=='1'){
                                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',"<?=$val['list'][0]['answer_jump_question_id']?>");
                                                    }
                                                }
                                            });
                                        </script>
                                        <?php $current_row++;?>
                                        <?php endfor; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="jump_box" style="text-align: center;padding-top:50px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="" onClick="checkgridopen(this,'<?=$msg?>');">下一题</button>
                            </div>
                            <script>
                                $(function(){
                                    // 逻辑跳转
                                    if("<?=$val['list'][0]['answer_jump_touch']?>"=="1"){
                                        // jump_set(question_id,jump_question_id);
                                        var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'email': ?>
                        <!-- 邮箱题 -->
                        <div id="<?=$val['question_id']?>" class="email question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <input tabindex="<?=$index?>" class="email<?=$answer['answer_code']?>" type="text"  data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" value="<?=$answer['answer_value']??''?>"/>
                                <script>
                                    // 邮箱的正则验证
                                    var reg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
                                    $(".email<?=$answer['answer_code']?>").blur(function () {
                                        if (!reg.test($(this).val())) {
                                            $(this).val("");
                                        }else{
                                            var index = $(this).attr('tabindex');
                                            question_list.<?=$val['question_id']?>.list[index].answer_value=$(this).val();

                                            // 逻辑跳转
                                            if($('.email<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                var answer_jump_question_id = $('.email<?=$answer['answer_code']?>').attr('data-jump_id');
                                                $('.email<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            }
                                        }
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'mobile': ?>
                        <!-- 手机号题 -->
                        <div id="<?=$val['question_id']?>" class="telphone question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <input tabindex="<?=$index?>" class="telphone<?=$answer['answer_code']?>" type="text"  data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" value="<?=$answer['answer_value']??''?>"/>
                                <script>
                                    // 手机的正则验证
                                    var regtel = /^1[3456789]\d{9}$/;
                                    $(".telphone<?=$answer['answer_code']?>").blur(function () {
                                        if (!regtel.test($(this).val())) {
                                            $(this).val("");
                                        }else{
                                            var index = $(this).attr('tabindex');
                                            question_list.<?=$val['question_id']?>.list[index].answer_value=$(this).val();
                                            // 逻辑跳转
                                            if($('.telphone<?=$answer['answer_code']?>').attr('data-jump')=="1"){
                                                var answer_jump_question_id = $('.telphone<?=$answer['answer_code']?>').attr('data-jump_id');
                                                $('.telphone<?=$answer['answer_code']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            }
                                        }
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'ranked': ?>
                        <!-- 排序题 -->
                        <div id="<?=$val['question_id']?>" class="ranked question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <ul>
                                <?php foreach ($val['list'] as $index=> $answer) : ?>
                                    <li tabindex="<?=$index?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>">
                                        <?php if(isset($answer['answer_value'])):?>
                                            <span index="<?=$answer['answer_value']??''?>" class="active"><?=$answer['answer_value']??''?></span>
                                        <?php else:?>
                                            <span index=""></span>
                                        <?php endif;?>
                                        <?=$answer['answer_label']?>
                                    </li>
                                    <script>
                                        $(document).ready(function() {
                                            question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                if(item.answer_value){
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                }else{
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                }
                                            });
                                            <?php if(isset($answer['answer_value'])):?>
                                                // 逻辑跳转
                                                <?php if($answer['answer_jump_touch']==1):?>
                                                    var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                <?php endif;?>
                                            <?php else:?>
                                                
                                            <?php endif;?>
                                        });
                                    </script>
                                <?php endforeach; ?>
                            </ul>
                            <script>
                                // 排序题
                                $(".question<?=$val['question_id']?> li").mouseenter(function () {
                                    $(this).css({ background: "#cfe8f9" }).siblings().css({ background: "#f5f5f5" });
                                });

                                $(".question<?=$val['question_id']?> li").click(function () {
                                    var i = $(".question<?=$val['question_id']?> li .active").length + 1;
                                    if (!$(this).children("span").hasClass("active")) {
                                        $(this).children("span").text(i);
                                        $(this).children("span").attr("index", i);
                                        $(this).children("span").addClass("active");
                                    } else {
                                        var click_index;
                                        $(this).children("span").text("");
                                        click_index = $(this).children("span").attr("index");
                                        $(this).children("span").attr("index", "");
                                        $(this).children("span").removeClass("active");
                                        $(".question<?=$val['question_id']?> li .active").each(function () {
                                            if ($(this).text() > click_index) {
                                            $(this).text($(this).text() - 1);
                                            $(this).attr("index", $(this).text() - 1);
                                            }
                                        });
                                    }
                                    var jump = true;
                                    $(".question<?=$val['question_id']?> li").each(function () {
                                        if (!$(this).children("span").hasClass("active")) {
                                            jump = false;
                                        }
                                        var index = $(this).attr('tabindex');
                                        var result = $(this).children("span").attr('index');
                                        question_list.<?=$val['question_id']?>.list[index].answer_value=result;
                                    });

                                    if(jump&&$(".question<?=$val['question_id']?> li:eq(0)").attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(".question<?=$val['question_id']?> li:eq(0)").attr('data-jump_id');
                                        $(".question<?=$val['question_id']?>").find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }else{
                                        $(".question<?=$val['question_id']?>").find('.next_btn').attr('data-question_id',"");
                                    }
                                });
                            </script>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'ratio': ?>
                        <!-- 占比打分题 -->
                        <div id="<?=$val['question_id']?>" class="ratio scoring question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label']);
                                $config = explode('_', $title[1]);
                                $msg = $config[0]??'';
                                $min = $config[1]??'';
                                $max = $config[2]??'';
                            ?>
                            <p class="title"><?=$title[0]?><font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <ul>
                                <?php foreach ($val['list'] as $index => $answer) : ?>
                                    <li>
                                        <?php
                                            $ans_title = explode('@', $answer['answer_label']);
                                            $ans_arr = explode('_', $ans_title[1]);
                                        ?>
                                        <span><?=$ans_title[0]?></span>
                                        <input tabindex="<?=$index?>" class="ratio<?=$val['question_id']?>" type="text" onKeyUp="value=value.replace(/[^\d]/g,'')" style="width: 300px; height: 32px; color: rgb(102, 102, 102);" data-msg="<?=$ans_arr[0]?>" data-min="<?=$ans_arr[1]?>" data-max="<?=$ans_arr[2]?>" data-jump="<?=$answer['answer_jump_touch']?>" data-jump_id="<?=$answer['answer_jump_question_id']?>" value="<?=$answer['answer_value']??''?>"/>
                                        <span class="red"></span>
                                    </li>
                                    <script>
                                        $(document).ready(function() {
                                            question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                if(item.answer_value){
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                }else{
                                                    question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                }
                                            });
                                            <?php if(isset($answer['answer_value'])):?>
                                                // 逻辑跳转
                                                <?php if($answer['answer_jump_touch']==1):?>
                                                    var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                <?php endif;?>
                                            <?php else:?>
                                                
                                            <?php endif;?>
                                        });
                                    </script>
                                <?php endforeach; ?>
                            </ul>
                            <script>
                                $('.ratio<?=$val['question_id']?>').on('blur input', function() {
                                    var cent = parseInt($(this).val());
                                    var min = parseInt($(this).data('min'));
                                    var max = parseInt($(this).data('max'));
                                    if (min > 0 && cent < min || max > 0 && cent > max) {
                                        $(this).next().html($(this).data('msg'));
                                    } else {
                                        $(this).next().html('');
                                    }

                                    var score = 0;
                                    var total = parseInt('<?=$config[1]?>');
                                    $('.ratio<?=$val['question_id']?>').each(function() {
                                        if ($(this).val()) {
                                            score += parseInt($(this).val());
                                        }
                                        var index = $(this).attr('tabindex');
                                        question_list.<?=$val['question_id']?>.list[index].answer_value=$(this).val();
                                    });

                                    if (score != total) {
                                        $('.msgbox<?=$val['question_id']?>').html('*<?=$config[0]?>');
                                    } else {
                                        $('.msgbox<?=$val['question_id']?>').html('*');
                                    }

                                    // 逻辑跳转
                                    if($(this).attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'interval': ?>
                        <!-- 区间题 -->
                        <div id="<?=$val['question_id']?>" class="interval question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <?php
                                $title = explode('@', $val['question_label'])[0];
                                $t_msg = explode('@', $val['question_label'])[1];
                            ?>
                            <p class="title"><?=$title?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <?php
                                $title1 = explode('@', $val['list'][0]['answer_label']??'')[0];
                                $config1 = explode('@', $val['list'][0]['answer_label']??'')[1];
                                $msg1 = explode('_', $config1)[0]??'';
                                $min = explode('_', $config1)[1]??'';
                                $title2 = explode('@', $val['list'][1]['answer_label']??'')[0];
                                $config2 = explode('@', $val['list'][1]['answer_label']??'')[1];
                                $msg2 = explode('_', $config2)[0]??'';
                                $max = explode('_', $config2)[1]??'';
                            ?>
                            <input tabindex="0" class="interval<?=$val['question_id']?> width_100" type="text" style="display: inline-block;" class="width_100" onkeyup="value=value.replace(/[^\d]/g,'')" placeholder="<?=$title1?>" data-jump="<?=$val['list'][0]['answer_jump_touch']?>" data-jump_id="<?=$val['list'][0]['answer_jump_question_id']?>" value="<?=$val['list'][0]['answer_value']??''?>"/>
                            -
                            <input tabindex="1" class="interval<?=$val['question_id']?> width_100" type="text" style="display: inline-block;" class="width_100" onkeyup="value=value.replace(/[^\d]/g,'')" placeholder="<?=$title2?>" data-jump="<?=$val['list'][1]['answer_jump_touch']?>" data-jump_id="<?=$val['list'][1]['answer_jump_question_id']?>" value="<?=$val['list'][1]['answer_value']??''?>"/>
                            <script>
                                $('.interval<?=$val['question_id']?>').on('blur input', function() {
                                    $('.msgbox<?=$val['question_id']?>').html('*');
                                    var cent1 = parseInt($('.interval<?=$val['question_id']?>:eq(0)').val());
                                    var cent2 = parseInt($('.interval<?=$val['question_id']?>:eq(1)').val());
                                    var min = parseInt('<?=$min?>');
                                    var max = parseInt('<?=$max?>');
                                    if (min > 0 && (cent1 < min||cent2 < min)) {
                                        $('.msgbox<?=$val['question_id']?>').html('*<?=$msg1?>');
                                        return false;
                                    }
                                    if (max > 0 && (cent1 > max || cent2 > max)) {
                                        $('.msgbox<?=$val['question_id']?>').html('*<?=$msg2?>');
                                        return false;
                                    }
                                    if(cent1 > cent2){
                                        $('.msgbox<?=$val['question_id']?>').html('*<?=$t_msg?>');
                                        return false;
                                    }

                                    $('.interval<?=$val['question_id']?>').each(function(){
                                        var index = $(this).attr('tabindex');
                                        question_list.<?=$val['question_id']?>.list[index].answer_value=$(this).val();
                                    });

                                    // 逻辑跳转
                                    if($(this).attr('data-jump')=="1"){
                                        var answer_jump_question_id = $(this).attr('data-jump_id');
                                        $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                                
                                $(document).ready(function() {
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(item.answer_value){
                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                        }
                                    });
                                    <?php if(isset($val['list'][0]['answer_value'])):?>
                                        // 逻辑跳转
                                        <?php if($val['list'][0]['answer_jump_touch']==1):?>
                                            var answer_jump_question_id = '<?=$val['list'][0]['answer_jump_question_id']?>';
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        <?php endif;?>
                                    <?php else:?>
                                        
                                    <?php endif;?>
                                });
                            </script>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'record': ?>
                        <!-- 录音题 -->
                        <div id="<?=$val['question_id']?>" class="record question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font></p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                                <?php
                                    $title = explode('@', $answer['answer_label'])[0]??'';
                                    $config = explode('@', $answer['answer_label'])[1]??'';
                                    $msg = explode('_', $config)[0]??'';
                                    $min = explode('_', $config)[1]??'';
                                    $max = explode('_', $config)[2]??'';
                                ?>
                                <span style="display: block;margin-bottom:10px;margin-right:10px;font-size:14px"><?=$title?></span><span style="color: red;"></span>
                                <?php if(isset($answer['answer_value'])):?>
                                    <div style="width:100%;height:65px;border: 1px solid #dee4ec;border-radius: 50px;background: #f1f3fa;overflow: hidden;">
                                        <div class="record_box record_box<?=$answer['variable_id']?>">
                                            <audio controls="" src="<?=$answer['answer_link']?>"></audio>
                                        </div>
                                        <div class="btn_box">
                                            <button class="record_open_btn" onClick="recOpen(this,rec<?=$answer['variable_id']?>)">重录</button>
                                            <button class="record_stop_btn" onClick="recStop(this,rec<?=$answer['variable_id']?>)">停止</button>
                                        </div>
                                    </div>
                                    <div>
                                        <textarea tabindex="<?=$index?>" que="<?=$val['question_id']?>" class="height_80 record<?=$val['question_id']?>" style="margin-bottom: 15px;width: 100%;color: rgb(102, 102, 102);padding-top: 5px;border-color: rgb(222, 228, 236);background-color: #c0e0e821;"><?=$answer['answer_value']??''?></textarea>
                                    </div>
                                <?php else:?>
                                    <div style="width:100%;height:65px;border: 1px solid #dee4ec;border-radius: 50px;background: #f1f3fa;overflow: hidden;">
                                        <div class="record_box record_box<?=$answer['variable_id']?>">
                                            请点击右侧的录音按钮开始录音
                                        </div>
                                        <div class="btn_box">
                                            <button class="record_open_btn" onClick="recOpen(this,rec<?=$answer['variable_id']?>)">录音</button>
                                            <button class="record_stop_btn" onClick="recStop(this,rec<?=$answer['variable_id']?>)">停止</button>
                                        </div>
                                    </div>
                                    <div style="display:none;">
                                        <textarea tabindex="<?=$index?>" que="<?=$val['question_id']?>" class="height_80 record<?=$val['question_id']?>" style="margin-bottom: 15px;width: 100%;color: rgb(102, 102, 102);padding-top: 5px;border-color: rgb(222, 228, 236);background-color: #c0e0e821;"></textarea>
                                    </div>
                                <?php endif;?>
                                <script>
                                    var rec<?=$answer['variable_id']?> = Recorder({
                                        type: "wav",
                                        sampleRate: 16000,
                                        bitRate: 16
                                    });
                                    $(document).ready(function() {
                                        question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                            if(item.answer_value){
                                                question_list.<?=$val['question_id']?>.list[i].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                            }
                                        });
                                        <?php if(isset($answer['answer_value'])):?>
                                            // 逻辑跳转
                                            <?php if($answer['answer_jump_touch']==1):?>
                                                var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            <?php endif;?>
                                        <?php else:?>
                                            
                                        <?php endif;?>
                                    });
                                </script>
                            <?php endforeach; ?>
                            <script>
                                $('.record<?=$val['question_id']?>').on('input blur',function(){
                                    var que_num = $(this).attr('que');
                                    var index = $(this).attr('tabindex');
                                    question_list[que_num].list[index].answer_value=$(this).val();
                                });
                                $(function(){
                                    // 逻辑跳转
                                    if("<?=$val['list'][0]['answer_jump_touch']?>"=="1"){
                                        // jump_set(question_id,jump_question_id);
                                        var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                            <div class="jump_box" style="text-align: center;padding-bottom:80px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'logo': ?>
                        <!-- 设置问卷logo -->
                        <script>
                            // 默认健康通logo 
                            var url = '/theme/survey/img/logoh.png';
                            var logo_name = '<?=$val["question_label"]?>';
                            <?php if (count($val['list']) > 0) : ?>
                                url = '<?=$val['list'][0]["answer_label"]?>';
                            <?php endif; ?>
                            $('#logo_img').attr('src', url);
                            $('#logo_name').html(logo_name);
                        </script>
                        <?php break; ?>
                    <?php
                    case 'buttonlabel': ?>
                        <!-- 设置问卷按钮名称 -->
                        <?php
                            $button_title = explode('_', $val['question_label']);
                        ?>
                        <script>
                            $(document).ready(function(){
                                $('.prev_btn').html('<?=$button_title[0]?>');
                                $('.next_btn').html('<?=$button_title[1]?>');
                            });
                        </script>
                        <?php break; ?>
                    <?php
                    case 'comment': ?>
                        <!-- 点赞评论题型 -->
                        <div id="<?=$val['question_id']?>" class="comment question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title" style="font-weight: 600;"></p>
                            <div class="comment_content" style="box-sizing:border-box">
                                <?=$val['question_label']?>
                            </div>
                            <div class="comment_num">
                                <?php
                                    $pr_all = $this->db->from('qk_dr_comment')->where(['pid'=>$info['id'],'question_id'=>$val['question_id'],'shumb_up'=>2])->count_all_results();
                                    $is_shumb = $this->db->from('qk_dr_comment')->where(['pid'=>$info['id'],'question_id'=>$val['question_id'],'r'=>$r,'s'=>$s])->get();
                                    $is_shumb = $is_shumb ? $is_shumb->row_array():[];
                                ?>
                                <?php if(isset($is_shumb['shumb_up'])&&$is_shumb['shumb_up']==2):?>
                                    <span class="img_box active">
                                        <img src="/theme/survey/img/gooded.png" alt="" />
                                        <font><?=$pr_all?></font>
                                    </span>
                                <?php else:?>
                                    <span class="img_box">
                                        <img src="/theme/survey/img/good.png" alt="" />
                                        <font><?=$pr_all?></font>
                                    </span>
                                <?php endif;?>
                                <?php
                                    $com_all = $this->db->from('qk_dr_comment')->where(['pid'=>$info['id'],'question_id'=>$val['question_id'],'message !='=>''])->count_all_results();
                                ?>
                                <span class="img_box"><img src="/theme/survey/img/comment.png" alt="" />
                                    <font><?=$com_all?></font>
                                </span>
                                <div class="comment_box" style="display: none;">
                                <p style="text-align: center;">
                                    <input type="text" />
                                    <button>发送</button>
                                </p>
                                <?php
                                    $com_list = $this->db->from('qk_dr_comment')->where(['pid'=>$info['id'],'question_id'=>$val['question_id'],'message !='=>''])->order_by('id desc')->get();
                                    $com_list = $com_list ? $com_list->result_array():[];
                                ?>
                                <div class="comment_detail">
                                    <?php foreach($com_list as $c=>$k):?>
                                        <?php if($k['r']==$r&&$k['s']==$s):?>
                                            <p data-id="<?=$k['id']?>"><span style="color:red;cursor:pointer;margin-right:10px;">X</span><?=$k['message']?></p>
                                        <?php else:?>
                                            <p"><?=$k['message']?></p>
                                        <?php endif;?>
                                    <?php endforeach;?>
                                </div>
                                </div>
                            </div>
                            <script>
                                $(".comment .comment_num span:first").click(function () {
                                    var _this = $(this);
                                    if (_this.hasClass("active")) {
                                        return false;
                                    }
                                    _this.addClass("active");
                                    $.ajax({
                                        url: "/sv/dr/shumb_up",
                                        type: "post",
                                        data: {
                                            r:getQueryVariable('r'),
                                            s:getQueryVariable('s'),
                                            pid:'<?=$info['id']?>',
                                            question_id:'<?=$val['question_id']?>',
                                            shumb_up:2,
                                        },
                                        async: true,
                                        dataType: 'json',
                                        success: function (res) {
                                            _this.children("img").attr("src", "/theme/survey/img/gooded.png");
                                            _this.children("font").text(+_this.text() + 1);
                                        },
                                        error: function (e) {
                                            console.log(e);
                                        }
                                    });
                                });
                                $(".comment .comment_num span:nth-child(2)").click(function (e) {
                                    e.stopPropagation();
                                    $(this).toggleClass("active");
                                    $(".comment_box").toggle();
                                    if ($(this).hasClass("active")) {
                                        $(this).children("img").attr("src", "/theme/survey/img/commented.png");
                                    } else {
                                        $(this).children("img").attr("src", "/theme/survey/img/comment.png");
                                    }
                                });
                                // 点击其他地方隐藏
                                $(".comment_box").click(function (e) {
                                    e.stopPropagation();
                                });
                                $(document).click(function () {
                                    $(".comment_box").hide();
                                })
                                
                                // 回车发送
                                $(".comment_box input").keyup(function(e){
                                    if(e.keyCode==13){
                                        send_msg()
                                    }
                                })
                                // 点击按钮发送
                                $(".comment_box button").click(function () {
                                    send_msg()
                                });
                                // 发送信息
                                function send_msg(){
                                    var text = $(".comment_box input").val();
                                    if (text.trim()== "") {
                                        return false;
                                    }
                                    $.ajax({
                                        url: "/sv/dr/add_comment",
                                        type: "post",
                                        data: {
                                            pid:'<?=$info['id']?>',
                                            question_id:'<?=$val['question_id']?>',
                                            r:getQueryVariable('r'),
                                            s:getQueryVariable('s'),
                                            message:text,
                                        },
                                        async: true,
                                        dataType: 'json',
                                        success: function (res) {
                                            $(".comment_box .comment_detail").prepend('<p data-id="'+res.result+'"><span style="color:red;cursor:pointer;margin-right:10px;">X</span>' + text +'</p>');
                                            $(".comment_box input").val("");
                                            $(".comment .comment_num span:nth-child(2)")
                                            .children("font")
                                            .text(+$(".comment .comment_num span:nth-child(2)").text() + 1);
                                        },
                                        error: function (e) {
                                            console.log(e);
                                        }
                                    });
                                }

                                $(".comment_box").on("click", "span", function () {
                                    var _this = $(this);
                                    var id = _this.parent().attr('data-id');
                                    if(!id){
                                        return false;
                                    }
                                    $.ajax({
                                        url: "/sv/dr/del_comment",
                                        type: "post",
                                        data: {
                                            id:id,
                                        },
                                        async: true,
                                        dataType: 'json',
                                        success: function (res) {
                                            _this.parent().remove();
                                            $(".comment .comment_num span:nth-child(2)")
                                            .children("font")
                                            .text(+$(".comment .comment_num span:nth-child(2)").text() - 1);
                                        },
                                        error: function (e) {
                                            console.log(e);
                                        }
                                    });
                                });
                                $(document).ready(function(){
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(item.answer_value){
                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                        }
                                    });
                                    // 逻辑跳转
                                    if("<?=$val['list'][0]['answer_jump_touch']?>"=="1"){
                                        // jump_set(question_id,jump_question_id);
                                        var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });

                                // 
                            </script>
                            <div class="jump_box" style="text-align: center;padding-bottom:80px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'selectinput':?>
                        <!-- 多选输入 -->
                        <div id="<?=$val['question_id']?>" class="selectinput multi question<?=$val['question_id']?> question_box" <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font></p>
                            <?php foreach ($val['list'] as $index => $answer) : ?>
                            <label class="label">
                                <span></span> <input tabindex="<?=$index?>" class="noshow selectinput<?=$val['question_id'].'_'.$answer['answer_code']?>" name="<?=$val['question_id']?>" type="checkbox" /><?=str_replace("@selectinput@",'<input tabindex="'.$index.'" name="'.$val['question_id'].'_'.$answer['answer_code'].'_other" class="selectinput'.$val['question_id'].'_other" type="text" value="'.($answer['answer_input']??'').'">',$answer['answer_label'])?>
                            </label>
                            <script>
                                $(document).ready(function(){
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(item.answer_value){
                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                        }
                                    });
                                    <?php if(isset($answer['answer_value'])&&$answer['answer_value']==1):?>
                                        $('.selectinput<?=$val['question_id'].'_'.$answer['answer_code']?>').prev().css("background-position", "0 -17px");

                                        $('.selectinput<?=$val['question_id'].'_'.$answer['answer_code']?>').prop('checked',true);
                                        $('.selectinput<?=$val['question_id'].'_'.$answer['answer_code']?>').attr('value',1);

                                        // 逻辑跳转
                                        <?php if($answer['answer_jump_touch']==1):?>
                                            var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        <?php endif;?>
                                    <?php else:?>
                                    
                                    <?php endif;?>
                                });
                            </script>
                            <?php endforeach; ?>
                            <script>
                                question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                    if(item.answer_value==1){
                                        question_list.<?=$val['question_id']?>.list[i].old_answer = 1;
                                    }
                                });
                                $('input[name="<?=$val['question_id']?>"]').change(function(){
                                    $('input[name="<?=$val['question_id']?>"]').each(function(){
                                        var index = $(this).attr('tabindex');
                                        if($(this).prop('checked')==true){
                                            $(this).attr('value','1');
                                            if(question_list.<?=$val['question_id']?>.list[index].old_answer == 1){
                                                question_list.<?=$val['question_id']?>.list[index].count_size="";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[index].count_size="+1";
                                            }
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 1;
                                        }else{
                                            $(this).attr('value','0');
                                            if(question_list.<?=$val['question_id']?>.list[index].old_answer == 1){
                                                question_list.<?=$val['question_id']?>.list[index].count_size="-1";
                                            }else{
                                                question_list.<?=$val['question_id']?>.list[index].count_size="";
                                            }
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = 0;
                                        }
                                        if($('input[name="<?=$val['question_id']?>"]:checked').length==0){
                                            $(this).closest('.question_box').find('.next_btn').attr('data-question_id','');
                                        }else{
                                            var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                            $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                            $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                        }
                                    });
                                });
                                // 多选输入题
                                $(".selectinput<?=$val['question_id']?>_other").keyup(function () {
                                    var w_str = $(this).val();
                                    if (w_str) {
                                     var result = w_str.match(/[A-Za-z0-9]/g);
                                     var w_length=0;
                                      if (result) {
                                        w_length =result.length;
                                        }
                                        var l_length = $(this).val().length - w_length;
                                        var width = l_length * 15 + w_length * 8;
                                        $(this).width(20 + width + "px");
                                    }
                                    var text = $(this).val().length;
                                    var index = $(this).attr('tabindex');
                                    question_list.<?=$val['question_id']?>.list[index].answer_input = $(this).val();
                                });

                                $(document).ready(function(){
                                    $(".selectinput label input[type='text']").width("50px");
                                    $(".selectinput label input[type='text']").click(function () {
                                        if (!$(this).prev().prop("checked")) {
                                             $(this).prev().trigger("click");
                                            }
                                            });

                                    // 逻辑跳转
                                    if("<?=$val['list'][0]['answer_jump_touch']?>"=="1"){
                                        // jump_set(question_id,jump_question_id);
                                        var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                                
                            </script>
                            <div class="jump_box" style="text-align: center;padding-bottom:80px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                        </div>
                        <?php break; ?>
                    <?php
                    case 'certificate':?>
                        <!-- 授权书 -->
                        <div id="<?=$val['question_id']?>" class="signname certificate question<?=$val['question_id']?> question_box"  <?php if($val['question_hide']==1):?>style="display:none;"<?php endif;?>>
                            <div class="certificate_box" style="border: 1px solid #ccc; padding:20px;">
                                <p class="title" style="text-align: center; padding-top: 10px; font-size: 18px;"><?=$val['question_label']?></p>
                                <?php foreach ($val['list'] as $index => $answer) : ?>
                                    <?php
                                        $title = explode('@', $answer['answer_label']??'')[0]??'';
                                        $config = explode('@', $answer['answer_label']??'')[1]??'';
                                        $msg = explode('_', $config)[0]??'';
                                        $min = explode('_', $config)[1]??'';
                                        $max = explode('_', $config)[2]??'';
                                    ?>
                                    <p class="selectinput" style="text-align: right; margin-bottom: 30px; <?php if($index==0):?>margin-top: 30px;<?php endif;?>">
                                        <span style="display: inline-block; width: 80px; vertical-align: sub;"><?=$title?>:</span>
                                        <input tabindex="<?=$index?>" class="certificateInput<?=$val['question_id']?>" name="certificate<?=$val['question_id'].'_'.$answer['answer_code']?>" type="text" data-msg="<?=$msg ?>" data-min="<?=$min ?>" data-max="<?=$max ?>" data-title="<?=$title?>" value="<?=$answer['answer_value']?>"/>
                                    </p>
                                    <script>
                                        // 填写信息
                                        $('input[name="certificate<?=$val['question_id'].'_'.$answer['answer_code']?>"]').on('blur input',function(){
                                            var text = $(this).val().length;
                                            var min = parseInt($(this).data('min'));
                                            var max = parseInt($(this).data('max'));
                                            var msg = $(this).data('msg');
                                            var title = $(this).data('title');
                                            if(text<min || text>max){
                                                $('.msgbox<?=$val['question_id']?>').html(msg);
                                            }else{
                                                $('.msgbox<?=$val['question_id']?>').html('');
                                            }
                                            var index = $(this).attr('tabindex');
                                            question_list.<?=$val['question_id']?>.list[index].answer_value = $(this).val();
                                            // 逻辑跳转
                                            if($(this).attr('data-jump')=="1"){
                                                var answer_jump_question_id = $(this).attr('data-jump_id');
                                                $(this).closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                            }
                                        });
                                    </script>
                                <?php endforeach; ?>

                                <p class="selectinput sign_date" style="text-align: right; margin-bottom: 30px;">
                                    <span style="display: inline-block; width: 80px; vertical-align: sub;"
                                    >授权日期:</span>
                                    <input class="sign_y<?=$val['question_id']?>" style="font-weight: 600;font-style:italic;" type="text" value="<?=$val['sign_y']??date('Y')?>" readonly/>
                                    <span>年</span>
                                    <input class="sign_m<?=$val['question_id']?>" type="text" style="font-weight: 600;font-style:italic;" value="<?=$val['sign_m']??date('m')?>" readonly/>
                                    <span>月</span>
                                    <input class="sign_d<?=$val['question_id']?>" type="text" style="font-weight: 600;font-style:italic;" value="<?=$val['sign_d']??date('d')?>" readonly/>
                                    <span>日</span>
                                </p>

                                <p class="tips  msgbox msgbox<?=$val['question_id']?>" style="text-align: center;color: red;font-weight: 600;height:25px;line-height:25px;"></p>

                                <div style="padding: 0 20px 1px; background: darkgrey;">
                                    <div class="button" style="padding: 10px 0 0;height: 20px;box-sizing: border-box;">
                                    <span style="float: left; font-size: 18px; font-weight: 600;">请在方框内手写签名</span>
                                    <button class="clearSign<?=$answer['answer_code']?>" style="background-color: #5c5b5b; float: right;">重置</button>
                                    <!-- <button class="importImg" style="background-color: #ccc;">获取</button> -->
                                    </div>

                                    <?php if(isset($val['sign_value'])):?>
                                        <div tabindex="<?=$index?>" class="certificateContent<?=$val['question_id']?>" style="border: 2px dotted black;background: #f5f5f5;overflow: hidden;display:none;" data-jump="<?=$val['list'][0]['answer_jump_touch']?>" data-jump_id="<?=$val['list'][0]['answer_jump_question_id']?>"></div>
                                        <img width="100%" class="view_certificate<?=$val['question_id']?>" style="border: 1px solid #ccc;border-top:none;background:#f5f5f5;overflow: hidden;box-sizing:border-box;" src="<?=$val['sign_value']??''?>">
                                        <script>
                                            $(document).ready(function() {
                                                question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                                    if(item.answer_value){
                                                        question_list.<?=$val['question_id']?>.list[i].count_size="";
                                                    }else{
                                                        question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                                    }
                                                });
                                                // 逻辑跳转
                                                <?php if($answer['answer_jump_touch']==1):?>
                                                    var answer_jump_question_id = '<?=$answer['answer_jump_question_id']?>';
                                                    $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                                <?php endif;?>
                                            });
                                        </script>
                                    <?php else:?>
                                        <div tabindex="<?=$index?>" class="certificateContent<?=$val['question_id']?>" style="border: 2px dotted black;background: #f5f5f5; overflow: hidden;" data-jump="<?=$val['list'][0]['answer_jump_touch']?>" data-jump_id="<?=$val['list'][0]['answer_jump_question_id']?>"></div>
                                        <img style="border: 1px solid #ccc;border-top:none;display:none;" src="">
                                    <?php endif;?>
                                    <input type="hidden" name="certificate<?=$val['question_id']?>" value="">

                                </div>

                            </div>
                            <div class="jump_box" style="text-align: center;padding:30px;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn certificate_submit" data-question_id="" onClick="up_certificate('certificate<?=$val['question_id']?>');">下一题</button>
                            </div>
                        </div>

                        <script>
                            // 授权书
                            $(".certificateContent<?=$val['question_id']?>").jSignature({
                                height: "260",
                                "background-color": "#f5f5f5",
                                color: "#666",
                            });

                            //重置画布
                            $(".clearSign<?=$answer['answer_code']?>").click(function () {
                                // $(this).parent().next().jSignature("clear");
                                $(".certificateContent<?=$val['question_id']?>").show().jSignature("clear");
                                $('.view_certificate<?=$val['question_id']?>').hide();
                                $('input[name="certificate<?=$val['question_id']?>"]').val('');
                            });
                            $(".certificateContent<?=$val['question_id']?>").bind('change', function(e){
                                var base64 = $(this).jSignature("getData");

                                var base64 = $(this).jSignature("getData", "image");
                                if(!base64){
                                    return false;
                                }

                                var src = "data:" + base64[0] + "," + base64[1];
                                var index = $(this).attr('tabindex');
                                $('input[name="certificate<?=$val['question_id']?>"]').val(base64[1]);

                                // 逻辑跳转
                                if($('.certificateContent<?=$val['question_id']?>').attr('data-jump')=="1"){
                                    var answer_jump_question_id = $('.certificateContent<?=$val['question_id']?>').attr('data-jump_id');
                                    $('.certificateContent<?=$val['question_id']?>').closest('.question_box').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                    $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                }
                            });

                            $(document).ready(function() {
                                question_list.<?=$val['question_id']?>.sign_y = $(".sign_y<?=$val['question_id']?>").val();
                                question_list.<?=$val['question_id']?>.sign_m = $(".sign_m<?=$val['question_id']?>").val();
                                question_list.<?=$val['question_id']?>.sign_d = $(".sign_d<?=$val['question_id']?>").val();
                                <?php if(isset($val['sign_value'])):?>
                                    // 逻辑跳转
                                    <?php if($val['list'][0]['answer_jump_touch']==1):?>
                                        var answer_jump_question_id = '<?=$val['list'][0]['answer_jump_question_id']?>';
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    <?php endif;?>
                                <?php else:?>
                                    
                                <?php endif;?>
                            });
                        </script>
                        <?php break; ?>
                    <?php
                    default: ?>
                        <!-- 未知题型设置成隐藏 -->
                        <div class=" question<?=$val['question_id']?> question_box" style="display:none;">
                            <p class="title"><?=$val['question_label']?> <font class="msgbox msgbox<?=$val['question_id']?>" color="red">*</font>
                            </p>
                            <input tabindex="0" id="<?=$val['question_id']?>" name="<?=$val['question_id']?>" value="" type="text" data-jump="<?=$val['list'][0]['answer_jump_touch']?>" data-jump_id="<?=$val['list'][0]['answer_jump_question_id']?>"/>
                            <div class="jump_box" style="text-align: center;">
                                <button class="prev_btn" data-question_id="">上一题</button>
                                <button class="next_btn" data-question_id="">下一题</button>
                            </div>
                            <script>
                                $(document).ready(function(){
                                    question_list.<?=$val['question_id']?>.list.forEach(function(item,i){
                                        if(item.answer_value){
                                            question_list.<?=$val['question_id']?>.list[i].count_size="";
                                        }else{
                                            question_list.<?=$val['question_id']?>.list[i].count_size="+1";
                                        }
                                    });
                                    // 逻辑跳转
                                    if("<?=$val['list'][0]['answer_jump_touch']?>"=="1"){
                                        // jump_set(question_id,jump_question_id);
                                        var answer_jump_question_id = "<?=$val['list'][0]['answer_jump_question_id']?>";
                                        $('.question<?=$val['question_id']?>').find('.next_btn').attr('data-question_id',answer_jump_question_id);
                                        $('.question'+answer_jump_question_id).find('.prev_btn').attr('data-question_id','<?=$val['question_id']?>');
                                    }
                                });
                            </script>
                        </div>
                        <?php break; ?>
                <?php endswitch; ?>
            <?php endforeach; ?>

        </div>
        <!-- <div class="submit">
            <button id="smt_btn">提交</button>
        </div> -->

    </div>
    <div class="footer">
        <ul>
            <li style="text-align: center;">
                Copyright © 2004-<?= date('Y') ?> 健康通(北京)网络科技有限公司.
            </li>
            <li style="text-align: right;">京ICP备13039326号-8</li>
        </ul>
    </div>
    <script>
        // 答卷人ID
        var respid = getQueryVariable('r');
        if(respid){
            question_list.responseid.list[0].answer_value=respid;
            question_list.respid.list[0].answer_value=respid;
        }
        var uid = getQueryVariable('s');
        if(uid){
            question_list.uid.list[0].answer_value=uid;
        }

        var username = '<?=$survey['name']??''?>';
        question_list.username.list[0].answer_value=username;

        //浏览器的正式名称
        question_list.browsertype.list[0].answer_value='<?=$browser?>';
        //浏览器的版本号
        question_list.browserversion.list[0].answer_value='<?=$browser_version?>';
        // 设备型号
        var agent = navigator.userAgent.toLowerCase();
        if (/ipad|ipod/.test(agent) && /mobile/.test(agent)) {
            // 平板类型
            question_list.DeviceType.list[2].answer_value='<?=$MobileBrand?>';
        } else if (/mobile/.test(agent)) {
            // 触屏类型
            question_list.DeviceType.list[1].answer_value='<?=$MobileBrand?>';
        } else if (/windows|mac|linux/.test(agent)) {
            // 桌面类型
            question_list.DeviceType.list[0].answer_value='<?=$MobileBrand?>';
        } else {
            // 其他
            question_list.DeviceType.list[3].answer_value='<?=$MobileBrand?>';
        }

        // IP地址
        question_list.ip.list[0].answer_value='<?=$ip?>';
        // 第一次进入问卷时间
        if(!question_list.interview_start.list[0].answer_value){
            question_list.interview_start.list[0].answer_value='<?=date('Y-m-d')?>';
            question_list.interview_start.list[1].answer_value='<?=date('H:i:s')?>';
        }else{
            // $('#interview_start').text(question_list.interview_start.list[0].answer_value);
            // $('#interview_start_time').text(question_list.interview_start.list[1].answer_value);
        }

        if(question_list.Timestamp1.list[0].answer_value==''){
            // 本次进入时间戳
            // $('input[name="Timestamp1"]').val('<?=date('Y-m-d H:i:s')?>');
            question_list.Timestamp1.list[0].answer_value='<?=date('Y-m-d H:i:s')?>';
        }else{
            // $('input[name="Timestamp1"]').val(question_list.Timestamp1.list[0].answer_value);
        }

        // 跳转链接
        if($('.opentexturl_screened').length>0){
            var userId = cookie_get('uid');
            var param = cookie_get('param');
            var r = getQueryVariable('r');
            var s = getQueryVariable('s');
            question_list.url_screened.list[0].answer_value=$('.opentexturl_screened').data('label').replace('{uid}',userId).replace('{param}',param).replace('{r}',r).replace('{s}',s);
            question_list.url_quotafull.list[0].answer_value=$('.opentexturl_quotafull').data('label').replace('{uid}',userId).replace('{param}',param).replace('{r}',r).replace('{s}',s);
            question_list.url_complete.list[0].answer_value=$('.opentexturl_complete').data('label').replace('{uid}',userId).replace('{param}',param).replace('{r}',r).replace('{s}',s);
        }

        // 是否启用cookie
        var is_cookie = navigator.cookieEnabled;
        if(!is_cookie){
            // $('#cookie_error_1').prop('checked',true);
            // $('#cookie_error_1').prev().css({'margin-right': '5px', 'border': '5px solid rgb(83, 164, 244)'});
            question_list.cookie_error.list[0].answer_value=1;
        }else{
            question_list.cookie_error.list[0].answer_value=0;
        }

        // 问卷状态, 告警
        var surveyStatus = '<?=$survey['survey_status']??''?>';
        if(surveyStatus){
            // $('input[name="surveyStatus"][value="'+surveyStatus+'"]').prop('checked',true);
            // $('input[name="surveyStatus"][value="'+surveyStatus+'"]').prev().css({'margin-right': '5px', 'border': '5px solid rgb(83, 164, 244)'});
            // $('#warnflg_2').prop('checked',true);
            // $('#warnflg_2').prev().css({'margin-right': '5px', 'border': '5px solid rgb(83, 164, 244)'});
            question_list.surveyStatus.list.forEach(function(item,i){
                if(item.answer_label==surveyStatus){
                    question_list.surveyStatus.list[i].answer_value=1;
                }else{
                    question_list.surveyStatus.list[i].answer_value=0;
                }
            });
            question_list.warnflg.list[0].answer_value=0;
            question_list.warnflg.list[1].answer_value=1;
        }else{
            // $('#warnflg_1').prop('checked',true);
            // $('#warnflg_1').prev().css({'margin-right': '5px', 'border': '5px solid rgb(83, 164, 244)'});
            question_list.warnflg.list[0].answer_value=1;
            question_list.warnflg.list[1].answer_value=0;
        }

        // 事件名称
        var touch_event = "click";
        if ($(window).width() < 750) {
            touch_event = "touchend";
        }

        // 本次进入问卷时间
        var start_time = new Date();
        var diff = 0;
        // 屏幕触摸事件，点击事件
        $(document).on(touch_event,function(){
            // 上次点击时间戳
            var touch_time = new Date();
            var month=touch_time.getMonth()+1<10 ?"0"+(touch_time.getMonth()+1):(touch_time.getMonth()+1);
            var day=touch_time.getDate()<10 ?"0"+touch_time.getDate():touch_time.getDate();
            var hour=touch_time.getHours()<10 ?"0"+touch_time.getHours():touch_time.getHours();
            var minute=touch_time.getMinutes()<10 ?"0"+touch_time.getMinutes():touch_time.getMinutes();
            var second=touch_time.getSeconds()<10 ?"0"+touch_time.getSeconds():touch_time.getSeconds();

            // $('#last_touched').text(touch_time.getFullYear()+'-'+month+'-'+day);
            // $('#last_touched_time').text(hour+':'+minute+':'+second);
            // $('#lastcomplete').text(touch_time.getFullYear()+'-'+month+'-'+day);
            // $('#lastcomplete_time').text(hour+':'+minute+':'+second);
            question_list.last_touched.list[0].answer_value=touch_time.getFullYear()+'-'+month+'-'+day;
            question_list.last_touched.list[1].answer_value=hour+':'+minute+':'+second;
            question_list.lastcomplete.list[0].answer_value=touch_time.getFullYear()+'-'+month+'-'+day;
            question_list.lastcomplete.list[1].answer_value=hour+':'+minute+':'+second;

            diff = touch_time.getTime()-start_time.getTime();
            // $('input[name="Timediff"]').val(diff/1000);
            if(!question_list.Timediff.list[0].answer_value){
                question_list.Timediff.list[0].answer_value = 0;
            }
            question_list.Timediff.list[0].answer_value += parseInt(diff/1000);
        });

        $(document).ready(function(){
            // 第一题隐藏上一题按钮
            $('.jump_box:eq(0) .prev_btn').hide();
            // 最后一题改提交按钮
            $('.question<?=$finish_id?>').find('.next_btn').html('<?=$button_title[2]??"提交"?>');
            // 当前显示的题目
            <?php if(isset($survey['last_question_id'])&&$survey['last_question_id']):?>
                $('.question<?=$survey['last_question_id']?>').show().siblings().hide();
            <?php endif;?>
            // 开启计时
            setTime('<?=$survey['last_question_id']??''?>');
        });

        // 上一题按钮
        $('.prev_btn').click(function(){
            var question_id = $(this).attr('data-question_id');
            if(question_id && $('.question'+question_id).length>0){
                $(this).closest('.question_box').hide();
                $('.question'+question_id).show();
                // 开启下一题计时
                setTime(question_id);
            }else{
                var current_question_id = $(this).closest('.question_box').attr('id');
                question_id = get_prev_question(current_question_id);
                $(this).closest('.question_box').hide();
                $('.question'+question_id).show();
                // 开启下一题计时
                setTime(question_id);
            }
        });

        function get_prev_question(question_id){
            var prev_question_id = '';
            for(var p in question_list){
                var list = question_list[p].list;
                if(question_list[p].question_show_logic&&list[0].answer_jump_question_id==question_id){
                    var logic_json = JSON.parse(question_list[p].question_show_logic);
                    var check_question_id = logic_json[0].split('_')[0];
                    question_list[check_question_id].list.forEach(function(val,key){
                        if(val.variable_id==logic_json[0]){
                            if(val.answer_value=="1"){
                                prev_question_id = p;
                            }else{
                                prev_question_id = get_prev_question(p);
                            }
                        }
                    });
                }else if(list[0].answer_jump_question_id==question_id){
                    prev_question_id = p;
                }
            }
            return prev_question_id;
        }

        // 下一题按钮
        $(document).on('click', '.next_btn', function(){
            console.log('next_btn');
            $('video').trigger('pause');
            // 有错误信息
            if($(this).closest('.question_box').find('.msgbox').length>0&&$(this).closest('.question_box').find('.msgbox').html()!=''&&$(this).closest('.question_box').find('.msgbox').html()!='*'||$(this).closest('.question_box').find('.red').length>0&&$(this).closest('.question_box').find('.red').html()!=''){
                return false;
            }

            var check = false;
            // 有错误信息
            if($(this).closest('.question_box').find('.red').length>0){
                $(this).closest('.question_box').find('.red').each(function(){
                    if($(this).html()!=''){
                        check = true;
                    }
                })
            }

            // 有为空span输入框(用在年，年月，日期，时间题)
            $(this).closest('.question_box').find('.input:visible').each(function(){
                if($(this).html()==''){
                    check = true;
                }
            });
            // 有为空textarea输入框(用在文本题)
            $(this).closest('.question_box').find('textarea').each(function(){
                if($(this).val()==''){
                    check = true;
                }
            });
            // 有为空input输入框
            $(this).closest('.question_box').find('input[type="text"]:visible').each(function(){
                if($(this).val()==''&&$(this).attr('data-min')){
                    check = true;
                }
            });
            // 有为空file输入框(用在上传题)
            $(this).closest('.question_box').find('input[type="file"]').each(function(){
                if($(this).val()=='' && $(this).attr('data-file')=="0"){
                    check = true;
                }
            });
            if(check){
                return false;
            }

            var last_question_id = $(this).closest('.question_box').attr('id');

            var next_time = new Date();
            diff = next_time.getTime()-start_time.getTime();
            if(!question_list[last_question_id].answer_time){
                question_list[last_question_id].answer_time = 0;
            }
            question_list[last_question_id].answer_time += parseInt(diff/1000);

            question_list[last_question_id].list.forEach(function(item,i){
                /*if(item.count_size&&item.answer_value==1){
                    // 更新答题计数
                    $.ajax({
                        url: "/sv/dr/count_size",
                        type: "post",
                        data: {
                            pid:'<?=$info['id']?>',
                            question_id:last_question_id,
                            code:item.answer_code,
                            count_size:item.count_size,
                        },
                        async: true,
                        dataType: 'json',
                        success: function (res) {
                            console.log(res);
                        },
                        error: function (e) {
                            console.log(e);
                        }
                    });
                    question_list[last_question_id].list[i].count_size="";
                }*/
                if(question_list[last_question_id].list[i].answer_value){
                    question_list[last_question_id].list[i].old_answer=1;
                }else{
                    question_list[last_question_id].list[i].old_answer=0;
                }
            });

            // 开启下一题计时
            setTime(last_question_id);

            // 更新答题时间
            $.ajax({
                url: "/sv/dr/time_log",
                type: "post",
                data: {
                    pid:'<?=$info['id']?>',
                    r:getQueryVariable('r'),
                    s:getQueryVariable('s'),
                    question_id:last_question_id,
                    uid:'<?=$survey['uid']??''?>',
                    name:'<?=$survey['name']??''?>',
                    unit_name:'<?=$survey['unit']??''?>',
                    unit_level:'<?=$survey['unit_level']??''?>',
                    department:'<?=$survey['department']??''?>',
                    title:'<?=$survey['title']??''?>',
                    start_time:parseInt(start_time.getTime()/1000),
                    view_time_diffe:parseInt(diff/1000),
                },
                async: true,
                dataType: 'json',
                success: function (res) {
                    console.log(res);
                },
                error: function (e) {
                    console.log(e);
                }
            });

            // 重置开始时间节点
            start_time = new Date();
            cookie_set('project_id','<?=$info["id"]?>');
            cookie_set('r',getQueryVariable('r'));
            cookie_set('s',getQueryVariable('s'));

            var question_id = $(this).attr('data-question_id');
            if($('.question'+question_id).length>0){
                var next_question_id = question_id;
                if(question_list[question_id].question_show_logic){
                    next_question_id = get_next_question(question_id);
                    $('.question'+next_question_id).find('.prev_btn').attr('data-question_id',last_question_id);
                }
                $(this).closest('.question_box').hide();
                $('.question'+next_question_id).show();
                smt_btn(next_question_id,next_question_id);
                console.log(question_list);
            }else{
                question_id = question_id.split(',');
                for(var i = 0; i < question_id.length; i++){
                    if(['s','q','c','o','f','u'].indexOf(question_id[i])>-1){
                        smt_btn(question_id[i],last_question_id);
                        return false;
                    }
                }
            }
        });

        function get_next_question(question_id){
            var logic_json = JSON.parse(question_list[question_id].question_show_logic);
            var check = false;
            var check_question_id = logic_json[0].split('_')[0];
            /* question_list[check_question_id].list.forEach(function(val,key){
                if(val.variable_id==logic_json[0]){
                    if(val.answer_value=="1"){
                        check = true;
                    }else{
                        check = false;
                    }
                }
            }); */

            for (let index = 0; index < logic_json.length; index++) {
                const element = logic_json[index];
                var check_question_id = element.split('_')[0];
                question_list[check_question_id].list.forEach(function(val,key){
                    if(val.variable_id==element){
                        if(val.answer_value=="1"){
                            check = true;
                        }
                    }
                });
            }

            if(check){
                return question_id;
            }else{
                var next_id = question_list[question_id].list[0].answer_jump_question_id;
                if(question_list[next_id].question_show_logic){
                    return get_next_question(next_id);
                }else{
                    return next_id;
                }
            }
        }

        $(document).ready(function(){
            $('p img').on('click',function(){
                var url = $(this).attr('src');
                window.open(url, '_blank').location;
            });
        });
        var timer;
        function setTime(question_id){
            clearInterval(timer);
            var uid = getQueryVariable('uid');
            n=cookie_get('<?=$info['id']?>_time_'+question_id+'_'+uid)?parseInt(cookie_get('<?=$info['id']?>_time_'+question_id+'_'+uid)):0;
            timer = setInterval(function(){
                n++;
                var h=parseInt(n/3600);
                var i=parseInt(n/60)-h*60;
                var s=parseInt(n%60);
                var txt=toDub(h)+":"+toDub(i)+":"+toDub(s);
                $('.timer').html(txt);
                cookie_set('<?=$info['id']?>_time_'+question_id+'_'+uid,n);
            }, 1000);
        }
        //补零
        function toDub(n){
            return n<10?"0"+n:""+n;
        }

        var url = window.location.pathname.substring(8,10);
        if(url=='41'){
            $('.prev_btn').remove();
        }

        var keep_time = question_list.Timediff.list[0].answer_value;
        // 页面计时器
        setInterval(function(){
            if(typeof(last_question_id) == "undefined" || last_question_id == null){
                var start_time = new Date();
            }else{
                console.log("计时器关闭");
                return false;
            }
            keep_time = keep_time + 5;
            console.log("计时器运行中");
            question_list.Timediff.list[0].answer_value = keep_time;
            console.log(question_list.Timediff.list[0].answer_value);
            smt_btn('','');
        }, 5000);
    </script>
</body>

</html>
