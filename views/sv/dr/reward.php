<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no" />
    <title>健康通-上医说</title>
    <script src="/theme/bk/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
            box-sizing: border-box;
        }

        a {
            text-decoration: none;
        }

        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
            position: relative;
        }

        input[type="text"] {
            -webkit-appearance: none;
        }

        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }

        .title {
            text-align: center;
            padding-top: 5px;
            padding-bottom: 5px;
            font-weight: 700;
            font-size: 30px;
        }

        .tab {
            height: 40px;
        }

        .tab ul {
            height: 100%;
            border-bottom: 1px solid #2b3a99;
            border-top: 1px solid #2b3a99;
        }

        .tab ul li {
            border-right: 1px solid #2b3a99;
            list-style: none;
            float: left;
            height: 100%;
            width: 33.3%;
            line-height: 40px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }

        .tab ul li:last-child {
            border-right: none;
        }

        .tab_content {
            text-align: center;
            padding-top: 15px;
        }

        .privacy, .checkbox {
            padding-left: 40px;
            text-align: left;
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            vertical-align: middle;
            margin-right: 5px;
        }

        /*.tab_content input[type="text"] {*/
            /*border: 2px solid #fd5592;*/
            /*padding: 5px;*/
            /*width: 80%;*/
            /*outline: none;*/
            /*height: 46px;*/
            /*font-size: 14px;*/
            /*border-radius: 2px;*/
            /*margin-bottom: 20px;*/
        /*}*/

        input[type="text"] {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .tab_content .select {
            position: relative;
            color: #666;
            margin: 0 auto;
            border: 2px solid #fd5592;
            padding: 10px 5px 5px;
            width: 80%;
            height: 46px;
            text-align: left;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .tab_content select {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            opacity: 0;
        }

        .wx {
            background: #fff;
            position: relative;
            text-align: center;
        }

        .wx .ewm img {
            width: 240px;
        }

        .wx .ewm .tip {
            width: 100%;
            color: #2b3a99;
        }

        .wx .ewmed {
            width: 240px;
            margin: 0 auto;
            text-align: center;
            display: none;
        }

        .wx .ewmed img {
            width: 240px;
        }

        .error_msg {
            margin-top: 15px;
            color: red;
        }

        .btn {
            position: absolute;
            text-align: center;
            width: 88%;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .btn button {
            cursor: pointer;
            font-size: 24px;
            width: 45%;
            padding: 10px;
            border: none;
            outline: none;
            border-radius: 2px;
        }

        .btn .confirm {
            color: #fff;
            background: #f85691;
        }

        .btn .cancel {
            color: #000;
            background: transparent;
            border: 1px solid #fd5592;
        }

        .privacy_dialog_box {
            z-index: 999;
            display: none;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .privacy_dialog {
            width: 80%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
        }

        .privacy_dialog h4 {
            color: #fff;
            background: #2b3a99;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }

        .privacy_dialog .privacy_content {
            padding: 20px;
            border-left: 1px solid #ccc;
            border-right: 1px solid #ccc;
        }

        .privacy_dialog .privacy_btn button {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #fd5592;
            color: #fff;
            cursor: pointer;
        }



        .success_dialog_box {
            z-index: 999;
            display: none;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .success_dialog {
            width: 80%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
        }

        .success_dialog h4 {
            color: #fff;
            background: #2b3a99;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }

        .success_dialog .success_content {
            padding: 20px;
            border-left: 1px solid #ccc;
            border-right: 1px solid #ccc;
            text-align: center;
        }

        .success_dialog .success_btn button {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #fd5592;
            color: #fff;
            cursor: pointer;
        }

        .tab_content .other input[type="radio"] {
            display: none;
        }

        .tab_content .other .radio {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 1px solid #ccc;
            border-radius: 50%;
            vertical-align: middle;
        }

        .checked {
            border:5px solid #445ad4 !important;
        }
    </style>
</head>

<body>
<div class="container">
    <!-- 隐私协议对话框 -->
    <div class="privacy_dialog_box">
        <div class="privacy_dialog">
            <h4>个人所得税代缴代付协议</h4>
            <div class="privacy_content">
                <p>受访者须知：</p>
                <p style="margin-top: 15px">
                    根据根据中国人民共和国有关税法的规定，个人劳务报酬依法应当缴纳个人所得税，服务单位有义务代扣代缴个人所得税。因此请您理解并同意本公司【健康通(北京)网络科技有限公司】在向您支付劳务报酬时，为您代扣代缴个人所得税，该过程需要使用您的个人信息进行登记申报
                </p>
                <p style="margin-top: 20px">
                    特别说明：缴纳个税不影响您的项目礼金支付金额。
                </p>
                <p style="margin-top: 20px">健康通（北京）网络科技有限公司</p>
            </div>
            <div class="privacy_btn">
                <button class="acpt_btn">确定</button>
            </div>
        </div>
    </div>

    <div class="success_dialog_box">
        <div class="success_dialog">
            <h4>提交成功！！！</h4>
            <div class="success_content">
                <p>七个工作日内支付</p>
            </div>
            <div class="success_btn">
                <button><a style="color:#fff;" href="http://www.gooddr.com?code=<?=$gooddr_code;?>" target="_blank">健康通官网</a></button>
            </div>
        </div>
    </div>

    <div class="title">确认收款账户<font color="#66a325">(<?php echo $pid == 2248 ? "200" : $point / 100; ?>元)</font></div>

    <input type="hidden" name="clicked_request" value="" />
    <input type="hidden" name="wx_request" value="<?php echo isset($account_info[EXCHANGE_WEBCHAT_AUTO]) ? "success" : ""; ?>" />

    <div class="tab">
        <ul>
            <li class="pay_alipay" data-type="alipay" data-id="<?=EXCHANGE_ALIPAY?>" style="background: #2b3a99; color: #fff">支付宝</li>
            <?php if($answer['uid']):?>
            <li class="pay_wx" data-type="wx" data-id="<?=EXCHANGE_WEBCHAT_AUTO?>">微信</li>
            <?php endif;?>
            <li class="pay_bank" data-type="other" data-id="other">其它</li>
        </ul>
    </div>

    <form action="/sv/dr/payment_sub" id="the_from" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
        <div class="tab_content">
            <!-- 支付宝 -->
            <div class="alipay">
                <div class="content">
                    <input type="text" name="alipay_payment_name" placeholder="请输入支付宝用户名[实名]" value="<?php echo isset($account_info[EXCHANGE_ALIPAY]) ? $account_info[EXCHANGE_ALIPAY]['payment_name'] : ""; ?>" />
                    <input type="text" name="alipay_payment_account" placeholder="请输入支付宝收款账号" value="<?php echo isset($account_info[EXCHANGE_ALIPAY]) ? $account_info[EXCHANGE_ALIPAY]['payment_account'] : ""; ?>" />
                </div>
            </div>

            <!-- 微信 -->
            <div class="wx" style="display: none">
                <?php if (!isset($account_info[EXCHANGE_WEBCHAT_AUTO])) {?>
                <!--未授权-->
                <div class="ewm">
                    <div class="tips">扫描二维码绑定健康通授权微信收款</div>
                    <img src="/uploads/quick_survey/<?=$pid?>/<?=$filename?>" alt="" />
                    <div class="tip">(截图通过微信扫描识别二维码)</div>
                </div>

                <div class="ewmed">
                    <img src="/theme/go/image/success.png" alt="">
                    <p>已授权健康通支付</p>
                </div>
                <?php } else {?>
                    <div class="ewmed" style="display: block;">
                        <img src="/theme/go/image/success.png" alt="">
                        <p>已授权健康通支付</p>
                    </div>
                <?php }?>
                <input type="text" name="wx_payment_name" placeholder="请输入微信用户名[实名]" style="margin-top: 10px;" value="<?php echo isset($account_info[EXCHANGE_WEBCHAT_AUTO]) ? $account_info[EXCHANGE_WEBCHAT_AUTO]['payment_name'] : ""; ?>" />
            </div>

            <!-- 其它 -->
            <div class="other" style="display: none">
                <div class="content" style="font-size:20px;text-align:left;padding-left: 40px;">
                    <?php if ($is_phone_type) {?>
                    <div>
                        <label>
                            <span class="radio"></span>
                            <input class="other_payment_type" type="radio" name="other_payment_type" value="<?=EXCHANGE_MOBILE?>">手机充值
                        </label>
                        <font color="red">(不建议)</font>
                        <div style="display: none;" id="other_phone">
                        <input type="text" name="mobile_payment_account" id="mobile_payment_account" placeholder="请输入手机号码" value="<?php echo isset($account_info[EXCHANGE_MOBILE]) ? $account_info[EXCHANGE_MOBILE]['payment_account'] : ""; ?>" /></div>
                    </div>
                    <?php }?>
                    <div>
                        <label>
                            <span class="radio"></span>
                            <input class="other_payment_type" type="radio" name="other_payment_type" value="<?=DON_BE_POLITE?>" />慈善捐赠<br />
                        </label>
                        <span style="font-size: 16px;">（上海红十字会合作代赠）</span>
                    </div>
                </div>
            </div>
        </div>

        <!--独立多选框 start-->
        <div class="checkbox" id="default_payment_type" style="margin-bottom: 50px;">
            <p class="lock_account" style="margin-left: 30px; font-size: 12px; color: red;display:none;"></p>
        </div>
        <!--独立多选框 end-->

        <div class="btn">
            <div class="privacy" style="padding-left: 18px">
                <label>
                    <input class="privacy_input" type="checkbox" name="privacy" value="1" />
                    <span>阅读并同意<a href="javascript:;">《个税代缴代付协议》</a></span>
                </label>
            </div>

            <input  type="hidden" name="pid" value="<?=$pid?>">
            <input  type="hidden" name="r" value="<?=$r?>">
            <input  type="hidden" name="s" value="<?=$s?>">
            <input id="scene" type="hidden" name="scene" value="<?=$scene?>">
            <input id="reward_key" type="hidden" name="reward_key" value="<?=$reward_key?>">
            <input type="hidden" name="payment_type" value="<?=EXCHANGE_ALIPAY?>">
            <!-- 提示信息 -->
            <div class="error_msg" style="margin-bottom: 10px"></div>
            <button class="confirm" type="submit">确 认</button>
            <button class="cancel" type="reset">重 置</button>
        </div>
    </form>

<script>

    // 单选框的点击效果
    $(" .tab_content .other input").click(function () {
        if ($(this).prop("checked"))  {
            $(".tab_content .other .radio").removeClass("checked")
            $(this).prev().addClass("checked")
        }
        var payment_type = $(this).val();
        console.log(payment_type)
        if (payment_type == 209) {//手机号码
            $("#other_phone").show();
        } else {
            $("#other_phone").hide();
        }
    })

    var timeId;
    // 下拉框的选中效果
    $(".container").on("change", "select", function() {
        var text = $(this).children("select option:selected").text();
        $(this).parent().children("span").text(text);
    });
    // 点击效果
    $(".tab ul li").click(function() {
        //独立多选框 start
        $(".checkbox input[type='checkbox']").prop("checked", false);
        $(".checkbox input[name='payment_type_default']").val("");
        //独立多选框 end

        // 设置input的value
        $("input[name='payment_type']").val($(this).data("id"))
        $(this)
            .css({
                background: "#2b3a99",
                color: "#fff"
            })
            .siblings()
            .css({
                background: "#fff",
                color: "#000"
            });
        var type = $(this).data("type");
        $("." + type)
            .show()
            .siblings()
            .hide();

        // 微信支付请求检测是否已做支付
        $("#default_payment_type").show();
        $("#id_card_info").show();
        if (type == "wx") {
            if ($("input[name='wx_request']").val() == "success") {
                clearInterval("1")
            } else {
                var wtimeId = setInterval("check_order_info()", 2000);
                $("input[name='clicked_request']").val(wtimeId);
            }
        } else if(type == "other"){//其它支付方式
            $("#default_payment_type").hide();
            $("#id_card_info").hide();
            clearInterval($("input[name='clicked_request']").val())
            clearInterval("1")
        } else{
            clearInterval($("input[name='clicked_request']").val())
            clearInterval(timeId)
        }

    });

    // 默认绑定支付方式--支付宝
    $(".tab .pay_alipay").trigger("click");
    $(".checkbox input").prop("checked", true);
    $(".checkbox .lock_account").show();

    <?php if (isset($account_info[EXCHANGE_WEBCHAT_AUTO])) {?>
    $(".ewm").hide();
    $(".ewmed").show();
    <?php }?>

    // 微信支付请求检测是否已做支付
    function check_order_info() {
        $("#wechat").find('font').remove();
        $.ajax({
            type: "POST",
            url: "/sv/dr/check_order_info",
            data: "scene=<?=$scene?>",
            success: function(str) {
                var json_msg = $.parseJSON(str);
                if (json_msg.rs_code == "success") {
                    $(".ewm").hide();
                    $(".ewmed").show();
                    clearInterval(timeId);
                    clearInterval($("input[name='clicked_request']").val());
                    $("input[name='wx_request']").val("success");
                }
            }
        });
    }

    // 隐私协议
    $(document).click(function() {
        $(".privacy_dialog_box").hide();
    });
    $(".privacy_dialog").click(function(e) {
        e.stopPropagation();
    });
    $(".privacy a").click(function(e) {
        e.stopPropagation();
        $(".privacy_dialog_box").show();
        // 对话框的确定按钮
        $(".acpt_btn").click(function() {
            $(".privacy_dialog_box").hide();
            $(".privacy_input").prop("checked", true);
        });
    });


    var common_js = {
        //提交表单 表单对象，显示提示信息的
        form_sumbit : function (item_form, msg_id){
            $(item_form).ajaxSubmit({
                target:'',
                beforeSubmit : null,
                success : common_js.show_response(msg_id),
                url : item_form.location,
                type : 'post',
                dataType : 'text',
                clearForm : false,
                resetForm : false,
                cache : false,
            });
            return false;
        },
        //接受PHP返回信息并重绘提示框
        show_response : function (msg_id){
            return function(str){
                var res = jQuery.parseJSON(str);
                if (res.rs_code == 'success') {
                    $(".success_dialog_box").show();
                    $("."+msg_id).html(res.rs_msg);
                    $("#next_pro").text("继续参与下一个问卷[" + res.rs_msg.point + "]");
                    $("#next_pro").show();
                    $("#next_pro").click(function() {
                        window.location.href = res.rs_msg.partner_link
                    })
                } else {
                    $("."+msg_id).html(res.rs_msg);
                }
            }
        },
        tver : '1.0.1'
    }
    common_js;
</script>
</body>

</html>
