</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0,user-scalable=no"
    />
    <title>健康通-上医说</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        .msg{
            color:red;
        }
        input {
            -webkit-appearance: none;
        }
        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }
        body,
        html {
            height: 100%;
        }
        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
        }
        .title {
            text-align: center;
            padding-top: 50px;
            font-weight: 700;
            font-size: 30px;
        }
        .content {
            text-align: center;
            padding-top: 50px;
        }
        .content input {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 70%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }
        .content .code {
            width: 70%;
            height: 46px;
            margin: 0 auto;
            position: relative;
        }
        .content .code input {
            width: 100%;
        }
        .content .code .get_code {
            position: absolute;
            right: 0;
            height: 100%;
            line-height: 46px;
            background: #f85691;
            color: #fff;
            padding: 0 10px;
            font-size: 14px;
            cursor: pointer;
        }
        .content .tips {
            font-size: 12px;
            text-align: left;
            width: 70%;
            margin: 0 auto;
            margin-top: 5px;
        }
        .btn {
            text-align: center;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .btn button {
            cursor: pointer;
            min-width: 200px;
            background: #f85691;
            padding: 10px;
            color: #fff;
            font-size: 24px;
            border: none;
            outline: none;
            border-radius: 2px;
        }

        .error_msg {
            margin-bottom: 10px;
            color: red;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo" style="text-align: center; padding-top: 80px">
            <img style="height: 80px" src="/theme/go/image/jkt_logo_1.png" alt="" />
        </div>

      <div class="content">
        <input type="text" name="tel" id="phone"  placeholder="请输入手机号码" />
        <p class="code">
            <input type="text" id="code" name="verify_code" placeholder="请输入验证码" />
            <span class="get_code">获取验证码</span>
        </p>
        <p class="tips">验证码请查阅手机短信</p>
      </div>

      <div class="btn">
        <div class="msg login_tips"></div>
        <button class="login_btn">登 录</button>
      </div>
    </div>
    <script src="/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
    <script>
        $(function () {
            // 手机验证
            $("#phone").blur(function () {
                if (!/^1[3456789]\d{9}$/.test($(this).val().trim())) {
                    $(".login_tips").text("请输入正确的手机号");
                } else {
                    $(".login_tips").text("");
                }
            });
            // 验证码验证
            $("#code").blur(function () {
                if ($(this).val().trim() != $(this).val()) {
                    $(".login_tips").text("请输入正确的验证码");
                } else {
                    $(".login_tips").text("");
                }
            });

            // 获取验证码
            var scene = "<?=$scene?>";
            $(".get_code").click(function () {
                if (!$(this).hasClass("send")) {
                    var verify_mobile = $("#phone").val().trim();
                    if (verify_mobile) {
                        $.ajax({
                            url: "/sv/dr/reward_code",
                            type: "post",
                            data: {
                                pid: "<?=$pid?>",
                                r: "<?=$r?>",
                                s: "<?=$s?>",
                                mobile: verify_mobile
                            },
                            dataType: "json",
                            success: function (info) {
                                if (info.err_msg == "success") {
                                    $(".get_code").addClass("send");
                                    var time = 60;
                                    getRandomCode();
                                    //倒计时
                                    function getRandomCode() {
                                        if (time === 0) {
                                            time = 60;
                                            $(".get_code").removeClass("send");
                                            $(".get_code").text("重新发送");
                                            return;
                                        } else {
                                            time--;
                                            $(".get_code").text(time + "s");
                                        }
                                        setTimeout(function () {
                                            getRandomCode();
                                        }, 1000);
                                    }
                                }else{
                                    $(".login_tips").text(info.rs_msg);
                                }
                            }
                        })
                    } else {
                        $(".login_tips").text("请输入手机号")
                    }

                }
            });

            // 登录 17301849323
            $(".login_btn").click(function () {
                var verify_code = $("#code").val()
                var verify_mobile = $("#phone").val().trim();
                if(!/^1[3456789]\d{9}$/.test(verify_mobile)){
                    $(".login_tips").text("请输入正确的手机号");
                    return false;
                }
                if(!verify_code){
                    $(".login_tips").text("请输入正确的验证码");
                    return false;
                }

                $.ajax({
                    type: "post",
                    url: "/sv/dr/reward_code_check/<?=$pid?>?r=<?=$r?>&s=<?=$s?>",
                    data: {
                        scene: scene,
                        verify_mobile: verify_mobile,
                        verify_code: verify_code,
                    },
                    dataType: "json",
                    success: function (info) {
                        if (info.rs_code == "success") {
                            cookie_set('reward_key',info.rs_msg);
                            window.location.href = '/sv/dr/reward/<?=$pid?>?r=<?=$r?>&s=<?=$s?>&reward_key='+info.rs_msg;
                        }else{
                            $(".login_tips").text(info.rs_msg);
                        }
                    }
                })
            });
        });
        //获取cookie
        function cookie_get(name) {
            let arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg)) {
                if (typeof arr[2] == 'undefined' || arr == '') return '';
                return (unescape(arr[2]));
            } else
                return null;
        }
        //设置cookie
        function cookie_set(c_name, value, expiredays) {
            var exdate = new Date();
            exdate.setTime(exdate.getTime() + getExpireTime());
            //exdate.setDate(exdate.getDate() + expiredays);
            document.cookie = c_name + "=" + escape(typeof value !== 'string' ? JSON.stringify(value) : value) + " ;expires=" + exdate.toGMTString() + ";path=/";
        }
        //现在距离当天结束时间:毫秒
        function getExpireTime()
        {
            var date = new Date();
            var hour = 23-date.getHours();
            var min = 59-date.getMinutes();
            var ms = (3600*hour + 60*min)*1000;
            return ms;
        }
    </script>
</body>
</html>
