<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="content-type" content="no-cache, must-revalidate" />
    <title>Home</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            background: #f5f5f5;
            height: 100%;
            color: #4a4a4a;
            overflow: hidden;
        }

        input,
        button {
            outline: none;
            border: none;
            background: transparent;
            -webkit-appearance: none;
        }

        ol,
        ul {
            list-style: none;
        }

        .radius {
            border-radius: 5px;
        }

        /* 清浮动 */
        .clearfix::before,
        .clearfix::after {
            content: "";
            display: table;
        }

        .clearfix:after {
            clear: both;
        }

        /* IE 6/7 */
        .clearfix {
            zoom: 1;
        }

        .container::-webkit-scrollbar {
            display: none;
        }

        .container {
            background: #fff;
            overflow: auto;
            max-height: 90%;
            padding: 10px;
            padding-top: 95px;
        }

        .container .head {
            position: fixed;
            z-index: 99999;
            background: #fff;
            padding: 10px;
        }

        .search {
            position: relative;
            height: 50px;
        }

        .search img {
            width: 20px;
            position: absolute;
            left: 10px;
            top: 10px;
        }

        .search input {
            border: 1px solid #ccc;
            width: 100%;
            height: 40px;
            padding-left: 40px;
            font-size: 14px;
        }

        .department {
            height: 30px;
            line-height: 30px;
            overflow: hidden;
            position: relative;
        }

        .department img {
            position: absolute;
            cursor: pointer;
            width: 18px;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .department ul {
            height: 100%;
            white-space: nowrap;
            overflow-y: hidden;
            overflow-x: auto;
            padding-right: 100px;
        }

        .department ul li {
            display: inline-block;
            padding: 0 5px;
        }

        .recommend {
            height: 80px;
            margin-top: 10px;
        }

        .recommend ul {
            height: 100%;
            white-space: nowrap;
            overflow-y: hidden;
            overflow-x: auto;
        }

        ul::-webkit-scrollbar {
            display: none;
        }

        .recommend ul li {
            display: inline-block;
            width: 50px;
            height: 100%;
            vertical-align: top;
            margin: 0 5px;
            text-align: center;
            position: relative;
        }

        .recommend ul li div {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #d63b25;
            position: relative;
            margin-bottom: 10px;
        }

        .recommend ul li div img {
            width: 40px;
            height: 40px;
            border: 2px solid #000;
            border-radius: 50%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .recommend ul li div .state {
            background: #eb4a44;
            color: #fff;
            font-size: 12px;
            padding: 0 3px;
            border-radius: 3px;
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            border: 2px solid #fff;
        }

        .recommend ul li .explain {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            height: 20px;
            color: #000;
            z-index: 9;
        }

        .comment_num .comment_box {
            text-align: left;
            position: absolute;
            width: 100%;
            max-height: 300px;
            overflow: auto;
            padding: 20px 30px;
            box-sizing: border-box;
            background: #fff;
            border: 1px solid #dee4ec;
            z-index: 999;
        }

        .project {
            margin-top: 5px;
        }

        .project .item video {
            width: 100%;
        }

        .project .item .item_detail {
            padding: 10px;
        }

        .project .item .item_detail .user {
            cursor: pointer;
        }

        .project .item .item_detail .user img {
            float: left;
        }

        .project .item .item_detail .user div {
            float: left;
            max-width: 85%;
        }

        .project .item .item_detail .comment_num {
            text-align: right;
            position: relative;
        }

        .comment_num .comment_box input {
            width: 100%;
            height: 40px;
            border: 1px solid #ccc;
            padding-left: 20px;
        }

        .comment_num .comment_box button {
            padding: 5px 10px;
            background: #53a4f4;
            font-size: 16px;
            color: #fff;
            margin-top: 20px;
        }

        .project .item .item_detail .comment_num span {
            cursor: pointer;
        }

        .project .item .item_detail .comment_num span img {
            height: 20px;
            width: 20px;
            vertical-align: middle;
        }

        .project .item .item_detail img {
            width: 35px;
            height: 35px;
            vertical-align: top;
            border-radius: 50%;
        }

        .footer {
            height: 70px;
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            background: #fff;
            border-top: 1px solid #f5f7f8;
        }

        .footer ul li {
            float: left;
            height: 100%;
            padding-top: 28px;
            cursor: pointer;
            position: relative;
            width: 20%;
            text-align: center;
            color: #ccc;
        }

        .footer ul li img {
            width: 20px;
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
        }

        .button {
            width: 23%;
            height: 30px;
            line-height: 30px;
            text-align: center;
            background: #f5f7f8;
            border-radius: 20px;
            font-size: 12px;
            margin-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .draggable-element {
            cursor: move;
        }

        video {
            width: 100% !important;
            height: auto !important;
        }

        .red {
            color: #d63b25 !important;
        }

        .border_red {
            border: 1px dashed #d63b25 !important;
            color: #d63b25 !important;
        }

        .manage {
            display: none;
            height: 100%;
            background: #fff;
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 99999;
            padding-bottom: 10px;
            overflow: auto;
        }

        .manage .title {
            text-align: center;
            height: 60px;
            line-height: 60px;
            font-size: 16px;
            position: relative;
        }

        .manage .title span {
            position: absolute;
            top: 0px;
            right: 20px;
            width: 40px;
            height: 40px;
            margin-top: 10px;
            cursor: pointer;
        }

        .manage .content .my {
            padding: 0 20px;
        }

        .manage .content .my input {
            width: 60%;
        }

        .manage .content .my span:first-child {
            font-size: 14px;
        }

        .manage .content .my .edit {
            display: inline-block;
            padding: 3px 15px;
            background: #ffd4ce;
            border-radius: 20px;
            font-size: 12px;
            color: #d63b25;
        }

        .manage .content .detail {
            padding: 20px 20px;
        }

        .manage .more .detail span {
            background: #fff;
            border: 1px solid #f5f7f8;
        }

        /* pc */
        @media screen and (min-width: 750px) {
            .container {
                width: 818px;
                margin: 0 auto;
                margin-top: 50px;
            }

            .container .head {
                width: 818px;
                top: 30px;
                left: 50%;
                transform: translateX(-50%);
                padding-top: 30px;
            }

            .department ul {
                width: 96%;
            }

            .footer {
                width: 818px;
            }

            .manage .content .my .edit {
                float: right;
                margin-right: 80px;
            }
        }

        /* phone */
        @media screen and (max-width: 750px) {
            .container {
                width: 100%;
            }

            .container .head {
                width: 100%;
                top: 0;
                left: 0;
            }

            .department ul {
                width: 93%;
            }

            .footer {
                width: 100%;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="head radius">
            <div class="search">
                <img src="/theme/survey/img/search.png" alt="" />
                <input type="text" class="radius" placeholder="请输入搜索内容" />
            </div>


            <div class="department">
                <img src="/theme/survey/img/icon_add.png" alt="" />
                <ul class="clearfix">
                    <li>心血管内科</li>
                    <li>呼吸内科</li>
                    <li>消化内科</li>
                    <li>神经内科</li>
                    <li>心血管内科</li>
                    <li>呼吸内科</li>
                    <li>消化内科</li>
                    <li>神经内科</li>
                    <li>心血管内科</li>
                    <li>呼吸内科</li>
                    <li>消化内科</li>
                    <li>神经内科</li>
                    <li>心血管内科</li>
                    <li>呼吸内科</li>
                    <li>消化内科</li>
                    <li>神经内科</li>
                </ul>
            </div>
        </div>



        <div class="recommend">
            <ul class="clearfix">
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
                <li>
                    <div>
                        <img src="/theme/survey/img/timg.jpeg" alt="" />
                        <span class="state">直播中</span>
                    </div>
                    <span class="explain">正在直播</span>
                </li>
            </ul>
        </div>

        <div class="project">
            <?php foreach ($list as $key => $val) : ?>
                <div class="item">
                    <!-- <video src="" controls="controls" ></video> -->
                    <?= $val['home_info'] ?>
                    <div class="item_detail">
                        <div class="user clearfix">
                            <img src="/theme/survey/img/timg.jpeg" alt="" style="margin-right: 10px;" />
                            <div>
                                <p><?= $val['title'] ?> · <font color="#d63b25">关注</font>
                                </p>
                                <p style="font-size: 12px; color: #939aa3;">66万粉丝</p>
                            </div>
                        </div>
                        <div class="comment_num clearfix">
                            <span class="img_box" style="margin-right: 10px;">
                                <img src="/theme/survey/img/good.png" alt="" />
                                <font>200</font>
                            </span>
                            <span class="img_box">
                                <img src="/theme/survey/img/comment.png" alt="" />
                                <font>200</font>
                            </span>
                            <div class="comment_box" style="display: none;">
                                <p style="text-align: center;">
                                    <input type="text" class="radius" />
                                    <button class="radius">发送</button>
                                </p>
                                <div class="comment_detail">
                                    <p>评论1</p>
                                    <p>评论2</p>
                                    <p>评论3</p>
                                    <p>评论4</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="footer">
        <div class="footer">
            <ul>
                <li data-img="study">
                    <img src="/theme/survey/img/study.png" alt="" />
                    学习
                </li>
                <li data-img="find">
                    <img src="/theme/survey/img/find.png" alt="" />
                    发现
                </li>
                <li data-img="profit">
                    <img src="/theme/survey/img/profit.png" alt="" />
                    收益
                </li>
                </li>
                <li data-img="video">
                    <img src="/theme/survey/img/video.png" alt="" />
                    直播
                </li>
                <li data-img="my">
                    <img src="/theme/survey/img/my.png" alt="" />
                    我的
                </li>
            </ul>
        </div>
    </div>

    <div class="manage">
        <div class="title">
            <h3>科室管理</h3>
            <span><img src="/theme/survey/img/close.png" alt="" style="width: 100%;" /></span>
        </div>
        <div class="content my_manage">
            <div class="my">
                <span>我的科室</span>
                <input type="text" placeholder="点击进入我的科室" />
                <span class="edit">编辑</span>
            </div>

            <div class="detail">
                <button class="button draggable-element red">呼吸内科</button>
                <button class="button draggable-element red">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
                <button class="button draggable-element">呼吸内科</button>
            </div>
        </div>

        <div class="content more">
            <div class="my">
                <span>更多频道</span>
                <input type="text" placeholder="点击添加频道" />
            </div>

            <div class="detail">
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button">呼吸内科</button>
                <button class="button border_red">更多</button>
            </div>
        </div>
    </div>
</body>

<script src="/theme/survey/js/juqery-1.11.3.js"></script>
<script src="/theme/survey/js/drag-arrange.js"></script>

<script>
    $(function() {
        // department
        $(".department img").click(function() {
            $(".manage").show();
        });
        $(".manage .title span").click(function() {
            $(".manage").hide();
        });

        // video
        $("video").attr("poster", "/theme/survey/img/poster.png");
        $("video").attr("webkit-playsinline", "true")
        $("video").attr("playsinline", "true")

        // 底部的点击效果
        $(".footer ul li").click(function() {
            var img = $(this).data("img");
            $(this)
                .children("img")
                .attr("src", "/theme/survey/img/" + img + "ed.png");
            $(this).css("color", "#6f68df");

            $(this)
                .siblings()
                .each(function() {
                    $(this)
                        .children("img")
                        .attr("src", "/theme/survey/img/" + $(this).data("img") + ".png");
                });
            $(this).siblings().css("color", "#ccc");
        });

        // manage的拖拽效果
        $(".draggable-element").arrangeable();

        // 评论题型
        // 点击其他地方隐藏
        $(".comment_box").click(function(e) {
            e.stopPropagation();
        });
        $(document).click(function() {
            $(".comment_box").hide();
        });
        $(".comment_num span:nth-child(1)").click(function() {
            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                $(this).children("img").attr("src", "/theme/survey/img/gooded.png");
                $(this)
                    .children("font")
                    .text(+$(this).text() + 1);
            } else {
                $(this).children("img").attr("src", "/theme/survey/img/good.png");
                $(this)
                    .children("font")
                    .text(+$(this).text() - 1);
            }
        });
        $(".comment_num span:nth-child(2)").click(function(e) {
            e.stopPropagation();
            $(this).toggleClass("active");
            $(this).next().toggle();
            if ($(this).hasClass("active")) {
                $(this).children("img").attr("src", "/theme/survey/img/commented.png");
            } else {
                $(this).children("img").attr("src", "/theme/survey/img/comment.png");
            }
        });

        $(".comment_box button").click(function() {
            var text = $(".comment_box input").val();
            if (text.trim() != "") {
                $(".comment .comment_num span:nth-child(2)")
                    .children("font")
                    .text(+$(this).text() + 1);
                $(".comment_box .comment_detail").prepend(
                    "<p>" +
                    "<span style='color:red;cursor:pointer;margin-right:10px;'>X</span>" +
                    text +
                    "</p>"
                );
                $(".comment_box input").val("");
            }

            $(".comment_box").on("click", "span", function() {
                $(this).parent().remove();
            });
        });
    });
</script>

</html>