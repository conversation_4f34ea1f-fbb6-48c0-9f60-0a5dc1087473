<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="Cache-Control" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>wenjuan</title>
    <!-- 引入 ECharts 文件 -->
    <script src="/theme/survey/js/echarts.min.js"></script>
    <script src="/theme/survey/js/cookie.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            background: #f5f5f5;
        }

        .report {
            background: #fff;
        }

        .report .chartbox {
            padding-top: 20px;
            margin-bottom: 20px;
        }

        @media screen and (max-width: 750px) {
            .logo {
                width: 95%;
                margin: 0 auto;
                padding: 25px 0 10px 0;
                border-bottom: 1px solid #dee4ec;
            }

            #logo_img {
                height: 30px;
                float: left;
            }

            #logo_name {
                height: 30px;
                line-height: 30px;
                margin-left: 3px;
            }

            .head {
                width: 95%;
                margin: 0 auto;
                position: relative;
                margin-bottom: 30px;
            }

            .head h2 {
                font-weight: 400;
                color: rgb(76, 76, 76);
                font-size: 18px;
                text-align: center;
                margin: 20px 0;
            }

            .head p img {
                border: 1px solid #868c8cc2;
                width: 100%;
            }

            .head .line {
                position: absolute;
                width: 100%;
                height: 1.5px;
                background: #53a4f4;
                bottom: -20px;
                left: 0;
            }

            .report .chartbox .chart,
            .report .chartbox h3,
            .report .chartbox table {
                width: 95%;
            }

            .report .chartbox h3 {
                font-size: 14px;
            }
        }

        @media screen and (min-width: 750px) {
            .logo {
                width: 718px;
                margin: 0 auto;
                padding: 25px 0 10px 0;
                border-bottom: 1px solid #dee4ec;
            }

            #logo_img {
                height: 30px;
                float: left;
            }

            #logo_name {
                height: 30px;
                line-height: 30px;
                margin-left: 3px;
            }

            .head {
                width: 718px;
                margin: 0 auto;
                position: relative;
                margin-bottom: 30px;
            }

            .head h2 {
                font-weight: 400;
                color: rgb(76, 76, 76);
                font-size: 18px;
                text-align: center;
                margin: 20px 0;
            }

            .head p img {
                border: 1px solid #868c8cc2;
                width: 100%;
            }

            .head .line {
                position: absolute;
                width: 100%;
                height: 2.5px;
                background: #53a4f4;
                bottom: -20px;
                left: 0;
            }

            .report {
                width: 818px;
                margin: 0 auto;
                margin-top: 20px;
            }

            .report .chartbox .chart,
            .report .chartbox h3,
            .report .chartbox table {
                width: 718px;
            }

            .report .chartbox h3 {
                font-size: 18px;
            }
        }

        .report .chartbox .chart {
            height: 400px;
            margin: 0 auto;
        }

        .report .chartbox h3 {
            text-align: center;
            margin: 0 auto;
            border: 1px solid #000;
            background: #8eb1e5;
            padding: 10px;
        }

        .report .chartbox table {
            margin: 20px auto;
            border-collapse: collapse;
            border-spacing: 0;
            font-size: 14px;
        }

        .report .chartbox table tr th,
        .report .chartbox table tr td {
            padding: 5px 0;
            text-align: right;
            border: 1px solid #000;
        }

        .report .chartbox table tr th {
            background-color: #70a0eb;
        }

        .report .chartbox table tr td {
            font-weight: 300;
        }

        .report .chartbox table tr th:first-child,
        .report .chartbox table tr td:first-child {
            text-align: left;
        }
    </style>
</head>



<body>
    <div class="report">
        <div class="logo">
            <img id="logo_img" src="https://www.drsay.cn/theme/sv/img/2020-05-30/72cf4eaf7478d068c4f16f0ec92013f1.png" />
            <span id="logo_name"><span style="color: blue;">【医学文献】</span></span>
        </div>
        <div class="head">
            <h2>同行学习洞察统计分析结果</h2>
            <p>
                <img src="https://www.drsay.cn/theme/sv/img/2020-05-30/042119aa3926f837e066fddc3c50dfe8.png" />
            </p>
            <div class="line"></div>
        </div>

        <!-- 同行学习时间对照表折线图 -->
        <div class="line chartbox">
            <h3>同行学习时间对照表</h3>
            <table>
                <thead>
                    <tr>
                        <th>学术文章</th>
                        <th>我的用时(秒)</th>
                        <th>同行平均用时(秒)</th>
                    </tr>
                </thead>
                <tbody id="time_control_table">
                </tbody>
            </table>
            <div id="time_control" class="chart"></div>
            <script type="text/javascript">
                // 同行数据
                var data = [];
                var legend = [];
                var arr = JSON.parse('<?= json_encode($time_control, true); ?>');
                // 我的数据
                var my_data = [];
                var my_legend = [];
                var question_list = <?= json_encode($my_list, true); ?>;
                for (var item in arr) {
                    data.push(arr[item].value);
                    legend.push(item);

                    var answer_time = 0;
                    question_list.forEach(function(val, index) {
                        if (val.question_id == item) {
                            answer_time += parseInt(val.view_time_diffe);
                        }
                    });
                    my_data.push(answer_time);
                    my_legend.push(item);
                    document.getElementById('time_control_table').innerHTML += '<tr><td>' + item + '(' + arr[item].name + ')' + '</td><td>' + answer_time + '</td><td>' + arr[item].value + '</td></tr>';
                }
                // 同行学习时间对照表折线图
                var time_control_chart = echarts.init(
                    document.getElementById("time_control")
                );
                time_control_chart.setOption({
                    title: {
                        text: "同行学习时间对照表",
                        //  subtext: "虚构数据",
                        // left: "center",
                    },
                    legend: {
                        data: ["我的", "同行"],
                        top:'5%',
                    },
                    tooltip: {
                        trigger: "item",
                        formatter: "{a} <br/>{b} : {c}",
                    },
                    xAxis: {
                        type: "category",
                        data: legend,
                    },
                    yAxis: {
                        type: "value",
                        // max: 320,
                        // min: 280,
                    },
                    series: [{
                            name: "我的",
                            data: my_data,
                            type: "line",
                        },
                        {
                            name: "同行",
                            data: data,
                            type: "line",
                        },
                    ],
                });
            </script>
        </div>

        <?php foreach ($question_list as $key => $val) : ?>
            <div class="pei chartbox">
                <h3><?= $val['question_label'] ?></h3>
                <table>
                    <thead>
                        <tr>
                            <th>选项</th>
                            <th>数量</th>
                        </tr>
                    </thead>

                    <tbody>
                        <?php foreach ($val['list'] as $k => $v) : ?>
                            <tr>
                                <td><?= explode('（', $v['answer_label'])[0] ?></td>
                                <td><?= $v['count_size'] ?></td>
                            </tr>
                            <script>
                            </script>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div id="<?= $v['variable_id'] ?>_chart" class="chart"></div>
                <script>
                    // 医院类别统计柱形图
                    var <?= $v['variable_id'] ?>_chart = echarts.init(
                        document.getElementById("<?= $v['variable_id'] ?>_chart")
                    );

                    var x = [];
                    var y = [];
                    var s = [];
                    <?php foreach ($val['list'] as $k => $v) : ?>
                        x.push("<?= explode('（', $v['answer_label'])[0] ?>");
                        y.push("<?= $v['count_size'] ?>");
                    <?php endforeach; ?>

                    var option = {
                        title: {
                            text: "<?= $val['question_label'] ?>",
                            //  subtext: "虚构数据",
                            // left: "center",
                        },
                        tooltip: {},
                        legend: {
                            data: ["人数"],
                        },
                        xAxis: {
                            data: x,
                            axisLabel: {
                                interval: 0, // 坐标轴刻度标签的显示间隔
                                rotate: 0   // 标签倾斜的角度
                            },
                        },
                        yAxis: {},
                        series: [{
                            name: "",
                            type: "bar",
                            data: y,
                            itemStyle: {
                                color: "#8eb1e5",
                            },
                        }, ],
                    };
                    <?= $v['variable_id'] ?>_chart.setOption(option);
                </script>
            </div>
        <?php endforeach; ?>

        <!-- 地区分布饼图 -->
        <div class="pei chartbox">
            <h3>地区分布</h3>
            <table>
                <thead>
                    <tr>
                        <th>地区</th>
                        <th>人数</th>
                    </tr>
                </thead>
                <tbody id="area_table">
                </tbody>
            </table>
            <div id="area" class="chart"></div>
            <script>
                var data = [];
                var arr = JSON.parse('<?= json_encode($area, true); ?>');
                for (var item in arr) {
                    data.push({
                        value: arr[item],
                        name: item,
                    });
                    document.getElementById('area_table').innerHTML += '<tr><td>' + item + '</td><td>' + arr[item] + '</td></tr>';
                    // $('#area_table').append('<tr><td>'+item+'</td><td>'+arr[item]+'</td></tr>');
                }

                // 地区分布饼图
                var area_chart = echarts.init(document.getElementById("area"));
                area_chart.setOption({
                    title: {
                        text: "地区分布",
                        //  subtext: "虚构数据",
                        // left: "center",
                    },
                    tooltip: {
                        trigger: "item",
                        formatter: "{a} <br/>{b} : {c} ({d}%)",
                    },
                    series: [{
                        name: "地区分布",
                        type: "pie",
                        radius: "65%",
                        center: ["50%", "50%"],
                        selectedMode: "single",
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: "rgba(0, 0, 0, 0.5)",
                            },
                        },
                    }, ],
                });
            </script>
        </div>

        <!--  医院等级分布饼图 -->
        <div class="pei chartbox">
            <h3>医院等级分布</h3>
            <table>
                <thead>
                    <tr>
                        <th>医院等级</th>
                        <th>人数</th>
                    </tr>
                </thead>
                <tbody id="hospital_grade_table">
                </tbody>
            </table>
            <div id="hospital_grade" class="chart"></div>
            <script>
                var data = [];
                var legend = [];
                var arr = JSON.parse('<?= json_encode($hospital_grade, true); ?>');
                for (var item in arr) {
                    data.push({
                        value: arr[item],
                        name: item,
                    });
                    document.getElementById('hospital_grade_table').innerHTML += '<tr><td>' + item + '</td><td>' + arr[item] + '</td></tr>';
                    legend.push(item);
                }
                console.log(data);

                //  医院等级分布饼图
                var hospital_grade_chart = echarts.init(
                    document.getElementById("hospital_grade")
                );
                hospital_grade_chart.setOption({
                    title: {
                        text: "医院等级分布",
                        //  subtext: "虚构数据",
                        // left: "center",
                    },
                    tooltip: {
                        trigger: "item",
                        formatter: "{a} <br/>{b} : {c} ({d}%)",
                    },
                    legend: {
                        bottom: 10,
                        // left: "center",
                        data: legend,
                    },
                    series: [{
                        name: "医院等级分布",
                        type: "pie",
                        radius: "65%",
                        center: ["50%", "50%"],
                        selectedMode: "single",
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: "rgba(0, 0, 0, 0.5)",
                            },
                        },
                    }, ],
                });
            </script>
        </div>

        <!--  医师职称分布饼图 -->
        <div class="pei chartbox">
            <h3>医师职称分布</h3>
            <table>
                <thead>
                    <tr>
                        <th>医师职称</th>
                        <th>比例</th>
                    </tr>
                </thead>

                <tbody id="doctor_title_table">
                </tbody>
            </table>
            <div id="doctor_title" class="chart"></div>
            <script>
                var data = [];
                var legend = [];
                var arr = JSON.parse('<?= json_encode($doctor_title, true); ?>');
                for (var item in arr) {
                    data.push({
                        value: arr[item],
                        name: item,
                    });
                    document.getElementById('doctor_title_table').innerHTML += '<tr><td>' + item + '</td><td>' + arr[item] + '</td></tr>';
                    legend.push(item);
                }
                console.log(data);

                //  医师职称分布饼图
                var doctor_title_chart = echarts.init(
                    document.getElementById("doctor_title")
                );
                doctor_title_chart.setOption({
                    title: {
                        text: "医师职称分布",
                        //  subtext: "虚构数据",
                        // left: "center",
                    },
                    tooltip: {
                        trigger: "item",
                        formatter: "{a} <br/>{b} : {c} ({d}%)",
                    },
                    legend: {
                        bottom: 10,
                        // left: "center",
                        data: legend,
                    },
                    series: [{
                        name: "医师职称分布",
                        type: "pie",
                        radius: "65%",
                        center: ["50%", "50%"],
                        selectedMode: "single",
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: "rgba(0, 0, 0, 0.5)",
                            },
                        },
                    }, ],
                });
            </script>
        </div>

        <!--  客户统计饼图 -->
        <!-- <div class="pei chartbox">
            <h3>客户统计</h3>
            <table>
                <thead>
                    <tr>
                        <th>地区</th>
                        <th>人数</th>
                    </tr>
                </thead>
                <tbody id="customer_table">
                </tbody>
            </table>
            <div id="customer" class="chart"></div>
            <script>
                var data = [];
                var legend = [];
                var arr = JSON.parse('<?= json_encode($area, true); ?>');
                for(var item in arr){
                    data.push(arr[item]);
                    document.getElementById('customer_table').innerHTML +='<tr><td>'+item+'</td><td>'+arr[item]+'</td></tr>';
                    // $('#area_table').append('<tr><td>'+item+'</td><td>'+arr[item]+'</td></tr>');
                    legend.push(item);
                }
                //  客户统计饼图
                var customer_chart = echarts.init(document.getElementById("customer"));
                customer_chart.setOption({
                    title: {
                        text: "客户统计",
                        //  subtext: "虚构数据",
                        // left: "center",
                    },
                    series: [{
                        name: "客户统计",
                        type: "bar",
                        radius: "65%",
                        center: ["50%", "50%"],
                        selectedMode: "single",
                        data: data,
                        itemStyle: {
                            color: "#8eb1e5",
                        },
                    }, ],
                    
                    tooltip: {},
                    xAxis: {
                        data: legend,
                    },
                    yAxis: {},
                    series: [{
                        name: "",
                        type: "bar",
                        data: data,
                        itemStyle: {
                            color: "#8eb1e5",
                        },
                    }],
                });
                
            </script>
        </div> -->

        <!-- 医院等级统计柱形图 -->
        <div class="bar chartbox">
            <h3>医院等级统计</h3>
            <table>
                <thead>
                    <tr>
                        <th>医院</th>
                        <th>数量</th>
                    </tr>
                </thead>

                <tbody id="grade_statistics_table">
                </tbody>
            </table>
            <div id="grade_statistics" class="chart"></div>
            <script>
                var data = [];
                var legend = [];
                var arr = JSON.parse('<?= json_encode($hospital_grade, true); ?>');
                for (var item in arr) {
                    data.push(arr[item]);
                    document.getElementById('grade_statistics_table').innerHTML += '<tr><td>' + item + '</td><td>' + arr[item] + '</td></tr>';
                    legend.push(item);
                }
                console.log(data);
                //   医院等级统计柱形图
                var grade_statistics_chart = echarts.init(
                    document.getElementById("grade_statistics")
                );
                var option = {
                    title: {
                        text: "医院等级统计",
                        // left: "center",
                    },
                    tooltip: {},
                    xAxis: {
                        data: legend,
                    },
                    yAxis: {},
                    series: [{
                        name: "",
                        type: "bar",
                        data: data,
                        itemStyle: {
                            color: "#8eb1e5",
                        },
                    }, ],
                };
                grade_statistics_chart.setOption(option);
            </script>
        </div>

        <!--  城市等级饼图 -->
        <!-- <div class="pei chartbox">
            <h3>城市等级</h3>
            <table>
                <thead>
                    <tr>
                        <th>城市等级</th>
                        <th>数量</th>
                    </tr>
                </thead>

                <tbody id="city_level_table">
                </tbody>
            </table>
            <div id="city_level" class="chart"></div>
            <script>
                var data = [];
                var legend = [];
                var arr = JSON.parse('<?= json_encode($city_level, true); ?>');
                for(var item in arr){
                    data.push({
                        value:arr[item],
                        name:item,
                    });
                    document.getElementById('city_level_table').innerHTML +='<tr><td>'+item+'</td><td>'+arr[item]+'</td></tr>';
                    legend.push(item);
                    // $('#area_table').append('<tr><td>'+item+'</td><td>'+arr[item]+'</td></tr>');
                }
                // 城市等级饼图
                var city_level_chart = echarts.init(
                    document.getElementById("city_level")
                );
                city_level_chart.setOption({
                    title: {
                        text: "城市等级",
                        //  subtext: "虚构数据",
                        // left: "center",
                    },
                    tooltip: {
                        trigger: "item",
                        formatter: "{a} <br/>{b} : {c} ({d}%)",
                    },
                    legend: {
                        bottom: 10,
                        // left: "center",
                        data: legend,
                    },
                    series: [{
                        name: "城市等级",
                        type: "pie",
                        radius: "65%",
                        center: ["50%", "50%"],
                        selectedMode: "single",
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: "rgba(0, 0, 0, 0.5)",
                            },
                        },
                    }, ],
                });
            </script>
        </div> -->

        <!--  医院类别统计柱形图 -->
        <!-- <div class="pei chartbox">
            <h3>医院类别统计</h3>
            <table>
                <thead>
                    <tr>
                        <th>医院类别</th>
                        <th>数量</th>
                    </tr>
                </thead>

                <tbody id="unit_type_table">
                </tbody>
            </table>
            <div id="unit_type" class="chart"></div>
            <script>
                var data = [];
                var legend = [];
                var arr = JSON.parse('<?= json_encode($unit_type, true); ?>');
                for(var item in arr){
                    data.push(arr[item]);
                    document.getElementById('unit_type_table').innerHTML +='<tr><td>'+item+'</td><td>'+arr[item]+'</td></tr>';
                    legend.push(item);
                }
                console.log(data);
                // 医院类别统计柱形图
                var unit_type_chart = echarts.init(
                    document.getElementById("unit_type")
                );
                var option = {
                    title: {
                        text: "医院类别统计",
                        // left: "center",
                    },
                    tooltip: {},
                    xAxis: {
                        data: legend,
                    },
                    yAxis: {},
                    series: [{
                        name: "",
                        type: "bar",
                        data: data,
                        itemStyle: {
                            color: "#8eb1e5",
                        },
                    }, ],
                };
                unit_type_chart.setOption(option);
            </script>
        </div> -->

    </div>

</body>

</html>