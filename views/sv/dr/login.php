<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="Cache-Control" content="no-cache" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>登录</title>
    <script src="/theme/survey/js/jquery-3.4.1.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
            background: #efefef;
        }

        .login {
            width: 98%;
            height: 100%;
            background: #fff;
            margin: 0 auto;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            padding-bottom: 100px;
        }

        @media screen and (min-width: 750px) {
            .login {
                width: 360px;
                height: 90%;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        .login .head {
            width: 100%;
            height: 190px;
            position: relative;
        }

        .login .head .logo {
            width: 118px;
            height: 118px;
            border-radius: 50%;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 65px;
            background-color: #fff;
            z-index: 9;
            overflow: hidden;
        }

        .login .head .logo img {
            width: 120px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .login .head::before {
            content: "";
            width: 100%;
            height: 130px;
            background: linear-gradient(to right, #8f69f0 0%, #336ffe 100%);
            position: absolute;
            left: 0;
            top: 0;
            z-index: 9;
        }

        .login .head::after {
            content: "";
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(to right, #8f69f0 0%, #336ffe 100%);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 10px;
        }

        .login .title {
            text-align: center;
            padding: 30px;
            font-size: 28px;
            color: #06407e;
            font-weight: 700;
        }

        .login .input {
            width: 70%;
            margin: 0 auto;
            margin-bottom: 40px;
        }

        .login .input input:first-child {
            margin-bottom: 40px;
        }

        .login .input input {
            padding-left: 15px;
            width: 100%;
            height: 30px;
            border: none;
            outline: none;
            font-size: 20px;
            border-radius: 0;
            -webkit-appearance: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            border-bottom: 1px solid #8f69f0;
        }

        .login .input span {
            float: right;
            font-size: 14px;
            cursor: pointer;
            margin-top: 10px;
            color: #8f69f0;
        }

        .login .btn {
            width: 70%;
            margin: 0 auto;
            position: relative;
        }

        .login .btn .tips {
            height: 20px;
            line-height: 10px;
            color: red;
            text-align: center;
            font-size: 16px;
        }

        .login .btn button {
            cursor: pointer;
            width: 100%;
            height: 50px;
            line-height: 50px;
            border-radius: 15px;
            outline: none;
            border: none;
            background: linear-gradient(to right, #8f69f0 0%, #336ffe 100%);
            color: #fff;
            font-size: 18px;
            position: absolute;
            z-index: 999999;
        }

        .login .footer {
            width: 100%;
            height: 80px;
            text-align: center;
            color: #fff;
            font-size: 12px;
            position: absolute;
            bottom: -20px;
            left: 0;
            overflow: hidden;
        }

        .login .footer .copy {
            height: 140px;
            background: linear-gradient(to right, #8f69f0 0%, #336ffe 100%);
            border-radius: 50%;
            line-height: 80px;
        }
    </style>
</head>

<body>
    <div class="login">
        <div class="head">
            <div class="logo">
                <img src="/theme/survey/img/logo.png" alt="" />
            </div>
        </div>

        <div class="title">健康通</div>

        <div class="input">
            <input id="mobile" type="text" placeholder="手机号" />
            <input id="vcode" type="text" placeholder="验证码" />
            <span id="send_btn" onclick="send_code();">获取验证码</span>
        </div>

        <div class="btn">
            <p class="tips"></p>
            <button id="smt_btn" onclick="check_code();">进入问卷</button>
        </div>


        <div class="footer">
            <div class="copy"> Copyright © <?= date('Y') ?> 健康通网络科技有限公司.</div>
        </div>
    </div>
</body>
<script>
    var timmer;
    const wait = 59;
    var second = wait;

    function send_code() {
        var mobile = $('#mobile').val();
        if($('#send_btn').html()!='获取验证码'){
            return false;
        }

        if (second < wait) {
            return false;
        }

        if (!/^1[3456789]{1}\d{9}$/.test(mobile)) {
            $('.tips').html('手机号码不正确！');
            return false;
        } else {
            $('.tips').html('');
        }
        $('#send_btn').html('等待发送中...');

        $.ajax({
            url: "/sv/dr/send_code",
            type: "post",
            dataType: "json",
            data: {
                'mobile': mobile,
            },
            success: function(data) {
                if (data.err_msg == 'success') {
                    timmer = setInterval(() => {
                        if (second > 0) {
                            $('#send_btn').html('重新发送(' + second + '秒)');
                            second--;
                        } else {
                            $('#send_btn').html('获取验证码');
                            clearInterval(timmer);
                            second = wait;
                        }
                    }, 1000);
                } else {
                    $('.tips').html(data.result).show();
                }
            }
        });
    }

    function check_code() {

        var mobile = $('#mobile').val();
        var vcode = $('#vcode').val();
        if (!mobile) {
            $('.tips').html('请输入手机号码！');
            return false;
        }
        if (!vcode) {
            $('.tips').html('请输入验证码！');
            return false;
        }

        $.ajax({
            url: "/sv/dr/check_code",
            type: "post",
            dataType: "json",
            data: {
                'mobile': mobile,
                'vcode': vcode,
            },
            success: function(data) {
                if (data.err_msg == 'success') {
                    location.href = '/sv/dr/project/<?= $project_id ?>?mobile=' + mobile;
                } else {
                    $('.tips').html(data.result).show();
                }
            }
        });
    }
</script>

</html>