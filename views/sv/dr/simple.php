<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <link id="suit_css" rel="stylesheet" href="" />
    <title>调查问卷</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: rgba(0, 0, 0, 0.05);
            color: #000;
        }

        ul li {
            list-style: none;
        }

        input[type="text"] {
            border: none;
            outline: none;
            -webkit-appearance: none;
            -webkit-tap-highlight-color: rgba(255, 255, 255, 255);
        }

        textarea,
        button {
            border: none;
            outline: none;
        }

        textarea {
            resize: none;
        }

        .label input[type="checkbox"],
        .label input[type="radio"] {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 问卷简介 -->
        <div class="brief">
            <img class="logo" src="/theme/survey_n/img/logo1.png" alt="" />
            <h4>问卷标题</h4>
            <p>
                问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述
                问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述问卷描述
            </p>
            <img class="next" src="/theme/survey_n/img/next.png" alt="" />
        </div>

        <!-- 内容 -->
        <div class="content">
            <!-- 进度条 -->
            <div class="progress_box">
                <div class="progress">
                    <div class="rate"></div>
                </div>
            </div>

            <!-- 题目列表 -->
            <div class="question_list">
                <!-- 单选题 -->
                <section class="question single">
                    <div class="question_head">
                        <div class="question_title">
                            <span class="number"><b>S/N</b></span>
                            <span class="text">单选题</span>
                            <span style="color: red">*</span>
                        </div>
                        <div class="question_desc">题目描述</div>
                    </div>

                    <div class="question_body">
                        <label class="label">
                            <span class="outer"></span>
                            <input type="radio" value="1" />选项一
                        </label>
                        <label class="label">
                            <span class="outer"></span>
                            <input type="radio" value="2" />选项二
                        </label>
                        <label class="label">
                            <span class="outer"></span>
                            <input type="radio" value="2" />选项三
                        </label>
                    </div>
                </section>

                <!-- 多选题 -->
                <section class="question multi">
                    <div class="question_head">
                        <div class="question_title">
                            <span class="number"><b>S/N</b></span>
                            <span class="text">多选题</span>
                            <span style="color: red">*</span>
                        </div>
                        <div class="question_desc">题目描述</div>
                    </div>

                    <div class="question_body">
                        <label class="label">
                            <span class="outer"></span>
                            <input type="checkbox" value="1" />选项一
                        </label>
                        <label class="label">
                            <span class="outer"></span>
                            <input type="checkbox" value="2" />选项二
                        </label>
                        <label class="label">
                            <span class="outer"></span>
                            <input type="checkbox" value="2" />选项三
                        </label>
                    </div>
                </section>

                <!-- 打分题 -->
                <section class="question scoring">
                    <div class="question_head">
                        <div class="question_title">
                            <span class="number"><b>S/N</b></span>
                            <span class="text">打分题</span>
                            <span style="color: red">*</span>
                        </div>
                        <div class="question_desc">题目描述</div>
                    </div>

                    <div class="question_body">
                        <p>
                            <span>非常不满意</span>
                            <span>非常满意</span>
                        </p>
                        <div class="grade">
                            <span data-grade="0">0</span>
                            <span data-grade="1">1</span>
                            <span data-grade="2">2</span>
                            <span data-grade="3">3</span>
                            <span data-grade="4">4</span>
                            <span data-grade="5">5</span>
                            <span data-grade="6">6</span>
                            <span data-grade="7">7</span>
                            <span data-grade="8">8</span>
                            <span data-grade="9">9</span>
                            <span data-grade="10">10</span>
                        </div>
                    </div>
                </section>

                <!-- 开放文本题目 -->
                <section class="question opentextarea">
                    <div class="question_head">
                        <div class="question_title">
                            <span class="number"><b>S/N</b></span>
                            <span class="text">开放文本题目</span>
                            <span style="color: red">*</span>
                        </div>
                        <div class="question_desc">题目描述</div>
                    </div>

                    <div class="question_body">
                        <textarea class="inputs_textarea" rows="3" placeholder="请输入"></textarea>
                    </div>
                </section>

                <!-- 多选输入题目 -->
                <section class="question selectinput multi">
                    <div class="question_head">
                        <div class="question_title">
                            <span class="number"><b>S/N</b></span>
                            <span class="text">多选输入题目</span>
                            <span style="color: red">*</span>
                        </div>
                        <div class="question_desc">题目描述</div>
                    </div>

                    <div class="question_body">
                        <label class="label">
                            <span></span><input type="checkbox" /> 选择一
                            (如有特别喜欢，可填<input type="text" />)
                        </label>
                        <label class="label">
                            <span></span><input type="checkbox" />选择二
                            (如有特别喜欢，可填<input type="text" />)
                        </label>
                        <label class="label">
                            <span></span> <input type="checkbox" />选择三
                            (如有特别喜欢，可填<input type="text" />)
                        </label>
                        <label class="label">
                            <span></span> <input type="checkbox" />选择四
                            (如有特别喜欢，可填<input type="text" />)
                        </label>
                    </div>
                </section>
            </div>

            <!-- 提交按钮 -->
            <div class="page_control">
                <button class="btn btn_submit"><span>提交</span></button>
            </div>
        </div>
        <div class="footer"></div>
    </div>
    <script src="/theme/survey/js/jquery-3.4.1.min.js"></script>
    <script id="suit_js"></script>
    <script>
        // 适应
        if ($(window).width() > 750) {
            $("#suit_css").attr("href", "/theme/survey_n/css/pc.css");
            $("#suit_js").attr("src", "/theme/survey_n/js/pc.js");
        } else {
            $("#suit_css").attr("href", "/theme/survey_n/css/mobile.css");
            $("#suit_js").attr("src", "/theme/survey_n/js/mobile.js");
        }
    </script>
</body>

</html>