<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>医来说</title>

    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
<!--    <link href="/theme/new_manage/css/bootstrap.css" rel="stylesheet">-->
    <style>
        .dataTables_paginate{float: right;}
    </style>
</head>

<body>

<div id="wrapper">
    <div class="gray-bg" id="page-wrapper" style="margin: 0px;!important;">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                <ul class="nav navbar-top-links navbar-left">
                    <li>
                        <h3 style="color: #fff;font-size: 28px;margin-left: 20px;/*margin-top: 15px;*/line-height: 40px;">医来说<span style=" margin-left:6px;font-size:12px; color:#00CCCC;">让更有价值的人获得更多机会！</span></h3>
                    </li>
                </ul>

                <ul class="nav navbar-top-links navbar-right">
                    <li>
                        <a href="/i_survey_adm/out">
                            <i class="fa fa-sign-out"></i><?php echo $this->session->userdata("i_survey_adm_name");?> Log out
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight" style="margin-top: -10px;">
            <div class="row">
                <div class="col-lg-12" style="padding-right: 0px;padding-left: 0px; border:0px solid #FFFFFF;">
                    <div class="tabs-container" style="padding-left:0px; padding-right:0px; margin-left:0px; margin-right:0px; font-size:12px;">
                        <ul class="nav nav-tabs">
                            <li class="active"><a href="/i_survey_adm/index">在线调研</a></li>
                            <li><a href="/i_survey_adm/i_survey_user">集团账号</a></li>
                        </ul>
                        <div class="tab-content" style="padding-left:0px; padding-right:0px; margin-left:0px; margin-right:0px; font-size:12px;">
                            <div class="tab-pane active">
                                <div class="panel-body" style="padding-left:0px; padding-right:0px; margin-left:0px; margin-right:0px; font-size:12px;">
                                    <form onsubmit="return common_js.form_sumbit(this,'show_msg','处理中','post','');">
                                        <table class="table">
                                            <thead>
            <!--                                编号  题目  需求量  完成量  结束时间  单价  答卷礼金（输入框）   推荐礼金（输入框）   状态-->
                                            <tr>
                                                <th style="width:40px;">编号</th>
                                                <th style="width:140px;">编号</th>
                                                <th style="width:140px;">项目名称</th>
                                                <th>客户</th>
                                                <th>销售</th>
                                                <th style="width:70px;">需求</th>
                                                <th style="width:70px;">完成</th>
                                                <th style="width:130px;">结束</th>
                                                <th style="width:90px;">单价</th>
                                                <th style="width:90px;">答卷礼金</th>
                                                <th style="width:90px;">推荐礼金</th>
                                                <th style="width:60px;">状态</th>
                                                <th style="width:200px;">操作</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php if(!empty($project_list)){
                                                $i = 1;
                                                foreach ($project_list as $v){
                                                    //剩余天数
                                                    $days = $v['pro_end_time'] - time();
                                                    $surplus_day = 0;
                                                    if ($days > 0) {
                                                        $surplus_day = ceil($days/86400);
                                                    }
                                                    ?>
                                                    <tr>
                                                        <td><?php echo $offset+($i++);?></td>
                                                        <td><?php echo cus_pid($v['id'])?></td>
                                                        <td><?php echo $v['pro_name']?></td>
                                                        <td><?php echo $v['client_name']?></td>
                                                        <td><?php echo $admin_list[$v['sales_id']]?></td>
                                                        <td><?php echo $v['pro_sample_num'];?></td>
                                                        <td><?php echo $v['c_num'];?></td>
                                                        <td><?php echo date("Y-m-d", $v['pro_end_time']);?></td>
                                                        <td><?php echo $v['pro_price'];?></td>
                                                        <td>
                                                            <input type="text" class="form-control" placeholder="" name="answer_question_money[<?php echo $v['id'];?>]" value="<?php echo $v['answer_question_money'] ? $v['answer_question_money'] : "";?>">
                                                        </td>
                                                        <td>
                                                            <input type="text" class="form-control" placeholder="" name="recommend_money[<?php echo $v['id'];?>]" value="<?php echo $v['recommend_money'] ? $v['recommend_money'] : "";?>">
                                                        </td>
                                                        <td><?php echo $project_status[$v['pro_status']];?></td>
                                                        <td>
                                                            <span onclick="change_show(<?php echo $v['id'];?>)">
                                                                <input type="checkbox" style="width:50px;" class="js-switch form-control is_show_<?php echo $v['is_request_support'];?>" <?php echo $v['is_request_support'] == 1 ? "checked" : "";?> />
                                                            </span>
                                                            <a title="点击查看详情或处理" data-toggle="modal" data-target="#edit_info"  href="javascript:;">需求</a>
                                                        </td>
                                                    </tr>
                                                <?php } ?>
                                            <?php } else { ?>
                                                <tr><td colspan="13" style="text-align: center"><code>暂无数据!</code></td></tr>
                                            <?php } ?>
                                            </tbody>
                                            <tfoot>
                                            <tr>
                                                <td colspan="13">
                                                    <div class="col-sm-1" style="margin-top: 7px;margin-left: -10px;">
                                                        <div class="input-group m-b">
                                                            <div class="input-group-btn">
                                                                <input type="hidden" name="back_url" value="<?php echo $_SERVER['REQUEST_URI']?>" />
                                                                <input type="hidden" name="act" value="edit_project" />
                                                                <button class="btn btn-warning" type="submit">批量提交</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div align="right" style="padding-top:10px; margin-right:5px;"><?php echo $pagination?></div>
                                                </td>
                                            </tr>
                                            </tfoot>
                                        </table>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
  </div>

        <div class="bottom">
            <div class="footer">
                <div>
                    <strong>Copyright</strong> © 2014 - <?php echo date('Y');?> elsay Limited All Rights Reserved.
                </div>
            </div>
        </div>

    </div>

</div>

<!--需求-->
<div class="modal inmodal fade" id="edit_info" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="width:1200px; font-size:13px;">
        <input type="hidden" name="back_url" value="<?php echo $_SERVER['REQUEST_URI']?>" />
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span
                            aria-hidden="true">&times;</span><span
                            class="sr-only">Close</span></button>
                <h4 class="modal-title">【HCP0007545：关于神经内分泌瘤的调查-定量】项目需求</h4>
                <small class="font-bold" style="color:#FF0000;">
                    注意:项目需求请按以下要求进行样本采集
                </small>
            </div>
            <div class="modal-body">
                <fieldset>
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label lable-title">需求文档：</label>
                        <div class="col-sm-2">下载</div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label lable-title">样本配额：</label>
                        <div class="col-sm-2">下载</div>
                    </div>
                </fieldset>
            </div>
        </div>
    </div>
</div>

<!-- Mainly scripts -->
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="/theme/new_manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/plugins/peity/jquery.peity.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/new_manage/js/inspinia.js"></script>
<script src="/theme/new_manage/js/plugins/pace/pace.min.js"></script>

<!-- iCheck -->
<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/demo/peity-demo.js"></script>

<link href="/theme/new_manage/css/plugins/switchery/switchery.css" rel="stylesheet">
<!-- Switchery -->
<script src="/theme/new_manage/js/plugins/switchery/switchery.js"></script>


<!-- Sweet alert -->
<script src="/theme/new_manage/js/plugins/sweetalert/sweetalert.min.js"></script>


<script src="/theme/new_manage/assets/js/jquery.form.js"></script>
<script src="/theme/new_manage/assets/js/comm_init_swal.js"></script>

<script>
    $(document).ready(function(){
        var elem = document.querySelectorAll('.js-switch');
        $.each(elem,function(k, html){
            var switchery = new Switchery(html,{color: '#1AB394', size:"small"});
        });

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });

    //是否需要外部支持
    function change_show(id)
    {
        $.ajax({
            type: "POST",
            url: "/i_survey_adm/index",
            data: "id="+id+"&act=change_show",
            success: function(msg){
                console.log(msg);
            }
        });
    }
</script>

</body>

</html>
