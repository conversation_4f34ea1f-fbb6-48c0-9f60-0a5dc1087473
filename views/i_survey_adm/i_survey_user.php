<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>医来说</title>

    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
    <!--    <link href="/theme/new_manage/css/bootstrap.css" rel="stylesheet">-->
    <style>
        .dataTables_paginate {
            float: right;
        }
    </style>
</head>

<body>

<div id="wrapper">
    <div class="gray-bg" id="page-wrapper" style="margin: 0px;!important;">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                <ul class="nav navbar-top-links navbar-left">
                    <li>
                        <h3 style="color: #fff;font-size: 28px;margin-left: 20px;/*margin-top: 15px;*/line-height: 40px;">
                            医来说<span style=" margin-left:6px;font-size:12px; color:#00CCCC;">让更有价值的人获得更多机会！</span></h3>
                    </li>
                </ul>

                <ul class="nav navbar-top-links navbar-right">
                    <li>
                        <a href="/i_survey_adm/out">
                            <i class="fa fa-sign-out"></i><?php echo $this->session->userdata("i_survey_adm_name"); ?>
                            Log out
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight" style="margin-top: -10px;">
            <div class="row">
                <div class="col-lg-12" style="padding-right: 0px;padding-left: 0px; border:0px solid #FFFFFF;">
                    <div class="tabs-container"
                         style="padding-left:0px; padding-right:0px; margin-left:0px; margin-right:0px; font-size:12px;">
                        <ul class="nav nav-tabs">
                            <li><a href="/i_survey_adm/index">在线调研</a></li>
                            <li class="active"><a href="/i_survey_adm/i_survey_user">集团账号</a></li>
                        </ul>
                        <div class="tab-content"
                             style="padding-left:0px; padding-right:0px; margin-left:0px; margin-right:0px; font-size:12px;">
                            <div class="tab-pane active">
                                <div class="panel-body"
                                     style="padding-left:0px; padding-right:0px; margin-left:0px; margin-right:0px; font-size:12px;">
                                    <form onSubmit="return common_js.form_sumbit(this,'show_msg','处理中','post','');">
                                        <table class="table table-stripped table-bordered">

                                            <thead>
                                            <tr>
                                                <th colspan="27" style="width:20px;text-align: right;">
                                                    <input type="hidden" name="back_url" value="<?php echo $_SERVER['REQUEST_URI']?>" />
                                                    <button id="smt_btn" class="btn btn-primary btn-sm" type="submit">
                                                        <i class="fa fa-check"></i>&nbsp;Submit</button>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th style="width:20px;"><input type="checkbox" name=""
                                                                               class="i-checks select_modify"></th>
                                                <th>执业医院</th>
                                                <th>医院等级</th>
                                                <th>所在科室</th>
                                                <th>职称</th>
                                                <th>身份证</th>
                                                <th>资格证书</th>
                                                <th>执业证书</th>
                                                <th>手机号</th>
                                                <th>支付宝姓名</th>
                                                <th>支付宝账号</th>
                                                <th>名称</th>
                                                <th>账号</th>
                                                <th>密码</th>
                                                <th>专区</th>
                                                <th>项目</th>
                                                <th>完成</th>
                                                <th>有效</th>
                                                <th>预收</th>
                                                <th>实收</th>
<!--                                                <th>状态</th>-->
                                                <th>
                                                    <div class="html5buttons">
                                                        <div class="dt-buttons btn-group">
                                                            <a class="btn btn-default buttons-pdf buttons-html5"
                                                               tabindex="0"
                                                               aria-controls="DataTables_Table_0"><span>添加</span></a>
                                                        </div>
                                                    </div>
                                                </th>

                                            </tr>
                                            </thead>
                                            <tbody>
                                                <?php if($list){
                                                    $now_time = time();
                                                    $i_survey_adm_id = $this->session->userdata("i_survey_adm_id");
                                                ?>
                                                    <?php foreach($list as $v){
                                                        //快捷进入问卷列表的地址
                                                        $encode_str = substr(md5("{$v['code']}||{$v['password']}||{$now_time}||{$i_survey_adm_id}||". PROJECT_ENCODE_KEY), 8, 16);
                                                        $code = base64_encode("{$v['code']}||{$v['password']}||{$now_time}||{$i_survey_adm_id}||{$encode_str}");
                                                    ?>
                                                        <tr>
                                                            <td><input style="font-size:11px;" type="checkbox" name="" class="i-checks checked_input select_modify"></td>
                                                            <td><?php echo $v['hospital'];?></td>
                                                            <td><?php echo $v['hospital_level'];?></td>
                                                            <td><?php echo $v['department'];?></td>
                                                            <td><?php echo $v['job_title'];?></td>
                                                            <td><?php echo $v['id_card'];?></td>
                                                            <td><?php echo $v['credentials_no'];?></td>
                                                            <td><?php echo $v['license_no'];?></td>
                                                            <td>
                                                                <input style="font-size:11px;" type="text" class="form-control" name="i_survey_user_old[<?php echo $v['id'];?>][mobile]" placeholder="手机号" value="<?php echo $v['mobile'];?>" />
                                                            </td>
                                                            <td>
                                                                <input style="font-size:11px;" type="text" class="form-control" name="i_survey_user_old[<?php echo $v['id'];?>][payment_name]" placeholder="支付宝姓名" value="<?php echo $v['payment_name'];?>" />
                                                            </td>
                                                            <td>
                                                                <input style="font-size:11px;" type="text" class="form-control" name="i_survey_user_old[<?php echo $v['id'];?>][payment_account]" placeholder="支付宝账号" value="<?php echo $v['payment_account'];?>" />
                                                            </td>
                                                            <td>
                                                                <input style="font-size:11px;" type="text" class="form-control" name="i_survey_user_old[<?php echo $v['id'];?>][name]" placeholder="真实姓名" value="<?php echo $v['name'];?>" />
                                                            </td>
                                                            <td>
                                                                <?php echo $v['code'];?>
                                                            </td>
                                                            <td>
                                                                <input style="font-size:11px;" type="text" class="form-control" name="i_survey_user_old[<?php echo $v['id'];?>][password]" placeholder="密码" value="<?php echo $v['password'];?>" />
                                                            </td>
                                                            <td>
                                                                <?php if($v['status'] == 1){?>
                                                                <a href="/i_survey/login?code=<?php echo $code;?>" target="_blank">进入</a>
                                                                <?php }?>
                                                            </td>
                                                            <td><?php echo $i_survey_c[$v['id']]['pid_num'];?></td>
                                                            <td><?php echo $i_survey_c[$v['id']]['complete_c'];?></td>
                                                            <td><?php echo $i_survey_c[$v['id']]['finish_c'];?></td>
                                                            <td><?php echo $i_survey_c[$v['id']]['estimated_revenue'];?></td>
                                                            <td><?php echo $i_survey_c[$v['id']]['real_income'];?></td>
                                                            <td>
                                                                <span onClick="change_show(<?php echo $v['id'];?>)">
                                                                    <input type="checkbox" style="width:50px;" class="js-switch form-control is_show_<?php echo $v['status'];?>" <?php echo $v['status'] == 1 ? "checked" : "";?> />
                                                                </span>
<!--                                                                <button class="btn btn-white" onClick="delete_i_survey();"><i class="fa fa-trash"></i></button>-->
                                                            </td>
                                                        </tr>
                                                    <?php }?>
                                                <?php }?>
                                                <!--分页-->
                                                <tr>
                                                    <td><input type="checkbox" name="" class="i-checks select_modify"></td>
                                                    <td colspan="27">
                                                        <div class="html5buttons">
                                                            <div class="dt-buttons btn-group">
                                                                <?php echo $pagination;?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <!--分页-->
                                            </tbody>
                                        </table>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <div class="bottom">
            <div class="footer">
                <div>
                    <strong>Copyright</strong> © 2014 - <?php echo date('Y'); ?> elsay Limited All Rights Reserved.
                </div>
            </div>
        </div>

    </div>
</div>


<table class="prepend_content" style="display:none">
    <tr>
        <td><input style="font-size:11px;" type="checkbox" name="" class="i-checks checked_input select_modify"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td><input style="font-size:11px;" type="text" class="form-control" name="mobile" placeholder="手机号"></td>
        <td><input style="font-size:11px;" type="text" class="form-control" name="payment_name" placeholder="支付宝姓名"></td>
        <td><input style="font-size:11px;" type="text" class="form-control" name="payment_account" placeholder="支付宝账号"></td>
        <td><input style="font-size:11px;" type="text" class="form-control" name="name" placeholder="真实姓名"></td>
        <td><input style="font-size:11px;" type="text" class="form-control" name="code" placeholder="账号" onKeyUp="value=value.replace(/[^\w\.\/]/ig,'')"></td>
        <td><input style="font-size:11px;" type="text" class="form-control" name="password" placeholder="密码" value=""></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
        <td></td>
    </tr>
</table>
<!-- Mainly scripts -->
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="/theme/new_manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/plugins/peity/jquery.peity.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/new_manage/js/inspinia.js"></script>
<script src="/theme/new_manage/js/plugins/pace/pace.min.js"></script>

<!-- iCheck -->
<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/demo/peity-demo.js"></script>

<link href="/theme/new_manage/css/plugins/switchery/switchery.css" rel="stylesheet">
<!-- Switchery -->
<script src="/theme/new_manage/js/plugins/switchery/switchery.js"></script>


<!-- Sweet alert -->
<script src="/theme/new_manage/js/plugins/sweetalert/sweetalert.min.js"></script>


<script src="/theme/new_manage/assets/js/jquery.form.js"></script>
<script src="/theme/new_manage/assets/js/comm_init_swal.js"></script>

<script>
    $(document).ready(function () {
        var elem = document.querySelectorAll('.js-switch');
        $.each(elem,function(k, html){
            var switchery = new Switchery(html,{color: '#1AB394', size:"small"});
        });

        //  指定列name、id和event的改变
        function change(num, change_name, change_id, event, change_event, value) {
            $(".prepend_content tbody tr td:nth-child(" + num + ")").children().attr("name", change_name)
            $(".prepend_content tbody tr td:nth-child(" + num + ")").children().attr("id", change_id)
            if (value) {
                $(".prepend_content tbody tr td:nth-child(" + num + ")").children().attr("value", value)
            }
            if (event, change_event) {
                $(".prepend_content tbody tr td:nth-child(" + num + ")").children().attr(event, change_event)
            }
        }

        <?php if(!$list){?>
        //  默认的一行
        $(".tab-content table").prepend(
            $(".prepend_content tbody").html()
        );
        <?php }?>

        var index = 1
        //  点击增加添加一行
        $("table thead .html5buttons div").click(function () {
            index++
            change(2, "i_survey_user_new[" + index + "][mobile]", "i_survey_user_new_mobile" + index + "", "", "");
            change(3, "i_survey_user_new[" + index + "][payment_name]", "i_survey_user_new_payment_name" + index + "", "", "");
            change(4, "i_survey_user_new[" + index + "][payment_account]", "i_survey_user_new_payment_account" + index + "", "", "");
            change(5, "i_survey_user_new[" + index + "][name]", "i_survey_user_new_name" + index + "", "", "");
            change(6, "i_survey_user_new[" + index + "][code]", "i_survey_user_new_code" + index + "", "", "");
            change(7, "i_survey_user_new[" + index + "][password]", "i_survey_user_new_password" + index + "", "", "", get_rand_code());
            $(".tab-content table").prepend(
                $(".prepend_content tbody").html()
            );
            // 取消全选按钮的选中
            $('table thead tr:last-child th:first-child div').removeClass("checked")
            $("table tbody tr:last-child td:first-child div").removeClass("checked")
        })

        // 动态添加的多选框效果
        $("table").on("mouseenter", "tr .icheckbox_square-green", function () {
            $(this).addClass("hover")
        })
        $("table").on("mouseleave", "tr .icheckbox_square-green", function () {
            $(this).removeClass("hover")
        })
        $("table").on("click", "tr .icheckbox_square-green", function (e) {
            $(this).toggleClass("checked")
        })

        //   删除指定的一行
        $("table").on("click", "tbody tr td:last-child button", function () {
            if ($("table tr td:first-child div").hasClass("checked")) {
                $(this)
                    .parent()
                    .parent()
                    .remove();
            }
        })

        // 绑定icheck
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
        // 全选效果
        // 头部的全选
        function top_all_checked() {
            // 全选
            $('table').on('ifChecked', 'thead tr:last-child th:first-child div', function (event) {
                $('.checked_input').parent().addClass('checked');
                $('tbody tr:last-child .icheckbox_square-green').removeClass('checked');
            });
            //反选
            $('table').on('ifUnchecked', 'thead tr:last-child th:first-child div', function (event) {
                $('.checked_input').parent().removeClass('checked');
            });
        }

        top_all_checked()

        // 底部的全选
        function bottom_all_checked() {
            // 全选
            $('table').on('ifChecked', 'tbody tr:last-child td:first-child div', function (event) {
                $('.checked_input').parent().addClass('checked');
            });
            //反选
            $('table').on('ifUnchecked', 'tbody tr:last-child td:first-child div', function (event) {
                $('.checked_input').parent().removeClass('checked');
            });
        }

        bottom_all_checked()
    });



    //随机数
    function get_rand_code(str_length=8)
    {
        var str = '1234567890abcdefghijklmnopqrstuvwxyz';
        var result = '';
        var l = str.length-1;
        var num=0;
        for(var i = 0;i < str_length; i++){
            num = randomNum(0, l);
            a = str[num];
            result =result+a;
        }
        return result;
    }

    //生成从minNum到maxNum的随机数
    function randomNum(minNum,maxNum){
        switch(arguments.length){
            case 1:
                return parseInt(Math.random()*minNum+1,10);
                break;
            case 2:
                return parseInt(Math.random()*(maxNum-minNum+1)+minNum,10);
                break;
            default:
                return 0;
                break;
        }
    }
</script>

</body>

</html>
