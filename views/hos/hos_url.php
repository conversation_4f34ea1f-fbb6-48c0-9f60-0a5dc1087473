<!DOCTYPE html>
<html>
<head>
    <title>医疗机构网址</title>
    <style type="text/css">
.b {
font-size: 50px;
        margin-left: 10px;
        display: inline-block;
        padding-bottom: 5px;
        font-weight:600;
        color:#CC6600;
        margin-left:50px;
}
.lxdw{
    border:1 solid #330099;
    margin-bottom: 20px;
}

.wjgljcon{
    border:2 solid #330099;
    display: block;
}
a{
font-size: 46px;
        display: inline-block;
        padding-bottom: 5px;
        font-weight:600;
        color: #000099;
        margin-left:50px;
}

.btn_new{
    margin-bottom: 1rem;
    background-color: #1ab394;
    border-color: #1ab394;
    color: #FFF;
    font-size: 22px;
    font-weight: 600;
    border-radius: 4px;
    min-height: 32px;
    min-width: 60px;
    max-width: 100px;
    min-height: 50px;
}

.search{
    margin-left: 50px;
    margin-top: 20px;
}

    </style>
</head>

<body>
    <form action="/hc_web/hos_url" class="form-horizontal search" id="item_form" enctype="multipart/form-data" >
        <input type="text" name="search_info" id = "search_info" style="width: 500px; height: 50px; font-size: 18px; font-weight: 600;margin-right: 10px;" placeholder="模糊搜索" value="<?php echo $search_info;?>">
        <button id="submit_but" name="submit_but" class="btn_new btn btn-primary" style="margin-bottom:1rem;" type="submit" value="search" />搜索</button>
    </form>
    <?php
        foreach ($hos as $key => $v_class) {?>
    <h3 class="b"><?php echo $key;?></h3>
    <div class="wjgljcon" style="display: block; border:1 solid #009999;">
    <?php foreach ($v_class as $key_url => $v_url) {?>
    <div class="lxdw"><a href="<?php echo $v_url['url'];?>"    target="_blank"><?php echo $v_url['url_name'];?> </a></div>
    <?php }?>
    </div>
    <?php }?>
    <footer style="margin-top: 80px;margin-bottom: 50px;">
        <div style="font-size: 40px; font-weight: 700;text-align: center;">健康通网络科技有限公司</div>
    </footer>
</body>
</html>
