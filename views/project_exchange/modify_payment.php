<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no" />
    <title><?php echo $title;?></title>
    <script src="/theme/bk/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <script src="/theme/bk/comm_init.js?<?php echo time();?>"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
            box-sizing: border-box;
        }

        a {
            text-decoration: none;
        }

        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
            position: relative;
        }

        input[type="text"] {
            -webkit-appearance: none;
        }

        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }

        .title {
            text-align: center;
            padding-top: 5px;
            padding-bottom: 5px;
            font-weight: 700;
            font-size: 30px;
        }

        .tab {
            height: 40px;
        }

        /*.tab ul {
            height: 100%;
            border-bottom: 1px solid #2b3a99;
            border-top: 1px solid #2b3a99;
        }*/
        .tab .ul_class {
            height: 100%;
            border-bottom: 1px solid #2b3a99;
            border-top: 1px solid #2b3a99;
        }

        .tab .ul_class_wy {
            height: 100%;
            border-bottom: 1px solid #03DBC6;
            border-top: 1px solid #03DBC6;
        }

        /*.tab ul li {
            border-right: 1px solid #2b3a99;
            list-style: none;
            float: left;
            height: 100%;
            width: 50%;
            line-height: 40px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }*/

        .tab ul .li_class {
            border-right: 1px solid #2b3a99;
            list-style: none;
            float: left;
            height: 100%;
            width: 50%;
            line-height: 40px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }
        .tab ul .li_class_wy {
            border-right: 1px solid #03DBC6;
            list-style: none;
            float: left;
            height: 100%;
            width: 50%;
            line-height: 40px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }

        .tab ul li:last-child {
            border-right: none;
        }

        .tab_content {
            text-align: center;
            padding-top: 15px;
        }

        .privacy,
        .checkbox {
            padding-left: 40px;
            text-align: left;
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            vertical-align: middle;
            margin-right: 5px;
        }

        /*.tab_content input[type="text"] {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }*/
        .tab_content .input_class {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .tab_content .input_class_wy {
            border: 2px solid #03DBC6;
            padding: 5px;
            width: 80%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .tab_content .select {
            position: relative;
            color: #666;
            margin: 0 auto;
            border: 2px solid #fd5592;
            padding: 10px 5px 5px;
            width: 80%;
            height: 46px;
            text-align: left;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }

        .tab_content select {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            opacity: 0;
        }

        .wx {
            background: #fff;
            position: relative;
            text-align: center;
        }

        .wx .ewm img {
            width: 240px;
        }

        .wx .ewm .tip {
            width: 100%;
            color: #2b3a99;
        }

        .wx .ewmed {
            width: 240px;
            margin: 0 auto;
            text-align: center;
            display: none;
        }

        .wx .ewmed img {
            width: 240px;
        }

        .error_msg {
            margin-top: 15px;
            color: red;
        }

        .btn {
            text-align: center;
            margin: 0 auto;
            margin-top: 20px;
        }

        .btn button {
            cursor: pointer;
            font-size: 24px;
            width: 40%;
            padding: 10px;
            border: none;
            outline: none;
            border-radius: 2px;
        }

        .btn .confirm_class {
            color: #fff;
            background: #f85691;
        }
        .btn .confirm_class_wy {
            color: #fff;
            background: #03DBC6;
        }

        .btn .cancel_class {
            color: #000;
            background: transparent;
            border: 1px solid #fd5592;
        }
        .btn .cancel_class_wy {
            color: #000;
            background: transparent;
            border: 1px solid #03DBC6;
        }

        .privacy_dialog_box {
            z-index: 999;
            display: none;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .privacy_dialog {
            width: 80%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
        }

        /*.privacy_dialog h4 {
            color: #fff;
            background: #2b3a99;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }*/
        .privacy_dialog .dialog_top_class {
            color: #fff;
            background: #2b3a99;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }

        .privacy_dialog .dialog_top_class_wy {
            color: #000;
            background: #b3efe9;
            font-size: 18px;
            text-align: center;
            padding: 10px 0;
        }

        .privacy_dialog .privacy_content {
            padding: 20px;
            border-left: 1px solid #ccc;
            border-right: 1px solid #ccc;
        }

        /*.privacy_dialog .privacy_btn button {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #fd5592;
            color: #fff;
            cursor: pointer;
        }*/
        .privacy_dialog .privacy_btn .dialog_bottom_button {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #fd5592;
            color: #fff;
            cursor: pointer;
        }
        .privacy_dialog .privacy_btn .dialog_bottom_button_wy {
            text-align: center;
            font-size: 18px;
            width: 100%;
            padding: 10px;
            outline: none;
            background: #fff;
            border: none;
            background: #03DBC6;
            color: #fff;
            cursor: pointer;
        }
    </style>
</head>

<body>
<?php
$input_class = "input_class";
$btn_submit_class = "confirm_class";
$btn_cancel_class = "cancel_class";
$ul_class = "ul_class";
$li_class = "li_class";
$dialog_top_class = "dialog_top_class";//弹出层顶部
$dialog_bottom_button = "dialog_bottom_button";//弹出层底部按钮
$menu_bg_color = "#2b3a99";
$project_sys_id = $this->session->userdata("project_sys_id");
if (in_array($project_sys_id, [7,8,9])) {//网医
    $input_class .= "_wy";
    $get_code_class .= "_wy";
    $btn_submit_class .= "_wy";
    $btn_cancel_class .= "_wy";
    $ul_class .= "_wy";
    $li_class .= "_wy";
    $dialog_top_class .= "_wy";
    $dialog_bottom_button .= "_wy";
    $menu_bg_color = "#03DBC6";
}
?>
<div class="container">
    <!-- 隐私协议对话框 -->
    <div class="privacy_dialog_box">
        <div class="privacy_dialog">
            <h4 class="<?php echo $dialog_top_class;?>">个人所得税代缴代付协议</h4>
            <div class="privacy_content">
                <p>受访者须知：</p>
                <p style="margin-top: 15px">
                    根据根据中国人民共和国有关税法的规定，个人劳务报酬依法应当缴纳个人所得税，服务单位有义务代扣代缴个人所得税。因此请您理解并同意本公司【健康通(北京)网络科技有限公司】在向您支付劳务报酬时，为您代扣代缴个人所得税，该过程需要使用您的个人信息进行登记申报
                </p>
                <p style="margin-top: 20px">
                    特别说明：缴纳个税不影响您的项目礼金支付金额。
                </p>
                <p style="margin-top: 20px">健康通（北京）网络科技有限公司</p>
            </div>
            <div class="privacy_btn">
                <button class="<?php echo $dialog_bottom_button;?>">确定</button>
            </div>
        </div>
    </div>

    <div class="title">确认收款账户</div>

    <input type="hidden" name="clicked_request" value="" />
    <input type="hidden" name="wx_request" value="<?php echo $wx_is_finish; ?>" />

    <div class="tab">
        <ul class="<?php echo $ul_class;?>">
            <?php if( empty($pid) || !in_array($pid, array(
                17857,
                17883,
                17884,
                17873,
                17881,
                17874,
                1675,
            )) ){?>
            <li class="pay_wx <?php echo $li_class;?>" data-type="wx" data-id="8779" style="background: <?php echo $menu_bg_color;?>; color: #fff">微信</li>
            <?php }?>
            <li class="pay_alipay <?php echo $li_class;?>" data-type="alipay" data-id="206">支付宝</li>
        </ul>
    </div>

    <form action="/project_exchange_n/modify_payment_sub" id="the_from" onsubmit="return common_js.form_sumbit(this, 'error_msg')">

        <div class="tab_content">
            <!-- 微信 -->
            <div class="wx">
                <div class="ewm">
                    <div class="tips">扫描二维码绑定健康通授权微信收款</div>
                    <img src="data:image/jpg/png/gif;base64,<?php echo base64_encode($file_name); ?>" alt="" />
                    <div class="tip">(截图通过微信扫描识别二维码)</div>
                </div>

                <div class="ewmed">
                    <img src="/theme/go/image/success.png" alt="">
                    <p>已授权健康通支付</p>
                </div>
                <input <?php echo $member_uid > 0 ? "readonly" : "";?> class="<?php echo $input_class;?>" type="text" name="wx_payment_name" placeholder="请输入本人微信用户名[实名]" style="margin-top: 10px;" value="<?php echo $wx_name; ?>" />
            </div>

            <!-- 支付宝 -->
            <div class="alipay" style="display: none">
                <div class="content">
                    <input <?php echo $member_uid > 0 ? "readonly" : "";?> class="<?php echo $input_class;?>" type="text" name="alipay_payment_name" placeholder="请输入本人支付宝用户名[实名]" value="<?php echo $alipay_name; ?>" />
                    <input class="<?php echo $input_class;?>" type="text" name="alipay_payment_account" placeholder="请输入本人支付宝收款账号" value="<?php echo $alipay_account; ?>" />
                </div>
            </div>

        </div>


        <div class="btn">
            <div class="privacy">
                <label>
                    <input type="checkbox" name="privacy" value="1" />
                    <span>阅读并同意<a href="javascript:;">《个税代缴代付协议》</a></span>
                </label>
            </div>


            <input type="hidden" name="payment_type_default" />
            <input type="hidden" name="scene" value="<?php echo $scene; ?>" />
            <input type="hidden" name="payment_type" value="<?php echo EXCHANGE_WEBCHAT_AUTO; ?>">
            <!-- 提示信息 -->
            <div class="error_msg" style="margin-bottom: 10px"><!--<?php echo $order_info['failure_cause'] ? $order_info['failure_cause'] : ""; ?>--></div>
            <button class="confirm <?php echo $btn_submit_class;?>" type="submit">确 认</button>
            <button class="cancel <?php echo $btn_cancel_class;?>" type="reset">重 置</button>

        </div>
</div>
</form>

<script>
    var timeId;
    // 下拉框的选中效果
    $(".container").on("change", "select", function() {
        var text = $(this).children("select option:selected").text();
        $(this).parent().children("span").text(text);
    });
    // 点击效果
    $(".tab ul li").click(function() {
        //独立多选框 start
        $(".checkbox input[type='checkbox']").prop("checked", false);
        $(".checkbox input[name='payment_type_default']").val("");
        $(".checkbox .lock_account").hide()
        //独立多选框 end

        // 设置input的value
        $("input[name='payment_type']").val($(this).data("id"))
        $(this)
            .css({
                background: "<?php echo $menu_bg_color;?>",
                color: "#fff"
            })
            .siblings()
            .css({
                background: "#fff",
                color: "#000"
            });
        var type = $(this).data("type");
        $("." + type)
            .show()
            .siblings()
            .hide();

        // 微信支付请求检测是否已做支付
        if (type == "wx") {
            if ($("input[name='wx_request']").val() == "success") {
                clearInterval("1")
            } else {
                var wtimeId = setInterval("check_order_info()", 2000)
                $("input[name='clicked_request']").val(wtimeId)
            }

        } else {
            clearInterval($("input[name='clicked_request']").val())
            clearInterval(timeId)
        }

    });

    // lock_account的显示
    var lock_account = '<?php echo $lock_account ? $lock_account : ''; ?>';

    if (lock_account) {
        // 支付宝
        if (lock_account == "206") {
            // 默认绑定支付方式
            $(".tab .pay_alipay").trigger("click")
            $(".checkbox input").prop("checked", true)
            $(".checkbox .lock_account").show()
        }
        // 微信
        if (lock_account == "8779") {
            // 默认绑定支付方式
            $(".tab .pay_wx").trigger("click")
            $(".checkbox input").prop("checked", true)
            $(".checkbox .lock_account").show()
        }
        // 银行
        if (lock_account == "208") {
            // 默认绑定支付方式
            $(".tab .pay_bank").trigger("click")
            $(".checkbox input").prop("checked", true)
            $(".checkbox .lock_account").show()
        }

    }

    //多选框的点击效果
    $("input[type='checkbox']").click(function() {});


    <?php if ($wx_is_finish === "success") { ?>
    // $(".ewm").hide()
    // $(".ewmed").show()
    <?php } else { ?>
    <?php if ($order_info['payment_type'] == EXCHANGE_WEBCHAT_AUTO) { ?>
    // $(".ewm").hide()
    // $(".ewmed").show()
    <?php } ?>
    <?php } ?>

    // 微信支付请求检测是否已做支付
    function check_order_info() {
        $.ajax({
            type: "POST",
            url: "/project_exchange/check_order_info",
            data: "scene=<?php echo $scene; ?>",
            success: function(str) {
                var json_msg = $.parseJSON(str);
                if (json_msg.rs_code == "success") {
                    $(".ewm").hide()
                    $(".ewmed").show()
                    clearInterval(timeId)
                    clearInterval($("input[name='clicked_request']").val())
                    $("input[name='wx_request']").val("success")
                }
            }
        });
    }

    // 隐私协议
    $(document).click(function() {
        $(".privacy_dialog_box").hide();
    });
    $(".privacy_dialog").click(function(e) {
        e.stopPropagation();
    });
    $(".privacy a").click(function(e) {
        e.stopPropagation();
        $(".privacy_dialog_box").show();
        // 对话框的确定按钮
        $(".privacy_dialog_box .privacy_btn button").click(function() {
            $(".privacy_dialog_box").hide();
            $(".privacy input").prop("checked", true)
        });
    });
</script>
</body>

</html>
