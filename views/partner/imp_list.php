<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?php echo $title?></title>

    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
<!--    <link href="/theme/new_manage/css/bootstrap.css" rel="stylesheet">-->
    <style>
        .dataTables_paginate{float: right;}
    </style>
</head>

<body>

<div id="wrapper">
    <div class="gray-bg" id="page-wrapper" style="margin: 0px;!important;">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                <ul class="nav navbar-top-links navbar-right">
                    <li>
                        <a href="javascript:;">
                            <i class="fa fa-sign-out"></i> Log out
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="row wrapper border-bottom white-bg page-heading" style="background-color:#ffbe78;">
            <div class="col-lg-12" >
                <h2 style="float: left;"><?php echo $this->input->get("pid", true);?></h2>
                <h2 style="float: right;">健康通(北京)</h2>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-title">
                            <h5>执行明细</h5>
                            <div class="ibox-tools"><a class="btn btn-sm btn-warning" style="color:#fff;" href="/partner/project_partner">返回</a> </div>
                        </div>

                        <div class="ibox-content">

                            <table class="table">
                                <thead>
                                <tr>
                                    <th>姓名</th>
                                    <th>性别</th>
                                    <th>药店名称</th>
                                    <th>城市等级</th>
                                    <th>省份</th>
                                    <th>城市</th>
                                    <th>连锁/单体</th>
                                    <th>店员/店长</th>
                                    <th>完成状态</th>
                                    <th>邀请状态</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>张*</td>
                                    <td>男</td>
                                    <td>同仁堂</td>
                                    <td>一线城市</td>
                                    <td>上海</td>
                                    <td>徐家汇</td>
                                    <td>单体</td>
                                    <td>店员</td>
                                    <td></td>
                                    <td>已发邀请</td>
                                </tr>
                                <tr>
                                    <td>李*</td>
                                    <td>男</td>
                                    <td>国药</td>
                                    <td>一线城市</td>
                                    <td>北京</td>
                                    <td>朝阳区</td>
                                    <td>连锁</td>
                                    <td>店长</td>
                                    <td>完成</td>
                                    <td>已发邀请</td>
                                </tr>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="10">
                                        <?php echo $pagination?>
                                    </td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row " style="display: none">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div>
                                <div class="chat-activity-list">
                                    <?php if(!empty($client_reback)){
                                        krsort($client_reback);
                                        foreach ($client_reback as $v_reb){
                                        ?>
                                        <?php if($v_reb['source'] == 2){ ?>
                                                <div class="chat-element">
                                                    <a href="#" class="pull-left">
                                                        <img alt="image" class="img-circle" src="/theme/management/img/a8.jpg">
                                                    </a>
                                                    <div class="media-body ">
                                                        <small class="pull-right"><?php echo date('Y-m-d H:i',$v_reb['time'])?></small>
                                                        <strong> Admin</strong>
                                                        <p class="m-b-xs">
                                                           <?php echo $v_reb['message']?>
                                                        </p>
                                                        <small class="text-muted"></small>
                                                    </div>
                                                </div>
                                           <?php  } else { ?>
                                                <div class="chat-element right">
                                                    <a href="#" class="pull-right">
                                                        <img alt="image" class="img-circle" src="/theme/management/img/a4.jpg">
                                                    </a>
                                                    <div class="media-body text-right ">
                                                        <small class="pull-left"><?php echo date('Y-m-d H:i',$v_reb['time'])?></small>
                                                        <strong>Client</strong>
                                                        <p class="m-b-xs">
                                                            <?php echo $v_reb['message']?>
                                                        </p>
                                                    </div>
                                                </div>
                                            <?php }?>
                                    <?php } } ?>
                                </div>
                            </div>
                            <div class="chat-form">
                                <div class="form-group">
                                    <textarea class="form-control" id="send_message" placeholder="Message"></textarea>
                                </div>
                                <div class="text-right">
                                    <button type="button" class="btn btn-sm btn-primary m-t-n-xs" onclick="send_message(<?php echo $pid?>)"><strong>发送消息</strong></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="bottom">
            <div class="footer">
                <div>
                    <strong>Copyright</strong> © 2014 - 2019 Gooddr.com Limited All Rights Reserved.
                </div>
            </div>
        </div>

    </div>

</div>
<!-- Mainly scripts -->
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="/theme/new_manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/plugins/peity/jquery.peity.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/new_manage/js/inspinia.js"></script>
<script src="/theme/new_manage/js/plugins/pace/pace.min.js"></script>

<!-- iCheck -->
<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/demo/peity-demo.js"></script>

<script>
    $(document).ready(function(){
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });
    function send_message(pid) {
        var message = $('#send_message').val();
        $.post('/project_finish/send_message',{message:message,pid:pid},function (d) {
            if(d.rs_code == 'success'){
                alert(d.rs_msg);
                window.location.reload();
            } else {
                alert(d.rs_msg);
            }
        },'json');
    }
</script>

</body>

</html>
