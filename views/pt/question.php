<link rel="stylesheet" href="/theme/pt/css/Question.css" />


<div class="moblie-bg">
	<div class="Health-header">
		<h3 class="Health-header-titile">问卷</h3>
		<a href="/pt/pt/active"><img src="/theme/pt/images/Health-Shape.png" alt="返回" class="Health-header-back"></a>
	</div>
	<div class="Question-wrapper">
		<div class="Question-wrapper-title">
			<img src="/theme/pt/images/Question-Logo.png" alt="问卷logo">
		</div>
		<div class="Question-wrapper-pages">
			<span class="Question-pages-title">关于晚期/转移性肺癌治疗研究</span>
			<span class="Question-pages-item">1/5</span>
			<div class="Question-clearfix"></div>
		</div>
		<div class="Question-content">
			<h3 class="Question-content-title">Q1.是否有非癌症合并症？是哪种？</h3>
			<ul class="Question-content-items optionul">
				<li>
					<div class="Question-item-radio">
						<input type="radio" id="System" name="System" class="Question-radio-ben" checked>
						<label for="System">呼吸系统</label>
					</div>
				</li>
				<li>
					<div class="Question-item-radio">
						<input type="radio" id="System01" name="System" class="Question-radio-ben">
						<label for="System01">心脏</label>
					</div>
				</li>
				<li>
					<div class="Question-item-radio">
						<input type="radio" id="System02" name="System" class="Question-radio-ben">
						<label for="System02">高血压</label>
					</div>
				</li>
				<li>
					<div class="Question-item-radio">
						<input type="radio" id="System03" value="optins" name="System" class="Question-radio-ben">
						<label for="System03">未见记录有非癌症合并</label>
					</div>
				</li>
			</ul>
			<div class="Question-content-btn">
				<a href="javascript:;" class="Question-radio-right" onclick="jumpNext()">下一题</a>
				<div class="Question-clearfix"></div>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript" src="/theme/pt/js/Response.js"></script>
<script>
	$(function() {
		$(".Health-header-back").click(function() {
			window.history.go(-1)
		})
	})

	function jumpNext() {
		if (document.getElementById("System03").checked) {
			window.location = base_url + "question_two";
		} else {
			window.location = base_url + "question_frist";
		}

	}
</script>