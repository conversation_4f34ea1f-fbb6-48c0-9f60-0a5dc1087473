<link rel="stylesheet" href="/theme/pt/css/pointWithdraw.css" />

<style>
    * {
        margin: 0;
        padding: 0;
    }

    .Health-header {
        height: .88rem;
        background: #20537d;
        position: fixed;
        width: 100%;
        z-index: 3;
    }

    .Health-header-titile {
        font-size: .36rem;
        color: #FFFFFF;
        text-align: center;
        font-weight: 500;
        line-height: .88rem;
    }

    .Health-header-back {
        position: absolute;
        top: .26rem;
        left: .3rem;
        height: .36rem;
    }

    .point-withdraw-wrapper {
        margin: 0;
        padding: 0;
        padding-top: .88rem;
        background: #FFFFFF;
    }

    .point-withdraw-content {
        text-align: center;
        margin: .35rem .28rem;
        border: .8px solid #E1E1E1;
    }

    .point-withdraw-top {
        padding-top: .68rem;
        background: #fbfbfb;
        border-bottom: .8px solid #E1E1E1;
        height: 1.6rem;
    }

    .point-withdraw-top span:nth-of-type(1) {
        float: left;
        margin-left: .29rem;
        font-size: .36rem;
        color: #1D1D1D;

    }

    .point-withdraw-top span:nth-of-type(2) {
        font-size: .36rem;
        color: #1D1D1D;
        float: left;
        margin-left: .28rem;
    }

    .point-withdraw-top img:nth-of-type(1) {
        width: .28rem;
        float: left;
        margin-left: .18rem;
        margin-top: .1rem;
    }

    .point-withdraw-top img:nth-of-type(2) {
        float: right;
        width: .3rem;
        margin-right: .54rem;
        margin-top: .15rem;
    }

    .point-withdraw-top p {
        float: left;
        display: block;
        margin-left: 2.37rem;
        font-size: .28rem;
        color: #F5A623;
    }

    .point-withdraw-middle {
        padding-bottom: .3rem;
        border: .8px solid #E1E1E1;
    }

    .point-withdraw-middle span:nth-of-type(1) {
        float: left;
        margin-left: .29rem;
        margin-top: .48rem;
        font-size: .36rem;
        color: #1D1D1D;
        padding-right: 70%;
    }

    .point-withdraw-middle span:nth-of-type(2) {
        float: left;
        margin-top: .49rem;
        margin-left: .56rem;
        font-size: .86rem;
        margin-right: 0 !important
    }

    .point-withdraw-middle input {
        position: relative;
        display: inline-block;
        font-size: .36rem;
        top: .2rem;
        left: -.5rem;
        outline: none;
    }

    .point-withdraw-bottom span:nth-of-type(1) {
        float: left;
        display: none;
        margin-left: .6rem;
        margin-top: .18rem;
        font-size: .28rem;
        color: #92949A;
    }

    .point-withdraw-bottom span:nth-of-type(2) {
        float: left;
        display: none;
        margin-left: .4rem;
        margin-top: .18rem;
        font-size: .28rem;
        color: #368ECF;
    }

    .point-withdraw-bottom button {
        margin-left: 5%;
        margin-top: .6rem;
        margin-bottom: .4rem;
        font-size: .36rem;
        color: #FFFFFF;
        text-align: center;
        width: 90%;
        background: #23547b;
        padding-top: .2rem;
        padding-bottom: .2rem;
        border-radius: 8px;
    }

    .point-withdraw-choose-bank {

        position: fixed;
        display: none;
        top: 0rem;
        left: 0;
        z-index: 9998;
        width: 100%;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.5);
    }

    .point-withdraw-bank-content {
        position: fixed;
        bottom: 0;
        background: #FFFFFF;
        width: 100%;

    }

    .point-withdraw-bank-header {
        margin-top: .91rem;
        padding-bottom: .2rem;
        border-bottom: .8px solid #D3D3D3;
    }

    .point-withdraw-bank-header img {
        float: left;
        margin-left: .3rem;
        width: .3rem;
        margin-top: .18rem;
    }

    .point-withdraw-bank-header-content {
        text-align: center;
    }

    .point-withdraw-bank-header-content span:nth-of-type(1) {
        display: block;
        font-size: .36rem;
        color: #1D1D1D;
        margin-right: .5rem;
    }

    .point-withdraw-bank-header-content span:nth-of-type(2) {
        display: block;
        font-size: .28rem;
        color: #92949A;
    }

    ul {
        list-style: none;
        margin: 0;
        padding: 0;
        padding-bottom: .3rem;
    }

    li {
        margin: 0 !important;
        padding: 0 !important;
        height: 1.39rem;
        border-bottom: .8px solid #E1E1E1;
    }

    li img {
        float: left;
        width: .45rem;
        margin-left: .3rem;
        margin-top: .45rem
    }

    li span {
        float: left;
        margin-left: .3rem;
        margin-top: .4rem;
        font-size: .36rem;
        color: #1D1D1D;
    }

    .point-withdraw-bank-header-tip {
        float: left;
        margin-left: .3rem;
        margin-top: .35rem;
    }

    .point-withdraw-bank-header-tip p:nth-of-type(1) {
        margin-left: .3rem;
        height: .36rem;
        margin: 0;
        line-height: .36rem;
        font-size: .36rem;
        color: #1D1D1D;
    }

    .point-withdraw-bank-header-tip p:nth-of-type(2) {
        margin-left: .3rem;
        height: .21rem;
        margin: 0;
        margin-top: .1rem;
        line-height: .21rem;
        font-size: .21rem;
        color: #92949A;
    }

    /* li span:nth-of-type(2){
    display: block;
    font-size: .36rem;
    color: #1D1D1D;
    } */

    .point-withdraw-record {
        display: block;
        text-align: center;
        margin-top: .68rem;
        font-size: .36rem;
        color: #5CACEE;
    }

    .point-withdraw-input-vertify {
        position: fixed;
        display: none;
        top: 0rem;
        left: 0;
        z-index: 9999;
        width: 100%;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.5);

    }

    .point-withdraw-vertify-content {
        background: #FFFFFF;
        text-align: center;
        margin: 2rem 1rem;
        border-radius: .15rem;
        padding-bottom: 1rem;
        margin: 0 auto;
        width: 6rem;
        top: 50%;
        position: fixed;
        transform: translate(-50%), -50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .point-withdraw-vertify-header {
        border-bottom: .8px solid #E1E1E1;
    }

    .point-withdraw-vertify-header img {
        /* float: left;
            margin-left: .52rem; */
        position: relative;
        margin-left: .1rem;
        width: .3rem;
    }

    .point-withdraw-vertify-header span {
        /* display: block;
            float: left;
            margin-left: .52rem; */
        margin-top: .4rem;
        font-size: .36rem;
        color: #1D1D1D;
    }

    .point-withdraw-vertify-main span:nth-of-type(1) {
        display: block;
        position: relative;
        margin-top: .13rem;
        font-size: .32rem;
        color: #1D1D1D;
    }

    .point-withdraw-vertify-main span:nth-of-type(2) {
        display: block;
        margin-top: .34rem;
        font-size: .72rem;
        color: #1D1D1D;
    }

    .point-withdraw-input-number {
        text-align: center;
        margin: .34rem .2rem;
        margin-left: 0.87rem;
    }

    .point-withdraw-input-number input:nth-of-type(1) {
        float: left;
        width: .71rem;
        height: .71rem;
        margin-left: 0 !important;
        padding-left: 0 !important;
        border: 1px solid #979797;
        font-size: .32rem;
        text-align: center;
    }

    .point-withdraw-input-number input:nth-of-type(2) {
        float: left;
        width: .71rem;
        height: .71rem;
        margin-left: 0 !important;
        padding-left: 0 !important;
        border-bottom: 1px solid #979797;
        border-top: 1px solid #979797;
        border-right: 1px solid #979797;
        font-size: .32rem;
        text-align: center;
    }

    .point-withdraw-input-number input:nth-of-type(3) {
        float: left;
        width: .71rem;
        height: .71rem;
        margin-left: 0 !important;
        padding-left: 0 !important;
        border-bottom: 1px solid #979797;
        border-top: 1px solid #979797;
        border-right: 1px solid #979797;
        font-size: .32rem;
        text-align: center;
    }

    .point-withdraw-input-number input:nth-of-type(4) {
        float: left;
        width: .71rem;
        height: .71rem;
        margin-left: 0 !important;
        padding-left: 0 !important;
        border-bottom: 1px solid #979797;
        border-top: 1px solid #979797;
        border-right: 1px solid #979797;
        font-size: .32rem;
        text-align: center;
    }

    .point-withdraw-input-number input:nth-of-type(5) {
        float: left;
        width: .71rem;
        height: .71rem;
        margin-left: 0 !important;
        padding-left: 0 !important;
        border-bottom: 1px solid #979797;
        border-top: 1px solid #979797;
        border-right: 1px solid #979797;
        font-size: .32rem;
        text-align: center;
    }

    .point-withdraw-input-number input:nth-of-type(6) {
        float: left;
        width: .71rem;
        height: .71rem;
        margin-left: 0 !important;
        padding-left: 0 !important;
        border-bottom: 1px solid #979797;
        border-top: 1px solid #979797;
        border-right: 1px solid #979797;
        font-size: .32rem;
        text-align: center;
    }

    input:disabled {
        opacity: 1;
        color: #333;
    }

    input[type="submit"],
    input[type="reset"],
    input[type="text"],
    input[type="password"],
    input[type="number"],
    input[type="button"],
    button,
    input[type="date"],
    textarea {
        -webkit-appearance: none;
        border: none;
        background: none;
    }

    textarea {
        resize: none;
    }

    .true-input {
        position: absolute;
        z-index: 10000;
        bottom: 0.34rem;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        width: 100%;
        height: 1.31rem;
        color: rgba(255, 255, 255, 1);
        caret-color: rgba(255, 255, 255, 1);
        background: #fff;
    }
</style>

<script>
    window.onload = function() {
        var originalHeight = document.documentElement.clientHeight || document.body.clientHeight;
        window.onresize = function() {
            //键盘弹起与隐藏都会引起窗口的高度发生变化
            var resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
            if (resizeHeight - 0 < originalHeight - 0) {
                if (!inIOSPhone()) {
                    console.log("键盘弹起")
                    $(".point-withdraw-vertify-content").css("top", "62%")
                }
            } else {
                if (!inIOSPhone()) {
                    console.log("键盘收起")
                    $(".point-withdraw-vertify-content").css("top", "50%")
                }
            }
        }
        //ios监听键盘弹起
        document.body.addEventListener('focusin', () => {
            //软键盘弹出的事件处理
            if (inIOSPhone()) {
                console.log("键盘弹起")
                $(".point-withdraw-vertify-content").css("top", "62%")
            }
        })

        // //ios监听键盘收起  
        document.body.addEventListener('focusout', () => {
            //软键盘收起的事件处理
            if (inIOSPhone()) {
                console.log("键盘收起")
                $(".point-withdraw-vertify-content").css("top", "50%")
            }
        })
    }
    //判断手机Android还是ios
    function inIOSPhone() {
        var u = navigator.userAgent,
            app = navigator.appVersion;
        var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //g
        var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
        if (isAndroid) {
            return false;
        }
        if (isIOS) {
            return true;
        }
    }

    function chooseBankChoose() {
        $(".point-withdraw-choose-bank").css("display", "block")
    }

    function closeBankChoose() {
        $(".point-withdraw-choose-bank").css("display", "none")
    }

    function inputCrash() {
        var crashNum = $("#crashNum").val()
        if (crashNum != "" || crashNum != 'undefined') {
            $("#showAllCrash").css("display", "block")
            $("#showCrashText").css("display", "block")
            $("#toWithdraw").html("提现")
        }
    }

    function phoneVertify() {
        $(".point-withdraw-input-vertify").css("display", "block")
    }

    function closeVertifyInput() {
        $(".point-withdraw-input-vertify").css("display", "none")
    }

    function toRecord() {
        window.location.href = base_url + "withdrawal_record"
    }

    function inputSmsCode() {
        var smsCode = $(".true-input").val()
        var singleSms = $(":password")
        for (var i = 0; i < 6; i++) {
            var codeData = singleSms.eq(i).val()
            if (codeData >= 0 && codeData < 10 && codeData != '') {
                continue;
            }
            singleSms.eq(i).val(smsCode.substring(i))
            if (singleSms.eq(5).val() != "") {
                window.location.href = base_url + "withdrawal_process"
            }
        }
    }
</script>

<div class="Health-header">
    <h3 class="Health-header-titile">积分提现</h3>
    <a onclick="window.history.go(-1)"><img src="/theme/pt/images/Health-Shape.png" alt="返回" class="Health-header-back"></a>
</div>
<div class="point-withdraw-wrapper">

    <div class="point-withdraw-content">
        <div class="point-withdraw-top" onclick="chooseBankChoose()">
            <span>储蓄卡</span>
            <img src="/theme/pt/images/pointWithdraw/china-bank-icon.png" />
            <span>中国银行（8848）</span>
            <img src="/theme/pt/images/frequeQuestion/freque-question-arrow.png" />
            <p>单日交易限额</p>
        </div>
        <div class="point-withdraw-middle">
            <span>提现金额</span>
            <span>¥ </span>
            <input type="number" id="crashNum" oninput="inputCrash()" autofocus="autofocus" />
        </div>
        <div class="point-withdraw-bottom">
            <span id="showAllCrash">零钱余额 ¥3，899.04</span>
            <span id="showCrashText">全部提现</span>
            <button id="toWithdraw" onclick="phoneVertify()">下一步</button>
        </div>
    </div>
    <span class="point-withdraw-record" onclick="toRecord()">提现记录</span>
    <div class="point-withdraw-choose-bank">
        <div class="point-withdraw-bank-content">
            <div class="point-withdraw-bank-header">
                <img src="/theme/pt/images/pointWithdraw/close-choose-bank.png" onclick="closeBankChoose()" />
                <div class="point-withdraw-bank-header-content">
                    <span>选择到账银行卡</span>
                    <span>请留意各银行到账时间</span>
                </div>
            </div>
            <ul>
                <li>
                    <img src="/theme/pt/images/pointWithdraw/china-bank-icon.png" />
                    <div class="point-withdraw-bank-header-tip">
                        <p>中国银行(8848)</p>
                        <p>当天24点钱到账</p>
                    </div>
                </li>
                <li>
                    <img src="/theme/pt/images/pointWithdraw/add-bank.png" />
                    <span>添加银行卡</span>
                </li>
                <li>
                    <img src="/theme/pt/images/pointWithdraw/wx-icon.png" />
                    <span>提现到微信</span>
                </li>
                <li>
                    <img src="/theme/pt/images/pointWithdraw/phone-icon.png" />
                    <span>手机充值</span>
                </li>
            </ul>
        </div>
    </div>

    <div class="point-withdraw-input-vertify">
        <div class="point-withdraw-vertify-content">
            <div class="point-withdraw-vertify-header">
                <img src="/theme/pt/images/pointWithdraw/close-choose-bank.png" onclick="closeVertifyInput()" />
                <span>请输入手机验证码</span>
            </div>
            <div class="point-withdraw-vertify-main">
                <span>提现</span>
                <span>¥10000.00</span>
                <div class="point-withdraw-input-number" id="val-box">
                    <input type="password" maxlength="1" name="val-item" class="aaaa"></input>
                    <input type="password" maxlength="1" name="val-item"></input>
                    <input type="password" maxlength="1" name="val-item"></input>
                    <input type="password" maxlength="1" name="val-item"></input>
                    <input type="password" maxlength="1" name="val-item"></input>
                    <input type="password" maxlength="1" name="val-item"></input>
                </div>

                <input type="tel" class="true-input" maxlength="6" autofocus="autofocus" oninput="inputSmsCode()" />
            </div>
        </div>
    </div>
</div>