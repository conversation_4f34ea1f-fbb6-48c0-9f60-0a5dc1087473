    <link rel="stylesheet" type="text/css" href="/theme/pt/css/main.css">

    <div class="Health-header">
        <h3 class="Health-header-titile">个人资料认证</h3>
        <a onclick="window.history.go(-1)"><img src="/theme/pt/images/comm-back.png" alt="返回" class="Health-header-back"></a>
    </div>
    <div class="mine-authentication-wrapper">
        <div class="mine-authentication-content">
            <ul>
                <li class="mine-authentication-item">
                    <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>我的二维码</p><a href="/pt/pt/mine_qrcode"><img class="mine-authentication-erweima" src="/theme/pt/images/mine/erweima-icon.png" /></a>
                </li>
                <li class="mine-authentication-item mine-authentication-item-flex">
                <p class=" mine-authentication-left"><span class="mine-authentication-left-tip">*</span>姓<i class="mine-authentication-two-space"></i>名</p>
                    <input class="mine-authentication-right-flex mine-authentication-right-input" id="name" value="白鸥" />
                    <i id="del-name" class="mine-authentication-del"></i>
                </li>
                <li class="mine-authentication-item">
                    <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>性<i class="mine-authentication-two-space"></i>别</p>
                    <div class="mine-authentication-right-sex"><input class="mine-authentication-sex" name="sex" type="radio" />男</input><input class="mine-authentication-sex mine-authentication-right-input" checked name="sex" type="radio" />女</input></div>
                </li>
                <li class="mine-authentication-item mine-authentication-item-flex">
                    <p id="proince-click" class="mine-authentication-left mine-authentication-left-flex"><span class="mine-authentication-left-tip">*</span>省份<span class="mine-authentication-country" id="proince">上海市</span></p>
                    <p id="city-click" class="mine-authentication-left mine-authentication-left-flex mine-authentication-country-1">城市<span class="mine-authentication-country mine-authentication-country-2" id="city">上海市</span></p>
                </li>
                <li class="mine-authentication-item mine-authentication-item-flex">
                    <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>出生日期</p>
                    <p class="mine-authentication-right-flex" onfocus="this.blur();" id="birthday">1990-9-10</p><i class="mine-authentication-direction"></i>
                </li>
                <li class="mine-authentication-item mine-authentication-item-flex">
                    <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>手机号码</p>
                    <p class="mine-authentication-right-flex">18350217317</p><span class="mine-authentication-edit">修改</span>
                </li>
                <li class="mine-authentication-item mine-authentication-item-flex">
                    <p class="mine-authentication-left"><span class="mine-authentication-left-tip mine-authentication-left-tip-hide">*</span>邮<i class="mine-authentication-two-space"></i>箱</p>
                    <input class="mine-authentication-right-flex mine-authentication-right-input" id="email" value="" placeholder="请填写" />
                    <i id="del-email" class="mine-authentication-del"></i>
                </li>
                <li class="mine-authentication-item mine-authentication-item-flex">
                    <p class="mine-authentication-left"><span class="mine-authentication-left-tip">*</span>身份证</p>
                    <input class="mine-authentication-right-flex mine-authentication-right-input" id="idcard" value="" placeholder="请填写身份证号" />
                    <i id="del-idcard" class="mine-authentication-del"></i>
                </li>
            </ul>
        </div>
        <a class="mine-authentication-confirm" onclick="window.history.go(-1)">确定</a>
        <div class="mine-authentication-bottom-choose">
            <div class="mine-authentication-bottom-content">
                <ul class="mine-authentication-bottom-content-ul"></ul>
            </div>
        </div>
    </div>

    <script src="/theme/pt/js/dataPicker.js"></script>
    <script src="/theme/pt/js/city.js"></script>

    <script>
        $('#del-email').hide()
        $('#del-idcard').hide()
        $('.mine-authentication-bottom-choose').hide()

        //清除姓名
        $('#del-name').click(function() {
            $("#name").val('')
            $(this).hide()
            $("#name").focus()
        })
        //监听姓名输入
        $('#name').bind('input propertychange', function() {
            if ($(this).val() != '') {
                $('#del-name').show()
            } else {
                $('#del-name').hide()
            }
        })

        //清除邮箱
        $('#del-email').click(function() {
            $("#email").val('')
            $(this).hide()
            $("#email").focus()
        })
        //监听邮箱输入
        $('#email').bind('input propertychange', function() {
            if ($(this).val() != '') {
                $('#del-email').show()
            } else {
                $('#del-email').hide()
            }
        })

        //清除身份证号
        $('#del-idcard').click(function() {
            $("#idcard").val('')
            $(this).hide()
            $("#idcard").focus()
        })
        //监听身份证号
        $('#idcard').bind('input propertychange', function() {
            if ($(this).val() != '') {
                $('#del-idcard').show()
            } else {
                $('#del-idcard').hide()
            }
        })

        var calendar = new datePicker();
        calendar.init({
            'trigger': '#birthday',
            /*选择器，触发弹出插件*/
            'type': 'date',
            /*date 调出日期选择 datetime 调出日期时间选择 time 调出时间选择 ym 调出年月选择*/
            'minDate': '1900-1-1',
            /*最小日期*/
            'maxDate': new Date().getFullYear() + '-' + (new Date().getMonth() + 1) + '-' + new Date()
                .getDate(),
            /*最大日期*/
            'onSubmit': function() {
                /*确认时触发事件*/
                var theSelectData = calendar.value;
                $('#birthday').html(theSelectData)
            },
            'onClose': function() {
                /*取消时触发事件*/
            }
        });

        //获取省份数据
        var proinceArr = []
        var cityArr = ['上海市']
        window.allProvice = provice
        provice.map(function(item) {
            proinceArr.push(item.name)
        })

        $('#proince-click').click(function() {
            //选择省份
            //清除城市数据
            cityArr = []
            $('#city').html('')
            let liArr = ''
            proinceArr.map(function(item) {
                liArr = liArr + createListItem(item)
            })
            showBottomSelecItem(liArr)
            $('.mine-authentication-bottom-content-item').click(function() {
                //设置城市数据
                cityArr = []
                var cityDataArr = provice[$(this).index()]
                console.log(cityDataArr.name)
                //设置省份
                $('#proince').html(cityDataArr.name)
                cityDataArr.city.map(function(cityItem) {
                    cityArr.push(cityItem.name)
                })

                $('.mine-authentication-bottom-choose').hide()
            })

        })

        $('#city-click').click(function() {
            //选择城市
            let liArr = ''
            cityArr.map(function(item) {
                liArr = liArr + createListItem(item)
            })
            showBottomSelecItem(liArr)
            $('.mine-authentication-bottom-content-item').click(function() {
                //设置城市数据
                $('#city').html(cityArr[$(this).index()])
                $('.mine-authentication-bottom-choose').hide()
            })

        })

        //创建列表数据
        function createListItem(item) {
            return "<li class='mine-authentication-bottom-content-item'>" + item +
                "</li>"
        }
        //显示列表数据
        function showBottomSelecItem(html) {
            $('.mine-authentication-bottom-content-ul').html('')
            $('.mine-authentication-bottom-content-ul').html(html)
            $('.mine-authentication-bottom-choose').show()
        }
    </script>