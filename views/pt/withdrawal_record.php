<link rel="stylesheet" type="text/css" href="/theme/pt/css/main.css">

<!-- 头部 -->
<ul class="home-top-title">
    <li class="close" id="black_to_home" onclick="window.history.go(-1)">
        <img src="/theme/pt/images/home/<USER>">
    </li>
    <li>提现记录</li>
    <li>
    </li>
</ul>
<div class="withdrawl-record-wrapper" style="padding-top: 1rem;">
    <div>
        <ul>
            <li class="withdrawl-record-item">
                <div class="withdrawl-record-header">
                    <p class="withdrawl-record-title">2018年7月<i onfocus="this.blur();" id="showDate" class="withdrawl-record-direct"></i></p>
                    <p class="withdrawl-record-detail">支出¥0.00 收入¥300</p>
                </div>
                <ul>
                    <li class="withdrawl-record-child-item">
                        <img src="/theme/pt/images/point/withdrwal-icon.png" class="withdrawl-record-icon" />
                        <div class="withdrawl-record-child-detail">
                            <p class="withdrawl-record-title">提现到-中国银行(88888888811111111188888)</p>
                            <span class="withdrawl-record-detail">7月7日 11:53</span>
                            <span class="withdrawl-record-title withdrawl-record-detail-price">300.00</span>
                        </div>
                    </li>
                    <li class="withdrawl-record-child-item">
                        <img src="/theme/pt/images/point/withdrwal-icon.png" class="withdrawl-record-icon" />
                        <div class="withdrawl-record-child-detail">
                            <p class="withdrawl-record-title">提现到-中国银行(88888888888888)</p>
                            <span class="withdrawl-record-detail">7月9日 11:53</span>
                            <span class="withdrawl-record-title withdrawl-record-detail-price">300.00</span>
                        </div>
                    </li>
                </ul>
            </li>
            <li class="withdrawl-record-item">
                <div class="withdrawl-record-header">
                    <p class="withdrawl-record-title">2018年8月<i class="withdrawl-record-direct"></i></p>
                    <p class="withdrawl-record-detail">支出¥0.00 收入¥300</p>
                </div>
                <ul>
                    <li class="withdrawl-record-child-item">
                        <img src="/theme/pt/images/point/withdrwal-icon.png" class="withdrawl-record-icon" />
                        <div class="withdrawl-record-child-detail">
                            <p class="withdrawl-record-title">提现到-中国银行(88888888811111111188888)</p>
                            <span class="withdrawl-record-detail">8月8日 11:53</span>
                            <span class="withdrawl-record-title withdrawl-record-detail-price">300.00</span>
                        </div>
                    </li>

                </ul>
            </li>
            <li class="withdrawl-record-item">
                <div class="withdrawl-record-header">
                    <p class="withdrawl-record-title">2018年9月<i class="withdrawl-record-direct"></i></p>
                    <p class="withdrawl-record-detail">支出¥0.00 收入¥300</p>
                    <span class="withdrawl-record-title withdrawl-record-detail-price">300.00</span>
                </div>
                <ul>
                    <li class="withdrawl-record-child-item">
                        <img src="/theme/pt/images/point/withdrwal-icon.png" class="withdrawl-record-icon" />
                        <div class="withdrawl-record-child-detail">
                            <p class="withdrawl-record-title">提现到-中国银行(88888888811111111188888)</p>
                            <span class="withdrawl-record-detail">9月9日 11:53</span>
                            <span class="withdrawl-record-title withdrawl-record-detail-price">300.00</span>
                        </div>
                    </li>
                    <li class="withdrawl-record-child-item">
                        <img src="/theme/pt/images/point/withdrwal-icon.png" class="withdrawl-record-icon" />
                        <div class="withdrawl-record-child-detail">
                            <p class="withdrawl-record-title">提现到-中国银行(88888888888888)</p>
                            <span class="withdrawl-record-detail">9月10日 11:53</span>
                            <span class="withdrawl-record-title withdrawl-record-detail-price">300.00</span>
                        </div>
                    </li>
                    <li class="withdrawl-record-child-item">
                        <img src="/theme/pt/images/point/withdrwal-icon.png" class="withdrawl-record-icon" />
                        <div class="withdrawl-record-child-detail">
                            <p class="withdrawl-record-title">提现到-中国银行(88888888888888)</p>
                            <span class="withdrawl-record-detail">9月12日 11:53</span>
                            <span class="withdrawl-record-title withdrawl-record-detail-price">300.00</span>
                        </div>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</div>


<script src="/theme/pt/js/dataPicker.js"></script>

<script>
    var calendar = new datePicker();
    calendar.init({
        'trigger': '#showDate',
        /*选择器，触发弹出插件*/
        'type': 'ym',
        /*date 调出日期选择 datetime 调出日期时间选择 time 调出时间选择 ym 调出年月选择*/
        'minDate': '1900-1-1',
        /*最小日期*/
        'maxDate': new Date().getFullYear() + '-' + (new Date().getMonth() + 1) + '-' + new Date()
            .getDate(),
        /*最大日期*/
        'onSubmit': function() {
            /*确认时触发事件*/
            var theSelectData = calendar.value;
            // $('#birthday').html(theSelectData)
        },
        'onClose': function() {
            /*取消时触发事件*/
        }
    });
</script>