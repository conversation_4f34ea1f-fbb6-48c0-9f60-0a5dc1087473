<link rel="stylesheet" href="/theme/pt/css/Health.css" />

<div class="Health-b">
    <ul class="home-top-title">
        <li class="close" id="black_to_home">
            <img src="/theme/pt/images/home/<USER>">
        </li>
        <li>心血管</li>
        <li>
        </li>
    </ul>
    <div class="Cardiovascular-wrapper" style="padding-top: 1rem;">
        <div class="Health-search">
            <input type="text" placeholder="输入关键字" class="Health-search-border" />
            <img src="/theme/pt/images/Healrh-Search-Bar.png" alt="搜索" class="Health-search-icon" />
        </div>
        <div class="Health-content">
            <ul class="Cardiovascular-content-items">
                <li class="Cardiovascular-content-item"><a href="/pt/pt/health_assess_detail">体质指数和体表面积</a></li>
                <li class="Cardiovascular-content-item"><a href="/pt/pt/health_assess_detail">CRUSADE出血风险评估</a></li>
                <li class="Cardiovascular-content-item"><a href="/pt/pt/health_assess_detail">年龄</a></li>
                <li class="Cardiovascular-content-item"><a href="/pt/pt/health_assess_detail">GRACE缺血风险评估</a></li>
                <li class="Cardiovascular-content-item"><a href="/pt/pt/health_assess_detail">CRUSADE出血风险评估</a></li>
            </ul>
        </div>
    </div>
</div>

<script type="text/javascript" src="/theme/pt/js/Response.js"></script>
<script>
    $(function() {
        $("#black_to_home").click(function() {
            window.history.go(-1)
        })
    })
</script>