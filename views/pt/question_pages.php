<link rel="stylesheet" href="/theme/pt/css/Question.css" />

<div class="moblie-bg">
	<div class="Health-header">
		<h3 class="Health-header-titile">问卷</h3>
		<a><img src="/theme/pt/images/Health-Shape.png" alt="返回" class="Health-header-back"></a>
	</div>
	<div class="Question-wrapper">
		<div class="Question-wrapper-title">
			<img src="/theme/pt/images/Question-Logo.png" alt="问卷logo">
		</div>
		<div class="Question-wrapper-pages">
			<span class="Question-pages-title">关于晚期/转移性肺癌治疗研究</span>
			<span class="Question-pages-item">4/5</span>
			<div class="Question-clearfix"></div>
		</div>
		<div class="Question-content">
			<h3 class="Question-content-title">Q4.血压检查结果?</h3>
			<ul class="Question-question-items">
				<li class="Question-question-item">
					<label>收缩压</label><input type="text" placeholder="30~180" class="Question-question-input" id="systolicVal" onblur="systolic()" /><span>mm/Hg</span>
				</li>
				<li class="Question-question-item">
					<label>舒张压</label><input type="text" placeholder="30~240" class="Question-question-input" id="diastolicVal" onblur="diastolic()" /><span>mm/Hg</span>
				</li>
			</ul>
			<div class="Question-question-tip" id="Questionstyle">
				<p>您输入的数值有误,请检查！</p>
			</div>
			<div class="Question-content-btn">
				<a href="/pt/pt/question_two" class="Question-radio-left">上一题</a>
				<a href="/pt/pt/question_bank" class="Question-radio-right">下一题</a>
				<div class="Question-clearfix"></div>
			</div>
		</div>
	</div>

</div>

<script type="text/javascript" src="/theme/pt/js/Response.js"></script>
<script>
	function systolic() {
		var QuestionStyle = document.getElementById('Questionstyle');
		var num = document.getElementById('systolicVal').value;

		if (parseInt(num) >= 30 && parseInt(num) <= 180 && parseInt(num) != undefined) {
			QuestionStyle.style.display = "none";
		} else {
			QuestionStyle.style.display = "block";
		}
	}

	function diastolic() {
		var QuestionStyle = document.getElementById('Questionstyle');
		var num2 = document.getElementById('diastolicVal').value;

		if (parseInt(num2) >= 30 && parseInt(num2) <= 240 && parseInt(num2) != undefined) {
			QuestionStyle.style.display = "none";
		} else {
			QuestionStyle.style.display = "block";
		}
	}
	$(function() {
		$(".Health-header-back").click(function() {
			window.history.go(-1)
		})
	})
</script>