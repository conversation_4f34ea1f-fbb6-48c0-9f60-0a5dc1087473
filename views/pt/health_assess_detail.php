    <link rel="stylesheet" href="/theme/pt/css/Health.css" />

    <script type="text/javascript">
        function computeBMI() {
            var w = document.getElementById("CardWeight").value; //体重
            var h = document.getElementById("CardHeight").value; //身高

            document.getElementById("BMI").value = Math.round(w / (h * h / 10000) * 10) / 10;
        }

        function computeBSA() {
            var w = document.getElementById("CardWeight").value; //体重
            var h = document.getElementById("CardHeight").value; //身高

            document.getElementById("BSA").value = Math.round(Math.sqrt((w * h / 3600)) * 100) / 100;
        }


        function computeArea() {
            var w = document.getElementById("CardWeight").value; //体重
            var h = document.getElementById("CardHeight").value; //身高
            var result;

            if (document.getElementById("Adult").checked)
                result = Math.round((0.010061 * h + 0.010124 * w - 0.010099) * 1000) / 1000;
            if (document.getElementById("Child").checked)
                if (w < 30)
                    result = Math.round((w * 0.035 + 0.1) * 1000) / 1000;
                else
                    result = Math.round((1.05 + (w - 30) * 0.02) * 1000) / 1000;

            document.getElementById("Area").value = result;
        }

        function computeStevenson() {
            var w = document.getElementById("CardWeight").value; //体重
            var h = document.getElementById("CardHeight").value; //身高

            document.getElementById("Stevenson").value = Math.round((0.0061 * h + 0.0128 * w - 0.1529) * 1000) / 1000;

        }

        function computeDrug() {
            var w = document.getElementById("CardWeight").value; //体重
            var h = document.getElementById("CardHeight").value; //身高
            var result;

            if (document.getElementById("Adult").checked)
                result = 1;
            if (document.getElementById("Child").checked)
                if (w < 30)
                    result = Math.round((w * 0.035 + 0.1) / 1.7 * 100) / 100;
                else
                    result = Math.round((1.05 + (w - 30) * 0.02) / 1.7 * 100) / 100;

            document.getElementById("Drug").value = result;
        }
    </script>


    <div class="moblie-bg">
        <ul class="home-top-title">
            <li class="close" id="black_to_home">
                <img src="/theme/pt/images/home/<USER>">
            </li>
            <li>心血管</li>
            <li>
            </li>
        </ul>
        <div class="Cardiovascular-wrapper" style="padding-top:0.98rem;">
            <div class="Cardiovascular-detail-mark">
                <div class="Cardiovascular-detail-left">
                    <p>体质指数和体表面积</p>
                </div>
                <div class="Cardiovascular-detail-right">
                    <div class="Cardiovascular-reselect">
                        <select class="Cardiovascular-detail-select">
                            <option>SI</option>
                        </select>
                    </div>
                </div>
                <div class="Health-clearFix"></div>
            </div>
            <div class="Cardiovascular-detail-information">
                <ul>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>体重</p>
                        </div>
                        <div class="Cardiovascular-information-right">
                            <input id="CardWeight" type="text" value="85" class="Cardiovascular-information-input" />
                            <span>千克</span>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>身高</p>
                        </div>
                        <div class="Cardiovascular-information-right">
                            <input id="CardHeight" type="text" value="185" class="Cardiovascular-information-input" />
                            <span>厘米</span>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>成人/儿童</p>
                        </div>
                        <div class="Cardiovascular-information-right">
                            <div class="Cardiovascular-information-radio">
                                <input type="radio" id="Adult" class="Cardiovascular-original-radio" name="people" checked>
                                <label for="Adult">成人</label>
                            </div>
                            <div class="Cardiovascular-information-radio">
                                <input type="radio" id="Child" class="Cardiovascular-original-radio" name="people">
                                <label for="Child">小孩</label>
                            </div>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                </ul>
            </div>
            <div class="Cardiovascular-detail-BMI">
                <ul>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>BMI：</p>
                        </div>
                        <div class="Cardiovascular-information-right Cardiovascular-reset">
                            <input id="BMI" class="Cardiovascular-information-see" value="点击查看" onclick="computeBMI()" readonly></input>
                            <span>kg/m<sup>2</sup></span>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>体表面积简易公式：</p>
                        </div>
                        <div class="Cardiovascular-information-right Cardiovascular-reset">
                            <input id="BSA" class="Cardiovascular-information-see" value="点击查看" onclick="computeBSA()" readonly></input>
                            <span>m<sup>2</sup></span>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>中国人体表面积通用公式：</p>
                        </div>
                        <div class="Cardiovascular-information-right Cardiovascular-reset">
                            <input id="Area" class="Cardiovascular-information-see" value="点击查看" onclick="computeArea()" readonly></input>
                            <span>m<sup>2</sup></span>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>体积表面(Stevenson公式):</p>
                        </div>
                        <div class="Cardiovascular-information-right Cardiovascular-reset">
                            <input id="Stevenson" class="Cardiovascular-information-see" value="点击查看" onclick="computeStevenson()" readonly></input>
                            <span>m<sup>2</sup></span>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                    <li>
                        <div class="Cardiovascular-information-left">
                            <p>儿童用药剂量公式(以成人剂量为1):</p>
                        </div>
                        <div class="Cardiovascular-information-right Cardiovascular-reset">
                            <input id="Drug" class="Cardiovascular-information-see" value="点击查看" onclick="computeDrug()" readonly></input>
                        </div>
                        <div class="Health-clearFix"></div>
                    </li>
                </ul>
            </div>
            <div class="Cardiovascular-detail-tip">
                <div class="Cardiovascular-tip">
                    <h3>特别提醒：</h3>
                    <p>所有计算结果必须重新审对，且不能作为指导治疗的决策的唯一依据</p>
                </div>
                <div class="Cardiovascular-tip-calculation">
                    <h3>计算公式：</h3>
                    <p>简易体表面积计算公式(BSA) = [体重 (kg) x 身高 (cm) / 3600]<sup>-2</sup>，其中体重(kg)，身高 (cm)</p>
                    <p>体重小于30kg儿童体表面积=体重*0.035+0.1</p>
                    <p>小儿用药剂量=（成人剂量*小儿体表面积）/1.7</p>
                    <p>体重30kg以上儿童体表面积=1.05+(体重-30)*0.02</p>
                    <p>中国人体表面积通用公式：体表面积S(m2)=0.010061×身高+0.010124×体重-0.010099，其中身高(cm)，体重(kg)。</p>
                    <p>一般成人及儿童应用许文生氏(Stevenson)公式：体表面积S(m2)=0.0061×身高(cm)+ 0.0128×体重(kg)-0.1529 </p>
                    <p>参考文献：</p>
                    <p>胡咏梅,等。关于中国人体表面积公式的研究。 生理学报,1999年2月,51(1),45～48</p>

                </div>
            </div>

        </div>
    </div>

    <script type="text/javascript" src="/theme/pt/js/Response.js"></script>
    <script>
        $(function() {
            $("#black_to_home").click(function() {
                window.history.go(-1)
            })
        })
    </script>