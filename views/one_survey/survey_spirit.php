<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui"
    />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />

    <title>关于精神疾病领域的用药情况的调查</title>
<!--    <script src="/theme/--><?php //echo TEMPLATE_DIR;?><!--/js/jquery-3.1.1.min.js"></script>-->
    <script src="/theme/management/js/jquery-3.1.1.min.js"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR;?>/assets/js/jquery.form.js"></script>
<!--    <link href="/theme/--><?php //echo TEMPLATE_DIR;?><!--/css/bootstrap.min.css" rel="stylesheet">-->
<!--    <link href="/theme/--><?php //echo TEMPLATE_DIR;?><!--/font-awesome/css/font-awesome.css" rel="stylesheet">-->
<!--    <link href="/theme/--><?php //echo TEMPLATE_DIR;?><!--/css/animate.css" rel="stylesheet">-->
<!--    <link href="/theme/--><?php //echo TEMPLATE_DIR;?><!--/css/style.css" rel="stylesheet">-->
    <link href="/theme/<?php echo TEMPLATE_DIR;?>/css/plugins/select2/select2.min.css" rel="stylesheet">
    <style>
        a {
            color: #337ab7;
            text-decoration: none;
        }
        .nav > li > a {
            color: #a7b1c2;
            font-weight: 600;
            padding: 14px 20px 14px 25px;
        }
        .nav.navbar-right > li > a {
            color: #999c9e;
        }
        .nav > li.active > a {
            color: #ffffff;
        }
        .navbar-default .nav > li > a:hover,
        .navbar-default .nav > li > a:focus {
            background-color: #293846;
            color: white;
        }
        .nav .open > a,
        .nav .open > a:hover,
        .nav .open > a:focus {
            background: #fff;
        }
        .nav.navbar-top-links > li > a:hover,
        .nav.navbar-top-links > li > a:focus {
            background-color: transparent;
        }
        .nav > li > a i {
            margin-right: 6px;
        }
        .navbar {
            border: 0;
        }
        .navbar-default {
            background-color: transparent;
            border-color: #2f4050;
        }
        .navbar-top-links li {
            display: inline-block;
        }
        .navbar-top-links li:last-child {
            margin-right: 40px;
        }
        .body-small .navbar-top-links li:last-child {
            margin-right: 0;
        }
        .navbar-top-links li a {
            padding: 20px 10px;
            min-height: 50px;
        }
        .dropdown-menu {
            border: medium none;
            border-radius: 3px;
            box-shadow: 0 0 3px rgba(86, 96, 117, 0.7);
            display: none;
            float: left;
            font-size: 12px;
            left: 0;
            list-style: none outside none;
            padding: 0;
            position: absolute;
            text-shadow: none;
            background-color: #fff;
            top: 100%;
            z-index: 21000;
        }
        .dropdown-menu > li > a {
            border-radius: 3px;
            color: inherit;
            line-height: 25px;
            margin: 4px;
            text-align: left;
            font-weight: normal;

            display: block;
            padding: 3px 20px;
            clear: both;


            white-space: nowrap;
        }
        .dropdown-menu > .active > a,
        .dropdown-menu > .active > a:focus,
        .dropdown-menu > .active > a:hover {
            color: #fff;
            text-decoration: none;
            background-color: #1ab394;
            outline: 0;
        }
        .dropdown-menu > li > a.font-bold {
            font-weight: 600;
        }
        .navbar-top-links .dropdown-menu li {
            display: block;
        }
        .navbar-top-links .dropdown-menu li:last-child {
            margin-right: 0;
        }
        .navbar-top-links .dropdown-menu li a {
            padding: 3px 20px;
            min-height: 0;
        }
        .navbar-top-links .dropdown-menu li a div {
            white-space: normal;
        }
        .nav.nav-tabs li {
            background: none;
            border: none;
        }

        .float-e-margins .btn {
            margin-bottom: 5px;
            /*margin-top: -4px;*/
        }
        .btn-primary {
            background-color: #1ab394;
            border-color: #1ab394;
            color: #FFFFFF;
        }
        .btn {
            border-radius: 3px;
        }
        .btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .btn-group-lg>.btn, .btn-lg {
            padding: 10px 16px;
            font-size: 18px;
            line-height: 1.3333333;
            border-radius: 6px;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        input[type="text"]{
            width: 85%;
            height: 28px;
            background: #FFFFCC;
            outline: none;
            border: 1px solid #cecece;
            border-radius:0;
        }

    </style>
</head>

<body>
<form class="form-horizontal" id="item_form_info">
    <div class="alert alert-error" style="display: none;" id="show_msg_block">
        <button data-dismiss="alert" class="close"></button>
        <span id="show_msg"></span>
    </div>
    <table width="90%" border="0" align="center">
        <tr>
            <td align="center" valign="middle">关于精神疾病领域的用药情况的调查</td>
        </tr>
        <tr>
            <td>
                尊敬的医师：<br /><br />

                <p style="text-indent:25px">您好！我们代表健康通（上海）网络科技有限公司，正在进行一项针对全国社区医院的精神疾病领域的用药情况的调研活动。本次调研活动的目的是了解国内治疗中枢神经系统的药物在社区医院层面的渗透程度。问卷的内容主要涉及到一些主流中枢药物在各社区医院的销售情况。</p>

                <p style="text-indent:25px">在问卷里您需要填写的信息主要为两大块：第一块您需要填写每一年（时间跨度为2010至2018年）您所在的医院的治疗中枢神经药物（胶囊和口服片剂）的总销售额，第二块您需要从名单中选择该年您所在的医院都销售哪些指定品牌和规格的药物，并填写这些药物在您的医院的具体销售份数和销售额。如果您医院的数据不足以覆盖2010至2018年所有年份，请至少填写2015至2018年的所有数据。</p>

                <p style="text-indent:25px">根据您对这些信息的熟悉程度，您填写问卷所需要花费的时间为10至20分钟不等。通过点击我们给您发送的链接，您可以在项目截止日期之前无限次进入问卷。您由于任何原因在问卷中途离开，都可以再点击我们发送给您的链接再次进入问卷。再次进入问卷的时候，您将会被直接导入上一次您离开的页面。即便是您已经完成并提交的问卷，您也可以再次进入，再次进入的时候您将会被重新导入到这个起始页面。</p>

                <p style="text-indent:25px">您提供的数据的真实度和准确度直接决定本次调研的质量。对于您拨冗为本次调研所作出的贡献，我们不胜感激！</p>

                <p style="text-indent:25px">我们承诺，访问中所涉及的个人信息和资料将被严格保密，不会泄露给第三方。您在问卷里提供的数据仅为本次的调研报告作数据支持。</p>

                <p style="text-indent:25px">本次调研为有偿活动。您完成问卷之后将有100元的礼金作为酬谢略表心意。礼金将在您完成问卷之后的三个工作日内默认以手机话费的形式充值到您当前的手机号码。</p>

                衷心感谢您为医疗事业做出的贡献！<br /><br />

                请点击下一页正式进入问卷。谢谢。<br /><br />

            </td>
        </tr>
        <tr>
            <td>
                <div class="alert alert-danger">
                    <span style="text-indent:25px">您在该页面填写的任何信息都会在网页上实时保存。您如果不便一次性填完数据，可以随时关闭页面，之后可以再次用同样的链接进入，您之前所填写的所有数据都不会丢失。</span>
                </div>

            </td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>请填写你所在的医院名称</td>
        </tr>
        <tr>
            <td>
<!--                <select class="select2_demo_3 form-control" name="search_name" id="input_select" style="width:50%;">-->
<!--                    <option></option>-->
<!--                    --><?php //foreach($hospital_info as $v_h){?>
<!--                    <option>--><?php //echo $v_h;?><!--</option>-->
<!--                    --><?php //}?>
<!--                </select><br />-->

                <input type="text" placeholder="请输入医院名字" onblur="insert_data_info('hospital',this.value);" id="hospital" name="data_info[hospital]"  class="typeahead_1" size="50" maxlength="60" value="<?php echo $get_hos_drug['hospital'];?>" style="height: 28px;border:1px solid #cecece;padding-left: 10px;font-size: 14px;
                width: 95%;" />
                <!--<label style="margin-left:auto" >
                    <input id="hospital" name="data_info[hospital]" type="text" value="" placeholder="请输入医院名字" size="50" maxlength="60" />
                </label>-->
            </td>
        </tr>
        <tbody style="<?php if(!$get_hos_drug){?>display: none;<?php }?>" id="question_content">
            <tr>
                <td height="10">&nbsp</td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="1" cellpadding="1" cellspacing="1">
                        <tr height="19">
                            <td style="color:#fff; height="19" colspan="2" align="center" bgcolor="<?php echo $first_q['bg_color'];?>"><?php echo $first_q['title'];?></td>
                        </tr>
                        <tr height="19">
                            <td  bgcolor="#99FFFF">药品</td>
                        </tr>
                        <?php $f = 0;foreach($first_q['option'] as $k_first => $v_first){?>
                            <tr height="19" <?php if($f%2 == 0){echo "bgcolor='#CCCCCC'";};?> >
                                <td height="19">
                                    <label style="cursor: pointer;" onclick="get_drugs('<?php echo $v_first;?>');">
                                        <input type="checkbox" id="f_<?php echo $v_first;?>" name="data_info[first][]" value="<?php echo $k_first;?>"  <?php if (in_array($survey_info[$v_first]['title'], $med_type)) {echo "checked";}?> />
                                        <?php echo $survey_info[$v_first]['title'];?>
                                    </label>
                                </td>
                            </tr>
                            <?php $f++;}?>
                    </table>

                </td>
            </tr>
        </tbody>

            <?php $j=1;foreach ($survey_info as $k_q => $v_q) {?>
            <tbody style="display:none;" class="question_num" id="<?php echo $k_q;?>">
            <tr>
                <td height="10">&nbsp<?php echo $j;?></td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="1" cellpadding="1" cellspacing="1">
                        <tr height="19">
                            <td style="color:#fff;" height="19" colspan="<?php echo count($v_q['option'])+2;?>" align="center" bgcolor="<?php echo $v_q['bg_color'];?>"><?php echo $v_q['title'];?></td>
                        </tr>
                        <tr height="19">
                            <td  height="19" bgcolor="#99FFFF">年份</td>
                            <td  bgcolor="#99FFFF">规格</td>
                            <?php foreach ($v_q['option'] as $k_option => $v_option){?>
                                <td bgcolor="#99FFFF"><?php echo $v_option;?></td>
                            <?php }?>
                        </tr>
                        <?php $i=1;for($start=$v_q['start_year'];$start<=$v_q['end_year'];$start++){?>
                            <tr height="19" <?php if($i%2 == 0){echo "bgcolor='#CCCCCC'";};?>>
                                <td rowspan="<?php if($j > 1){?>2<?php }else{echo 1;};?>" height="38"><?php echo $start;?></td>
                                <td>销售总额（元）</td>
                                <?php foreach ($v_q['option'] as $k_option_sales_money => $v_option_sales_money){?>
                                    <td><label><input type="text" name="data_info[year_sales_money][<?php echo $k_q;?>][<?php echo $k_option_sales_money;?>][<?php echo $start;?>]" size="12" onblur="insert_data_info('<?php echo $k_q;?>', this.value, 'sales_money', <?php echo $start;?>,<?php echo $k_option_sales_money;?>);" value="<?php echo $med_data_code[$k_q][$start]["sales_money"][$k_option_sales_money];?>" /></label></td>
                                <?php }?>
                            </tr>
                            <?php if($j > 1){?>
                                <tr height="19" <?php if($i%2 == 0){echo "bgcolor='#CCCCCC'";};?>>
                                    <td height="19">销售总量（盒）</td>
                                    <?php foreach ($v_q['option'] as $k_option_sales_num => $v_option_sales_num){?>
                                        <td><input type="text" name="data_info[year_sales_num][<?php echo $k_q;?>][<?php echo $k_option_sales_num;?>][<?php echo $start;?>]" size="12" onkeyup="this.value=this.value.replace(/\D/g,'')" onblur="insert_data_info('<?php echo $k_q;?>', this.value, 'sales_num', <?php echo $start;?>,<?php echo $k_option_sales_num;?>);" value="<?php echo $med_data_code[$k_q][$start]["sales_num"][$k_option_sales_num];?>" /></td>
                                    <?php }?>
                                </tr>
                            <?php }?>
                            <?php $i++;}?>
                    </table>

                </td>
            </tr>
            </tbody>
            <?php $j++;}?>

            <tr class="submit_btn" <?php if(!$get_hos_drug){echo 'style="display:none;"';}?>>
                <td height="80" align="center">
                    <button type="button" class="btn btn-primary btn-lg" onclick="submit_page();">如全部填写，请提交</button>
                </td>
            </tr>
<!--        <tr><td height="80" align="center"><label><input type="submit" name="Submit" value="   提    交   " /></label></td></tr>-->
        <input type="hidden" name="drugs" id="drugs" value="" />
    </table>
</form>

<script>
    $(document).ready(function(){
        var options = {
            target:'',
            beforeSubmit:operationAlert,
            success:showResponse,
            url:'/one_survey/survey_spirit',
            type:'post',
            dataType:'text',
            clearForm:false,
            resetForm:false
        };
        $('#item_form_info').ajaxForm(options);
    });
    function operationAlert()
    {
        if (!confirm('<?php echo CONFIRM_SUBMISSION;?>')){
            return false;
        }
    }
    function showResponse(responseText){
        var output = jQuery.parseJSON(responseText);
        alert(output.rs_msg);
        //重新加载页面
        location.reload();
    }

    function get_drugs(q_num, is_show=false){
        if (is_show) {
            $("#"+q_num).show();
        } else {
            if($('#f_'+q_num).is(':checked')) {//当前选中状态
                $("#"+q_num).show();
            } else {
                $("#"+q_num).hide();
            }
            //获取选中的药品
            var drugs = $('input:checkbox[name="data_info[first][]"]:checked');
            var drugs_info = new Array();
            drugs.each(function() {
                var isCheck = this.value;
                // question3 += isCheck;
                drugs_info.push(isCheck);
            });
            $("#drugs").val(drugs_info);
            // console.log(drugs_info);

            insert_data_info("med_type", drugs_info);
        }
    }
    
    
    $(document).ready(function(){
        $(".select2_demo_3").select2({
            placeholder: "请选择或输入",
            allowClear: true
        });

        $('#hospital').typeahead({
            source: <?php echo $hospital_arr;?>
        });
        <?php if($get_hos_drug){?>
            get_drugs("q1", true);
        <?php }?>
        <?php if($med_type_code){?>
            <?php foreach ($med_type_code as $v_m_type) {?>
                get_drugs("<?php echo $v_m_type;?>", true);
            <?php }?>
        <?php }?>

    });

    //单个数据提交
    function insert_data_info(q, q_option, q_type='', year='', q_option_name='') {
        if (q_option == undefined ) {
            return false;
        }
        $.post('/one_survey/insert_survey_spirit', {q:q,q_option:q_option,uid:'<?php echo $uid;?>',q_type:q_type,year:year,q_option_name:q_option_name}, function (t) {
            if (t.rs_code == "error") {
                alert(t.rs_msg);
                return false;
            } else {
                if (q == "hospital") {
                    $("#question_content").show();
                    $("#q1").show();
                    $(".submit_btn").show();
                }
            }
        }, 'json');
    }
    
    //关闭页面
    function submit_page() {
        $.post('/one_survey/survey_finish', {uid:'<?php echo $uid;?>'}, function (t) {
            if (t.rs_code == "error") {
                alert(t.rs_msg);
                return false;
            } else {
                alert(t.rs_msg);
                if (t.rs_backurl) {
                    location.href = t.rs_backurl;
                }
            }
        }, 'json');


        // if(confirm("您已提交信息，确定关闭页面吗？")){
        //     location.href="/one_survey/finish_page";
        // }
    }
</script>

<script src="/theme/management/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<!-- Typehead -->
<script src="/theme/management/js/plugins/typehead/bootstrap3-typeahead.min.js"></script>

<script src="/theme/<?php echo TEMPLATE_DIR;?>/js/plugins/select2/select2.full.min.js"></script>
</body>
</html>
