<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui"
    />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />

    <title>IQVIA问卷-血管外科</title>
    <script src="/theme/management/js/jquery-3.1.1.min.js"></script>
    <script src="/theme/<?php echo TEMPLATE_DIR;?>/assets/js/jquery.form.js"></script>
    <link href="/theme/<?php echo TEMPLATE_DIR;?>/css/plugins/select2/select2.min.css" rel="stylesheet">
    <style>
        a {
            color: #337ab7;
            text-decoration: none;
        }
        .nav > li > a {
            color: #a7b1c2;
            font-weight: 600;
            padding: 14px 20px 14px 25px;
        }
        .nav.navbar-right > li > a {
            color: #999c9e;
        }
        .nav > li.active > a {
            color: #ffffff;
        }
        .navbar-default .nav > li > a:hover,
        .navbar-default .nav > li > a:focus {
            background-color: #293846;
            color: white;
        }
        .nav .open > a,
        .nav .open > a:hover,
        .nav .open > a:focus {
            background: #fff;
        }
        .nav.navbar-top-links > li > a:hover,
        .nav.navbar-top-links > li > a:focus {
            background-color: transparent;
        }
        .nav > li > a i {
            margin-right: 6px;
        }
        .navbar {
            border: 0;
        }
        .navbar-default {
            background-color: transparent;
            border-color: #2f4050;
        }
        .navbar-top-links li {
            display: inline-block;
        }
        .navbar-top-links li:last-child {
            margin-right: 40px;
        }
        .body-small .navbar-top-links li:last-child {
            margin-right: 0;
        }
        .navbar-top-links li a {
            padding: 20px 10px;
            min-height: 50px;
        }
        .dropdown-menu {
            border: medium none;
            border-radius: 3px;
            box-shadow: 0 0 3px rgba(86, 96, 117, 0.7);
            display: none;
            float: left;
            font-size: 12px;
            left: 0;
            list-style: none outside none;
            padding: 0;
            position: absolute;
            text-shadow: none;
            background-color: #fff;
            top: 100%;
            z-index: 21000;
        }
        .dropdown-menu > li > a {
            border-radius: 3px;
            color: inherit;
            line-height: 25px;
            margin: 4px;
            text-align: left;
            font-weight: normal;

            display: block;
            padding: 3px 20px;
            clear: both;


            white-space: nowrap;
        }
        .dropdown-menu > .active > a,
        .dropdown-menu > .active > a:focus,
        .dropdown-menu > .active > a:hover {
            color: #fff;
            text-decoration: none;
            background-color: #1ab394;
            outline: 0;
        }
        .dropdown-menu > li > a.font-bold {
            font-weight: 600;
        }
        .navbar-top-links .dropdown-menu li {
            display: block;
        }
        .navbar-top-links .dropdown-menu li:last-child {
            margin-right: 0;
        }
        .navbar-top-links .dropdown-menu li a {
            padding: 3px 20px;
            min-height: 0;
        }
        .navbar-top-links .dropdown-menu li a div {
            white-space: normal;
        }
        .nav.nav-tabs li {
            background: none;
            border: none;
        }

        .float-e-margins .btn {
            margin-bottom: 5px;
            /*margin-top: -4px;*/
        }
        .btn-primary {
            background-color: #1ab394;
            border-color: #1ab394;
            color: #FFFFFF;
        }
        .btn {
            border-radius: 3px;
        }
        .btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .btn-group-lg>.btn, .btn-lg {
            padding: 10px 16px;
            font-size: 18px;
            line-height: 1.3333333;
            border-radius: 6px;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        input[type="text"]{
            width: 85%;
            height: 28px;
            background: #FFFFCC;
            outline: none;
            border: 1px solid #cecece;
            border-radius:0;
        }

    </style>
</head>

<body>

<table width="90%" border="0" align="center">
    <!--<tr>
        <td align="center" valign="middle">IQVIA问卷-血管外科</td>
    </tr>-->

    <tr>
        <td>
            尊敬的临床专家：<br /><br />

            <p style="text-indent:25px">您好！我们是健康通市场调研有限公司的工作人员，现正在开展心血管外科方面的市场研究。鉴于您是这方面的专家，今天希望能占用您2分钟左右的时间就临床上血管外科方面采访您，希望能得到您的配合。
            </p>

            <p style="text-indent:25px">本次研究是有偿的，您完成之后会有20元的礼金作为酬谢略表心意，礼金以手机话费的形式默认充值到您当前的手机号码，三个工作日内到账，感谢您为医疗事业做出的贡献！</p>
        </td>
    </tr>

    <tbody id="question_content">
    <?php $j=1;foreach ($question as $k => $v) {?>
        <tr>
            <td height="10">&nbsp</td>
        </tr>
        <tr>
            <td>
                <table width="100%" border="1" cellpadding="1" cellspacing="1">

                    <tr height="19">
                        <td style="color:#fff;" height="19" colspan="2" align="left" bgcolor="<?php echo $v['bg_color'];?>"><?php echo $v['qid']."、".$v['title'];?></td>
                    </tr>
                    <?php $f = 0;foreach($v['option'] as $k_first => $v_first){
                        $is_check = "";
                        if ($res_iqvia && in_array($k_first, $res_iqvia[$v['qid']])) {
                            $is_check = "checked";
                        }
                        ?>
                        <tr height="19" <?php if($f%2 == 0){echo "bgcolor='#CCCCCC'";};?> >
                            <td height="19">
                                <label style="cursor: pointer;">
                                    <input onclick="insert_data('<?php echo $v['input_type'];?>', '<?php echo $v['qid'];?>');" type="<?php echo $v['input_type'];?>" id="f_<?php echo $k_first;?>" name="data_info[<?php echo $v['qid'];?>][]" value="<?php echo $k_first;?>" <?php echo $is_check;?>  />
                                    <?php echo $v_first;?>
                                </label>
                            </td>
                        </tr>
                        <?php $f++;}?>

                </table>

            </td>
        </tr>
    <?php }?>
    </tbody>

    <tr class="submit_btn">
        <td height="80" align="center">
            <button type="button" class="btn btn-primary btn-lg" onclick="submit_page();">如全部填写，请提交</button>
        </td>
    </tr>
    <!--        <tr><td height="80" align="center"><label><input type="submit" name="Submit" value="   提    交   " /></label></td></tr>-->
    <input type="hidden" name="drugs" id="drugs" value="" />
</table>

<script>
    function insert_data(type, q_id){
        if (type == "radio") {//单选题
            //获取选中的药品
            var drugs = $('input:radio[name="data_info['+q_id+'][]"]:checked');
            var drugs_info = drugs.val();
        }

        if (type == "checkbox") {//多选题
            //获取选中的药品
            var drugs = $('input:checkbox[name="data_info['+q_id+'][]"]:checked');
            var drugs_info = new Array();
            drugs.each(function() {
                var isCheck = this.value;
                drugs_info.push(isCheck);
            });
        }


        insert_data_info(q_id, drugs_info, type);
    }


    //单个数据提交
    function insert_data_info(q, q_option,type) {
        if (q_option == undefined ) {
            return false;
        }
        $.post('/one_survey/insert_iqvia_two', {q:q,q_option:q_option,uid:'<?php echo $uid;?>',type:type}, function (t) {}, 'json');
    }

    //关闭页面
    function submit_page() {
        $.post('/one_survey/iqvia_two_finish', {uid:'<?php echo $uid;?>'}, function (t) {
            if (t.rs_code == "error") {
                alert(t.rs_msg);
                return false;
            } else {
                alert(t.rs_msg);
                if (t.rs_backurl) {
                    location.href = t.rs_backurl;
                }
            }
        }, 'json');
    }
</script>

<script src="/theme/management/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<!-- Typehead -->
<script src="/theme/management/js/plugins/typehead/bootstrap3-typeahead.min.js"></script>

<script src="/theme/<?php echo TEMPLATE_DIR;?>/js/plugins/select2/select2.full.min.js"></script>
</body>
</html>
