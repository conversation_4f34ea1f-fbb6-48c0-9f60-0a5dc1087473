<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康通 - 药房标注验证</title>
    <link href="https://admin.drsay.cn/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/style.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/sumoselect.min.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/select2/select2.min.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
    <link href="https://admin.drsay.cn/theme/new_manage/jquery.datetimepicker.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/toastr/toastr.min.css" rel="stylesheet">

    <!--    <script src="/theme/admin/common/layui/jquery-1.9.1.min.js"></script>-->
    <!--    <script src="/theme/admin/jquery.datetimepicker.js"></script>-->
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/js/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/js/jquery.sumoselect.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/third-party/template.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/js/plugins/toastr/toastr.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/umeditor.config.js" charset="utf-8"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/umeditor.min.js" charset="utf-8"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/datetimepicker/js/bootstrap-datetimepicker.js" charset="UTF-8"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/datetimepicker/js/locales/bootstrap-datetimepicker.fr.js" charset="UTF-8"></script>
    <!--    项目子菜单导航-->
    <script src="https://admin.drsay.cn/theme/new_manage/jquery-js/public.js"></script>

    <!--提交loading样式-->
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/ladda/ladda-themeless.min.css" rel="stylesheet">
    <style media="screen">
        html, body {height:auto;}
        .loginscreen.middle-box {max-width:500px; width:500px;}
        .middle-box {padding-top:0;}
        .box {padding-bottom:5px; margin:150px auto 0; background-color:#fff;}
        .text-center {text-align:left;}
        .top {max-height:80px; min-height:60px; line-height:60px; overflow:hidden; background-color:#1ab394; text-align:center;}
        .font {font-size:35px; font-weight:400; color:#fff; letter-spacing:5px;}
        .form-group {margin:0 20px 20px;}
        .btn-primary {margin-left:20px;}
        .m-t {margin-top:20px;}
        .form-control {height:44px;}
    </style>
</head>


<body class="gray-bg">
<div class="middle-box text-center loginscreen animated fadeInDown box">
    <div>
        <div class="top">
            <h5 class="logo-name font">药店标注验证</h5>
        </div>
        <!-- <h3>Welcome to drsay.cn</h3> -->
        <form class="m-t" role="form" id="formbox" action="" method="post" onsubmit="return common_js.form_sumbit(this, 'show_msg', '处理中', 'post', '');">
            <div class="form-group">
                <input type="text" name="name" class="form-control" value="老百姓大药房(飞虹路店)" id="name" placeholder="药店名" >
            </div>
            <div class="form-group">
                <input type="text" name="addr" class="form-control" value="上海市虹口区飞虹支路13号" id="addr" placeholder="药店地址" >
            </div>
            <button class="btn btn-primary block full-width m-b" id="submit_but" type="button" style="max-width:80px; min-height:44px;"/>开始测试</button>
            <p class="text-muted text-center" id="resmsg" style="color: red;padding-top: 6px;padding-right: 12px;padding-bottom: 6px;padding-left: 12px;font-size: 14px;"></p>
        </form>
    </div>
</div>
<script type="text/javascript">
    //loading 加载样式
    $(document).ready(function () {

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        // Bind normal buttons
        Ladda.bind('.ladda-button', {
            timeout: 2000
        });
        // Bind progress buttons and simulate loading progress
        Ladda.bind('.progress-demo .ladda-button', {
            callback: function (instance) {
                var progress = 0;
                var interval = setInterval(function () {
                    progress = Math.min(progress + Math.random() * 0.1, 1);
                    instance.setProgress(progress);

                    if (progress === 1) {
                        instance.stop();
                        clearInterval(interval);
                    }
                }, 200);
            }
        });

        var l = $('.ladda-button-demo').ladda();

        l.click(function () {
            // Start loading
            l.ladda('start');

            // Timeout example
            // Do something in backend and then stop ladda
            setTimeout(function () {
                l.ladda('stop');
            }, 12000)

        });

        // 保存
        $("#submit_but").click(function () {
            if ($("#name").val() == ''){
                $('#resmsg').html("请输入药店名称！");
                return false;
            }
            if ($("#addr").val() == ''){
                $('#resmsg').html("请输入药店地址！");
                return false;
            }
            $.ajax({
                url: "/demo/yf/index", type: 'POST', dataType: 'json', data: $('#formbox').serialize(),
                success: function (res) {
                    rmsg = res.rs_msg;
                    console.log(rmsg.resmsg);
                    $('#resmsg').html(rmsg.resmsg);
                }
            });
        });
    });
</script>


<script type="text/javascript" src="/theme/admin/jquery-js/jquery-nav.js"></script>
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<!--树形下拉js-->
<!--<script src="/theme/admin/tree1/js/jquery-git1.min.js"></script>-->
<script src="/theme/admin/tree1/js/jquery-sortable-lists.min.js"></script>
<!-- Mainly scripts -->

<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/new_manage/js/inspinia.js"></script>
<script src="/theme/new_manage/js/plugins/pace/pace.min.js"></script>

<!-- SUMMERNOTE -->
<script src="/theme/new_manage/js/plugins/summernote/summernote.min.js"></script>
<script src="/theme/new_manage/assets/js/comm_init.js"></script>
<script src="/theme/new_manage/assets/js/comm_init_swal.js"></script>
<script src="/theme/new_manage/assets/js/jquery.form.js"></script>
<script src="/theme/new_manage/js/plugins/select2/select2.full.min.js"></script>


<!-- Sweet alert -->
<!--登陆后的首页图表-->
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.tooltip.min.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.spline.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.resize.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.pie.js"></script>
<!-- ChartJS-->
<script src="/theme/new_manage/js/plugins/chartJs/Chart.min.js"></script>
<!--登陆后的首页图表-->
<!-- Nestable List -->
<script src="/theme/new_manage/js/plugins/nestable/jquery.nestable.js"></script>

<!--自动完成-->
<script src="/theme/new_manage/js/plugins/typehead/bootstrap3-typeahead.min.js"></script>
<script src="/theme/admin/select-js/jquery.contextmenu.r2.js"></script>
<!--地区选择-->
<script src="/theme/jquery-city/js/city-picker.data.js"></script>
<script src="/theme/jquery-city/js/city-picker.js"></script>



<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>
<script src="/theme/new_manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>

<!-- Ladda -->
<script src="/theme/new_manage/js/plugins/ladda/spin.min.js"></script>
<script src="/theme/new_manage/js/plugins/ladda/ladda.min.js"></script>
<script src="/theme/new_manage/js/plugins/ladda/ladda.jquery.min.js"></script>

<script src="/theme/management/js/plugins/footable/footable.all.min.js"></script>
<script src="/theme/management/js/plugins/jsTree/jstree.min.js"></script>

<!-- Data picker -->
<script src="/theme/management/js/plugins/datapicker/bootstrap-datepicker.js"></script>

<!-- Date range use moment.js same as full calendar plugin -->
<script src="/theme/new_manage/js/plugins/fullcalendar/moment.min.js"></script>
<!-- Date range picker -->
<script src="/theme/new_manage/js/plugins/daterangepicker/daterangepicker.js"></script>


<!--富文本编辑器-->
<!--<script src="/theme/--><!--/js/plugins/summernote/summernote.min.js"></script>-->

</body>

</html>
