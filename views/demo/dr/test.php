<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息验证</title>
    <link href="https://admin.drsay.cn/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/style.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/sumoselect.min.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/select2/select2.min.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
    <link href="https://admin.drsay.cn/theme/new_manage/jquery.datetimepicker.css" rel="stylesheet">
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/toastr/toastr.min.css" rel="stylesheet">

    <!--    <script src="/theme/admin/common/layui/jquery-1.9.1.min.js"></script>-->
    <!--    <script src="/theme/admin/jquery.datetimepicker.js"></script>-->
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/js/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/js/jquery.sumoselect.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/third-party/template.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/js/plugins/toastr/toastr.min.js"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/umeditor.config.js" charset="utf-8"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/umeditor/umeditor.min.js" charset="utf-8"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/datetimepicker/js/bootstrap-datetimepicker.js" charset="UTF-8"></script>
    <script src="https://admin.drsay.cn/theme/new_manage/datetimepicker/js/locales/bootstrap-datetimepicker.fr.js" charset="UTF-8"></script>
    <!--    项目子菜单导航-->
    <script src="https://admin.drsay.cn/theme/new_manage/jquery-js/public.js"></script>

    <!--提交loading样式-->
    <link href="https://admin.drsay.cn/theme/new_manage/css/plugins/ladda/ladda-themeless.min.css" rel="stylesheet">
    <style media="screen">
        html, body {height:auto;}
        .loginscreen.middle-box {max-width:500px; width:500px;}
        .middle-box {padding-top:0;}
        .box {padding-bottom:5px; margin:150px auto 0; background-color:#fff;}
        .text-center {text-align:left;}
        .top {max-height:80px; min-height:60px; line-height:60px; overflow:hidden; background-color:#1ab394; text-align:center;}
        .font {font-size:35px; font-weight:400; color:#fff; letter-spacing:5px;}
        .form-group {margin:0 20px 20px;}
        .btn-primary {margin-left:20px;}
        .m-t {margin-top:20px;}
        .form-control {height:44px;}

        #up-files {
            cursor: pointer;
            width: 100%;
            opacity: 0;
            filter: alpha(opacity=0);
            position: absolute
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>医疗卫生专业人士职业信息验证DEMO</h5>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <form>
                            <div class="col-sm-12 m-b-xs">
                                <div class="pull-right">
                                    <button type="submit" id="submit_but" class="btn btn-primary ladda-button">添加</button>
                                </div>
                                <div class="fileinput fileinput-new pull-right" data-provides="fileinput">
                                    <span class="btn btn-default btn-file"><span class="fileinput-new">选择文件</span>
                                    <span class="fileinput-exists">更改</span><input type="file" name="datafile" id="datafile"/></span>
                                    <span class="fileinput-filename" style="color:red;"></span>
                                    <a href="#" class="close fileinput-exists" data-dismiss="fileinput" style="float: none">×</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="table-responsive">
                        <table id="res_data"  class="table table-striped table-bordered table-hover" ></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    //loading 加载样式
    $(document).ready(function () {

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        // Bind normal buttons
        Ladda.bind('.ladda-button', {
            timeout: 2000
        });
        // Bind progress buttons and simulate loading progress
        Ladda.bind('.progress-demo .ladda-button', {
            callback: function (instance) {
                var progress = 0;
                var interval = setInterval(function () {
                    progress = Math.min(progress + Math.random() * 0.1, 1);
                    instance.setProgress(progress);

                    if (progress === 1) {
                        instance.stop();
                        clearInterval(interval);
                    }
                }, 200);
            }
        });

        var l = $('.ladda-button-demo').ladda();

        l.click(function () {
            // Start loading
            l.ladda('start');

            // Timeout example
            // Do something in backend and then stop ladda
            setTimeout(function () {
                l.ladda('stop');
            }, 12000)

        });

        // 保存
        $("#submit_but").click(function () {
            if ($("#datafile").val() == ''){
                $('#resmsg').html("请上传文件");
                return false;
            }
            $('#res_data').html('');
            $("#submit_but").attr("disabled", true);
            var files = document.getElementById('datafile').files;
            var new_files = files[0];
            var formData = new FormData();
            formData.append('datafile', new_files);
            formData.append('act', 'checkdata');
            $.ajax({
                type: 'POST',
                dataType: 'json',
                data: formData,
                url: '/demo/dr/index',
                contentType: false,
                processData: false,
                mimeType: "multipart/form-data",
                success: function(res) {
                    $("#submit_but").attr("disabled", false);
                    if (res.rs_code == 'success') {
                        var resd = res.rs_msg;
                        // console.log(resd);
                        var thtml = '<thead><tr><th>'+resd[0].id+'</th><th>'+resd[0].unit+'</th><th>'+resd[0].province+'</th><th>'+resd[0].city+'</th><th>'+resd[0].drname+'</th><th>'+resd[0].department+'</th><th>'+resd[0].job_title+'</th><th>'+resd[0].match_type+'</th></tr></thead><tbody>';
                        for(var i=1; i < resd.length; i++) {
                            thtml = thtml + '<tr><td>'+resd[i].id+'</td><td>'+resd[i].unit+'</td><td>'+resd[i].province+'</td><td>'+resd[i].city+'</td><td>'+resd[i].drname+'</td><td>'+resd[i].department+'</td><td>'+resd[i].job_title+'</td><td>'+resd[i].match_type+'</td></tr>';
                        }
                        thtml = thtml + '</tbody>';
                        $('#res_data').html(thtml);
                    }
                },
                error: function(error) {
                    console.log(error);
                },
                complete: function(response) {}
            });
        });
    });
</script>


<script type="text/javascript" src="/theme/admin/jquery-js/jquery-nav.js"></script>
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<!--树形下拉js-->
<!--<script src="/theme/admin/tree1/js/jquery-git1.min.js"></script>-->
<script src="/theme/admin/tree1/js/jquery-sortable-lists.min.js"></script>
<!-- Mainly scripts -->

<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/new_manage/js/inspinia.js"></script>
<script src="/theme/new_manage/js/plugins/pace/pace.min.js"></script>

<!-- SUMMERNOTE -->
<script src="/theme/new_manage/js/plugins/summernote/summernote.min.js"></script>
<script src="/theme/new_manage/assets/js/comm_init.js"></script>
<script src="/theme/new_manage/assets/js/comm_init_swal.js"></script>
<script src="/theme/new_manage/assets/js/jquery.form.js"></script>
<script src="/theme/new_manage/js/plugins/select2/select2.full.min.js"></script>


<!-- Sweet alert -->
<!--登陆后的首页图表-->
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.tooltip.min.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.spline.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.resize.js"></script>
<script src="/theme/new_manage/js/plugins/flot/jquery.flot.pie.js"></script>
<!-- ChartJS-->
<script src="/theme/new_manage/js/plugins/chartJs/Chart.min.js"></script>
<!--登陆后的首页图表-->
<!-- Nestable List -->
<script src="/theme/new_manage/js/plugins/nestable/jquery.nestable.js"></script>

<!--自动完成-->
<script src="/theme/new_manage/js/plugins/typehead/bootstrap3-typeahead.min.js"></script>
<script src="/theme/admin/select-js/jquery.contextmenu.r2.js"></script>
<!--地区选择-->
<script src="/theme/jquery-city/js/city-picker.data.js"></script>
<script src="/theme/jquery-city/js/city-picker.js"></script>



<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>
<script src="/theme/new_manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>

<!-- Ladda -->
<script src="/theme/new_manage/js/plugins/ladda/spin.min.js"></script>
<script src="/theme/new_manage/js/plugins/ladda/ladda.min.js"></script>
<script src="/theme/new_manage/js/plugins/ladda/ladda.jquery.min.js"></script>

<script src="/theme/management/js/plugins/footable/footable.all.min.js"></script>
<script src="/theme/management/js/plugins/jsTree/jstree.min.js"></script>

<!-- Data picker -->
<script src="/theme/management/js/plugins/datapicker/bootstrap-datepicker.js"></script>

<!-- Date range use moment.js same as full calendar plugin -->
<script src="/theme/new_manage/js/plugins/fullcalendar/moment.min.js"></script>
<!-- Date range picker -->
<script src="/theme/new_manage/js/plugins/daterangepicker/daterangepicker.js"></script>


<!--富文本编辑器-->
<!--<script src="/theme/--><!--/js/plugins/summernote/summernote.min.js"></script>-->

</body>

</html>
