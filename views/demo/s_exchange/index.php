<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="Cache-Control" content="no-cache" />
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
  <title>关于执业医师单病种的信息收集</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    li {
      list-style: none;
    }

    .container {
      background: #fff;
      padding: 0 10px 0 10px;
      max-width: 500px;
      margin: 0 auto;
    }

    .logo {
      padding: 25px 0 5px 0;
      border-bottom: 1px solid #dee4ec;
    }

    .logo #logo_img {
      height: 30px;
      float: left;
    }

    .logo #logo_name {
      height: 30px;
      line-height: 30px;
      margin-left: 3px;
    }

    .title {
      font-size: 18px;
      text-align: center;
      font-weight: 700;
      height: 60px;
      line-height: 60px;
      position: relative;
      margin-bottom: 20px;
    }

    .title::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 2.5px;
      background: #53a4f4;
      bottom: 0px;
      left: 0;
    }

    .sm {
      margin-bottom: 20px;
      background: #ebeef2;
      padding: 20px 10px;
      text-align: justify;
    }

    .sure_job {
      padding: 30px 0;
      background: #eaf0f1;
    }

    .sure_job .job li {
      text-align: center;
    }

    .sure_job .job li span {
      display: inline-block;
      width: 25%;
      text-align: left;
    }

    .sure_job .job li input {
      width: 65%;
    }

    input {
      outline: none;
      -webkit-appearance: none;
      -webkit-tap-highlight-color: rgba(255, 255, 255, 255);
      border: 1px solid #dee4ec;
      padding-left: 5px;
      font-size: 14px;
      box-sizing: border-box;
      margin: 10px 0;
      border-radius: 0;
      height: 50px;
      color: rgb(102, 102, 102);
      border-color: rgb(222, 228, 236);
    }

    .content {
      margin-top: 20px;
      padding: 30px 0;
      background: #eaf0f1;
    }

    .content .head {
      height: 90px;
      padding: 0 20px;
    }

    .content .head img {
      width: 25px;
      margin-top: 10px;
    }

    .content .head .add {
      float: right;
      text-align: center;
      cursor: pointer;
    }

    .content ul li {
      text-align: center;
      border: 1px solid #ccc;
      margin: 0 10px;
    }

    .content ul li:not(:last-child) {
      border-bottom: none;
    }

    .content ul li .disease_name,
    .content ul li .operation_name {
      width: 70% !important;
    }

    .content ul li .disease_num,
    .content ul li .operation_num {
      width: 18% !important;
    }

    .content ul li .del {
      cursor: pointer;
    }

    .content ul li .del img {
      width: 18px;
      vertical-align: middle;
    }

    button.save {
      margin: 20px 0;
      border: none;
      outline: none;
      color: #fff;
      padding: 10px 0;
      background: #53a4f4;
      width: 50%;
      font-size: 24px;
      border-radius: 5px;
    }

    .search_box {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #f5f5f5;
      padding: 10px;
      width: 80%;
      box-sizing: border-box;
      border-radius: 3px;
      border: 1px solid #ccc;
      font-size: 16px;
      z-index: 9;
      max-width: 400px;
    }

    .search_box input {
      height: 50px;
      line-height: 50px;
      width: 100%;
      padding: 0 10px;
    }

    .search_box ul {
      height: 300px;
      padding: 10px;
      overflow: auto;
    }

    .search_box ul li {
      padding-top: 10px;
      cursor: pointer;
    }

    .loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #fff;
      padding: 10px;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 搜索弹框 -->
    <div class="search_box">
      <input type="text" placeholder="请输入搜索内容" />
      <ul>
        <li>暂无数据</li>
        <!-- 挖个坑 -->
      </ul>
    </div>

    <!-- logo -->
    <div class="logo">
      <img id="logo_img" src="http://admin.drsay.cn/theme/dk/img/logo.png" />
      <span id="logo_name"></span>
    </div>

    <!-- 标题 -->
    <p class="title">关于执业医师单病种的信息收集</p>

    <!-- 描述 -->
    <div class="sm">请仔细认真填写以下信息，确保准确无误。</div>

    <!-- form表单 -->
    <form id="form" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
      <!-- 确认你的职业信息 -->
      <div class="sure_job">
        <p style="
              font-weight: 700;
              text-align: left;
              margin-bottom: 10px;
              padding-left: 12px;
            ">
          请确认或更改您的执业信息
        </p>
        <ul class="job">
          <li>
            <span>医院</span>
            <input name="hospital" type="text" placeholder="请输入执业医院名称" />
          </li>
          <li>
            <span>科室</span>
            <input name="department" type="text" placeholder="请输入所在科室" />
          </li>
          <li>
            <span>职称</span>
            <input name="title" type="text" placeholder="请输入你的职称" />
          </li>
        </ul>
      </div>

      <!-- 诊量内容 -->
      <div class="content">
        <!-- 头部 -->
        <div class="head">
          <p style="line-height: 45px; font-weight: 700">
            过去一年的诊疗量(请至少填写3个疾病名称)
          </p>
          <span style="color: red; font-size: 12px; float: left; margin-top: 13px">点击右侧按钮添加多个疾病</span>
          <span class="add disease_add">
            <img src="http://admin.drsay.cn/theme/dk/img/add.png" alt="" />
          </span>
        </div>

        <!-- 列表 -->
        <ul class="disease_list">
          <li>
            <input class="disease_name" name="disease_name[1]" id="disease1" type="text" placeholder="诊疗疾病名称" readonly />
            <input name="disease_id[1]" class="disease_id" type="hidden" />
            <input type="text" class="disease_num" name="disease_num[1]" placeholder="诊疗量" onkeyup="value=value.replace(/[^\d]/g,'')" />
            <span class="del" style="visibility: hidden">
              <img src="http://admin.drsay.cn/theme/dk/img/del.png" alt="" />
            </span>
          </li>
          <li>
            <input class="disease_name" name="disease_name[2]" id="disease2" type="text" placeholder="诊疗疾病名称" readonly />
            <input class="disease_id" name="disease_id[2]" type="hidden" />
            <input type="text" class="disease_num" name="disease_num[2]" placeholder="诊疗量" onkeyup="value=value.replace(/[^\d]/g,'')" />
            <span class="del" style="visibility: hidden">
              <img src="http://admin.drsay.cn/theme/dk/img/del.png" alt="" />
            </span>
          </li>
          <li>
            <input class="disease_name" name="disease_name[3]" id="disease3" type="text" placeholder="诊疗疾病名称" readonly />
            <input class="disease_id" name="disease_id[3]" type="hidden" />
            <input type="text" class="disease_num" name="disease_num[3]" placeholder="诊疗量" onkeyup="value=value.replace(/[^\d]/g,'')" />
            <span class="del" style="visibility: hidden">
              <img src="http://admin.drsay.cn/theme/dk/img/del.png" alt="" />
            </span>
          </li>
        </ul>
      </div>

      <!-- 手术内容 -->
      <div class="content">
        <!-- 头部 -->
        <div class="head">
          <p style="line-height: 45px; font-weight: 700">过去一年的手术量</p>
          <p style="color: red; font-size: 12px; float: left; margin-top: 13px">
            点击右侧按钮添加多个手术
          </p>
          <span class="add operation_add">
            <img src="http://admin.drsay.cn/theme/dk/img/add.png" alt="" />
          </span>
        </div>

        <!-- 列表 -->
        <ul class="operation_list"></ul>
      </div>

      <div style="text-align: center; margin-top: 10px">
        <button type="submit" class="save">提交</button>
      </div>
    </form>

    <iframe name="hidden" style="display: none"></iframe>

    <!-- form表单 -->
  </div>

  <!-- 疾病 -->
  <ul class="append_disease" style="display: none">
    <li>
      <input class="disease_name" name="disease_name[1]" type="text" placeholder="诊疗疾病名称" readonly />
      <input class="disease_id" name="disease_id[1]" type="hidden" />
      <input type="text" class="disease_num" name="disease_num[1]" placeholder="诊疗量" onkeyup="value=value.replace(/[^\d]/g,'')" />
      <span class="del">
        <img src="http://admin.drsay.cn/theme/dk/img/del.png" alt="" />
      </span>
    </li>
  </ul>

  <!-- 手术量 -->
  <ul class="append_operation" style="display: none">
    <li>
      <input class="operation_name" name="operation_name[1]" type="text" placeholder="手术名称" readonly />
      <input class="operation_id" name="operation_id[1]" type="hidden" />
      <input type="text" class="operation_num" name="operation_num[1]" placeholder="手术量" onkeyup="value=value.replace(/[^\d]/g,'')" />
      <span class="del">
        <img src="http://admin.drsay.cn/theme/dk/img/del.png" alt="" />
      </span>
    </li>
  </ul>

  <div class="loading">加载中...</div>

  <!-- 模版编译 -->
  <script type="text/html" id="searchTpl">
  {{ each list v i }}
  <li data-id="{{v.id}}">{{v.text}}</li>
  {{ /each }}
  </script>

  <script src="http://admin.drsay.cn/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
  <script src="http://admin.drsay.cn/theme/drcenter/js/template-web.js"></script>
  <script src="http://admin.drsay.cn/theme/bk/jquery.form.min.js"></script>

  <script>
    /************************** 表单提交 **************************/
    var common_js = {
      form_sumbit: function(item_form, msg_id) {
        $(item_form).ajaxSubmit({
          target: "",
          beforeSubmit: null,
          success: common_js.show_response(msg_id),
          url: "/dk/save",
          type: "post",
          dataType: "text",
          clearForm: false,
          resetForm: false,
          cache: false,
        });
        return false;
      },
      //接受PHP返回信息并重绘提示框
      show_response: function(msg_id) {
        return function(str) {
          var res = jQuery.parseJSON(str);
          alert(res.rs_msg);
          if (res.rs_backurl != "" && res.rs_backurl != undefined) {
            window.location.href = res.rs_backurl;
          }
        };
      },
      //版本号
      tver: "1.0.1",
    };
    common_js;
    /************************** 表单提交end **************************/
    $(function() {
      $(".loading").hide();
      /************************** 诊量内容 **************************/

      //  添加
      add_item("disease");
      // 删除诊疗
      delete_item("disease");
      // 搜索
      search_item("disease", "/dk/diagnosis_code", "diagnosis_name");

      /**************************手术量内容**************************/

      add_item("operation");
      // 删除诊疗
      delete_item("operation");
      // 搜索
      search_item("operation", "/dk/drgs_operation", "operation_name");

      /********************************** 公共方法 **********************************/

      // 禁用选择输入历史
      $("input").attr("autocomplete", "off");

      // 删除
      function delete_item(type) {
        $(".content").on("click", "." + type + "_list .del", function() {
          $(this).parent().remove();
        });
      }

      // 添加
      function add_item(type) {
        $(".content .head ." + type + "_add").click(function() {
          var list_length = $("." + type + "_list").children().length + 1;
          change_name(type, list_length);
          $("." + type + "_list").append($(".append_" + type + "").html());
        });
      }

      // 搜索
      function search_item(type, url, name) {
        $(".container").on("click", "." + type + "_name", function(e) {
          e.stopPropagation();
          init_search_box();
          var that = $(this);
          var timeOut = null;
          $(".search_box ").off("input").on("input", "input", function() {
            if (timeOut) {
              clearTimeout(timeOut)
            }
            timeOut = setTimeout(function() {
              var val = $(".search_box input").val()
              getRsMsg(url, {
                [name]: val,
              });
            }, 500)
          });
          $(".search_box")
            .off("click")
            .on("click", "ul li", function() {
              that.val($(this).text());
              that.next().attr("value", $(this).data("id"));
              $(".search_box").hide();
            });
        });
      }

      // 改变name
      function change_name(type, num) {
        $(".append_disease ." + type + "_name").attr(
          "name",
          type + "_name[" + num + "]"
        );
        $(".append_disease ." + type + "_num").attr(
          "name",
          type + "_num[" + num + "]"
        );
        $(".append_disease ." + type + "_id").attr(
          "name",
          type + "_id[" + num + "]"
        );
      }

      // 初始化search_box
      function init_search_box() {
        $(".search_box input").val("");
        $(".search_box ul").text("暂无数据!");
        $(".search_box").show();
      }

      // 发送ajax请求获取数据
      function getRsMsg(url, data) {
        $.ajax({
          async: false,
          type: "post",
          url: url,
          dataType: "json",
          data: data,
          beforeSend: function() {
            $(".search_box ul").text("搜索中....");
          },
          success: function(res) {
            if (!res.rs_msg.length) {
              $(".search_box ul").text("暂无数据!");
              return false
            }
            if (res.rs_code == "success") {
              $(".search_box ul").html(
                template("searchTpl", {
                  list: res.rs_msg,
                })
              );
            }
          },
        });
      }

      // 点击其他地方隐藏搜索框
      $(document).click(function() {
        $(".search_box").hide();
      });
      $(".search_box input").on("click", function(e) {
        e.stopPropagation();
      });
      /********************************** 公共方法end **********************************/
    });
  </script>
</body>

</html>