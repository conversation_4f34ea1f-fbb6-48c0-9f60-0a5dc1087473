<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
  <title>幸运转盘</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    body,
    html {
      height: 100%;
      width: 100%;
      background-color: #f5f5f5;
      color: #666;
      font-size: 16px;
    }

    #loading {
      position: fixed;
      z-index: 400;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0%;
      text-align: center;
      padding-top: 100px;
      color: #595758;
      background-color: #ffffff;
    }

    .prize {
      margin: 50px auto;
      background: #fff;
      width: 818px;
      height: 100%;
      padding-top: 50px;
    }

    #dc-luck-draw {
      position: relative;
      text-align: center;
      margin-top: 20px;
    }

    #dc-luck-draw #draw-chance {
      height: 70px;
      line-height: 50px;
    }

    .dc-hide {
      display: none;
    }

    #dc-luck-draw #luck-dial {
      margin: 0 auto;
      width: 290px;
      height: 290px;
      padding: 5px;
      position: relative;
      background: url("/theme/survey/img/border.gif") no-repeat center;
      background-size: contain;
      overflow: hidden;
    }

    #dc-luck-draw #luck-dial-content {
      box-sizing: content-box;
      position: absolute;
      left: 10px;
      top: 10px;
      height: 270px;
      width: 270px;
      border: 5px solid #feda71;
      border-radius: 50%;
    }

    #dc-luck-draw #luck-dial-content .prize-list {
      z-index: 1;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      padding: 0;
    }

    #dc-luck-draw #luck-dial-content .prize-list-item {
      list-style: none;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      color: #ff6d56;
      font-weight: bold;
      text-shadow: 0 1px 1px rgba(255, 255, 255, 0.6);
      font-size: 16px;
    }

    #dc-luck-draw #luck-dial-content .prize-list-item .prize-list-item-content {
      position: relative;
      display: block;
      width: 94px;
      margin: 0 auto;
      text-align: center;
      font-size: 12px;
      -webkit-transform-origin: 50% 135px;
      -ms-transform-origin: 50% 135px;
      transform-origin: 50% 135px;
    }

    #dc-luck-draw #luck-dial-content .prize-list-item .prize-list-item-content-title {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      line-height: 12px;
      padding-top: 20px;
      padding-bottom: 10px;
      word-break: break-all;
      word-wrap: break-word;
    }

    #dc-luck-draw #luck-dial-content .prize-list-item img {
      height: 30px !important;
    }

    #dc-luck-draw #draw-btn {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 70px;
      height: 90px;
      transform: translate(-50%, -54%);
      background: url(/theme/survey/img/button.png) no-repeat center;
      background-size: contain;
      font-family: PingFangSC-Semibold;
      font-size: 22px;
      color: #ffffff;
      line-height: 112px;
      text-align: center;
      cursor: pointer;
      z-index: 5;
    }

    .prize .create_question {
      margin-top: 60px;
      width: 200px;
      height: 50px;
      line-height: 50px;
      color: #fff;
      background: #53a4f4;
      border-radius: 5px;
      margin: 60px auto;
    }

    .prize .erweima {
      display: none;
      text-align: center;
      margin-top: 30px;
    }

    .prize .erweima .img {
      display: inline-block;
      width: 100px;
      height: 100px;
      background: url("/theme/survey/img/erweima.png");
      background-size: 120px;
      background-position: -10px -8px;
    }

    .dialog {
      /* position: fixed;
      z-index: 999;
      top: 200px;
      left: 50%;
      transform: translateX(-50%);
      background: #fff; */
      display: none;
      width: 300px;
      height: 200px;
      border: 1px solid #dee4ec;
      box-shadow: 10px 10px 5px #dee4ec;
      text-align: center;
      box-sizing: border-box;
      margin: 40px auto;
    }

    .dialog .title {
      position: relative;
      height: 50px;
    }

    .dialog .title img {
      width: 200px;
      position: absolute;
      top: -20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .dialog .title p {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      color: #fff;
    }

    .dialog .title span {
      position: absolute;
      right: 10px;
      color: #666;
      cursor: pointer;
    }

    .dialog .content {
      color: red;
      font-size: 34px;
      margin-top: 20px;
    }

    @keyframes rowinfinite {
      0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
      }

      100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    .rowinfinite {
      animation: rowinfinite 25s linear infinite;
    }
  </style>
</head>

<body>
  <div id="loading">加载中...</div>
  <div class="prize">
    <div class="consorry">
      <p id="start_title" style="text-align: center; font-size: 18px">点击开始按钮抽奖</p>
      <div class="wj_luck_draw" id="wj_luck_draw">
        <div id="dc-luck-draw">
          <div id="draw-chance">
            感谢您的参与！送您
            <span style="color: #ff6d56; font-size: 24px">1</span> 次抽奖机会
          </div>
          <div id="luck-dial">
            <div id="luck-dial-content" class="rowinfinite">
              <ul class="prize-list">
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0turn)">
                    <div class="prize-list-item-content-title" style="color: #ff6d56">
                      5元微信红包
                    </div>
                    <img src="/theme/survey/img/hongbao.png" />
                  </div>
                </li>
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0.125turn)">
                    <div class="prize-list-item-content-title" style="color: #ff6d56">
                      8元微信红包
                    </div>
                    <img src="/theme/survey/img/hongbao.png" />
                  </div>
                </li>
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0.25turn)">
                    <div class="prize-list-item-content-title" style="color: #ff6d56">
                      10元微信红包
                    </div>
                    <img src="/theme/survey/img/hongbao.png" />
                  </div>
                </li>
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0.375turn)">
                    <div class="prize-list-item-content-title" style="color: #ff6d56">
                      12元微信红包
                    </div>
                    <img src="/theme/survey/img/hongbao.png" />
                  </div>
                </li>
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0.5turn)">
                    <div class="prize-list-item-content-title" style="color: #ff6d56">
                      15元微信红包
                    </div>
                    <img src="/theme/survey/img/hongbao.png" />
                  </div>
                </li>
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0.625turn)">
                    <div class="prize-list-item-content-title" style="color: #ff6d56">
                      18元微信红包
                    </div>
                    <img src="/theme/survey/img/hongbao.png" />
                  </div>
                </li>
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0.75turn)">
                    <div class="prize-list-item-content-title" style="color: #ff6d56">
                      20元微信红包
                    </div>
                    <img src="/theme/survey/img/hongbao.png" />
                  </div>
                </li>
                <li class="prize-list-item">
                  <div class="prize-list-item-content" style="transform: rotate(0.875turn)">
                    <div class="prize-list-item-content-title" style="color: #aaa">
                      谢谢惠顾
                    </div>
                    <img src="/theme/survey/img/xiexie.png" />
                  </div>
                </li>
              </ul>
            </div>
            <div id="draw-btn">开始</div>
          </div>
        </div>


        <!-- 模态框 -->
        <div class="dialog">
          <div class="title">
            <img src="/theme/survey/img/choujiang.png" alt="" />
            <p>抽奖结果</p>
            <!-- <span>X</span> -->
          </div>
          <div class="content">谢谢惠顾</div>
        </div>
      </div>
    </div>
    <div class="create_question" style="text-align: center; cursor: pointer">
      健康通祝您好运
    </div>
    <div class="erweima">
      <p>扫描健康通微信支付码关注领取红包</p>
      <span class="img"></span>
      <p>扫码二维码领取红包</p>
    </div>
  </div>
  <!-- 模态框 -->
  <!-- <div class="dialog" style="display: none">
    <div class="title">
      <img src="/theme/survey/img/choujiang.png" alt="" />
      <p>抽奖结果</p>
      <span>X</span>
    </div>
    <div class="content">谢谢惠顾</div>
  </div> -->
  <script src="http://admin.drsay.cn/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
  <script src="/theme/survey/js//juqeryRotate.js"></script>
  <script>
    $(function() {
      $(window).load(function() {
        $("#loading").hide();
      });
      var i = 0;
      // 点击按钮开始抽奖
      $("#draw-btn").click(function() {
        i++;
        // 只允许点一次
        if (i < 2) {
          // 移除css动画，执行jq动画
          $("#luck-dial-content").removeClass("rowinfinite");
          var angle = Math.random() * 8;
          angle = Math.ceil(angle) * 45;
          $("#luck-dial-content").rotate({
            angle: 45,
            animateTo: angle + 2520,
            callback: function() {
              setTimeout(function() {
                var lis;
                if (angle > 0) {
                  lis = 9 - angle / 45;
                } else {
                  lis = 1;
                }
                $("#draw-chance span").text("0");
                var result = $(".prize-list li:nth-child(" + lis + ")")
                  .children()
                  .children()
                  .text()
                  .trim()
                $(".dialog .content").text(result);

                $("#start_title").css({
                  color: "red",
                  fontSize: "32px"
                })
                $("#start_title").text(result)

                // 未获奖
                if (result == "谢谢惠顾") {
                  $(".create_question").text("很遗憾，祝您下次好运")

                } else {
                  // 获奖
                  $(".create_question").text("提取奖金")
                  $(".create_question").click(function() {
                    window.location.href = "http://www.drsay.cn/demo/s_exchange/order"
                  })
                }

                $(".dialog").show()
                $("#luck-dial").hide()
                // $(".dialog").fadeIn(100);
              }, 1000);

              // setTimeout(function() {
              //   // $(".dialog").hide();
              //   // $(".create_question").hide();
              //   // $(".erweima").show();
              //   $(".create_question").text("提取奖金")
              //   $(".create_question").click(function() {
              //     window.location.href = "http://www.drsay.cn/demo/s_exchange/order"
              //   })
              // }, 3000);
            },
          });
        }
        // $("body").click(function() {
        //   if ($(".dialog").is(":visible")) {
        //     $(".dialog").hide();
        //     // $(".create_question").hide();
        //     // $(".erweima").show();
        //   }
        // });
      });

      // 适应
      $(window)
        .resize(function() {
          if ($(window).width() > 750) {
            $("body").css("color", "#666");
            $(".prize").css({
              margin: "50px auto",
              width: "818px",
            });
          } else {
            $("body").css("color", "#000");
            $(".prize").css({
              margin: "0px auto",
              width: "100%",
            });
          }
        })
        .trigger("resize");
    });

    function union(data, union) {
      var first = [];
      var second = [];
      var third = [];
      var selectedIndex = [0, 0, 0];
      var checked = [0, 0, 0];

      function creatList(obj, list) {
        obj.forEach(function(item, index, arr) {
          var temp = new Object();
          temp.text = item.name;
          temp.value = index;
          list.push(temp);
        });
      }
      creatList(data, first);
      if (data[selectedIndex[0]].hasOwnProperty("sub")) {
        creatList(data[selectedIndex[0]].sub, second);
      } else {
        second = [{
          text: "",
          value: 0
        }];
      }
      if (
        data[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty("sub")
      ) {
        creatList(data[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
      } else {
        third = [{
          text: "",
          value: 0
        }];
      }
      var unionpicker = new Picker({
        data: [first, second, third],
        selectedIndex: selectedIndex,
        title: "地址选择",
      });
      unionpicker.on("picker.select", function(selectedVal, selectedIndex) {
        var text1 = first[selectedIndex[0]].text;
        var text2 = second[selectedIndex[1]].text;
        var text3 = third[selectedIndex[2]] ?
          third[selectedIndex[2]].text :
          "";

        $("#" + union + "").text(text1 + " " + text2 + " " + text3);
      });
      unionpicker.on("picker.change", function(index, selectedIndex) {
        if (index === 0) {
          firstChange();
        } else if (index === 1) {
          secondChange();
        }

        function firstChange() {
          second = [];
          third = [];
          checked[0] = selectedIndex;
          var firstUnion = data[selectedIndex];
          if (firstUnion.hasOwnProperty("sub")) {
            creatList(firstUnion.sub, second);
            var secondUnion = data[selectedIndex].sub[0];
            if (secondUnion.hasOwnProperty("sub")) {
              creatList(secondUnion.sub, third);
            } else {
              third = [{
                text: "",
                value: 0
              }];
              checked[2] = 0;
            }
          } else {
            second = [{
              text: "",
              value: 0
            }];
            third = [{
              text: "",
              value: 0
            }];
            checked[1] = 0;
            checked[2] = 0;
          }
          unionpicker.refillColumn(1, second);
          unionpicker.refillColumn(2, third);
          unionpicker.scrollColumn(1, 0);
          unionpicker.scrollColumn(2, 0);
        }

        function secondChange() {
          third = [];
          checked[1] = selectedIndex;
          var first_index = checked[0];
          if (data[first_index].sub[selectedIndex].hasOwnProperty("sub")) {
            var secondUnion = data[first_index].sub[selectedIndex];
            creatList(secondUnion.sub, third);
            unionpicker.refillColumn(2, third);
            unionpicker.scrollColumn(2, 0);
          } else {
            third = [{
              text: "",
              value: 0
            }];
            checked[2] = 0;
            unionpicker.refillColumn(2, third);
            unionpicker.scrollColumn(2, 0);
          }
        }
      });
    }
  </script>
</body>

</html>