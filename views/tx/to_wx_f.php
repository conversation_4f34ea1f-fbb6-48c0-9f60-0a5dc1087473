<html lang="zh" style="display: block; font-size: 25.875px;"><head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui">
    <meta name="keywords" content="上医说">
    <meta name="description" content="上医说">
    <meta name="renderer" content="webkit">
    <meta name="robots" content="all,index,follow">
    <meta content="telephone=no" name="format-detection">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="full-screen" content="yes">
    <meta name="browsermode" content="application">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <meta name="msapplication-tap-highlight" content="no">
    <title>健康通-上医说</title>
    <link rel="shortcut icon" href="/theme/images/favicon.ico" type="image/x-icon">
    <!--    <link rel="stylesheet" href="http://medical.drrenew.com/theme/3sbioinc/font-awesome/css/font-awesome.css" />-->
    <link rel="stylesheet" href="/theme/management/font-awesome/css/font-awesome.css">
    <script src="/theme/management/js/jquery-3.1.1.min.js"></script>
    <link href="/theme/css/web_style.css" rel="stylesheet">
    <!--防止被网络广告劫持弹出广告-->
    <style>
        html {
            display: none;
        }

        body,
        html {
            overflow-x: hidden;
        }

        .logo {
            width: 100%;
            margin: 0px 0 5px 15px;
        }

        .logo .web {
            float: right;
            font-size: 24px;
            margin-right: 30px;
            margin-top: 20px;
            color: #0b3c82;

        }

        .logo img {
            width: auto;
            height: 45px;
        }

        .line {
            width: 100%;
            height: 1px;
            background: #a2bde8;
            margin: 15px 0;
        }

        input {
            -webkit-appearance: none;
            outline: none;
        }

        button {
            cursor: pointer;
        }

        .payment_style label {
            text-align: left;
            display: block;
            padding: 5px 15px;
        }

        /* .payment_style label input {
            width: 20px !important;
        } */

        .alpay {
            padding: 20px 0;
        }

        .alpay label {
            display: block;
        }

        .alpay label span {
            display: inline-block;
            margin-bottom: 10px;
        }

        .alpay label input {
            width: 100% !important;
            height: 44px;
        }

        .single .input {
            display: inline-block;
            width: 22px;
            height: 22px;
            border: 1px solid #ccc;
            border-radius: 50%;
            box-sizing: border-box;
            margin-right: 10px;
            vertical-align: middle;
        }

        .submit {
            width: 90%;
            padding: 10px;
            border-radius: 5px;
            background: #fff;
            border: none;
            font-size: 22px;
            color: #fff;
            outline: none;
            letter-spacing: 10px;
            background: #53a4f4;
        }

        .box_pic_center {
            padding-bottom: 30px;
        }

        /* 对话框 */
        .dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            position: fixed;
            left: 0;
            top: 0;
        }

        .dialog {
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            background: #CAE1FF;
            text-align: center;
            width: 86%;
            color: #fff;
            min-height: 260px;
            font-size: 24px;
            border-radius: 3px;
            border: 1px solid #010810;
            box-sizing: border-box;
        }

        .dialog .title {
            background: #103e80;
            padding: 15px 0;
            border-bottom: 1px solid #072a56;
            color: #fff;
        }

        .dialog .word {
            padding: 40px 0;
            color: #041b3d;
            word-break: break-word;
        }

        .dialog .button {
            width: 100%;
        }

        .dialog .button button {
            word-break: break-word;
            box-sizing: border-box;
            width: 100%;
            border: none;
            outline: none;
            padding: 10px;
            background: #53a4f4;
            font-size: 22px;
            color: #fff;
            border-top: 1px solid #CAE1FF;
        }

        .dialog .last button {
            border-bottom: 1px solid #406eb0;
        }

        .dialog .first button {
            border-top: 1px solid #406eb0;
        }

        .dialog .bg {
            height: 20px;
            background: #3e6fb7;
            margin-top: 30px;
        }


        .dialog .button button:hover {
            background: #eda124;
        }

        .confirm_dialog_container {
            display: none;
            width: 100%;
            height: 100%;
            position: fixed;
            top: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .confirm_dialog {
            width: 80%;
            min-height: 150px;
            margin: 0 auto;
            background: #fff;
            border: 1px solid #252424;
            border-radius: 5px;
            overflow: hidden;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .confirm_dialog .notice_title {
            padding: 10px 0;
            border-bottom: 1px solid #252424;
            background: #aa90da;
            font-size: 20px;
        }

        .confirm_dialog .notice_info {
            padding: 50px 0;
            font-size: 18px;
            border-bottom: 1px solid #252424;
        }

        .confirm_dialog .button {
            height: 44px;
            padding: 0px 30px;
        }

        .confirm_dialog .button button {
            height: 100%;
            min-width: 75px;
            border: 1px solid #ccc;
            outline: none;
            border-bottom: none;
            border-top: none;
            border-radius: 15px;
            font-size: 16px;
        }

        .confirm_dialog .button .cancel {
            background: #efefef;
            float: left;
        }

        .confirm_dialog .button .confirm {
            background: #aa90da;
            float: right;
        }

        .code {
            padding-top: 20px;
        }

        .code p {
            height: 40px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            border-radius: 0.15rem;
        }

        .code p input {
            height: 100%;
            padding: 0 10px;
            border: none;
        }

        /*.code p button {*/
        /*height: 100%;*/
        /*padding: 0 10px;*/
        /*border: none;*/
        /*outline: none;*/
        /*background: #a6ebcd;*/
        /*float: right;*/
        /*}*/

        .code p button {
            height: 100%;
            width: 120px;
            border: none;
            outline: none;
            background: #a6ebcd;
            float: right;
        }

        .code input[name="verify_code"] {
            width: 40%;
            float: left;
        }

        .disable {
            pointer-events: none;
        }
    </style>
    <script>
        if (self == top) {
            document.documentElement.style.display = 'block';
        } else {
            top.location = self.location;
        }
    </script>
    <!--防止被网络广告劫持弹出广告-->
    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(document.documentElement.getBoundingClientRect().width);
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + 'px';
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
<div class="layout">
    <div class="logo">
        <a href="http://www.gooddr.com/" target="_blank" title="drsay">
            <img src="/theme/admin/im/img/jkt_logo.png" title="drsay" alt="drsay" border="0">
        </a>

        <img style="float: right; margin-right: 30px;" src="http://www.drsay.cn/theme/images/jkt.jpg">
    </div>
    <div class="box_pic_top"></div>
    <div class="box_pic_center">
        <input type="hidden" name="scene" value="1346_2982112_9fd861">
        <h1 class="title_font">
            <span class="STYLE1">感谢您成功加入赛小欧互联网医院</span>
        </h1>
        <h2 style="text-align: center;">

            <p class="status" style="font-size:22px;padding: 10px 0;">为表达您对我们活动的支持，健康通送您5000积分表达感谢！</p>

            <!-- s c 和q -->
            <p style="font-size: 24px;padding: 10px;color: #66a325;font-weight: 700">您可通过以下方式获得</p>
            <!--<p style="font-size:28px;font-weight:700;color: #d0021b;padding: 20px 0;">12000<span style="margin:0 5px;">积分</span>=<span style="margin-left:5px;">120</span><span style="margin:0 5px;">元</span></p>-->
            <!-- s c 和q -->


            <!-- 分界线返回状态是c且未申请 -->
            <p class="line"></p>

            <div class="payment_style" style="font-size:20px;">
                <p class="single">
                    <label>
                        <span class="input"></span><input type="radio" style="display: none;" data-name="alpay" name="payment_type" value="206">转入我的支付宝<font color="green">(建议)</font>
                    </label>
                </p>


                <p class="single">
                    <label id="wechat">
                        <span class="input"></span><input type="radio" style="display: none;" data-name="wechat" name="payment_type" value="8779">转入我的微信零钱
                    </label>
                </p>


                <div class="wechat" style="display:none;;background:#CAE1FF;padding: 10px 0;width: 90%;margin: 0 auto;font-size: 12px;margin: 10px;">
                    <p>
                        请使用您的手机微信扫码或长按下图提现
                    </p>

                    <input type="hidden" name="wechat_open_id">
                </div>


                <!--                <p class="single">-->
                <!--                    <label>-->
                <!--                        <span class="input"></span><input type="radio" style="display: none;" data-name="point" name="payment_type" value="8774">转入我的会员积分-->
                <!--                        <input type="hidden" name="point">-->
                <!--                    </label>-->
                <!--                </p>-->
                <!---->
                <!--                -->
                <!---->
                <!--              -->
                <!--                <p class="single">-->
                <!--                    <label>-->
                <!--                        <span class="input"></span>-->
                <!--                        <input type="radio" style="display: none;" data-name="donation" name="payment_type" value="101">慈善捐赠<br>-->
                <!--                        <span style="font-size: 16px; margin-left: 27px;">（上海红十字会合作代赠）</span>-->
                <!--                    </label>-->
                <!--                </p>-->



                <!-- 手机验证码 -->
                <div class="code">
                    <p>
                        <input name="verify_mobile" type="text" placeholder="请输入手机号码">
                    </p>
                    <p>
                        <input name="verify_code" type="text" placeholder="请输入验证码" style="width: 210px;">
                        <button>获取验证码</button>
                    </p>
                </div>




                <p style="padding:20px 0;">
                    <span style="color:red;display: block;padding:10px 0;" class="submit_confirm"></span>
                    <button class="submit">确认</button>
                </p>

                <!-- 分界线返回状态是c且未申请 -->


                <!-- <p>请使用您的手机微信<font color='red'><b>扫一扫</b></font>扫码提现</p> -->
                <!-- 您的分值太低，无法进行提现，请联系项目管理员！ -->

            </div></h2>
        <br>
        <div align="center">
            <!-- <div style='padding-bottom:10px;'>二维码参数：-->                <!--;参数长度：-->                <!--</div>-->
        </div>
    </div>
    <div class="box_pic_bottom"></div>
</div>

<!-- 提交成功提示框 -->
<div class="dialog_container">
    <div class="dialog">
        <p class="title">提交成功!!!</p>
        <p class="word">7个工作日内支付</p>
        <p class="button first">
            <button></button>
        </p>
        <p class="button last">
            <a href="http://www.gooddr.com?code=2982112_1346_707515ea012ffe1c" target="_blank"><button>健康通官网</button></a>
        </p>
        <p class="bg"></p>
    </div>
</div>

<!-- 提交确认框 -->
<div class="confirm_dialog_container">
    <div class="confirm_dialog">
        <p class="notice_title">确认提醒</p>
        <p class="notice_info">您确认要选择这种支付方式吗?</p>
        <p class="button">
            <button class="cancel">取消</button>
            <button class="confirm">确认</button>
        </p>
    </div>
</div>


<div class="footer" style="clear:both;">
    Copyright © 2020 Powered by 健康通
</div>

<script>
    // 验证码表单的宽度
    $("input[name='verify_code']").width(
        $("input[name='verify_code']").parent().width() - 140
    );
</script>



</body></html>
