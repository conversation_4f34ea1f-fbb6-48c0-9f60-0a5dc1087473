

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DRSAY.CN</title>
    <script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<!--    <script src=/theme/admin/common/layui/jquery-1.9.1.min.js"></script>-->
    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
    <style media="screen">
        html, body {height:auto;}
        .loginscreen.middle-box {max-width:500px; width:500px;}
        .middle-box {padding-top:0;}
        .box {padding-bottom:5px; margin:150px auto 0; background-color:#fff;}
        .text-center {text-align:left;}
        .top {max-height:80px; min-height:60px; line-height:60px; overflow:hidden; background-color:#1ab394; text-align:center;}
        .font {font-size:35px; font-weight:400; color:#fff; letter-spacing:5px;}
        .form-group {margin:0 20px 20px;}
        .btn-primary {margin-left:20px;}
        .m-t {margin-top:20px;}
        .form-control {height:44px;}
    </style>
</head>

<body class="gray-bg">
<div class="middle-box text-center loginscreen animated fadeInDown box">
    <div>
        <div class="top">
            <h5 class="logo-name font">drsay</h5>
        </div>
        <!-- <h3>Welcome to drsay.cn</h3> -->
        <form class="m-t" role="form" id="isForm" action="" method="post">
            <div class="form-group">
                <input type="text" name="pid" class="form-control" value="" id="pid" placeholder="请输入项目编号" >
            </div>
            <div class="form-group">
                <input type="password" name="pid_pwd" class="form-control" value="" id="pid_pwd" placeholder="请输入项目访问密码" >
            </div>
            <button type="button" class="btn btn-primary block full-width m-b" onclick="register_login()" style="max-width:80px; min-height:44px;">Login</button>
        </form>
    </div>
</div>
<script src="/theme/new_manage/js/bootstrap.min.js"></script>

</body>
</html>
<script type="application/javascript">
    function register_login() {
        var pid = $('#pid').val();
        var pid_pwd = $('#pid_pwd').val();
        if(pid == ''){
            alert('请输入项目编号！');
            return false;
        }
        if(pid_pwd == ''){
            alert('请输入项目访问密码！');
            return false;
        }
        $.post("/project_finish/login",{pid:pid,pid_pwd:pid_pwd},function (t) {
            if(t.rs_code == 'success'){
                window.location.href='/project_finish/index'
            } else {
                alert(t.rs_msg);
            }
        },'json');
    }
</script>