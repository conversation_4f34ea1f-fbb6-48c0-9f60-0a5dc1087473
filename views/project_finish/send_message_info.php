
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, minimum-scale=1">
    <title>问卷标题</title>
    <link href="/theme/manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <!-- iCheck -->
    <link href="/theme/manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/manage/css/animate.css" rel="stylesheet">
    <link href="/theme/manage/css/style.css" rel="stylesheet">
    <link href="/theme/manage/css/plugins/awesome-bootstrap-checkbox/awesome-bootstrap-checkbox.css" rel="stylesheet">

    <link href="/theme/survey_template/question/temp0/temp0.css" rel="stylesheet">
</head>

<body>
<div class="ibox float-e-margins survey_temp0_top" >

    <!--在线聊天结束-->
    <div class="ibox float-e-margins survey_temp0_top" style="background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #E3EFFF), color-stop(100%, #95B7F3));">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox chat-view">
                    <div class="ibox-title" id="title_info" style=" border-bottom:2px solid rgb(63, 111, 187); background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #E3EFFF), color-stop(100%, #95B7F3));" >
                        <?php if(!empty($client_reback)){ ?>
                            Last message:  <?php echo date('Y-m-d H:i',$client_reback[0]['time'])?></small>
                        <?php }?>
                        Chat room panel
                    </div>

                    <div class="ibox-content">
                        <div class="row">

                            <div class="col-md-12 ">
                                <div class="chat-discussion">
                                    <?php
                                    if(!empty($client_reback)){
                                        krsort($client_reback);
                                        foreach ($client_reback as $v_reb){
                                            ?>
                                            <?php if($v_reb['source'] == 1){ ?>
                                                <div class="chat-message right">
                                                    <img class="message-avatar" src="http://www.wjtong.cc/theme/manage/img/tourist.jpg" alt="" >
                                                    <div class="message">
                                                        <a class="message-author" href="#"> Client </a>
                                                        <span class="message-date">  <?php echo date('Y-m-d H:i',$v_reb['time'])?> </span>
                                                        <span class="message-content">
                                                            <?php echo $v_reb['message']?>
                                                        </span>
                                                    </div>
                                                </div>
                                            <?php  } else { ?>
                                                <div class="chat-message left">
                                                    <img class="message-avatar" src="/theme/management/img/1421572400087_.jpg" alt="" >
                                                    <div class="message">
                                                        <a class="message-author" href="#"> Admin </a>
                                                        <span class="message-date">  <?php echo date('Y-m-d H:i',$v_reb['time'])?> </span>
                                                        <span class="message-content">
                                                            <?php echo $v_reb['message']?>
                                                        </span>
                                                    </div>
                                                </div>
                                            <?php }?>
                                        <?php } } ?>
                                </div>
                            </div>
                        </div>
                        <div class="row" >
                            <div class="col-lg-12" id="chat_msg_info">
                                <div class="chat-message-form" >
                                    <div class="form-group" style="border-top:2px solid rgb(63, 111, 187);border-bottom:2px solid rgb(63, 111, 187);">
                                        <textarea class="form-control message-input" name="message" id="send_message" placeholder="Enter message text"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary" id="button_info" type="button"  onclick="send_message(<?php echo $pid?>)" style=" width:120px; margin-top:-15px; float:right; margin-right:20px; border:1px solid #006666;"  >Send</button>
            </div>
        </div>
    </div>
    <!--在线聊天结束-->

</div>

<!-- Mainly scripts -->
<script src="/theme/manage/js/jquery-3.1.1.min.js"></script>
<script src="/theme/manage/js/bootstrap.min.js"></script>
<script src="/theme/manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="/theme/manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/manage/js/inspinia.js"></script>
<script src="/theme/manage/js/plugins/pace/pace.min.js"></script>

<!-- iCheck -->
<script src="/theme/manage/js/plugins/iCheck/icheck.min.js"></script>



<script>

    function send_message(pid) {
        var message = $('#send_message').val();
        $.post('/project_finish/send_message',{message:message,pid:pid},function (d) {
            if(d.rs_code == 'success'){
                alert(d.rs_msg);
                window.location.reload();
            } else {
                alert(d.rs_msg);
            }
        },'json');
    }

    $(document).ready(function () {
        //顶部聊天标题高度
        var title_info = $("#title_info").outerHeight();
        var chat_msg_info = $("#chat_msg_info").outerHeight();
        var button_info = $("#button_info").outerHeight();
        var client_height = document.body.clientHeight;
        //聊天内容高度
        var chat_height = client_height - (title_info + chat_msg_info + button_info + 40);
        $(".chat-discussion").css("height", chat_height+"px");

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });

    (function (doc, win) {
        var docEl = doc.documentElement,
            resizeEvt = 'onorientationchange' in window ? 'onorientationchange' : 'resize',
            recalc = function () {
                var clientWidth = docEl.clientWidth;
                if (!clientWidth) return;
                if (clientWidth >= 750) {
                    docEl.style.fontSize = (100/2)+'px';
                } else {
                    docEl.style.fontSize = 100 * (clientWidth / 750) + 'px';
                }
            };
        if (!doc.addEventListener) return;
        win.addEventListener(resizeEvt, recalc, false);
        doc.addEventListener('DOMContentLoaded', recalc, false);
    })(document, window);

</script>



</body>

</html>