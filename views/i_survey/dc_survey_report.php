<div class="wrapper wrapper-content animated fadeInRight" >
   <div class="row">
        <div class="col-lg-12" style="padding-left:0px; padding-right:0px;">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>收入统计</h5>
                    <div class="ibox-tools">
                        <a href="" class="btn btn-primary btn-xs">收入共：<?php echo $project_total ? $project_total['estimated_revenue']."【待审】" : 0;?>元</a>
                    </div>
                </div>
                <div class="ibox-content" style="padding-left:0px; padding-right:0px;">



                    <div class="table-responsive">
                    <table class="table table-hover issue-tracker">
                     <thead>
                    <tr>
                    <th data-toggle="true" ><small>编号</small></th>
                    <th data-toggle="true" ><small>标题</small></th>
                    <th data-toggle="true" ><small>答卷</small></th>
                    <th data-toggle="true" ><small >预收</small></th>
                    <th data-toggle="true" ><small >状态</small></th>
                    </tr>
                </thead>
                        <tbody>
                        <?php if(!empty($project_list)){
                                    $i = 1;
                                    $total_complete_c = $total_finish_c = $total_estimated_revenue = $total_real_income = 0;
                                    foreach ($project_list as $v){
                                        //总答卷份数
                                        $total_complete_c += $v['complete_c'];
                                        $total_finish_c += $v['finish_c'];
                                        $total_estimated_revenue += $v['estimated_revenue'];
                                        $total_real_income += $v['real_income'];
                                ?>
                        <tr>
                            <td><small><?php echo cus_pid($v['pid'])?></small></td>
                            <td ><small><?php echo $v['pro_name'];?></small></td>
                            <td><small><?php echo $v['complete_c'];?></small></td>
                            <td><small><?php echo $v['estimated_revenue'];?></small></td>
                            <td><small><?php echo $v['finish_c'] == 0 ? "待审" : $v['finish_c'];?></small></td>

                        </tr>
                       <?php } ?>
                      <?php } else { ?>
                      <tr><td colspan="5" style="text-align: center"><code>还未参与问卷!</code></td></tr>
                                <?php } ?>
                        <tr style="font-weight: bold;">
                                        <td colspan="2"><small>Total</small></td>
                                        <td><small><?php echo $total_complete_c > 0 ? $total_complete_c  : "";?></small></td>

                                        <td><small><?php echo $total_estimated_revenue > 0 ? $total_estimated_revenue  : "";?></small></td>
                                        <td><small><?php echo $total_real_income > 0 ? $total_real_income  : "";?></small></td>

                                    </tr>
                        </tbody>
                    </table>
                    </div>
                </div>

            </div>
        </div>
    </div>

</div>


