<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?php echo $title?></title>

    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
<!--    <link href="/theme/new_manage/css/bootstrap.css" rel="stylesheet">-->
    <style>
        .dataTables_paginate{float: right;}
    </style>
</head>

<body>

<div id="wrapper">
    <div class="gray-bg" id="page-wrapper" style="margin: 0px;!important;">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                <ul class="nav navbar-top-links navbar-left">
                    <li>
                        <a href="/i_survey/index"><i class="fa fa-arrow-left"></i> 返回</a>
                    </li>
                </ul>
                <ul class="nav navbar-top-links navbar-right">
                    <li>
                        <a href="/i_survey/out"><i class="fa fa-sign-out"></i><?php echo $this->session->userdata("i_survey_name");?> Log out</a>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="row wrapper border-bottom white-bg page-heading" style="background-color:#ffbe78;">
            <div class="col-lg-12" >
                <h2 style="float: left;"><?php echo cus_pid($pid)."【".$ip_addr."】"?></h2>
                <h2 style="float: right;"><?php echo $project_info['pro_name'];?></h2>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">

                        <div class="ibox-content">

                            <table class="table">
                                <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>省份</th>
                                    <th>城市</th>
                                    <th>医院</th>
                                    <th>医院等级</th>
                                    <th>医生姓名</th>
                                    <th>科室</th>
                                    <th>职称</th>
                                    <th>进入问卷</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php if(!empty($project_list)){
                                    $i = 1;
                                    foreach ($project_list as $v){
                                        //问卷链接地址
                                        $uid_str = project_link_encode($pid, $v['partner_id'], $v['groupno']);
                                        $partner_link = DRSAY_WEB . 'go/s/' . $uid_str . '?';
                                        $res_partner_link = !empty($v['partner_uid']) ? $partner_link.$v['partner_uid'] : '';

                                        $project_redirect = str_md5_code($pid."_".$v['id'].PROJECT_ENCODE_KEY);
                                        $project_redirect = $pid."_".$v['id']."_".$project_redirect;
                                        $url_project_redirect = "/i_survey/project_redirect/".$project_redirect;
                                        $is_show_link = true;
                                        if ($project_info['is_ip_astrict'] == 1) {//需要检测ip
                                            if ($v['province'] && $v['province'] != $province){
                                                $is_show_link = false;
                                            }
                                            if ($v['city'] && $v['city'] != $city){
                                                $is_show_link = false;
                                            }
                                        }
                                    ?>
                                        <tr>
<!--                                            <td>--><?php //echo $v['id'];?><!--</td>-->
                                            <td><?php echo $i++;?></td>
                                            <td><?php echo $v['province'] ? $v['province'] : "";?></td>
                                            <td><?php echo $v['city'] ? $v['city'] : "";?></td>
                                            <td><?php echo $v['unit_name'] ? $v['unit_name'] : "";?></td>
                                            <td><?php echo $v['unit_level'] ? $v['unit_level'] : "";?></td>
                                            <td><?php echo $v['name'] ? $v['name'] : "";?></td>
                                            <td><?php echo $v['department'] ? $v['department'] : "";?></td>
                                            <td><?php echo $v['job_title'] ? $v['job_title'] : "";?></td>
                                            <td>
                                                <?php if($is_show_link){?>
                                                <a href="<?php echo $url_project_redirect;?>" target="_blank">问卷开始</a>
                                                <?php }?>
                                            </td>
                                        </tr>
                                <?php } ?>
                                <?php } else { ?>
                                    <tr><td colspan="10" style="text-align: center"><code>暂无数据!</code></td></tr>
                                <?php } ?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="10">
                                        <?php echo $pagination?>
                                    </td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="bottom">
            <div class="footer">
                <div>
                    <strong>Copyright</strong> © 2014 - 2019 Gooddr.com Limited All Rights Reserved.
                </div>
            </div>
        </div>

    </div>

</div>
<!-- Mainly scripts -->
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="/theme/new_manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/plugins/peity/jquery.peity.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/new_manage/js/inspinia.js"></script>
<script src="/theme/new_manage/js/plugins/pace/pace.min.js"></script>

<!-- iCheck -->
<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/demo/peity-demo.js"></script>

<script>
    $(document).ready(function(){
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });
</script>

</body>

</html>
