<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row" >

        <div class="col-lg-12" style="padding-left:0px; padding-right:0px;">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>问卷列表</h5>
                    <div class="ibox-tools">
                       <button onClick="javascript:location.reload();" class="btn btn-white btn-sm" data-toggle="tooltip" data-placement="left" title="" data-original-title="Refresh inbox"><i class="fa fa-refresh"></i> Refresh</button>
                    </div>
                </div>
                <div class="ibox-content" style="padding-left:0px; padding-right:0px;">



                    <div class="table-responsive">
                    <table class="table table-hover issue-tracker">
                     <thead>
                    <tr>
                        <th data-toggle="true" ><small>省份</small></th>
                        <th data-toggle="true" ><small>城市</small></th>
                        <th data-toggle="true" ><small>医院</small></th>
                        <th data-toggle="true" ><small >等级</small></th>
                    <th data-toggle="true" ><small>姓名</small></th>

                    <th data-toggle="true" ><small>生日</small></th>
                    <th data-toggle="true" ><small>性别</small></th>

                    <th data-toggle="true" ><small>科室</small></th>

                    <th data-toggle="true" ><small >职称</small></th>
                    <th data-toggle="true" ><small >配额条件</small></th>
                    <th data-toggle="true" ><small >进入</small></th>
                    </tr>
                </thead>
                        <tbody>
                        <?php $i = 1;if(!empty($project_list_click)){?>
                            <?php foreach ($project_list_click as $v_click){
                                if ($arr_forbid_city) {
                                    if ($v_click['province'] && in_array($v_click['province'], $arr_forbid_city)) {continue;}
                                    if ($v_click['city'] && in_array($v_click['city'], $arr_forbid_city)) {continue;}
                                    if ($v_click['district'] && in_array($v_click['district'], $arr_forbid_city)) {continue;}
                                }
                                //问卷链接地址
                                $uid_str = project_link_encode($pid, $v_click['partner_id'], $v_click['groupno']);
                                $partner_link = DRSAY_WEB . 'go/s/' . $uid_str . '?';
                                $res_partner_link = !empty($v_click['partner_uid']) ? $partner_link.$v_click['partner_uid'] : '';
                                $project_redirect = str_md5_code($pid."_".$v_click['id'].PROJECT_ENCODE_KEY);
                                $project_redirect = $pid."_".$v_click['id']."_".$project_redirect;
                                $url_project_redirect = "/i_survey/project_redirect/".$project_redirect;

                                $is_show_link_click = true;
                                if ($project_info['is_ip_astrict'] == 1) {//需要检测ip
                                    if ($v_click['province'] && $v_click['province'] != $province){
                                        $is_show_link_click = false;
                                    }
                                    if ($v_click['city'] && $v_click['city'] != $city){
                                        $is_show_link_click = false;
                                    }
                                }
                            ?>
                        <tr>
                            <td><small><?php echo $v_click['province'] ? $v_click['province'] : "";?></small></td>
                            <td><small><?php echo $v_click['city'] ? $v_click['city'] : "";?></small></td>
                            <td><small><?php echo $v_click['unit_name'] ? $v_click['unit_name'] : "";?></small></td>
                            <td><small><?php echo $v_click['unit_level'] ? $v_click['unit_level'] : "";?></small></td>
                            <td><small><?php echo $v_click['name'] ? $v_click['name'] : "";?></small></td>

                            <td><small><?php echo $v_click['birthday'] ? date("Y") - date("Y",strtotime($v_click['birthday'])) : "";?></small></td>
                            <td><small><?php echo $v_click['gender'] ? $v_click['gender'] : "";?></small></td>

                            <td><small><?php echo $v_click['department'] ? $v_click['department'] : "";?></small></td>

                            <td><small><?php echo $v_click['job_title'] ? $v_click['job_title'] : "";?></small></td>
                            <td><small>抽样条件</small></td>
                            <td><small>
                             <?php if($is_show_link_click){?>
                                             <a href="<?php echo $url_project_redirect;?>" target="_blank"><span class="label label-primary">续答</span></a>
                                        <?php }?>
                            </small>
                            </td>

                        </tr>
                          <?php }?>
                        <?php }?>

                        <?php if(!empty($project_list)){
                            foreach ($project_list as $v){
                                if ($arr_forbid_city) {
                                    if ($v['province'] && in_array($v['province'], $arr_forbid_city)) {continue;}
                                    if ($v['city'] && in_array($v['city'], $arr_forbid_city)) {continue;}
                                    if ($v['district'] && in_array($v['district'], $arr_forbid_city)) {continue;}
                                }
                                //问卷链接地址
                                $uid_str = project_link_encode($pid, $v['partner_id'], $v['groupno']);
                                $partner_link = DRSAY_WEB . 'go/s/' . $uid_str . '?';
                                $res_partner_link = !empty($v['partner_uid']) ? $partner_link.$v['partner_uid'] : '';

                                $project_redirect = str_md5_code($pid."_".$v['id'].PROJECT_ENCODE_KEY);
                                $project_redirect = $pid."_".$v['id']."_".$project_redirect;
                                $url_project_redirect = "/i_survey/project_redirect/".$project_redirect;
                                $is_show_link = true;
                                if ($project_info['is_ip_astrict'] == 1) {//需要检测ip
                                    if ($v['province'] && $v['province'] != $province){
                                        $is_show_link = false;
                                    }
                                    if ($v['city'] && $v['city'] != $city){
                                        $is_show_link = false;
                                    }
                                }
                                ?>
                         <tr>
                             <td><small><?php echo $v['province'] ? $v['province'] : "";?></small></td>
                             <td><small><?php echo $v['city'] ? $v['city'] : "";?></small></td>
                             <td><small><?php echo $v['unit_name'] ? $v['unit_name'] : "";?></small></td>
                             <td><small><?php echo $v['unit_level'] ? $v['unit_level'] : "";?></small></td>
                            <td><small><?php echo $v['name'] ? $v['name'] : "";?></small></td>

                            <td><small><?php echo $v['birthday'] ? date("Y") - date("Y",strtotime($v['birthday'])) : "";?></small></td>
                             <td><small><?php echo $v['gender'] ? $v['gender'] : "";?></small></td>
                            <td><small><?php echo $v['department'] ? $v['department'] : "";?></small></td>

                            <td><small><?php echo $v['job_title'] ? $v['job_title'] : "";?></small></td>
                             <td><small>抽样条件</small></td>
                            <td><small>
                             <?php if($is_show_link){?>
                                            <a href="<?php echo $url_project_redirect;?>" target="_blank"><span class="label label-primary">答卷</span></a>
                                        <?php }?>
                            </small>
                            </td>

                        </tr>
                         <?php } ?>
                        <?php } else { ?>
                            <?php if(!$project_list_click && !$project_list){?>
                            <tr><td colspan="5" style="text-align: center"><code>目前还没有问卷被开放!</code></td></tr>
                            <?php }?>
                        <?php } ?>

                        </tbody>
                    </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

