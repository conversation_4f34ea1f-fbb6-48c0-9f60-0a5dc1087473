<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>医来说事</title>

    <link href="/theme/new_manage/css/bootstrap.min.css" rel="stylesheet">
    <link href="/theme/new_manage/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="/theme/new_manage/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="/theme/new_manage/css/animate.css" rel="stylesheet">
    <link href="/theme/new_manage/css/style.css" rel="stylesheet">
<!--    <link href="/theme/new_manage/css/bootstrap.css" rel="stylesheet">-->
    <style>
        .dataTables_paginate{float: right;}
    </style>
</head>

<body>

<div id="wrapper">
    <div class="gray-bg" id="page-wrapper" style="margin: 0px;!important;">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                <ul class="nav navbar-top-links navbar-right">
                    <li>
                        <a href="/i_survey/out">
                            <i class="fa fa-sign-out"></i><?php echo $this->session->userdata("i_survey_name");?> Log out
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <!--
        <div class="row wrapper border-bottom white-bg page-heading" style="background-color:#ffbe78;">
            <div class="col-lg-12" >
                <h2 style="float: left;">项目编号</h2>
                <h2 style="float: right;">项目名称</h2>
            </div>
        </div>
        -->
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">

                        <div class="ibox-content">

                            <table class="table">
                                <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>项目编号</th>
                                    <th>项目名称</th>
                                    <th>推荐奖励</th>
                                    <th>答卷奖励</th>
                                    <th>完成</th>
                                    <th>有效</th>
                                    <th>无效</th>
<!--                                    <th>PM</th>-->
<!--                                    <th>项目类型</th>-->
<!--                                    <th>需求量</th>-->
<!--                                    <th>完成量</th>-->
<!--                                    <th>缺少</th>-->
                                    <th>配额</th>
                                    <th>问卷</th>
                                    <th>剩余(天)</th>
                                    <th>支付</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php if(!empty($project_list)){
                                    $i = 1;
                                    foreach ($project_list as $v){
                                        //剩余天数
                                        $days = $v['pro_end_time'] - time();
                                        $surplus_day = 0;
                                        if ($days > 0) {
                                            $surplus_day = ceil($days/86400);
                                        }
                                    ?>
                                        <tr>
                                            <td><?php echo $offset+($i++);?></td>
                                            <td><?php echo cus_pid($v['id'])?></td>
                                            <td><?php echo $v['pro_name']?></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
<!--                                            <td>--><?php //echo $v['admin_name'];?><!--</td>-->
<!--                                            <td>--><?php //echo $v['pro_type'] ? $arr_pro_type[$v['pro_type']] : "";?><!--</td>-->
<!--                                            <td>--><?php //echo $v['pro_sample_num'] ? $v['pro_sample_num'] : "";?><!--</td>-->
<!--                                            <td>--><?php //echo $v['c_num'] ? $v['c_num'] : "";?><!--</td>-->
<!--                                            <td>--><?php //echo $v['pro_sample_num'] - $v['c_num'] > 0 ? "<code>".($v['pro_sample_num'] - $v['c_num'])."</code>" : "";?><!--</td>-->
                                            <td><?php echo $surplus_day > 0 ? $surplus_day : "";?></td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <a href="/i_survey/implement_list/<?php echo $v['id'];?>">进入</a>
                                            </td>
                                        </tr>
                                <?php } ?>
                                <?php } else { ?>
                                    <tr><td colspan="13" style="text-align: center"><code>暂无数据!</code></td></tr>
                                <?php } ?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="13">
                                        <?php echo $pagination?>
                                    </td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="bottom">
            <div class="footer">
                <div>
                    <strong>Copyright</strong> © 2014 - 2019 Gooddr.com Limited All Rights Reserved.
                </div>
            </div>
        </div>

    </div>

</div>
<!-- Mainly scripts -->
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<script src="/theme/new_manage/js/bootstrap.min.js"></script>
<script src="/theme/new_manage/js/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="/theme/new_manage/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/plugins/peity/jquery.peity.min.js"></script>

<!-- Custom and plugin javascript -->
<script src="/theme/new_manage/js/inspinia.js"></script>
<script src="/theme/new_manage/js/plugins/pace/pace.min.js"></script>

<!-- iCheck -->
<script src="/theme/new_manage/js/plugins/iCheck/icheck.min.js"></script>

<!-- Peity -->
<script src="/theme/new_manage/js/demo/peity-demo.js"></script>

<script>
    $(document).ready(function(){
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });
</script>

</body>

</html>
