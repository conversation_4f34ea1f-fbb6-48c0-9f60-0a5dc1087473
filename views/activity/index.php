<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>健康通-活动说明</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
        }

        input {
            -webkit-appearance: none;
        }

        .container {
            width: 15rem;
            min-height: 100%;
            margin: 0 auto;
        }

        .container header {
            color: #fff;
            text-align: center;
            padding-top: 3.5rem;
            background-size: 100% auto;
        }

        .container header h4 {
            font-weight: 400;
            font-size: 1.2rem;
        }

        .container header h4:last-child {
            text-shadow: #fff 1px 0 0, #fff 0 1px 0, #fff -1px 0 0, #fff 0 -1px 0;
            color: #ffc156;
            font-size: 1.4rem;
        }

        section {
            width: 13.8rem;
            background: #2e3af5;
            margin: 0 auto;
            margin-top: 1rem;
            padding: 0.56rem 0.48rem;
        }

        section .content {
            width: 12.8rem;
            margin: 0 auto;
            margin-top: 0.56rem;
            background-color: #fff;
            font-size: 0.6rem;
            color: #5b5b5b;
            line-height: 1.2rem;
            padding: 1.08rem 0.88rem;
        }

        section .btn {
            text-align: center;
            padding-top: 1rem;
        }

        section .btn button {
            outline: none;
            cursor: pointer;
            height: 1.6rem;
            line-height: 1.6rem;
            width: 9.36rem;
            color: #fff;
            border-radius: 1rem;
            border: none;
            font-size: 1rem;
            background: linear-gradient(to right, #ff9333, #ffc156);
        }
    </style>
</head>

<body>
    <div class="container">
        <img style="width: 100%;" src="https://www.drsay.cn/theme/sv/img/2022-05-18/4c740f2eaa046b5e0990989b765a1918.png?time=<?php echo time();?>">
    </div>
    <script>
        (function(doc, win) {
            var docEl = doc.documentElement,
                resizeEvt =
                "onorientationchange" in window ? "onorientationchange" : "resize",
                recalc = function() {
                    var clientWidth = docEl.clientWidth;
                    if (!clientWidth)
                        return;
                    if (clientWidth >= 750) {
                        docEl.style.fontSize = "30px";
                    } else {
                        docEl.style.fontSize = clientWidth / 15 + "px";
                    }
                };
            if (!doc.addEventListener)
                return;
            win.addEventListener(resizeEvt, recalc, false);
            doc.addEventListener("DOMContentLoaded", recalc, false);
        })(document, window);
    </script>
</body>

</html>