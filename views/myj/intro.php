<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>健康通-项目介绍</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }
            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                padding-bottom: 1rem;
            }
            header img {
                width: 100%;
            }
            article {
                padding: 0.6rem;
                color: #2a2a2a;
                font-size: 0.6rem;
                line-height: 1.2rem;
            }
            footer {
                text-align: center;
                margin-top: 2.56rem;
            }
            footer button {
                cursor: pointer;
                width: 10rem;
                height: 2rem;
                background: #561f80;
                border-radius: 10px;
                border: 1px solid #979797;
                color: #fff;
                font-size: 0.8rem;
                outline: none;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <img src="/theme/myj/img/head_bg.png" />
            </header>
            <article>
                <p>
                    健康通邀请您入驻妙医佳互联网医院，项目主办方是中国健康促进基金会。
                </p>
                <p>
                    通过本次活动您可以了解医学资讯，绑定您的患者进行在线沟通。由您自愿选择是否开通在线诊疗服务（需自行前往卫健委电子化注册系统备案）
                </p>
            </article>

            <footer><button onclick="invite()">我想报名</button></footer>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);
            function invite(){
                <?php 
                $time = time();
                $code_str = 'myj_'.$time;
                $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
                ?>
                location.href="/myj/invite?code=<?php echo $code;?>&time=<?php echo $time;?>";
            }
        </script>
    </body>
</html>
