<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>健康通-邀请信息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
        }

        input {
            -webkit-appearance: none;
        }

        .container {
            width: 15rem;
            min-height: 100%;
            margin: 0 auto;
            background: url("/theme/myj/img/body_bg.png");
            background-size: 100%;
            padding-bottom: 1rem;
        }

        header {
            font-size: 0.8rem;
            font-weight: 500;
            color: #000000;
            text-align: center;
            padding: 1.74rem 0;
        }

        section {
            padding: 0.6rem;
        }

        section .title {
            font-size: 0.56rem;
            color: #2a2a2a;
        }

        section form label {
            display: block;
            padding: 0.3rem 0;
        }

        section form label .desc {
            display: inline-block;
            width: 1.68rem;
            font-size: 0.56rem;
            color: #9b9b9b;
        }

        section form label input {
            width: 11.7rem;
            height: 1.5rem;
            background: #ffffff;
            border-radius: 0.8rem;
            border: 1px solid #e1e1e1;
            outline: none;
            padding: 0.2rem 0.3rem;
        }

        section form label p {
            display: inline-block;
            vertical-align: middle;
            position: relative;
            width: 11.7rem;
            height: 1.5rem;
            line-height: 1.5rem;
            background: #ffffff;
            border-radius: 0.8rem;
            border: 1px solid #e1e1e1;
            padding: 0 0.3rem;
            font-size: 0.56rem;
        }

        section form label p::after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border: 0.5rem solid transparent;
            border-top: 0.5rem solid #e1e1e1;
            right: 0.5rem;
            top: 40%;
        }

        section form label p select {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            opacity: 0;
        }

        article {
            padding: 1rem 0.5rem;
            font-size: 0.56rem;
            color: #a1a1a1;
        }

        footer {
            text-align: center;
            margin-top: 2rem;
        }

        footer button {
            cursor: pointer;
            width: 10rem;
            height: 2rem;
            background: #561f80;
            border-radius: 0.2rem;
            border: 1px solid #979797;
            font-size: 0.8rem;
            color: #fff;
            outline: none;
        }

        .error_tips {
            /*text-align: center;*/
            padding-left: 2.3rem;
            font-size: 0.5rem;
            color: red;
            height: 0.1rem;
            line-height: 0.2rem;
        }

        .verification {
            width: 5rem;
            height: 1.25rem;
            border: none;
            border-radius: 0.1rem;
            padding: 0.1rem;
            outline: none;
            background: #5e93fb;
            font-size: 0.52rem;
            color: #fff;
        }
    </style>
</head>

<body>
    <div class="container">
        <header>健康通邀请您入驻妙医佳互联网医院</header>

        <section>
            <p class="title">请您提供以下信息进行报名</p>
            <form id="subForm">
                <label>
                    <span class="desc">省份</span>
                    <p>
                        <span></span>
                        <select id="province_se" name="province_code" onchange="javascript:sel_city(this.value);">
                            <option value="0">请选择</option>
                            <?php foreach ($province as $k_province => $v_province) { ?>
                                <option value="<?php echo $k_province; ?>"><?php echo $v_province; ?></option>
                            <?php } ?>
                        </select>
                    </p>
                </label>
                <p class="error_tips" id="province_code"></p>
                <label>
                    <span class="desc">城市</span>
                    <p id="city">
                        <span></span>
                        <select name="city_code">
                            <option value="0">请选择</option>
                        </select>
                    </p>
                </label>
                <p class="error_tips" id="city_code"></p>
                <label>
                    <span class="desc">医院</span>
                    <p id="unit_str">
                        <span></span>
                        <select name="unit_name">
                            <option value="0">请选择</option>
                        </select>
                    </p>
                </label>
                <p class="error_tips" id="unit_name"></p>
                <label>
                    <span class="desc">科室</span>
                    <p>
                        <span></span>
                        <select name="dr_department">
                            <option value="">请选择</option>
                            <?php foreach($department as $depv){?>
                                <option value="<?php echo $depv;?>"><?php echo $depv;?></option>
                            <?php }?>
                        </select>
                    </p>
                </label>
                <p class="error_tips" id="dr_department"></p>
                <label>
                    <span class="desc">姓名</span>
                    <input type="text" name="dr_name" placeholder="请填写姓名" />
                </label>
                <p class="error_tips" id="dr_name"></p>
                <label>
                    <span class="desc">手机号</span>
                    <input type="number" name="dr_mobile" placeholder="请填写手机号" />
                </label>
                <p class="error_tips" id="dr_mobile"></p>
                <label>
                    <span class="desc">验证码</span>
                    <input style="width:4rem;" name="ver_code" type="text" placeholder="请输入验证码" />
                    <button class="verification" id="verification" type="button" onclick="obtain_vercode()">获取验证码</button>
                </label>
                <p class="error_tips" id="ver_code_err"></p>

                <input type="hidden" id="province_" name="province">
                <input type="hidden" id="city_" name="city">
            </form>
        </section>

        <article>
            温馨提醒：本次项目过程中需上传您的身份证正反面照片、医师资格证照片、执业证照片，用于执业身份确认。
        </article>

        <footer>
            <button onclick="subForm()">提交审核</button>
        </footer>
    </div>
    <?php
    $time = time();
    $code_str = 'myj_' . $time;
    $code = substr(md5($code_str . PROJECT_ENCODE_KEY), 8, 6);
    ?>
    <script src="/theme/<?= TEMPLATE_DIR ?>/js/jquery-3.1.1.min.js"></script>
    <script>
        (function(doc, win) {
            var docEl = doc.documentElement,
                resizeEvt =
                "onorientationchange" in window ? "onorientationchange" : "resize",
                recalc = function() {
                    var clientWidth = docEl.clientWidth;
                    if (!clientWidth)
                        return;
                    if (clientWidth >= 750) {
                        docEl.style.fontSize = "30px";
                    } else {
                        docEl.style.fontSize = clientWidth / 15 + "px";
                    }
                };
            if (!doc.addEventListener)
                return;
            win.addEventListener(resizeEvt, recalc, false);
            doc.addEventListener("DOMContentLoaded", recalc, false);
        })(document, window);
        var unit_name_ary = [];
        <?php foreach ($unit_name_ary as $unk => $unv) {
        ?>
            var un_ary = [];
            <?php foreach ($unv as $unkey => $unval) { ?>
                un_ary[<?php echo $unkey; ?>] = "<?php echo $unval; ?>";
            <?php } ?>
            unit_name_ary["<?php echo $unk; ?>"] = un_ary;
        <?php } ?>
        //查询城市
        function sel_city(obj, is_select = "") {
            $.post('/myj/sel_city', {
                province_id: obj,
                code: "<?php echo $code; ?>",
                time: "<?php echo $time; ?>"
            }, function(c) {
                $("#city").empty();
                var data = c.rs_msg;
                var html = '<span></span><select name="city_code" id="city_se" onchange="javascript:sel_district(this.value);"><option value="0">城市</option>';
                for (var i = 0; i < data.length; i++) {
                    html += '<option value="' + data[i]['sys_dictionary_id'] + '" ' + (is_select != '' && is_select == data[i]['val_translate'] ? "selected" : "") + ' >' + data[i]['val_translate'] + '</option>';
                }
                html += '</select>';
                $('#city').append(html);
                $('#province_').val($('#province_se option:selected').text());
                $('#city_').val('');

            }, 'json');
        }
        //转加城市名称
        function sel_district(obj, is_select = "") {
            var city_text = $('#city_se option:selected').text();
            $('#city_').val(city_text);
            var city_un_ary = unit_name_ary[city_text];
            var un_str = '<select name="unit_name"><option value="0">请选择</option>';
            $.each(city_un_ary, function(index, value) {
                un_str += '<option >' + value + '</option>';
            });
            un_str += "</select>";
            $('#unit_str').html('<span></span>' + un_str);
        }

        $(document).ready(function() {
            $("select[name='province_code']").blur(function() {
                check_province_code();
            });
            $("body").on('blur', "select[name='city_code']", function() {
                check_city_code();
            });
            $("body").on('blur', "select[name='unit_name']", function() {
                check_unit_name();
            });
            $("select[name='dr_department']").blur(function() {
                check_dr_department();
            });
            $("input[name='dr_name']").blur(function() {
                check_dr_name();
            });
            $("input[name='dr_mobile']").blur(function() {
                check_dr_mobile();
            });
            $("input[name='ver_code']").blur(function() {
                check_ver_code();
            });
        });

        function check_province_code() {
            var province_code = $("select[name='province_code']").val();
            if (province_code == '0') {
                $('#province_code').text('请选择省份！');
                return false;
            }
            var province = $("select[name='province_code'] option:selected").text();
            if(province!=''){
                var make = '';
                $.ajax({
                    url: '/myj/check_province_recruit',type: 'POST',
                    data: {province:province,code: "<?php echo $code; ?>",time: "<?php echo $time; ?>"},
                    async:false,dataType: 'json',
                    success: function(c) {
                        if (c.rs_code == 'error') {
                            $('#' + c.rs_backurl + '').text(c.rs_msg);
                            make='2';
                        }else{
                            $('#' + c.rs_backurl + '').text('');
                            make='1';
                        }
                    }
                });
                if(make=='2'){
                    return false;
                }
                return true;
            }
            $('#province_code').text('');
            return true;
        }
        
        function check_city_code() {
            var city_code = $("select[name='city_code']").val();
            if (city_code == '0') {
                $('#city_code').text('请选择城市！');
                return false;
            }
            $('#city_code').text('');
            return true;
        }

        function check_unit_name() {
            var str = $("select[name='unit_name'] option").length;
            if(str<=1){
                $('#unit_name').text('当前城市未开启招募，感谢您的支持！');
                return false;
            }
            var unit_name = $("select[name='unit_name']").val();
            if (unit_name == '0') {
                $('#unit_name').text('请选择医院！');
                return false;
            }
            $('#unit_name').text('');
            return true;
        }
        
        function check_dr_department(){
            var dr_department = $("select[name='dr_department']").val();
            if(dr_department==''){
                $('#dr_department').text('请选择科室！');
                return false;
            }
            $('#dr_department').text('');
            return true;
        }

        function check_dr_name() {
            var dr_name = $("input[name='dr_name']").val();
            if (dr_name == '') {
                $('#dr_name').text('请输入姓名！');
                return false;
            } else if (/^[\u4E00-\u9FA5]{2,10}$/.test(dr_name) === false) {
                $('#dr_name').text('请输入2-10位的中文名称！');
                return false;
            }
            $('#dr_name').text('');
            return true;
        }

        function check_dr_mobile() {
            var dr_mobile = $("input[name='dr_mobile']").val();
            if (dr_mobile == '') {
                $('#dr_mobile').text('请输入手机号！');
                return false;
            } else if (/^1(3|4|5|6|7|8|9)\d{9}$/.test(dr_mobile) === false) {
                $('#dr_mobile').text('请输入正确手机号格式！');
                return false;
            }
            $('#dr_mobile').text('');
            return true;
        }

        function check_ver_code() {
            var ver_code = $("input[name='ver_code']").val();
            if (ver_code == '') {
                $('#ver_code_err').text('请输入验证码！');
                return false;
            } else if (/^\d{5}$/.test(ver_code) === false) {
                $('#ver_code_err').text('请输入5位数字验证码！');
                return false;
            }
            $('#ver_code_err').text('');
            return true;
        }

        function check_make(type) {

            var i = 0;
            var checkProvinceCode = check_province_code();
            if (checkProvinceCode === false) {
                i += 1;
            }

            var checkCityCode = check_city_code();
            if (checkCityCode === false) {
                i += 1;
            }

            var checkUnitName = check_unit_name();
            if (checkUnitName === false) {
                i += 1;
            }
            
            var checkDrDepartment = check_dr_department();
            if(checkDrDepartment===false){
                i+=1;
            }

            var checkDrName = check_dr_name();
            if (checkDrName === false) {
                i += 1;
            }

            var checkDrMobile = check_dr_mobile();
            if (checkDrMobile === false) {
                i += 1;
            }

            if (type == 1) {
                var checkVerCode = check_ver_code();
                if (checkVerCode === false) {
                    i += 1;
                }
            }
            if (i != '0') {
                return false;
            }
            return true;
        }

        function subForm() {
            var check = check_make(1);
            if (check) {
                var form = document.querySelector("#subForm");
                var formData = new FormData(form);
                formData.append("code", "<?php echo $code; ?>");
                formData.append("time", "<?php echo $time; ?>");
                $.ajax({
                    url: '/myj/save_invite',
                    type: 'POST',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(data) {
                        if (data.rs_code == 'success') {
                            location.href = data.rs_backurl;
                        } else if (data.rs_code == 'error_vercode') {
                            $('#ver_code_err').text(data.rs_msg);
                            $('#ver_code_err').append("点击<a href='/myj/register?code=" + data.rs_backurl + "'>查看活动二维码</a>");
                        } else {
                            $('#' + data.rs_backurl + '').text(data.rs_msg);
                        }
                    }
                });
            }
        }

        function obtain_vercode() {
            var check = check_make(2);
            if (check) {
                var mobile = $("input[name='dr_mobile']").val();
                $.post('/myj/obtain_vercode', {
                    verify_mobile: mobile,province:$("input[name='province']").val(),
                    city:$("input[name='city']").val(),unit_name:$("select[name='unit_name']").val(),
                    dr_department:$("select[name='dr_department']").val(),dr_name:$("input[name='dr_name']").val(),
                    code: "<?php echo $code; ?>",
                    time: "<?php echo $time; ?>"
                }, function(c) {
                    if (c.rs_code == 'success') {
                        $('#verification').attr('disabled', true);
                        var time = 60;
                        getRandomCode();
                        //倒计时
                        function getRandomCode() {
                            if (time === 0) {
                                time = 60;
                                $("#verification").text("重新发送");
                                $('#verification').attr('disabled', false);
                                return;
                            } else {
                                time--;
                                $("#verification").text(time + "s");
                            }
                            setTimeout(function() {
                                getRandomCode();
                            }, 1000);
                        }
                        $('#ver_code_err').text('短信发送成功，请查收！');
                    } else if (c.rs_code == 'error_vercode') {
                        $('#ver_code_err').text(c.rs_msg);
                        $('#ver_code_err').append("点击<a href='/myj/register?code=" + c.rs_backurl + "'>查看活动二维码</a>");
                    } else {
                        $('#' + c.rs_backurl + '').text(c.rs_msg);
                    }
                }, 'json');
            }
        }


        // 下拉框的选中效果
        $("#subForm").on("change", "select", function() {
            var text = $(this).children("select option:selected").text()
            $(this).parent().children("span").text(text)
        })
    </script>
</body>

</html>