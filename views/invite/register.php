<!DOCTYPE html>
<html>
<head>
<title>上医说注册</title>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
<meta name="keywords" content="上医说-用户注册" />
<meta name="description" content="上医说-用户注册" />
<meta name="renderer" content="webkit" />
<meta name="robots" content="all,index,follow" />
<meta name="format-detection" content="telephone=no" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<link rel="shortcut icon" href="<?php echo base_url();?>theme/images/favicon.ico" />
<script type="text/javascript" src="<?php echo base_url();?>theme/<?php echo TEMPLATE_DIR;?>/js/jquery-3.1.1.min.js"></script>
    <script src="<?= base_url()?>/theme/<?php echo TEMPLATE_DIR;?>/assets/js/comm_init.js"></script>
    <script src="<?= base_url()?>/theme/<?php echo TEMPLATE_DIR;?>/assets/js/jquery.form.js"></script>
<!-- style start -->
<style>
	* {padding:0; margin:0;}
	body {font-size:.75rem; font-family:"microsoft yahei"; color:#333;}
	input, select, textarea, button {border:solid 1px #333; border-radius:.15rem;}
	a,a:hover {text-decoration:none; color:#666;}
	.content {max-width:750px; margin:58px auto; text-align:center;}
	.logo {width:100%; margin:0 auto 50px;}
	.logo a, .logo img {width:110px; height:110px; border-radius:10%;}
	.form, .protocol {padding:0 .75rem;}

	.form input, .form button {margin-bottom:.85rem;}
	.i_put {width:100%; height:1.75rem; color:#333; font-size:.6rem; border-radius:1rem; padding:0 .7rem;}
	.obt_btn {height:1.75rem; font-size:.6rem; color:#333; border-radius:1rem; padding:0 .7rem; background-color:transparent;}
	.r_btn {width:100%; height:1.75rem; font-size:.6rem; color:#333; border-radius:1rem; padding:0 .7rem; background-color:transparent;}
	.f_reg {width:100%; float:left; padding-top:55px;}
	.f_put {width:100%; float:left;}
	.f_put .f_l {float:left;}
	.f_put .f_r {float:right;}

	.protocol {clear:both;}
.link {font-size:.55rem; color:#666; text-decoration:underline; /*vertical-align:middle;*/}
	.check {width:16px; height:16px; border:0; border-radius:.1rem; border:solid 1px #666; vertical-align:middle;}
</style>
<!-- style end -->

<!-- font rem start -->
<script>
	function px2rem() {
	    var cw = parseInt(document.documentElement.getBoundingClientRect().width);
	    cw = cw > 640 ? 640 : cw;
	    window.rem = cw / 16;
	    document.documentElement.style.fontSize = window.rem + 'px';
	}
	px2rem();
</script>
<!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
	<div class="content">
		<div class="logo">
			<a href="<?php echo DRSAY_WEB;?>"><img src="/theme/images/default_avatar.png" title="上医说" alt="上医说" /></a>
		</div>

        <form action="" class="form-horizontal" id="item_form"
              onsubmit="return tqjs.form_sumbit(this,'show_msg','处理中','','','','<?php echo $get_lang[LANG_CONFIRM_SUBMISSION];?>');">
            <div class="alert alert-error" style="display: none;" id="show_msg_block">
                <button data-dismiss="alert" class="close"></button>
                <span id="show_msg"></span>
            </div>
			<div class="form">
				<div class="f_box" style="width:90%;">
					<input type="text" value="" placeholder="<?php echo $get_lang[LABEL_INVITE_REGISTER_MOBILE_PLACEHOLDER];?>" name="mobile" id="mobile" class="i_put" />
				</div>
				<!--<div class="f_put" style="width:90%;">
					<input type="password" placeholder="<?php echo $get_lang[LABEL_INVITE_REGISTER_PASSWORD_PLACEHOLDER];?>" name="pass" id="pass" class="i_put" />
				</div>-->
				<div class="f_put">
					<div class="f_l">
						<input style="width:130px;" type="text" name="verify" value="" id="reg_validation" placeholder="<?php echo $get_lang[LABEL_INVITE_REGISTER_VERIFY_PLACEHOLDER];?>" maxlength="6" class="obt_btn" />
					</div>
					<div class="f_r">
						<button class="obt_btn" id="J_getCode"><?php echo $get_lang[LABEL_GET_VERIFY_CODE]?></button>
						<button class="obt_btn" id="J_resetCode" style="display:none;" disabled><span id="J_second">60</span>s&nbsp;<?php echo $get_lang[LABEL_INVITE_REGISTER_VERIFY_AGAIN];?></button>
					</div>
				</div>

				<div class="f_reg">
					<input class="r_btn" id="regnow" type="submit" value="<?php echo $get_lang[LABEL_INVITE_REGISTER_BUTTON];?>" />
				</div>
			</div>
			<div class="protocol">
				<label for="check">
					<input type="checkbox" class="check" id="check" name="" value="1" checked />
					<a href="<?php echo DRSAY_WEB;?>/share/get_protocol/<?php echo $lang;?>" class="link"><?php echo $get_lang[LABEL_INVITE_REGISTER_PRIVACY];?></a>
				</label>
			</div>

			<input type="hidden" name="invite_id" id="invite_id" value="<?php echo $invite_id;?>" >
		</form>
	</div>

<!-- script start -->
<script>
$("#J_getCode").click(function(){
	var mobile = $("#mobile").val();
	if(! mobile.match(/^1[3-9]\d{9}$/)){alert('<?php echo $get_lang[LABEL_INVITE_REGISTER_MOBILE_PLACEHOLDER];?>');return false;}


  $.post('<?php echo USER_IMG_URL_NOTS;?>/api_comm/send_sms',{country:<?php echo $member_info['country'];?>,lang:<?php echo $lang;?>,mobile:mobile,type:<?php echo VERIFICATION_CODE_REG;?>},function(data){
    if (data.re_st=='error') {
        alert(data.re_info);
        return false;
    } else {
        alert(data.re_info);
        resetCode(); //倒计时
    }
  });

});

//$("#regnow").click(function(){
//    var mobile = $("#mobile").val();
//    var pass = $("#pass").val();
//    var verify_code = $("#reg_validation").val();
//    var invi_id = $("#invi_id").val();
//    var actid = $("#actid").val();
//    var sourceid = $("#sourceid").val();
//
//    if (! $("#check").prop("checked")){alert('<?//= $this->session->userdata('sys_lang_ary')[5149]?>//');return false;}
//    if(! mobile.match(/^1[3-9]\d{9}$/)){alert('<?//= $this->session->userdata('sys_lang_ary')[5155]?>//');return false;}
//    if (pass.length <6 || pass.length>20) {alert('<?//= $this->session->userdata('sys_lang_ary')[4797]?>//');return false;}
//    if (verify_code == '') {alert('<?//= $this->session->userdata('sys_lang_ary')[5173]?>//');return false;}
//
//    $.post('/mdapi/uregister',{invi_id:invi_id,actid:actid,sourceid:sourceid,mobile:mobile,password:pass,vcode:verify_code},function(data){
//      console.log(data);
//      if (data=='verify') {alert('您的验证码错误，请重输！');return false;}
//      else if (data=='error') {alert('用户已存在，请登录！');return false;}
//      else{window.location.href='http://api.meidal.com/user/share/1/2592';}
//    });
//  });
</script>

<script type="text/javascript">
  //倒计时
function resetCode(){
  $('#J_getCode').hide();
  $('#J_second').html('60');
  $('#J_resetCode').show();
  var second = 60;
  var timer = null;
  timer = setInterval(function(){
    second -= 1;
    if(second >0 ){
      ss = second
      $('#J_second').html(ss);
    }else{
      clearInterval(timer);
      $('#J_getCode').show();
      $('#J_resetCode').hide();
    }
  },1000);
}
</script>
<!-- script end -->
</body>
</html>
