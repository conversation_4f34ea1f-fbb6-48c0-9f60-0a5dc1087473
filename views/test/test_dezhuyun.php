<?php
$data = [
    [
        "id" => "1",
        "member_mobile" => "17301849323",
    ],
    [
        "id" => "2",
        "member_mobile" => "10086",
    ],
    [
        "id" => "3",
        "member_mobile" => "18111612697",
    ],
    [
        "id" => "4",
        "member_mobile" => "04006999999",
    ],
    [
        "id" => "5",
        "member_mobile" => "18896517105",
    ],
    [
        "id" => "6",
        "member_mobile" => "18742301599",
    ],
    [
        "id" => "7",
        "member_mobile" => "15004713789",
    ]

];
?>
<script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>
<table class="table table-bordered">
    <thead>
    <tr>
        <th>编号</th>
        <th>手机号码</th>
        <th>操作</th>
    </tr>
    </thead>
    <tbody>
    <?php foreach ($data as $v) {?>
        <tr>
            <td><?php echo $v['id'];?></td>
            <td style="padding: 5px 10px 5px 10px;"><?php echo $v['member_mobile'];?></td>
            <td>
                <?php if (!empty($v['member_mobile'])) { ?>
                    <a class="btn btn-danger btn-sm" title="点击开始访问" id="make_call_<?php echo $v['id'];?>" onclick="makecall(<?php echo $v['id']; ?>, '<?php echo $v['member_mobile']; ?>', '<?php echo CALL_TYPE_CUSTOM."_".$v['id']; ?>')">
                        呼叫
                    </a>
                    <a href="#" class="btn btn-default btn-sm" style="display: none;background-color:#B2B2B2;border-radius:4px;" id="drop_call_<?php echo $v['id'];?>" onClick="dropcall(<?php echo $v['id']; ?>)">
                        挂机
                    </a>
                <?php } ?>
            </td>
        </tr>
    <?php }?>
    </tbody>

</table>
<div id="data">

</div>
<div align="center">
    <div id="myCall">

    </div>
</div>

<script type="text/javascript" src="https://8338085.dezhuyun.com/dzfront/icall/lib/icall.js?time=<?php echo time();?>"></script>
<script>
    // /**
    //  * 初始化实例
    //  * @param Element  你在html里的dom元素或标签id
    //  * @param props iCall的属性参数（详见下节文档属性参数）
    //  */
    // // 本示例为js写法，若使用Vue、React等其他框架时，必须保证在初始化时能获取dom元素。如：Vue需在mounted中初始化。
    // var el = document.getElementById('myCall'); // 或 var el = 'maCall';
    // var iCall = new Icall(el, {
    //     option: {
    //         server: 'https://8338085.dezhuyun.com',  // 必传
    //         username: '<EMAIL>',
    //         password: 'Aa123456',
    //         tenancyId: 'qq164810562252850655eb7',
    //         loginType: 'WEB',
    //     },
    // }, () => {
    //     // 初始化完成后回调
    //     iCall.ref.signIn('token'); // 登陆
    // });

    var el = document.getElementById('myCall'); // 或 var el = 'maCall';
    var iCall = new Icall(el, {
        option: {
            server: 'https://8338085.dezhuyun.com',  // 必传
        },
        // 事件
        event: {
            onLoginFail: function(error) {//登录失败
                console.log("登录失败原因：");
                console.log(error);
                console.log("登录失败原因");
            },
            onCallDataChange: function(callData) {//呼叫数据改变
                // TODO
                console.log("\n呼叫数据开始：\n");

                $.post('/test_dz/call_data',{bk_data:callData},function (t) {
                    console.log(t);
                },'json');

                console.log(callData);
                console.log("\n呼叫数据结束：\n");
            },
            // onCallStatusChange: function(callStatus) {//呼叫状态改变
            //     switch(callStatus) {
            //         // 呼出接通
            //         case $Call.CallStatus.OutActiveWaitingContactCallData:
            //             // TODO
            //             break;
            //         // 呼入通话中
            //         case $Call.CallStatus.InActive:
            //             // TODO
            //             break;
            //     }
            // }),
            // onListenStart: function(monitorData) {//监听开始
            //     // TODO
            //     console.log("\n呼叫开始：\n");
            //     console.log(monitorData);
            //     console.log("\n呼叫开始：\n");
            // },
            // onListenEnd: function(monitorData) {//监听结束
            //     // TODO
            //     console.log("\n呼叫结束：\n");
            //     console.log(monitorData);
            //     console.log("\n呼叫结束：\n");
            // },
        },
    }, () => {
        // 初始化完成后回调
        iCall.ref.signIn('<?php echo $auth;?>'); // 登陆
    });


    //$(document).ready(function () {
    //    var el = document.getElementById('myCall');
    //    var iCall = new Icall(el, {
    //        option: {
    //            server: 'https://8338085.dezhuyun.com',  // 必传
    //        },
    //    });
    //    iCall.ref.signIn('<?php //echo $auth;?>//');
    //});



    //呼叫
    function makecall(id, mobile, extension) {

        var randomData = extension + '_' + createRand();//呼叫随路数据
        //自定义参数 限制最多256个字符
        if(randomData!='' && randomData.length > 256){
            alert('自定义参数 的长度不能超过256个字符！');
            return;
        }

        // 动态显示 隐藏 呼叫、挂断 Btn..
        var call_btn_hide = "#make_call_" + id;
        var drop_call_btn_show = "#drop_call_" + id;
        $(call_btn_hide).css("display", "none");
        $(drop_call_btn_show).css("display", "block");
        // 调起呼叫
        iCall.ref.call(mobile, randomData);

    }

    //挂断
    function dropcall(id){

        // 动态显示 隐藏 呼叫、挂断 Btn..
        var call_btn_hide = "#make_call_" + id;
        var drop_call_btn_show = "#drop_call_" + id;
        $(call_btn_hide).css("display", "block");
        $(drop_call_btn_show).css("display", "none");
        // 调起挂断
        iCall.ref.hangup();

    }



    //随机数
    function createRand() {
        var x = 1000;
        var y = 0;
        var randnum = parseInt(Math.random() * (x - y + 1) + y);
        return randnum;
    }
</script>