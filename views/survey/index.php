<!DOCTYPE>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="上医说" />
    <meta name="description" content="上医说" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>上医说</title>
    <link rel="shortcut icon" href="http://www.drsay.cn/theme/images/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="http://medical.drrenew.com/theme/3sbioinc/font-awesome/css/font-awesome.css" />
<!--    <script src="--><?//= base_url()?><!--/theme/--><?//= TEMPLATE_DIR?><!--/js/jquery-3.1.1.min.js"></script>-->
    <script src="/theme/new_manage/js/jquery-3.1.1.min.js"></script>

    <!--防止被网络广告劫持弹出广告-->
    <style>
        html{display:none;}
    </style>
    <script>
        if( self == top ) {
            document.documentElement.style.display = 'block' ;
        } else {
            top.location = self.location ;
        }
    </script>
    <!--防止被网络广告劫持弹出广告-->
    <style type="text/css">
        ,h2,h3,h4,h5,h6,form,fieldset,legend,img,input,textarea,section,th,td,hr,button {padding:0; margin:0;}
        body {font-family:"Microsoft Yahei"; font-size:.6rem; color:#333; /*background:url("http://www.ipanelonline.com/images/page_bg.jpg") repeat 0 0;*/ background-color:#CAE1FF; padding:5px 10px;}
        a {text-decoration:none;}
        img {border:0;}
        input {border:solid 1px #ccc; padding:10px; font-size:.6rem; font-weight:500; /*input {-webkit-appearance:none;}*/}
        -webkit-tap-highlight-color:rgba(0,0,0,0);
        table {font-size:.6rem;}
        .logo {width:247px; margin:0px 0 5px 15px;}
        .logo img {width:206px;}
        .box_pic_top {width:100%; background-color:#fff;}
        .box_pic_center {width:100%; background-color:#fff; border-radius:5px;}
        .box_pic_bottom {width:100%; background-color:#fff;}
        .box_tab {margin:5px 20px 0;}
        .layout {width:100%; margin:0 auto; line-height:24px;}
        .layout h1 {font-size:.75rem; text-align:center; padding:20px 0; margin:0 20px;}
        .title_font {border-bottom:4px solid #666; line-height:30px;}
        .layout h2 {font-weight:normal; font-size:.6rem; line-height:30px; padding:10px; border:1px dashed #F97C00; border-radius:.25rem; background:#FFFCED; text-align:left; margin:10px 20px 15px 20px;}
        .layout h3 {font-size:.6rem; padding:1rem 0 .2rem; text-align:left; color:#0E4B83; border-bottom:solid 1px #CCC; margin-bottom:5px;}
        .layout h3 span {font-weight:normal; font-size:13px; font-style:italic;}
        .shuoming {margin:0 0 10px 0; padding-bottom:5px;}
        .shuoming p {color:#f00; font-size:.6rem; margin:0 20px;}
        .next_btn {margin:10px 0; padding-top:10px; text-align:center;}
        .btn {min-width:120px; text-align:center; font-size:.6rem; font-weight:400; color:white; border:0; cursor:pointer; background-color:#23c6c8; border-color:#23c6c8; border-radius:.2rem; padding:.3rem 1rem; margin-left:1rem; margin-top:.5rem;}
        .footer {font-weight:300; font-size:.5rem; color:#333; text-align:center; padding:10px 0 20px;}
        .checd_box {display:block; padding:.3rem 0;}
        .checd_box input {vertical-align:middle;}
        .checd_box span {vertical-align:middle; padding-left:.3rem; font-size:.6rem;}
        .danger {background-color:#ed5565; border-color:#ed5565;}
        .success {background-color:#1c84c6; border-color:#1c84c6;}
        .primary {background-color:#1ab394; border-color:#1ab394;}
        .btn_row {width:100%; text-align: center;}
        .btn_row input {margin-bottom:.5rem; margin-left:0; max-width:300px;}
        .btn_bottom {padding-bottom:.5rem;}
        .textarea_border {border:solid 1px #ddd; background-color:#fff0f5; padding:.25rem; margin-top:.5rem; font-size:.6rem; width:100%;}
        .img_box {border:dashed 1px #1ab394; padding:.5rem; min-height:100px; text-align:center; margin-top:.5rem;}
        .img_dashed {width:20%; height:65px; line-height:65px; border:dashed 1px #1ab394; padding:.5rem; text-align:center; margin-top:.5rem; display:inline-block;}
        .img_left {margin-left:.3rem;}
        .img_box span {font-size:.65rem; color:#1ab394; display:block; margin:.2rem 0;}
        .img_dashed strong {font-weight:300; color:#ccc; font-size:.5rem;}
        .fa_plus {font-size:1rem; color:#ccc;}
        .fa_camera {font-style:normal; font-size:.7rem;}
        .i_text {margin-top:.3rem;}
        .qrcode {position:absolute; top:20px; right:20px;}
        .qrcode img {width:60px;}
    </style>
    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(document.documentElement.getBoundingClientRect().width);
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + 'px';
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
<div class="layout">
    <form action="" method="post">
        <div class="logo">
            <a href="javascript:;" target="_blank" title="drsay">
                <img src="/theme/admin/im/img/drsay_logo.jpg" title="drsay" alt="drsay" border="0" />
            </a>

            <div class="qrcode"><img src="/theme/admin/im/img/qrcode.jpg" alt="" /></div>
        </div>
        <div class="box_pic_top"></div>
        <div class="box_pic_center">
            <h1 class="title_font"><span class="STYLE1"><?php echo $app_project['pro_name'];?></span></h1>
<!--            <h1 class="title_font"><span class="STYLE1">非常欢迎您参加本次调查、您的声音将会得到足够的重视！</span></h1>-->

            <?php if(empty($survey_one_page)){ ?>
            <h2><?php echo $app_project['pro_info'];?></h2>
                <div class="shuoming">
                    <?php if(isset($app_prizes['prize_photo']) || !empty($app_prizes['prize_photo'])){ ?>
                        <img style="width:100%;" src="<?php echo $app_prizes['prize_photo'];?>">
                        <h3 style="text-align: center; padding: 10px 30px; "> <?php echo $app_prizes['prize_name'];?></h3>
                    <?php }?>
                </div>
            <?php  }?>
            <div class="box_tab" >

                <?php if(empty($survey_one_page)){ ?>
<!--                    <h3 style="padding-top: 0px;"> 安装App上医说</h3>-->
<!--                    <table style="margin-left:10px; font-size:.6rem;" width="90%" border="0" cellspacing="0" cellpadding="0">-->
<!--                        <tr>-->
<!--                            <td>-->
<!--                                <div id="" class="img_box">-->
<!--                                    <span>请扫描二维码加入上医说</span>-->
<!--                                    <img src="/theme/admin/im/img/qrcode.jpg" alt="" />-->
<!--                                </div>-->
<!--                            </td>-->
<!--                        </tr>-->
<!--                    </table>-->
               <?php  } else {echo $question_view;}?>
            </div>


            <?php if(!empty($survey_one_page)){ ?>
                <?php if($last_page){?>
                    <div class="btn_row btn_bottom">
                        <input name="button" type="button" class="btn primary next_page" id="button" value="提交" />
                    </div>
                <?php }else{?>
                    <div class="btn_row btn_bottom">
                        <p class="next_btn">
                            <input name="action" type="hidden" id="action" value="goto" />
<!--                            <a  href="javascript:history.go(-1);"><input name="button" type="button" class="btn danger" id="button" value="上一页" onclick="return chkSubmit();"/></a>-->
                            <input name="button" type="button" class="btn next_page" id="button" value="下一页"  />
                        </p>
                    </div>
                <?php }?>
            <?php } else { ?>
                <div class="btn_row">
                    <input name="button" type="button" class="btn success" id="button" value="开始"  onclick="survey_one_page();"/>
                </div>
            <?php }?>

        </div>
        <div class="box_pic_bottom"></div>
</div>
</form>
</div>

<div class="footer" style="clear:both;">
    Copyright &copy; <?php echo date('Y');?> Powered by drsay.cn.
</div>

<script type='application/javascript'>
    function survey_one_page()
    {
        var go_code = '<?php echo $go_code?>';
        var query_string_uid = '<?php echo $query_string_uid?>';
        $.post('/survey/survey_one_page/'+go_code+"?"+query_string_uid,function(f) {
            if (f.rs_code == "error") {
                alert(f.rs_msg);
                return false;
            }
            window.location.reload();
        },'json');
    }
</script>
</body>
</html>
