<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0,user-scalable=no"
    />
    <title>健康通-上医说</title>
    <script src="/theme/bk/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/comm_init.js?<?php echo time();?>"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        input {
            -webkit-appearance: none;
        }
        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }
        body,
        html {
            height: 100%;
        }
        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
        }
        .title {
            text-align: center;
            padding-top: 50px;
            font-weight: 700;
            font-size: 30px;
        }
        .content {
            text-align: center;
            padding-top: 50px;
        }
        .content input {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 70%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }
        .content .code {
            width: 70%;
            height: 46px;
            margin: 0 auto;
            position: relative;
        }
        .content .code input {
            width: 100%;
        }
        .content .code .get_code {
            position: absolute;
            right: 0;
            height: 100%;
            line-height: 46px;
            background: #f85691;
            color: #fff;
            padding: 0 10px;
            font-size: 14px;
            cursor: pointer;
        }
        .content .tips {
            font-size: 12px;
            text-align: left;
            width: 70%;
            margin: 0 auto;
            margin-top: 5px;
        }
        .btn {
            text-align: center;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .btn button {
            cursor: pointer;
            min-width: 200px;
            background: #f85691;
            padding: 10px;
            color: #fff;
            font-size: 24px;
            border: none;
            outline: none;
            border-radius: 2px;
        }

        .error_msg {
            margin-bottom: 10px;
            color: red;
        }
    </style>
</head>
<body>
<div class="container">
    <table>
        <thead>
            <th>项目名称</th>
            <th>医生姓名</th>
        </thead>
        <tbody>
        <?php if($list){?>
            <?php foreach ($list as $v) {?>
                <tr>
                    <td><?php echo $v['pro_name'];?></td>
                    <td><?php echo $v['dr_name'];?></td>
                </tr>
            <?php }?>
        <?php }?>
        </tbody>
    </table>
</div>

</body>
</html>
