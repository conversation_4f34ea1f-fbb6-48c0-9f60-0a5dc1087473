<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0,user-scalable=no"
    />
    <title>健康通-上医说</title>
    <script src="/theme/bk/jquery-3.1.1.min.js"></script>
    <script src="/theme/bk/comm_init.js?<?php echo time();?>"></script>
    <script src="/theme/bk/jquery.form.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        input {
            -webkit-appearance: none;
        }
        ::-webkit-input-placeholder {
            font-size: 14px;
        }

        ::-moz-placeholder {
            font-size: 14px;
        }

        :-ms-input-placeholder {
            font-size: 14px;
        }
        body,
        html {
            height: 100%;
        }
        .container {
            max-width: 420px;
            margin: 0 auto;
            height: 100%;
        }
        .title {
            text-align: center;
            padding-top: 50px;
            font-weight: 700;
            font-size: 30px;
        }
        .content {
            text-align: center;
            padding-top: 50px;
        }
        .content input {
            border: 2px solid #fd5592;
            padding: 5px;
            width: 70%;
            outline: none;
            height: 46px;
            font-size: 14px;
            border-radius: 2px;
            margin-bottom: 20px;
        }
        .content .code {
            width: 70%;
            height: 46px;
            margin: 0 auto;
            position: relative;
        }
        .content .code input {
            width: 100%;
        }
        .content .code .get_code {
            position: absolute;
            right: 0;
            height: 100%;
            line-height: 46px;
            background: #f85691;
            color: #fff;
            padding: 0 10px;
            font-size: 14px;
            cursor: pointer;
        }
        .content .tips {
            font-size: 12px;
            text-align: left;
            width: 70%;
            margin: 0 auto;
            margin-top: 5px;
        }
        .btn {
            text-align: center;
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .btn button {
            cursor: pointer;
            min-width: 200px;
            background: #f85691;
            padding: 10px;
            color: #fff;
            font-size: 24px;
            border: none;
            outline: none;
            border-radius: 2px;
        }

        .error_msg {
            margin-bottom: 10px;
            color: red;
        }
    </style>
</head>
<body>
<div class="container">
    <form id="the_from" onsubmit="return common_js.form_sumbit(this, 'error_msg')">
        <div class="logo" style="text-align: center; padding-top: 80px">
            <img style="height: 80px" src="/theme/go/image/jkt_logo_1.png" alt="" />
        </div>

        <div class="content">
            <input type="text" id="username" name="username" placeholder="Username" />
            <input type="password" id="password" name="password" placeholder="Password" />
            <p class="code">
                <input type="text" id="code" name="verify_code" placeholder="请输入验证码" />
                <span class="get_code">获取验证码</span>
            </p>
            <p class="tips">验证码请查阅手机短信</p>
        </div>

        <div class="btn">
            <div class="error_msg"></div>
            <input type="hidden" name="bk_code" value="<?php echo $bk_code;?>" />
            <input type="hidden" name="survey_uid_code" value="<?php echo $survey_uid_code;?>" />
            <button>登 录</button>
        </div>
    </form>
</div>

<script>
    $(function () {
        // 获取授权码
        $(".get_code").click(function () {
            if (!$(this).hasClass("send")) {
                var username = $("#username").val().trim();
                var password = $("#password").val().trim();
                if (username && password) {
                    $.ajax({
                        url: "/project_wx/pro_send_sms",
                        type: "post",
                        data: {
                            username: username,
                            password: password
                        },
                        dataType: "json",
                        success: function (info) {
                            $(".error_msg").text(info.rs_msg)
                            if (info.rs_code == "success") {
                                $(this).addClass("send");
                                var that = $(this);
                                var seconds = 60;
                                var timeId = setInterval(function () {
                                    seconds--;
                                    if (seconds > 0) {
                                        that.text(seconds + "s后重新发送");
                                    } else {
                                        clearInterval(timeId);
                                        that.removeClass("send");
                                        that.text("获取授权码");
                                    }
                                }, 1000);
                            }
                        }
                    })
                } else {
                    $(".error_msg").text("请输入账号或密码")
                }
            }
        });
    });
</script>
</body>
</html>
