<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>join</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
        }

        .container {
            padding: 30px 20px;
            width: 100%;
            min-height: 100%;
            max-width: 500px;
            border-left: 1px solid #ccc;
            border-right: 1px solid #ccc;
            margin: 0 auto;
        }

        .container .msg {
            text-align: center;
            font-size: 20px;
            padding: 10px 0;
            margin-bottom: 20px;
        }

        .container .msg img {
            width: 50px;
            vertical-align: middle;
        }

        .container header {
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            padding-bottom: 30px;
            color: #FF5722;
        }

        .container .sel {
            padding-left: 6px;
        }

        .container .sel p {
            font-size: 18px;
            padding-bottom: 10px;
        }

        .container .sel select {
            width: 200px;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            box-sizing: border-box;
            border-radius: 2px;
            height: 40px;
            line-height: 40px;
            outline: none;
        }

        .container .btn {
            text-align: center;
        }

        .container .btn button {
            outline: none;
            border: none;
            background-color: #479de6;
            color: #fff;
            width: 100px;
            text-align: center;
            height: 35px;
            line-height: 35px;
            font-size: 16px;
            border-radius: 2px;
            cursor: pointer;
        }

        .container .ewm {
            text-align: center;
        }

        .container .ewm img {
            width: 280px;
        }
    </style>
</head>

<body>
    <div class="container">

        <div class="msg">
            <img src="http://admin.drsay.cn/theme/sxo/img/logo.png" alt="">
            <span style="color:#335c8a;font-weight: bold;">健康通</span>
            <div style="margin-top:10px;">服务医师为有价值的医生创造更多合作机会，加入健康通企业群，您将得到一对一的专员服务，不间断的分发付费项目增加您阳光收入。</div>
        </div>

        <!--<header>了解项目动态,增加阳光收入</header>-->



        <div class="sel">
            <p>请根据您的科室选择加入</p>
            <select name="" id="dep">
                <option value="">--请选择--</option>
                <?php foreach ($info as $key => $val) { ?>
                    <option value="<?php echo $key; ?>"><?php echo $key; ?></option>
                <?php } ?>
            </select>
        </div>

        <!--            <div class="btn">
                <button>确认</button>
            </div>-->

        <div class="ewm">
            <img id="group" src="" alt="" />
        </div>
    </div>
</body>
<script src="/theme/<?= TEMPLATE_DIR ?>/js/jquery-3.1.1.min.js"></script>
<script type="text/javascript">
    var info_ary = [];
    <?php foreach ($info as $k => $v) { ?>
        info_ary["<?php echo $k; ?>"] = "<?php echo $v; ?>";
    <?php } ?>
    $(function() {
        $('#dep').change(function() {
            var val = $(this).val();
            if (val != '') {
                var srcstr = info_ary[val];
                $('#group').attr('src', srcstr);
            } else {
                $('#group').attr('src', "");
            }
        });
    });
</script>

</html>