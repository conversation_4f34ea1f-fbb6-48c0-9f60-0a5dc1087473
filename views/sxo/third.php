<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <title>third</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html,
        body {
            height: 100%;
        }

        .container {
            position: relative;
            background: #270073;
            text-align: center;
            min-height: 100%;
        }

        @media screen and (max-width: 750px) {
            .container .main {
                width: 100%;
            }

            .container .content {
                width: 100%;
            }
        }

        @media screen and (min-width: 750px) {
            .container .main {
                width: 380px;
            }

            .container .content {
                width: 380px;
            }
        }

        .container .title {
            width: 100%;
            color: #ffff00;
            position: absolute;
            top: 5%;
            text-align: center;
            font-size: 18px;
        }

        .container .content {
            padding-top: 100px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 0;
            z-index: 7;
        }

        .container .content .firsr_box,
        .container .content .second_box,
        .container .content .third_box,
        .container .content .finish_box {
            position: relative;
            margin: 0 auto;
            width: 80%;
            padding-bottom: 10px;
        }

        .container .content .firsr_box::after,
        .container .content .second_box::after,
        .container .content .third_box::after {
            content: "";
            position: absolute;
            background: #cc33ff;
            width: 1px;
            height: 100%;
            top: 50px;
            left: 27px;
        }

        .container .content .firsr_box::before,
        .container .content .second_box::before,
        .container .content .third_box::before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border: 15px solid;
            border-color: #cc33ff transparent transparent transparent;
            left: 12px;
            bottom: -16px;
        }

        .container .content .word {
            float: left;
            margin-top: 15px;
            margin-left: 15px;
            width: calc(100% - 90px);
            text-align: left;
            font-size: 14px;
        }

        .button {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
        }

        .button img {
            cursor: pointer;
        }

        .button p {
            cursor: pointer;
            font-size: 32px;
            color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 背景图 -->
        <img class="main" src="https://www.drsay.cn/theme/sxo/img/third_bg_n.jpg" alt="" />

        <!-- 标题 -->
        <div class="title">简单3步即可完成互联网项目</div>

        <!-- 内容 -->
        <div class="content">
            <!-- 第一步 -->
            <div class="firsr_box">
                <p style="height: 55px">
                    <img style="width: 55px; float: left" src="https://www.drsay.cn/theme/sxo/img/one.png" alt="" />
                    <span class="word">扫描二维码通过认证</span>
                </p>
                <p style="text-align: center">
                    <?php
                    $task_one = isset($task_one['local_qr_code']) ? $task_one['local_qr_code'] : '';
                    ?>
                    <img style="width: 120px" src="<?php echo $task_one; ?>" alt="" />
                </p>
            </div>
            <!-- 第二步 -->
            <div class="second_box">
                <p style="height: 55px; margin-bottom: 15px">
                    <img style="width: 55px; float: left" src="https://www.drsay.cn/theme/sxo/img/two.png" alt="" />
                    <span class="word">扫码添加数字赛小欧数字沟通专员微信</span>
                </p>
                <p style="text-align: center">
                    <?php
                    $task_two = isset($task_two_three['task_two_qrcode']) ? json_decode($task_two_three['task_two_qrcode'], true) : [];
                    $task_two_src = isset($task_two[0]) ? $task_two[0] : 0;
                    ?>
                    <img style="width: 120px" src="<?php echo $task_two_src; ?>" alt="" />
                </p>
            </div>
            <!-- 第三步 -->
            <div class="third_box" style="padding-bottom: 0">
                <p style="height: 55px;">
                    <img style="width: 55px; float: left" src="https://www.drsay.cn/theme/sxo/img/three.png" alt="" />
                    <span class="word">任意参与以下医学微信群</span>
                </p>
                <p style="text-align: center;margin-bottom: 5px;">
                    <?php
                    $task_three = isset($task_two_three['task_three_qrcode']) ? json_decode($task_two_three['task_three_qrcode'], true) : [];
                    $task_three_src_1 = isset($task_three[0][0]) ? $task_three[0][0] : 0;
                    ?>
                    <span style="font-size: 12px;display: block;margin-bottom: 8px;">赛小欧心脑糖研习社</span>
                    <img style="width: 130px;" src="<?php echo $task_three_src_1; ?>" title="<?php echo isset($task_three[0][1]) ? $task_three[0][1] : '';?>" />
                </p>
<!--                <p style="text-align: center;margin-bottom: 5px;">
                    <?php
                    $task_three_src_2 = isset($task_three[1][0]) ? $task_three[1][0] : 0;
                    ?>
                    <span style="font-size: 12px;display: block;margin-bottom: 8px;">心血管、神经内科分享群</span>
                    <img style="width: 80px;" src="<?php echo $task_three_src_2; ?>" title="<?php echo isset($task_three[1][1]) ? $task_three[1][1] : '';?>" />
                </p>-->


            </div>
            <!-- 结束 -->
            <div class="finish_box">
                <p style="height: 55px">
                    <img style="width: 55px; float: left" src="https://www.drsay.cn/theme/sxo/img/four.png" alt="" />
                </p>
            </div>
        </div>

        <!-- 按钮 -->
        <div class="button">
            <p>我已了解</p>
            <img style="height:170px;" src="https://www.drsay.cn/theme/sxo/img/button_finshed.png" alt="" />
        </div>
    </div>
</body>

</html>