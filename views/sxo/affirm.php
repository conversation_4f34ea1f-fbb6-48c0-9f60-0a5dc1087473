<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>健康通-确认基础信息</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }

            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-color: #5e93fb;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                padding-top: 3rem;
                text-align: center;
                color: #fff;
                font-size: 1.4rem;
            }

            .container header h4 {
                margin-bottom: 0.2rem;
                font-weight: normal;
            }

            section {
                font-size: 0.8rem;
                color: #fff;
                padding: 1rem 1.2rem;
            }

            section div {
                margin-bottom: 0.6rem;
            }

            section .radio p label {
                display: inline-block;
                width: 4rem;
                height: 0.8rem;
                line-height: 0.8rem;
                cursor: pointer;
            }
            section .radio p input {
                border: 1px solid #d8d8d8;
                width: 0.8rem;
                height: 0.8rem;
                vertical-align: middle;
                margin-right: 0.2rem;
            }
            section .btn {
                text-align: center;
                padding-top: 1rem;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
            }
            
            section .tip {
                padding-top: 2.2rem;
                font-size: 0.64rem;
                color: #fdab40;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>确认基础信息</h4>
            </header>

            <section>
                <?php if (!empty($info)) { ?>
                    <div class="title">
                        <p>姓名：<?php echo isset($info['dr_name']) ? $info['dr_name'] : ''; ?></p>
                        <p>科室：<?php echo isset($info['dr_department']) ? $info['dr_department'] : ''; ?></p>
                        <p>医院：<?php echo isset($info['unit_name']) ? $info['unit_name'] : ''; ?></p>
                    </div>
                <?php } ?>
                <div class="radio">
                    <p>Q1. 请问您平时接诊过程中是否接诊心脑病及内分泌相关疾病的患者？</p>
                    <p>
                        <label>
                            <input name="isDepPatient" type="radio" <?php
                            if (isset($info['is_dep_patient']) && ($info['is_dep_patient'] == 1)) {
                                echo 'checked';
                            }
                            ?> value="1" />是
                        </label>
                        <label>
                            <input name="isDepPatient" type="radio" <?php
                            if (isset($info['is_dep_patient']) && ($info['is_dep_patient'] != 1)) {
                                echo 'checked';
                            }
                            ?> value="2" />否
                        </label>
                    </p>
                </div>

                <div class="other">
                    Q2. 您已加入了什么互联网医院平台？
                    <input style="height: 36px;" type="text" name="internetPlatform" value="<?php echo isset($info['internet_platform']) ? $info['internet_platform'] : ''; ?>">
                </div>

                <div class="btn">
                    <div class="tip" id="ver_code_err"></div>
                    <button onclick="operation()">下一步</button>
                </div>
            </section>
        </div>
        <script src="/theme/<?= TEMPLATE_DIR ?>/js/jquery-3.1.1.min.js"></script>
        <script>
                        (function (doc, win) {
                            var docEl = doc.documentElement,
                                    resizeEvt =
                                    "onorientationchange" in window ? "onorientationchange" : "resize",
                                    recalc = function () {
                                        var clientWidth = docEl.clientWidth;
                                        if (!clientWidth)
                                            return;
                                        if (clientWidth >= 750) {
                                            docEl.style.fontSize = "30px";
                                        } else {
                                            docEl.style.fontSize = clientWidth / 15 + "px";
                                        }
                                    };
                            if (!doc.addEventListener)
                                return;
                            win.addEventListener(resizeEvt, recalc, false);
                            doc.addEventListener("DOMContentLoaded", recalc, false);
                        })(document, window);

                        function operation() {
                            var isDepPatient = $('input[name=isDepPatient]:checked').val();
                            if(isDepPatient=='' || isDepPatient==undefined){
                                $('#ver_code_err').text('请选择Q1！');
                                return false;
                            }
                            var internetPlatform = $('input[name=internetPlatform]').val();
                            if(internetPlatform==''){
                                $('#ver_code_err').text('请输入Q2！');
                                return false;
                            }
                            $.post('/sxo/affirmsave', {isDepPatient: isDepPatient, internetPlatform: internetPlatform,
                                code: "<?php echo $sid; ?>", id: "<?php echo isset($info['invite_info_id']) ? $info['invite_info_id'] : ''; ?>"},
                                    function (c) {
                                        if (c.rs_code == 'success') {
                                            location.href = "/sxo/first?code=<?php echo $sid; ?>";
                                        } else if (c.rs_code == 'errors') {
                                            location.href = "/";
                                        } else {
                                            $('#ver_code_err').text(c.rs_msg);
                                        }
                                    }, 'json');
                        }
        </script>
    </body>
</html>
