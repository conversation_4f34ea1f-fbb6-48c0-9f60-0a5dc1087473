<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
            />
        <title>qf</title>
        <style>
            * {
                margin: 0;
                padding: 0;
            }

            html,
            body {
                height: 100%;
            }

            .container {
                position: relative;
                text-align: center;
                height: 100%;
            }

            .container .main {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
            }
            @media screen and (max-width: 750px) {
                .container {
                    background: #270073;
                }

                .container .main {
                    width: 100%;
                }
                .container .button {
                    width: 100%;
                }
            }

            @media screen and (min-width: 750px) {
                .container .main {
                    width: 380px;
                }
                .container .button {
                    width: 380px;
                }
            }

            .container .ewm {
                width: 100%;
                position: absolute;
                z-index: 77;
                left: 50%;
                transform: translateX(-50%);
                top: 285px;
                cursor: pointer;
            }
            .container .ewm img {
                height: 150px;
            }
            .container .ewm p {
                color: #ff9800;
                font-size: 26px;
                font-weight: 700;
            }

            .container .button {
                position: absolute;
                z-index: 7;
                left: 50%;
                transform: translateX(-50%);
                top: 500px;
                cursor: pointer;
            }
            .container .button img {
                height: 170px;
            }
            .container .button .word {
                position: absolute;
                font-size: 32px;
                color: #fff;
                left: 30%;
                top: 60px;
            }
        </style>
        <script src="/theme/<?= TEMPLATE_DIR ?>/js/jquery-3.1.1.min.js"></script>
    </head>

    <body>
        <div class="container">
            <img class="main" src="https://www.drsay.cn/theme/sxo/img/qf_bg.jpg" alt="" />
            <div class="ewm">
                <p>领取丰厚奖励</p>
                <p style="margin-bottom: 10px">添加健康通专员微信</p>
                <img src="<?php echo isset($code) ? $code : 0; ?>" alt="" />
            </div>

            <?php 
            $participation= isset($participation)?$participation:0;
            if($participation==1 || $participation==3){
                ?>
                <div id="participation" class="button" onclick="participation('<?php echo isset($authen)?$authen:0;?>')">
                    <img src="https://www.drsay.cn/theme/sxo/img/button.png" alt="" />
                    <p class="word">确认参与</p>
                </div>
            <?php }?>
        </div>

        <script type="text/javascript">
            // 确认参与操作
            function participation(authen) {
                if(confirm('确认参与吗？')){
                    $.ajax({
                        type: "POST",url: "/sxo/participation",data: {'authen':authen},
                        success: function (str) {
                            var json_msg = $.parseJSON(str);
                            if (json_msg.rs_code == "success") {
                                alert(json_msg.rs_msg);
                                $('#participation').hide();
                            }else{
                                alert(json_msg.rs_msg);
                                return false;
                            }
                        }
                    });
                }
            }
        </script>
    </body>
</html>
