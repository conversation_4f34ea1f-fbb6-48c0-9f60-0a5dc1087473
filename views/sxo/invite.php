<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <title>健康通-邀请</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            height: 100%;
        }

        input[type="text"] {
            -webkit-appearance: none;
        }

        input::-webkit-input-placeholder {
            color: #c9caca;
            font-size: 0.48rem;
        }

        input::-moz-placeholder {
            color: #c9caca;
            font-size: 0.48rem;
        }

        input:-moz-placeholder {
            color: #c9caca;
            font-size: 0.48rem;
        }

        input:-ms-input-placeholder {
            color: #c9caca;
            font-size: 0.48rem;
        }

        .container {
            width: 15rem;
            min-height: 100%;
            margin: 0 auto;
            padding-bottom: 1rem;
            position: relative;
        }

        .container header {
            height: 12.16rem;
            background: #5e93fb;
        }

        .container header img {
            width: 100%;
        }

        section {
            width: 14.22rem;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 5rem;
            background: #fff;
            border-radius: 0.2rem 0.2rem 0 0;
            overflow: hidden;
        }

        section .title {
            text-align: center;
            height: 1.28rem;
            line-height: 1.28rem;
            background-color: #2e3af5;
            color: #fff;
            font-size: 0.56rem;
        }

        section label {
            display: block;
            padding: 0.32rem;
        }

        section label .desc {
            display: inline-block;
            width: 3rem;
            font-size: 0.52rem;
            color: #595757;
        }

        section label input {
            width: 10rem;
            height: 1.3rem;
            border: 1px solid #c9caca;
            border-radius: 0.1rem;
            padding: 0.3rem;
            outline: none;
        }

        section label p {
            display: inline-block;
            width: 10rem;
            height: 1.3rem;
            line-height: 1.3rem;
            border: 1px solid #c9caca;
            border-radius: 0.1rem;
            padding: 0 0.3rem;
            color: #c9caca;
            font-size: 0.48rem;
            position: relative;
        }

        section .practice label span {
            width: 4.6rem;
        }

        section .practice label input,
        section .practice label p {
            width: 8rem;
        }

        section label p::after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border: 0.3rem solid transparent;
            border-top: 0.3rem solid #e1e1e1;
            right: 0.5rem;
            top: 40%;
        }

        section label p select {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            opacity: 0;
        }

        section .radio {
            font-size: 0.6rem;
            color: #2a2a2a;
            padding: 0.4rem;
        }

        section .radio p {
            padding: 0.4rem 0;
        }

        section .radio p:last-child {
            text-align: center;
        }

        section .radio p span {
            display: inline-block;
            width: 4rem;
            height: 0.8rem;
            line-height: 0.8rem;
        }

        section .radio p input {
            border: 1px solid #d8d8d8;
            width: 0.8rem;
            height: 0.8rem;
            vertical-align: middle;
            margin-right: 0.2rem;
        }

        section .tip {
            margin-top: 1rem;
            font-size: 0.56rem;
            color: #a1a1a1;
            line-height: 1.2rem;
        }

        section .btn {
            text-align: center;
            padding-top: 1rem;
            padding: 1rem 0;
        }

        section .btn button {
            outline: none;
            cursor: pointer;
            height: 1.6rem;
            line-height: 1.6rem;
            width: 9.36rem;
            color: #fff;
            border-radius: 1rem;
            border: none;
            font-size: 1rem;
            background: linear-gradient(to right, #ff9333, #ffc156);
        }

        .verification {
            width: 5rem;
            height: 1.3rem;
            border: none;
            border-radius: 0.1rem;
            padding: 0.1rem;
            outline: none;
            background: #5e93fb;
            font-size: 0.52rem;
            color: #fff;
        }

        .error_tips {
            font-size: 0.5rem;
            color: red;
            height: 0.1rem;
            line-height: 0.1rem;
        }

        .practice .error_tips {
            text-align: right;
            padding-right: 0.3rem;
        }

        .base .error_tips {
            padding-left: 3.8rem;
        }

        .container header {
            height: 12.16rem;
            color: #fff;
            text-align: center;
            padding-top: 0.3rem;
            background-image: url("/theme/sxo/img/bg.png");
            background-size: 100% auto;
        }

        .container header h4 {
            font-weight: 400;
            font-size: 1.2rem;
        }

        .container header h4:last-child {
            text-shadow: #fff 1px 0 0, #fff 0 1px 0, #fff -1px 0 0, #fff 0 -1px 0;
            color: #ffc156;
            font-size: 1.4rem;
        }

        .container header img {
            width: 100%;
        }

        .checkbox {
            font-size: 0.56rem;
            color: #a1a1a1;
            padding-bottom: 1rem;
            cursor: pointer;
        }

        .checkbox input {
            cursor: pointer;
            position: relative;
            margin-right: 0.2rem;
            margin-left: 0.3rem;
            height: 0.5rem;
        }

        .checkbox input[type="checkbox"]::after {
            content: " ";
            position: absolute;
            background-color: #fff;
            border: 1px solid #ccc;
            color: #ccc;
            width: 0.5rem;
            height: 0.5rem;
            line-height: 0.5rem;
            text-align: center;
        }

        .checkbox input[type="checkbox"]:checked::after {
            content: "✓";
        }

        .checkbox p {
            margin-left: 1rem;
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h4>互联网医院小调查</h4>
            <h4>预报名</h4>
        </header>

        <section>
            <form id="subForm">

                <div class="practice">
                    <div class="title">执业信息</div>
                    <div class="detail">

                        <div style="margin:30px 10px;border:2px solid red;position:relative;padding-bottom:20px;">
                            <h6 style="text-align: center;font-size: 0.58rem;color:red;margin-top:10px;">二选一必填</h6>
                            <label>
                                <span class="desc" style="width: 3rem;">医师执业证</span>
                                <input style="width: 9rem;" name="license_no" type="text" placeholder="请输入医师执业证号" />
                            </label>
                            <p class="error_tips" id="license_no"></p>

                            <p style="font-size: 0.58rem;padding-left: 1rem;color: red;position:absolute;bottom: 2.5rem;">或</p>

                            <label>
                                <span class="desc" style="width: 3rem;">乡村医师证</span>
                                <input style="width: 9rem;" type="text" name="certificate_no" placeholder='请输入医师证件号'>
                            </label>
                            <p class="error_tips" id="certificate_no"></p>
                        </div>


                        <label>
                            <span class="desc">医师资格证号<font color="#2e3af5">[选填]</font></span>
                            <input name="credentials_no" type="text" placeholder="请输入医师资格证号" />
                        </label>
                        <p class="error_tips" id="credentials_no"></p>

                        <label>
                            <span class="desc">附件<font color="#2e3af5">[选填]</font></span>
                            <input type="file" name="enclosure" placeholder='请选择附件' accept=".jpg,.jpeg,.png,.bmp">
                        </label>
                        <p class="error_tips" id="enclosure"></p>

                        <label>
                            <span class="desc">执业类别<font color="#2e3af5">[选填]</font></span>
                            <p class="select">
                                <span>请选择</span>
                                <select name="dr_practice" id="practice_sort" onchange="sel_job_title(this.value);">
                                    <option value="">--请选择--</option>
                                    <?php foreach ($practice as $k_practice_sort => $v_practice_sort) { ?>
                                        <option value="<?php echo $k_practice_sort; ?>"><?php echo $v_practice_sort; ?></option>
                                    <?php } ?>
                                </select>
                            </p>
                        </label>
                        <label>
                            <span class="desc">职称<font color="#2e3af5">[选填]</font></span>
                            <p class="select" id="job_title">
                                <span>请选择</span>
                                <select name="dr_job_title" id="">
                                    <option value="">请选择</option>
                                </select>
                            </p>
                        </label>
                        <label>
                            <span class="desc">职务<font color="#2e3af5">[选填]</font></span>
                            <p class="select" id="position">
                                <span>请选择</span>
                                <select name="dr_position" id="">
                                    <option value="">请选择</option>
                                </select>
                            </p>
                        </label>
                    </div>
                </div>

                <div class="tip">
                    温馨提醒：健康通十分注重您的个人隐私，执业信息仅用于健康通确认您的执业医师身份。
                </div>

                <div class="radio">
                    <p>是否有心脑病及内分泌方面的患者</p>
                    <p>
                        <span><input name="is_dep_patient" type="radio" value="1" />是</span>
                        <span><input name="is_dep_patient" type="radio" value="2" />否</span>
                    </p>
                </div>

                <div class="radio">
                    <p>是否有互联网医院诊疗经验</p>
                    <p>
                        <span><input name="is_internet_make" type="radio" value="1" />有</span>
                        <span><input name="is_internet_make" type="radio" value="2" />无</span>
                    </p>
                </div>

                <div class="practice" id="internet_div" style="display:none;">
                    <div class="detail">
                        <label>
                            <span class="desc">互联网平台</span>
                            <input name="internet_platform" type="text" placeholder="请输入互联网平台名称" />
                        </label>
                        <p class="error_tips" id="internet_platform" style="height:0.6rem;"></p>
                    </div>
                </div>

                <div class="base">
                    <div class="title">基础信息</div>
                    <div class="detail">
                        <label>
                            <span class="desc">省份<font color="red">[必填]</font></span>
                            <p class="select">
                                <span>请选择</span>
                                <select id="province_se" name="province_code" onchange="javascript:sel_city(this.value);">
                                    <option value="0">请选择</option>
                                    <?php foreach ($province as $k_province => $v_province) { ?>
                                        <option value="<?php echo $k_province; ?>"><?php echo $v_province; ?></option>
                                    <?php } ?>
                                </select>
                            </p>
                        </label>
                        <p class="error_tips" id="province_code"></p>
                        <label>
                            <span class="desc">城市<font color="red">[必填]</font></span>
                            <p class="select" id="city">
                                请选择
                                <select name="city_code">
                                    <option value="0">请选择</option>
                                </select>
                            </p>
                        </label>
                        <p class="error_tips" id="city_code"></p>
                        <label>
                            <span class="desc">医院<font color="red">[必填]</font></span>
                            <input name="unit_name" type="text" placeholder="请输入医院名称" />
                        </label>
                        <p class="error_tips" id="unit_name"></p>
                        <label>
                            <span class="desc">科室<font color="red">[必填]</font></span>
                            <input name="dr_department" type="text" placeholder="请输入所在科室" />
                        </label>
                        <p class="error_tips" id="dr_department"></p>
                        <label>
                            <span class="desc">姓名<font color="red">[必填]</font></span>
                            <input name="dr_name" type="text" placeholder="请输入姓名" />
                        </label>
                        <p class="error_tips" id="dr_name"></p>
                        <label>
                            <span class="desc">手机号<font color="red">[必填]</font></span>
                            <input name="dr_mobile" type="number" placeholder="请输入手机号" />
                        </label>
                        <p class="error_tips" id="dr_mobile"></p>
                        <label>
                            <span class="desc">验证码</span>
                            <input style="width:3.6rem;" name="ver_code" type="text" placeholder="请输入验证码" />
                            <button class="verification" id="verification" type="button" onclick="obtain_vercode()">获取验证码</button>
                        </label>
                        <p class="error_tips" id="ver_code_err"></p>
                    </div>
                </div>

                <input type="hidden" id="province_" name="province">
                <input type="hidden" id="city_" name="city">
                <input type="hidden" id="agreement_val" name="agreement_val" value="0">
            </form>

            <div class="btn">
                <button onclick="subForm()">提交审核</button>
            </div>

            <div class="checkbox">
                <input id="agreement" type="checkbox" checked />免费成为健康通会员
                <p>
                    阅读并接受
                    <a target="view_window" href="http://www.gooddr.com/agreement.html">《用户协议》</a>及
                    <a target="view_window" href="http://www.gooddr.com/privacy.html">《隐私权保护声明》</a>
                </p>
            </div>

        </section>
    </div>
    <script>
        (function(doc, win) {
            var docEl = doc.documentElement,
                resizeEvt =
                "onorientationchange" in window ? "onorientationchange" : "resize",
                recalc = function() {
                    var clientWidth = docEl.clientWidth;
                    if (!clientWidth)
                        return;
                    if (clientWidth >= 750) {
                        docEl.style.fontSize = "30px";
                    } else {
                        docEl.style.fontSize = clientWidth / 15 + "px";
                    }
                };
            if (!doc.addEventListener)
                return;
            win.addEventListener(resizeEvt, recalc, false);
            doc.addEventListener("DOMContentLoaded", recalc, false);
        })(document, window);
    </script>

    <script src="/theme/<?= TEMPLATE_DIR ?>/js/jquery-3.1.1.min.js"></script>
    <script type="text/javascript">
        // 下拉框的选中效果
        $("#subForm").on("change", "select", function() {
            $(this).parent().children("span").css("color", "#000")
            var text = $(this).children("select option:selected").text()
            $(this).parent().children("span").text(text)
        })

        //查询城市
        function sel_city(obj, is_select = "") {
            $.post('/sxo/sel_city', {
                province_id: obj,
                code: "<?php echo $code; ?>"
            }, function(c) {
                $("#city").empty();
                var data = c.rs_msg;
                var html = '<span>请选择</span> <select class="form-control" name="city_code" id="city_se" onchange="javascript:sel_district(this.value);"><option value="0">城市</option>';
                for (var i = 0; i < data.length; i++) {
                    html += '<option value="' + data[i]['sys_dictionary_id'] + '" ' + (is_select != '' && is_select == data[i]['val_translate'] ? "selected" : "") + ' >' + data[i]['val_translate'] + '</option>';
                }
                html += '</select>';
                $('#city').append(html);
                $('#province_').val($('#province_se option:selected').text());

            }, 'json');
        }
        //转加城市名称
        function sel_district(obj, is_select = "") {
            $('#city_').val($('#city_se option:selected').text());
        }

        $(document).ready(function() {
            $("input[name='license_no']").blur(function() {
                check_license_no();
            });
            $("input[name='certificate_no']").blur(function() {
                check_license_no();
            });
            $("input[name='credentials_no']").blur(function() {
                check_credentials_no();
            });
            $("select[name='province_code']").blur(function() {
                check_province_code();
            });
            $("body").on('blur', "select[name='city_code']", function() {
                check_city_code();
            });
            $("input[name='unit_name']").blur(function() {
                check_unit_name();
            });
            $("input[name='dr_department']").blur(function() {
                check_dr_department();
            });
            $("input[name='dr_name']").blur(function() {
                check_dr_name();
            });
            $("input[name='dr_mobile']").blur(function() {
                check_dr_mobile();
            });
            $("input[name='ver_code']").blur(function() {
                check_ver_code();
            });
            $("input[name='is_internet_make']").click(function() {
                if ($(this).val() == 1) {
                    $('#internet_div').show();
                } else {
                    $('#internet_div').hide();
                    $('#internet_platform').text('');
                }
            });
            $("input[name='internet_platform']").blur(function() {
                check_is_internet_make();
            });
        });

        function check_license_no() {
            var license_no = $("input[name='license_no']").val();
            var certificate_no = $("input[name='certificate_no']").val();
            if (license_no == '' && certificate_no == '') {
                $('#certificate_no').text('请输入医师执业证、乡村医师证其中一项！');
                return false;
            }

            if (license_no != '') {
                $.post('/sxo/check_license_credentials', {
                        license_no: license_no,
                        code: "<?php echo $code; ?>"
                    },
                    function(c) {
                        if (c.rs_code == 'error') {
                            if (c.rs_backurl == 'license_credentials') {
                                $('#credentials_no').text(c.rs_msg);
                            } else {
                                $('#' + c.rs_backurl + '').text(c.rs_msg);
                                $('#' + c.rs_backurl + '').append("或联系<a href='/sxo/commissioner?code=error&codes=<?php echo $code; ?>'>健康通专员</a>");
                            }
                            return false;
                        } else if (c.rs_code == 'error_exist_license') {
                            $('#license_no').text(c.rs_msg);
                            $('#license_no').append("或联系<a href='/sxo/commissioner?code=" + c.rs_backurl + "'>健康通专员</a>");
                            return false;
                        }
                    }, 'json');
            }

            if (certificate_no != '') {
                $.post('/sxo/check_certificate_no', {
                        certificate_no: certificate_no,
                        code: "<?php echo $code; ?>"
                    },
                    function(c) {
                        if (c.rs_code == 'error') {
                            if (c.rs_backurl == 'certificate_no') {
                                $('#certificate_no').text(c.rs_msg);
                            }
                        } else if (c.rs_code == 'error_exist_certificate') {
                            $('#certificate_no').text(c.rs_msg);
                            $('#certificate_no').append("或联系<a href='/sxo/commissioner?code=" + c.rs_backurl + "'>健康通专员</a>");
                            return false;
                        }
                    }, 'json');
            }
            $('#license_no').text('');
            $('#certificate_no').text('');
            return true;
        }

        function check_credentials_no() {
            var credentials_no = $("input[name='credentials_no']").val();
            //                if (credentials_no == '') {
            //                    $('#credentials_no').text('请输入医师资格证号！');
            //                    return false;
            //                } else 

            if (credentials_no != '') {
                $.post('/sxo/check_license_credentials', {
                        credentials_no: credentials_no,
                        code: "<?php echo $code; ?>"
                    },
                    function(c) {
                        if (c.rs_code == 'error') {
                            if (c.rs_backurl == 'license_credentials') {
                                $('#credentials_no').text(c.rs_msg);
                            } else {
                                $('#' + c.rs_backurl + '').text(c.rs_msg);
                                $('#' + c.rs_backurl + '').append("或联系<a href='/sxo/commissioner?code=error&codes=<?php echo $code; ?>'>健康通专员</a>");
                            }
                            return false;
                        } else if (c.rs_code == 'error_exist_credentials') {
                            $('#credentials_no').text(c.rs_msg);
                            $('#credentials_no').append("或联系<a href='/sxo/commissioner?code=" + c.rs_backurl + "'>健康通专员</a>");
                            return false;
                        }
                    }, 'json');
            }
            $('#credentials_no').text('');
            return true;
        }

        function check_province_code() {
            var province_code = $("select[name='province_code']").val();
            if (province_code == '0') {
                $('#province_code').text('请选择省份！');
                return false;
            }
            $('#province_code').text('');
            return true;
        }

        function check_city_code() {
            var city_code = $("select[name='city_code']").val();
            if (city_code == '0') {
                $('#city_code').text('请选择城市！');
                return false;
            }
            $('#city_code').text('');
            return true;
        }

        function check_unit_name() {
            var unit_name = $("input[name='unit_name']").val();
            if (unit_name == '') {
                $('#unit_name').text('请输入医院名称！');
                return false;
            } else if (/^[a-zA-Z0-9\u4E00-\u9FA5]{2,50}$/.test(unit_name) === false) {
                $('#unit_name').text('请输入2-50位(包含：中英文数字)的医院名称！');
                return false;
            }
            $('#unit_name').text('');
            return true;
        }

        function check_dr_department() {
            var dr_department = $("input[name='dr_department']").val();
            if (dr_department == '') {
                $('#dr_department').text('请输入所在科室！');
                return false;
            } else if (/^[a-zA-Z0-9\u4E00-\u9FA5]{2,20}$/.test(dr_department) === false) {
                $('#dr_department').text('请输入2-20位(包含：中英文数字)的科室名称！');
                return false;
            }
            $('#dr_department').text('');
            return true;
        }

        function check_dr_name() {
            var dr_name = $("input[name='dr_name']").val();
            if (dr_name == '') {
                $('#dr_name').text('请输入姓名！');
                return false;
            } else if (/^[\u4E00-\u9FA5]{2,10}$/.test(dr_name) === false) {
                $('#dr_name').text('请输入2-10位的中文名称！');
                return false;
            }
            $('#dr_name').text('');
            return true;
        }

        function check_dr_mobile() {
            var dr_mobile = $("input[name='dr_mobile']").val();
            if (dr_mobile == '') {
                $('#dr_mobile').text('请输入手机号！');
                return false;
            } else if (/^1(3|4|5|6|7|8|9)\d{9}$/.test(dr_mobile) === false) {
                $('#dr_mobile').text('请输入正确手机号格式！');
                return false;
            }
            $('#dr_mobile').text('');
            return true;
        }

        function check_ver_code() {
            var ver_code = $("input[name='ver_code']").val();
            if (ver_code == '') {
                $('#ver_code_err').text('请输入验证码！');
                return false;
            } else if (/^\d{5}$/.test(ver_code) === false) {
                $('#ver_code_err').text('请输入5位数字验证码！');
                return false;
            }
            $('#ver_code_err').text('');
            return true;
        }

        function check_is_internet_make() {
            var internet_make = $("input[name='is_internet_make']:checked").val();
            var internet_platform = $("input[name='internet_platform']").val()
            if (internet_make == '1' && internet_platform == '') {
                $('#internet_platform').text('请输入互联网平台名称！');
                return false;
            } else if (internet_make == '1' && /^[a-zA-Z0-9\u4E00-\u9FA5]{2,50}$/.test(internet_platform) === false) {
                $('#internet_platform').text('请输入2-50位(包含：中英文数字)的互联网平台！');
                return false;
            }
            $('#internet_platform').text('');
            return true;
        }

        function check_make(type) {

            var i = 0;
            var checkLicenseNo = check_license_no();
            if (checkLicenseNo === false) {
                i += 1;
            }

            var checkCredentialsNo = check_credentials_no();
            if (checkCredentialsNo === false) {
                i += 1;
            }

            var checkProvinceCode = check_province_code();
            if (checkProvinceCode === false) {
                i += 1;
            }

            var checkCityCode = check_city_code();
            if (checkCityCode === false) {
                i += 1;
            }

            var checkUnitName = check_unit_name();
            if (checkUnitName === false) {
                i += 1;
            }

            var checkDrDepartment = check_dr_department();
            if (checkDrDepartment === false) {
                i += 1;
            }

            var checkDrName = check_dr_name();
            if (checkDrName === false) {
                i += 1;
            }

            var checkDrMobile = check_dr_mobile();
            if (checkDrMobile === false) {
                i += 1;
            }

            var checkIsInternetMake = check_is_internet_make();
            if (checkIsInternetMake === false) {
                i += 1;
            }

            if (type == 1) {
                var checkVerCode = check_ver_code();
                if (checkVerCode === false) {
                    i += 1;
                }
            }
            if (i != '0') {
                return false;
            }
            return true;
        }

        function subForm() {
            var check = check_make(1);
            if (check) {
                //检测是否选中协议
                var check_agreement = $('#agreement').is(':checked');
                if (check_agreement === false) {
                    $('#ver_code_err').text('请勾选免费成为健康通会员！');
                    return false;
                }
                $('#agreement_val').val(check_agreement);

                var form = document.querySelector("#subForm");
                var formData = new FormData(form);
                formData.append("code", "<?php echo $code; ?>");
                $.ajax({
                    url: '/sxo/save_invite',
                    type: 'POST',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(data) {
                        if (data.rs_code == 'success') {
                            location.href = data.rs_backurl;
                        } else if (data.rs_code == 'success_special') {
                            $('#ver_code_err').text('您已成功提交报名信息，请等待审核！');
                        } else if (data.rs_code == 'error_vercode') {
                            $('#ver_code_err').text(data.rs_msg);
                            $('#ver_code_err').append("或联系<a href='/sxo/commissioner?code=" + data.rs_backurl + "'>健康通专员</a>");
                        } else if (data.rs_code == 'error_license_no' || data.rs_code == 'error_credentials_no') {
                            $('#' + data.rs_backurl + '').text(data.rs_msg);
                            $('#' + data.rs_backurl + '').append("或联系<a href='/sxo/commissioner?code=error&codes=<?php echo $code; ?>'>健康通专员</a>");
                        } else {
                            if (data.rs_backurl == 'license_credentials') {
                                $('#credentials_no').text(data.rs_msg);
                            } else {
                                $('#' + data.rs_backurl + '').text(data.rs_msg);
                            }
                        }
                    }
                });
            }
        }

        //执业类别
        function sel_job_title(obj, job_title, positon) {
            var practice_sort = obj;
            if (practice_sort != '') {
                $.post("/sxo/sel_job_title", {
                    practice_sort: practice_sort,
                    code: "<?php echo $code; ?>"
                }, function(j) {
                    $("#job_title").empty();
                    $("#position").empty();
                    var data = j.rs_msg;
                    var j_html = '<span>请选择</span><select class="form-control" name="dr_job_title" id="job_title_se" onchange="javascript:text_job_title();"><option value="">请选择</option>';
                    for (var j = 0; j < data.length; j++) {
                        if (data[j]['big_class_id'] == 6) {
                            var job_title_selected = "";
                            if (job_title != 0 && data[j]['id'] == job_title) {
                                job_title_selected = "selected";
                            }
                            j_html += '<option value="' + data[j]['id'] + '" ' + job_title_selected + '>' + data[j]['val'] + '</option>';
                        }
                    }
                    j_html += '</select>';
                    $('#job_title').append(j_html);

                    var p_html = '<span>请选择</span><select class="form-control" name="dr_position" id="position_se"><option value="">请选择</option>';
                    for (var p = 0; p < data.length; p++) {
                        if (data[p]['big_class_id'] == 5) {
                            var positon_selected = "";
                            if (positon != 0 && data[p]['id'] == positon) {
                                positon_selected = "selected";
                            }
                            p_html += '<option value="' + data[p]['id'] + '" ' + positon_selected + '>' + data[p]['val'] + '</option>';
                        }
                    }
                    p_html += '</select>';
                    $('#position').append(p_html);
                }, 'json');
            }
        }

        function text_job_title() {
            $('#dr_job_title').val($('#job_title_se option:selected').text());
        }

        function obtain_vercode() {
            var check = check_make(2);
            if (check) {
                var mobile = $("input[name='dr_mobile']").val();
                if (mobile == '') {
                    $('#ver_code_err').text('手机号码不能为空！');
                    return false;
                }
                $.post('/sxo/obtain_vercode', {
                    verify_mobile: mobile,
                    code: "<?php echo $code; ?>"
                }, function(c) {
                    if (c.rs_code == 'success') {
                        $('#verification').attr('disabled', true);
                        var time = 60;
                        getRandomCode();
                        //倒计时
                        function getRandomCode() {
                            if (time === 0) {
                                time = 60;
                                $("#verification").text("重新发送");
                                $('#verification').attr('disabled', false);
                                return;
                            } else {
                                time--;
                                $("#verification").text(time + "s");
                            }
                            setTimeout(function() {
                                getRandomCode();
                            }, 1000);
                        }
                        $('#ver_code_err').text('短信发送成功，请查收！');
                    } else if (c.rs_code == 'error_vercode') {
                        $('#ver_code_err').text(c.rs_msg);
                        $('#ver_code_err').append("或联系<a href='/sxo/commissioner?code=" + c.rs_backurl + "'>健康通专员</a>");
                    } else {
                        $('#ver_code_err').text(c.rs_msg);
                    }
                }, 'json');
            }
        }
    </script>
</body>

</html>