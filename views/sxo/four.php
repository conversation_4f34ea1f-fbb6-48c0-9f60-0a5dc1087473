<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>task3</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }

            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-color: #5e93fb;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                padding-top: 1.8rem;
                text-align: center;
                color: #fff;
                font-size: 1.4rem;
            }

            .container header h4 {
                margin-bottom: 0.2rem;
                font-weight: normal;
            }

            section {
                padding: 1rem 1.5rem 1rem 2rem;
                font-size: 0.8rem;
                color: #fff;
            }

            section .tips {
                text-align: left;
            }
            section .tips p {
                text-align: justify;
            }
            section .tips .tip {
                text-align: center;
                margin-bottom: 0.4rem;
                position: relative;
            }
            section .tips .tip .tip_img {
                width: 1rem;
                position: absolute;
                left: -1.5rem;
                top: 0;
            }

            section .tips .qrcode {
                text-align: center;
            }
            section .tips .qrcode img {
                width: 4rem;
                margin-top: 1rem;
            }

            section .btn {
                text-align: center;
                padding-top: 3rem;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>任务三</h4>
            </header>
            <section>
                <div class="tips">
                    <div class="tip">
                        <p>
                            我们再邀请您加入赛小欧互联网心脑糖研习社的微信群，群中的学院小助手能为您查找权威指南共识、病例解析、诊疗经验和“互联网医院平台简介与最新资讯”，定期精彩分享互动，不容错过！请您扫码加入。
                        </p>
                    </div>

                    <div class="qrcode">
                        <?php if (!empty($code_img)) { ?>
                            <img src="<?php echo $code_img.'?t='.time(); ?>" alt="" />
                        <?php } ?>
                    </div>
                </div>
                <div class="btn">
                    <button>完 成</button>
                </div>
            </section>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);
        </script>
    </body>
</html>
