<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>task1</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }

            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-color: #5e93fb;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                padding-top: 1.8rem;
                text-align: center;
                color: #fff;
                font-size: 1.4rem;
            }

            .container header h4 {
                margin-bottom: 0.2rem;
                font-weight: normal;
            }

            section {
                padding: 1rem 1.5rem 1rem 2rem;
                font-size: 0.8rem;
                color: #fff;
            }

            section .tips {
                text-align: left;
            }
            section .tips p {
                text-align: justify;
            }
            section .tips .tip {
                text-align: center;
                margin-bottom: 0.4rem;
                position: relative;
            }
            section .tips .tip .tip_img {
                width: 1rem;
                position: absolute;
                left: -1.5rem;
                top: 0;
            }

            section .tips .tip .qrcode {
                text-align: justify;
            }
            section .tips .tip .qrcode img {
                width: 4rem;
                margin-top: 0.2rem;
            }

            section .btn {
                text-align: center;
                padding-top: 1rem;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>任务一</h4>
            </header>

            <section>
                <div class="tips">
                    <div class="tip">
                        <img class="tip_img" src="/theme/sxo/img/tip.png" alt="" />
                        <p>
                            老师，您好！赛小欧是赛诺菲公司的互联网医院活动，参与后可第一时间获得权威的专业医疗信息、互联网医疗资讯、专业领域诊疗指南等，知识无价先到先得
                        </p>
                    </div>

                    <div class="tip">
                        <img class="tip_img" src="/theme/sxo/img/tip.png" alt="" />
                        <p>请扫描以下二维码加入赛小欧互联网医院活动。</p>
                        <div class="qrcode">
                            <?php if (!empty($code_img)) { ?>
                                <img style="margin-right: 2rem" src="<?php echo $code_img; ?>" alt="" />
                            <?php } ?>
                            <img src="/theme/sxo/img/demo1.png" alt="" />
                        </div>
                    </div>

                    <div class="tip">
                        <img class="tip_img" src="/theme/sxo/img/tip_red.png" alt="" />
                        <p>
                            <font color="#fdab40">完成注册后请将个人中心截图保存。 </font>
                        </p>
                    </div>
                </div>
                <div class="btn">
                    <button onclick="operation()">下一步</button>
                </div>
            </section>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);

            function operation() {
                location.href = "/sxo/third?code=<?php echo $sid; ?>";
            }
        </script>
    </body>
</html>
