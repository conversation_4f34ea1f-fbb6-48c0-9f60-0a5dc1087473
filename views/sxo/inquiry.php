<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"/>
        <title>健康通-报名查询</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }
            input[type="text"] {
                -webkit-appearance: none;
            }
            input::-webkit-input-placeholder {
                color: #c9caca;
                font-size: 0.64rem;
            }
            input::-moz-placeholder {
                color: #c9caca;
                font-size: 0.64rem;
            }
            input:-moz-placeholder {
                color: #c9caca;
                font-size: 0.64rem;
            }
            input:-ms-input-placeholder {
                color: #c9caca;
                font-size: 0.64rem;
            }
            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-color: #5e93fb;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                padding-top: 3rem;
                text-align: center;
                color: #fff;
                font-size: 1.4rem;
            }

            .container header h4 {
                margin-bottom: 0.2rem;
                font-weight: normal;
            }
            section {
                text-align: center;
                padding: 1rem 1.2rem;
            }

            section p {
                padding: 0.3rem 0;
                position: relative;
            }
            section p input {
                width: 100%;
                height: 2.2rem;
                border: 1px solid #c9caca;
                border-radius: 0.2rem;
                padding: 0.3rem;
                outline: none;
                font-size: 0.64rem;
            }
            section .code input {
                width: 68%;
                position: absolute;
                left: 0;
            }
            section .code button {
                position: absolute;
                right: 0;
                width: 30%;
                height: 2.2rem;
                font-size: 0.64rem;
                background: #ffbb51;
                color: #fff;
                border-radius: 0.2rem;
                outline: none;
                border: none;
                cursor: pointer;
            }
            section .tip {
                padding-top: 2.2rem;
                font-size: 0.64rem;
                color: #fdab40;
            }

            section .btn {
                text-align: center;
                padding-top: 3rem;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>健康通</h4>
                <h4>互联网医院小调查</h4>
            </header>

            <section>
                <p class="phone">
                    <input name='dr_mobile' type="number" placeholder="请输入报名手机号" autocomplete="off" />
                </p>
                <p class="code">
                    <input type="number" name="ver_code" placeholder="请输入验证码" autocomplete="off" />
                    <button id="verification" onclick="inquiry_vercode()">获取验证码</button>
                </p>
                <div class="tip" id="ver_code_err"></div>

                <div class="btn">
                    <button onclick="inquiry_res()">查 询</button>
                </div>
            </section>
        </div>

        <script src="/theme/<?= TEMPLATE_DIR ?>/js/jquery-3.1.1.min.js"></script>
        <script>
                        (function (doc, win) {
                            var docEl = doc.documentElement,
                                    resizeEvt =
                                    "onorientationchange" in window ? "onorientationchange" : "resize",
                                    recalc = function () {
                                        var clientWidth = docEl.clientWidth;
                                        if (!clientWidth)
                                            return;
                                        if (clientWidth >= 750) {
                                            docEl.style.fontSize = "30px";
                                        } else {
                                            docEl.style.fontSize = clientWidth / 15 + "px";
                                        }
                                    };
                            if (!doc.addEventListener)
                                return;
                            win.addEventListener(resizeEvt, recalc, false);
                            doc.addEventListener("DOMContentLoaded", recalc, false);
                        })(document, window);

                        /**
                         * 查询结果
                         * @returns {undefined}
                         */
                        function inquiry_res() {
                            //正确手机格式验证
                            var checkDrMobile = check_dr_mobile();
                            if (checkDrMobile === false) {
                                return false;
                            }
                            
                            var checkVerCode = check_ver_code();
                            if (checkVerCode === false) {
                                return false;
                            }
                            
                            var mobile = $("input[name='dr_mobile']").val();
                            var ver_code = $("input[name='ver_code']").val();
                            $.post('/sxo/inquiry_res', {dr_mobile: mobile, ver_code: ver_code,code: "<?php echo $code; ?>"},
                            function (c) {
                                if (c.rs_code == 'success') {
                                    location.href=c.rs_backurl;
                                }else if (c.rs_code == 'errors') {
                                    $('#ver_code_err').text(c.rs_msg);
                                    $('#ver_code_err').append("联系<a href='" + c.rs_backurl + "'>健康通专员</a>");
                                }else{
                                    $('#ver_code_err').text(c.rs_msg);
                                }
                            }, 'json');
                        }

                        $(document).ready(function () {
                            $("input[name='dr_mobile']").blur(function () {
                                check_dr_mobile();
                            });
                            
                            $("input[name='ver_code']").blur(function () {
                                check_ver_code();
                            });
                        });
                        
                        function check_ver_code() {
                            var ver_code = $("input[name='ver_code']").val();
                            if (ver_code == '') {
                                $('#ver_code_err').text('请输入验证码！');
                                return false;
                            } else if (/^\d{5}$/.test(ver_code) === false) {
                                $('#ver_code_err').text('请输入5位数字验证码！');
                                return false;
                            }
                            $('#ver_code_err').text('');
                            return true;
                        }

                        function check_dr_mobile() {
                            var dr_mobile = $("input[name='dr_mobile']").val();
                            if (dr_mobile == '') {
                                $('#ver_code_err').text('请输入手机号！');
                                return false;
                            } else if (/^1(3|4|5|6|7|8|9)\d{9}$/.test(dr_mobile) === false) {
                                $('#ver_code_err').text('请输入正确手机号格式！');
                                return false;
                            }
                            $('#ver_code_err').text('');
                            return true;
                        }

                        /**
                         * 查询结果认证码
                         * @returns {undefined}
                         */
                        function inquiry_vercode() {
                            //正确手机格式验证
                            var checkDrMobile = check_dr_mobile();
                            if (checkDrMobile === false) {
                                return false;
                            }
                            
                            var mobile = $("input[name='dr_mobile']").val();
                            $.post('/sxo/inquiry_vercode', {verify_mobile: mobile, code: "<?php echo $code; ?>"}, function (c) {
                                if (c.rs_code == 'success') {
                                    $('#verification').attr('disabled', true);
                                    var time = 60;
                                    getRandomCode();
                                    //倒计时
                                    function getRandomCode() {
                                        if (time === 0) {
                                            time = 60;
                                            $("#verification").text("重新发送");
                                            $('#verification').attr('disabled', false);
                                            return;
                                        } else {
                                            time--;
                                            $("#verification").text(time + "s");
                                        }
                                        setTimeout(function () {
                                            getRandomCode();
                                        }, 1000);
                                    }
                                    $('#ver_code_err').text('短信发送成功，请查收！');
                                } else if (c.rs_code == 'error_vercode') {
                                    $('#ver_code_err').text(c.rs_msg);
                                } else if (c.rs_code == 'error_max_res') {
                                    $('#ver_code_err').text(c.rs_msg);
                                    $('#ver_code_err').append("或联系<a href='/sxo/commissioner?code=" + c.rs_backurl + "'>健康通专员</a>");
                                } else {
                                    $('#ver_code_err').text(c.rs_msg);
                                }
                            }, 'json');
                        }
        </script>
    </body>
</html>
