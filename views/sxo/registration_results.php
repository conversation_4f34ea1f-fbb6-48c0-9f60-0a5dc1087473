<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>result</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }

            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-color: #5e93fb;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                padding-top: 3rem;
                text-align: center;
                color: #fff;
                font-size: 1.4rem;
            }

            .container header h4 {
                margin-bottom: 0.2rem;
                font-weight: normal;
            }

            section {
                font-size: 0.8rem;
                color: #fff;
                padding: 1rem 1.2rem;
            }

            section div {
                margin-bottom: 0.6rem;
            }

            section .radio p label {
                display: inline-block;
                width: 4rem;
                height: 0.8rem;
                line-height: 0.8rem;
                cursor: pointer;
            }
            section .radio p input {
                border: 1px solid #d8d8d8;
                width: 0.8rem;
                height: 0.8rem;
                vertical-align: middle;
                margin-right: 0.2rem;
            }
            section .btn {
                text-align: center;
                padding-top: 1rem;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>恭喜您</h4>
                <h4>已通过审核</h4>
            </header>

            <section>
                <div class="title">您之前已完成一部分小调查，请确认您的答案。</div>
                <div class="radio">
                    <p>Q1. 请问您平时接诊过程中是否接诊心脑病及内分泌相关疾病的患者？</p>
                    <p>
                        <label><input name="radio" type="radio" <?php
                            if (isset($info['is_dep_patient']) && ($info['is_dep_patient'] == 1)) {
                                echo 'checked';
                            }
                            ?> />是</label>
                        <label><input name="radio" type="radio" <?php
                            if (isset($info['is_dep_patient']) && ($info['is_dep_patient'] != 1)) {
                                echo 'checked';
                            }
                            ?> />否</label>
                    </p>
                </div>

                <div class="other">
                    Q2. 您已加入了<span class="name">
                        <?php echo isset($info['internet_platform']) ? $info['internet_platform'] : ''; ?>
                    </span>互联网医院平台。
                </div>

                <div class="btn">
                    <button onclick="operation()">下一步</button>
                </div>
            </section>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);

            function operation() {
                location.href = "/sxo/sxo_tips?code=<?php echo $code; ?>&infoid=<?php echo $infoid;?>";
            }
        </script>
    </body>
</html>
