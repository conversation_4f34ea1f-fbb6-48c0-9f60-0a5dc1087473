<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>index</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }
            input {
                -webkit-appearance: none;
            }
            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                color: #fff;
                text-align: center;
                padding-top: 3.5rem;
                background-size: 100% auto;
            }
            .container header h4 {
                font-weight: 400;
                font-size: 1.2rem;
            }
            .container header h4:last-child {
                text-shadow: #fff 1px 0 0, #fff 0 1px 0, #fff -1px 0 0, #fff 0 -1px 0;
                color: #ffc156;
                font-size: 1.4rem;
            }
            section {
                width: 13.8rem;
                background: #2e3af5;
                margin: 0 auto;
                margin-top: 1rem;
                padding: 0.56rem 0.48rem;
            }
            section .content {
                width: 12.8rem;
                margin: 0 auto;
                margin-top: 0.56rem;
                background-color: #fff;
                font-size: 0.6rem;
                color: #5b5b5b;
                line-height: 1.2rem;
                padding: 1.08rem 0.88rem;
            }

            section .btn {
                text-align: center;
                padding-top: 1rem;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>健康通互联网医院小调查</h4>
                <h4>项目背景</h4>
            </header>
            <section>
                <div class="content">
                    <p>
                        本次活动是由健康通发起的互联网医院活动。只需要参与医师动动手指关注一个微信公众号以及加好友就可以获得健康通的奖励。同时，通过本次活动还可以长期获得心脑病及内分泌领域的医学资讯和临床文献。目前，心血管内科、内分泌、神经内科、风湿科、大内科（含消化内科、老年科等拥有心脑病及内分泌患者）、全科的科室医生可参与。
                    </p>

                    <div class="btn">
                        <button onclick="invite()">下一步</button>
                    </div>
                </div>
            </section>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);
            
            function invite(){
                location.href="/sxo/invites/<?php echo $code;?>";
            }
        </script>
    </body>
</html>
