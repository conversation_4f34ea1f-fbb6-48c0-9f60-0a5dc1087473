<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>健康通-微信专员</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }
            
            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-color: #5e93fb;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                color: #fff;
                text-align: center;
                padding-top: 0.3rem;
                background-size: 100% auto;
            }
            .container header h4 {
                font-weight: 400;
                font-size: 1.2rem;
            }
            .container header h4:last-child {
                text-shadow: #fff 1px 0 0, #fff 0 1px 0, #fff -1px 0 0, #fff 0 -1px 0;
                color: #ffc156;
                font-size: 1.4rem;
            }
            section {
                text-align: center;
                padding-top: 1rem;
            }
            section .msg {
                font-size: 0.56rem;
                width: 14.1rem;
                color: #ffffff;
                margin: 0 auto;
                text-align: left;
                margin-bottom: 1rem;
            }
            section img {
                width: 9.62rem;
            }
            section p {
                font-size: 0.56rem;
                color: #fffcfc;
                font-weight: 500;
                margin-top: 0.4rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>健康通互联网医院小调查</h4>
                <h4>预报名</h4>
            </header>

            <section>
                <div class="msg">
                    <?php
                    if (!empty($msg)) {
                        echo $msg;
                    } else {
                        ?>
                        您已成功提交报名信息，请添加健康通专员微信，了解报名审核进度。
                    <?php } ?>
                </div>
                <?php if (!empty($code)) { ?>
                    <img src="<?php echo $code; ?>" alt="" />
                    <p>扫一扫上面的图案，加我微信</p>
                <?php } ?>
            </section>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);
        </script>
    </body>
</html>
