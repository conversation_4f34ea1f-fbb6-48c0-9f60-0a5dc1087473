<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>task2</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }

            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-color: #5e93fb;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }
            .container header {
                padding-top: 1.8rem;
                text-align: center;
                color: #fff;
                font-size: 1.4rem;
            }

            .container header h4 {
                margin-bottom: 0.2rem;
                font-weight: normal;
            }

            section {
                padding: 1rem 1.5rem 1rem 2rem;
                font-size: 0.8rem;
                color: #fff;
            }

            section .tips {
                text-align: left;
            }
            section .tips p {
                text-align: justify;
            }
            section .tips .tip {
                text-align: center;
                margin-bottom: 0.4rem;
                position: relative;
            }
            section .tips .tip .tip_img {
                width: 1rem;
                position: absolute;
                left: -1.5rem;
                top: 0;
            }

            section .tips .tip .qrcode {
                text-align: justify;
            }
            section .tips .tip .qrcode img {
                width: 4rem;
                margin-top: 0.2rem;
            }

            section .btn {
                text-align: center;
                padding-top: 3rem;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>任务二</h4>
            </header>

            <section>
                <div class="tips">
                    <div class="tip">
                        <img class="tip_img" src="/theme/sxo/img/tip.png" alt="" />
                        <p>
                            第二步请扫码添加赛小欧沟通专员企业微信为好友。添加好友之后，赛小欧将为您推送互联网医院相关专业信息及行业资讯。
                        </p>
                    </div>

                    <div class="tip">
                        <img class="tip_img" src="/theme/sxo/img/tip.png" alt="" />
                        <p>麻烦您添加后<font color="#fdab40">主动发送任意表情并截图聊天对话框</font</p>
                        <div class="qrcode">
                            <?php if (!empty($code_img)) { ?>
                                <img style="margin-right: 2rem" src="<?php echo $code_img; ?>" alt="" />
                            <?php } ?>
                            <img src="/theme/sxo/img/demo2.png" alt="" />
                        </div>
                    </div>
                </div>
                <div class="btn">
                    <button onclick="operation()">下一步</button>
                </div>
            </section>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);
            
            function operation() {
                location.href = "/sxo/four?code=<?php echo $sid; ?>";
            }
        </script>
    </body>
</html>
