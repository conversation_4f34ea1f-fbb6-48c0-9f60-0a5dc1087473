<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
            />
        <title>健康通-邀请</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body,
            html {
                height: 100%;
            }
            .container {
                width: 15rem;
                min-height: 100%;
                margin: 0 auto;
                background-image: url("/theme/sxo/img/bg.png");
                background-repeat: no-repeat;
                background-size: 100%;
            }

            .container header {
                padding-top: 3rem;
                text-align: center;
                color: #fff;
                font-size: 1.4rem;
                margin-bottom: 2rem;
            }

            .container header h4 {
                margin-bottom: 0.2rem;
                font-weight: normal;
            }
            section .btn {
                text-align: center;
            }

            section .btn button {
                outline: none;
                cursor: pointer;
                height: 1.6rem;
                line-height: 1.6rem;
                width: 9.36rem;
                color: #fff;
                border-radius: 1rem;
                border: none;
                font-size: 1rem;
                background: linear-gradient(to right, #ff9333, #ffc156);
                margin-bottom: 2rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <h4>健康通</h4>
                <h4>互联网医院小调查</h4>
            </header>
            <section>
                <div class="btn">
                    <button class="sign_up" onclick="sign_up()">我要报名</button>
                    <button class="progress" onclick="progress()">报名进度查询</button>
                </div>
            </section>
        </div>
        <script>
            (function (doc, win) {
                var docEl = doc.documentElement,
                        resizeEvt =
                        "onorientationchange" in window ? "onorientationchange" : "resize",
                        recalc = function () {
                            var clientWidth = docEl.clientWidth;
                            if (!clientWidth)
                                return;
                            if (clientWidth >= 750) {
                                docEl.style.fontSize = "30px";
                            } else {
                                docEl.style.fontSize = clientWidth / 15 + "px";
                            }
                        };
                if (!doc.addEventListener)
                    return;
                win.addEventListener(resizeEvt, recalc, false);
                doc.addEventListener("DOMContentLoaded", recalc, false);
            })(document, window);
            
            function sign_up(){
                location.href="/sxo/prointroduce/<?php echo $code;?>";
            }
            
            function progress(){
                location.href="/sxo/inquiry/<?php echo $code;?>";
            }
        </script>
    </body>
</html>
