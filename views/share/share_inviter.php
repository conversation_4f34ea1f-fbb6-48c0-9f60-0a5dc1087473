<!DOCTYPE>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="加入上医说" />
    <meta name="description" content="加入上医说" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>加入美哒</title>
    <style type="text/css">
        html,body,ul,li,ol,dl,dd,dt,p,h1,h2,h3,h4,h5,h6,form,fieldset,legend,img,input,textarea,section,th,td,hr,button {padding:0; margin:0;}
        body {font-family:"Microsoft Yahei"; font-size:.6rem; color:#333; background-color:#fff;}
        a {text-decoration:none;}
        img {border:none 0; width:100%;}
        ol,ul,li {list-style:none;}
        .meidal {max-width:750px; min-height:700px; margin:0 auto; background-color:#e3404a; position:relative;}
        .meidal p {font-size:.55rem; font-weight:300; color:#fff; text-align:center; padding:10px 0;}
        .head {max-width:720px; margin:0 auto; padding:10px 15px; background-color:#f7f4fb; text-align:left; color:#333; line-height:24px;}
    </style>
    <link rel="shortcut icon" href="<?php echo base_url();?>/theme/images/favicon.ico" type="image/x-icon" />
    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(document.documentElement.getBoundingClientRect().width);
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + 'px';
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
<div class="head"><?php echo $tip;?></div>
<div class="meidal">
    <img src="<?php echo $share_qr_code;?>" alt="" />
    <p>Copyright &copy; <?php echo date("Y");?> Meidal Inc. All Rights Reserved.</p>
</div>
</body>
</html>
