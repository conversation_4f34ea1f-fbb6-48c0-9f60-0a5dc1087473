<!DOCTYPE>
<html lang="zh">
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
<meta name="keywords" content="上医说分享" />
<meta name="description" content="上医说分享" />
<meta name="renderer" content="webkit" />
<meta name="robots" content="all,index,follow" />
<meta content="telephone=no" name="format-detection" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="full-screen" content="yes" />
<meta name="browsermode" content="application" />
<meta name="x5-fullscreen" content="true" />
<meta name="x5-page-mode" content="app" />
<meta name="msapplication-tap-highlight" content="no" />
<title>美哒分享</title>
<link rel="shortcut icon" href="<?php echo base_url();?>/theme/images/favicon.ico" type="image/x-icon" />

<!-- [sui css start] -->
<link rel="stylesheet" href="<?php echo base_url();?>/theme/css/sm.min.css" />
<link rel="stylesheet" href="<?php echo base_url();?>/theme/css/sm-extend.min.css" />
<!-- [sui css end] -->
<style>
	body,dd,dl,fieldset,form,h1,h2,h3,h4,h5,h6,input,legend,ol,p,select,td,textarea,th,ul {padding:0; margin:0;}
	body {/*max-width:750px;*/ margin:0 auto;}
	img {border:0;}
	.title span {font-weight:400;}
	.facebook-card .card-header {padding-top:.75rem; padding-bottom:0;}
	.facebook-avatar img {border-radius:50%; width:40px; height:40px;}
	.item-media img {border-radius:50%; width:40px;}
	.facebook-name span, .item-title span {color:#fd6404; font-weight:400; font-size:.7rem;}
	.facebook-date span {color:#898989; font-weight:400; font-size:.6rem;}
	.item-subtitle span {color:#333; font-weight:400; font-size:.65rem;}
	.card-content {padding:0 .5rem .5rem;}
	.card-content img {width:30%; display:inline-block!important; margin-top:.2rem; margin-right:.3rem; margin-bottom:.3rem; vertical-align:top; border:solid 1px #dedede;}
	.list-block .item-subtitle {white-space:normal;}
	.card-footer {min-height:1.8rem; padding:0 .5rem 0 0; margin:0 .5rem;}
	.card-footer:before {background-color:#fff;}
	.link, .card-footer span {font-weight:300; font-size:.6rem;}
	.commentaries {background-color:#fff; color:#333; margin:.5rem 0 0; padding:.5rem; border-bottom:solid 1px #e5e5e5;}
	.commentaries a {float:right; vertical-align:middle;}
	.commentaries span {vertical-align:bottom;}
</style>

<!-- font rem start -->
<script>
	function px2rem() {
	    var cw = parseInt(document.documentElement.getBoundingClientRect().width);
	    cw = cw > 640 ? 640 : cw;
	    window.rem = cw / 16;
	    document.documentElement.style.fontSize = window.rem + 'px';
	}
	px2rem();
</script>
<!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
	<div class="page-group">
        <div class="page page-current">
        <!-- 你的html代码 -->
		<!-- <header class="bar bar-nav"><h1 class="title"><span>帖子正文</span></h1></header> -->

			<div class="content" style="padding-bottom:.5rem;">
				<!-- 这里是页面内容区 -->
				<div class="card facebook-card" style="margin:0; box-shadow:none;">
				    <div class="card-header no-border">
				      <div class="facebook-avatar">
						  <img src="<?php echo $res_blogs['avatar'];?>" />
					  </div>
				      <div class="facebook-name"><span><?php echo $res_blogs['nickname'];?></span></div>
				      <div class="facebook-date"><span><?php echo $res_blogs['add_time'];?></span></div>
				    </div>

					<div class="card-content-inner"><?php echo $res_blogs['content'];?></div>
				    <div class="card-content">
						<?php foreach ($res_blogs['images'] as $iv) {?>
							<a href="<?php echo USER_IMG_URL_NOTS.$iv;?>" data-lightbox="image-1" data-title="My caption">
								<img src="<?php echo USER_IMG_URL_NOTS.$iv;?>" class="img-sm" data-lightbox="roadtrip" />
							</a>
						<?php } ?>
					</div>

					<!--
                    <div class="card-content-inner" style="padding:.5rem;">
                        <strong>扫码注册成为美哒用户获得100美哒币，赶快加入美的计划吧！</strong>
                    </div>
				    <div class="card-content">
						<img src="<?=$forum['qrcode']?>" />
					</div>
					-->
				  </div>

				<!-- [评论 start] -->
				<div class="content-block-title commentaries">
                    评论<a href="javascript:;" class="link">
                        <span class="icon icon-friends"></span>
                        <span><?php echo count($res_blogs['blogs_criticism'])?></span>
                    </a>
                </div>

				<?php foreach ($res_blogs['blogs_criticism'] as $cv) { ?>
				<div class="card" style="margin:0; box-shadow:none; border-bottom:solid 1px #e5e5e5;">
				  	<div class="card-content">
				      	<div class="list-block media-list">
					        <ul>
					          <li class="item-content" style="padding-left:0;">
					            <div class="item-media" style="margin-top:0;">
					              <img src="<?php echo $cv['avatar'];?>" style="width:44px; height:44px;" />
					            </div>
					            <div class="item-inner" style="margin-left:.4rem; padding-bottom:0;">
					              <div class="item-title-row">
					                <div class="item-title"><span><?php echo $cv['nickname']?></span></div>
					              </div>
					              <div class="item-subtitle">
									  <span><?php echo $cv['content']?></span>
								  </div>
					            </div>
					          </li>
					        </ul>
						</div>
			    	</div>
					<!-- <div class="card-footer no-border" style="background-color:#ededed;">
					  <a href="javascript:;" class="link"><span>Meidal-001</span></a><a href="javascript:;" class="link"><span>共92条</span></a>
					</div> -->
				    <div class="card-footer">
				      <span><?php echo $cv['comment_time'];?></span>
				      <!-- <a href="javascript:;" class="link"><span class="icon icon-friends"></span> <span>92</span></a> -->
				    </div>
			  </div>
			<?php } ?>
			<!-- [评论 end] -->
        </div>
    </div>

	<!-- [sui js start] -->
	<!-- <script type='text/javascript' src='http://g.alicdn.com/sj/lib/zepto/zepto.min.js' charset='utf-8'></script> -->
    <!-- <script type='text/javascript' src='http://g.alicdn.com/msui/sm/0.6.2/js/sm.min.js' charset='utf-8'></script> -->
    <!-- <script type='text/javascript' src='http://g.alicdn.com/msui/sm/0.6.2/js/sm-extend.min.js' charset='utf-8'></script> -->
	<!-- [sui js end] -->
	<link rel="stylesheet" href="<?= base_url()?>/theme/dist/css/lightbox.min.css">
	<script src="<?= base_url()?>/theme/management/js/jquery-3.1.1.min.js"></script>
	<script src="<?= base_url()?>/theme/dist/js/lightbox.min.js"></script>

	<script type="text/javascript">
	    lightbox.option({'positionFromTop':50,'resizeDuration': 100,'wrapAround': true});
	</script>
</body>
</html>
