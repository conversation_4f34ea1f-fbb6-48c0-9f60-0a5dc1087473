<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no" />
<title>健康通-上医说</title>
<script src="/theme/bk/jquery-3.1.1.min.js"></script>
<script src="/theme/bk/uni.webview.1.5.2.js"></script>
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body,
    html {
        height: 100%;
    }

    .container_class {
        background: #2b3a99;
        text-align: center;
        padding: 0 30px;
        max-width: 420px;
        margin: 0 auto;
        height: 100%;
    }

    .container_class_wy {
        background: #b3efe9;
        text-align: center;
        padding: 0 30px;
        max-width: 420px;
        margin: 0 auto;
        height: 100%;
    }

    .logo {
        padding-top: 50px;
    }

    .logo img {
        height: 80px;
        margin: 0 auto;
        margin-bottom: 20px;
    }

    .content {
        padding-top: 10px;
        width: 90%;
        /*height: 260px;*/
        max-height: 360px;
        background: #fff;
        margin: 0 auto;
        text-align: center;
        border-radius: 8px;
    }

    .content img {
        width: 150px;
        margin-top: 15px;
    }

    .content .tips {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 30px;
    }

    /*button {
        outline: none;
        border: none;
        background: #f85691;
        padding: 10px 0;
        width: 90%;
        color: #fff;
        font-size: 24px;
        margin-top: 50px;
        border-radius: 2px;
        cursor: pointer;
    }*/

    .button_class {
        outline: none;
        border: none;
        background: #f85691;
        padding: 10px 0;
        width: 90%;
        color: #fff;
        font-size: 24px;
        margin-top: 50px;
        border-radius: 2px;
        cursor: pointer;
    }

    .button_class_wy {
        outline: none;
        border: none;
        background: #03DBC6;
        padding: 10px 0;
        width: 90%;
        color: #fff;
        font-size: 24px;
        margin-top: 50px;
        border-radius: 2px;
        cursor: pointer;
    }

    .copyright {
        position: fixed;
        font-size: 14px;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        width: 100%;
    }
    a{
        text-decoration-line: none;
    }
</style>
</head>

<body>
    <?php
    $container_class = "container_class";
    $button_class = "button_class";
    $project_sys_id = $this->session->userdata("project_sys_id");
    $sys_id = "";
    if (in_array($project_sys_id, [7,8,9])) {//网医
        $container_class = "container_class_wy";
        $button_class = "button_class_wy";
        $sys_id = "/".$project_sys_id;
    }
    echo "<!--{$project_sys_id}-->";
    ?>
    <div class="<?php echo $container_class;?>">
        <div class="logo">
            <img src="/theme/go/image/jkt_logo_2.png" alt="" />
        </div>

        <div class="content">
            <?php echo $st_msg;?>
        </div>

        <div class="btn">
            <?php if ($st == "p_f") { ?>
                <a href="/project_exchange/modify_payment<?php echo $sys_id;?>?scene=<?php echo $fail_scene; ?>"><button class="<?php echo $button_class;?>">重新设置收款账户</button></a>
            <?php } else { ?>
                <button class="close <?php echo $button_class;?>">退 出</button>
            <?php } ?>
        </div>

        <div class="copyright">
            <div><EMAIL> <span><?php echo date("Y");?></span></div>
            <div>
                <a style="color:#fff;" href="https://beian.miit.gov.cn/" target="_blank">京ICP备13039326号-8</a >
            </div>
        </div>
    </div>

    <script>
        $(function() {
            $(".close").click(function() {
               <?php
                if( $project_sys_id == 7 ){?>//网医（PC）
                window.location.href = "https://www.idr.cn/#/research";
               <?php } else if(in_array($project_sys_id, [8, 9])) {?>//网医（IOS）、网医（安卓）
                if ( window.uni ) {
                    window.uni.navigateBack({ delta: 1 });
                } else {
                    // 原跳转
                    window.location.href = "wangyizhongguo://";
                }
               <?php } else {?>
                    window.location.href = "about:blank";
                    window.close();
                <?php }?>
            })
        })
    </script>
</body>

</html>
