<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0,,user-scalable=no" />
        <title>forbidden</title>
        <style>
            * {
                margin: 0;
                padding: 0;
            }

            .container {
                text-align: center;
                padding: 20px;
                color: #666;
                font-size: 14px;
            }

            .container img {
                width: 40px;
            }

            .container p {
                margin-bottom: 10px;
            }

            #copy_btn {
                font-size: 16px;
                outline: none;
                border: none;
                background: #479de6;
                padding: 5px 20px;
                color: #fff;
                border-radius: 30px;
            }
        </style>
    </head>

    <body>
        <div class="container">
            <img src="/theme/go/image/warn.png" />
            <p>请复制链接在浏览器中打开</p>
            <p>
                <?php echo $uri; ?>
            </p>
            <p><button type="button" data-clipboard-action="copy" id="copy_btn">复 制</button></p>
        </div>
    </body>
    <script src="/theme/js/clipboard.min.js"></script>
    <script>
        var clipboard = new ClipboardJS('#copy_btn', {
            text: function () {
                return "<?php echo $uri; ?>";
            }
        });

        clipboard.on('success', function (e) {
            alert('复制成功');
        });

        clipboard.on('error', function (e) {
            alert('复制失败，请选中链接地址复制');
        });
    </script>

</html>