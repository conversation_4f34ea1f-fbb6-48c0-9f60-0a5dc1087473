<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no" />
    <title>login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: PingFang SC;
        }

        html,
        body {
            height: 100%;
            width: 100%;
            color: #313131;
        }

        ol,
        ul {
            list-style: none;
        }

        input,
        button {
            outline: none;
            border: none;
            background: transparent;
        }

        textarea {
            border: none;
            resize: none;
            outline: none;
        }

        button {
            cursor: pointer;
        }

        a {
            text-decoration: none;
        }

        .thin {
            font-weight: 300;
        }

        .strong {
            font-weight: bold;
        }

        .pointer {
            cursor: pointer;
        }

        .inline_block {
            display: inline-block;
        }

        @media screen and (min-width: 750px) {
            .login_container {
                width: 450px;
                min-height: 600px;
                background: url("/theme/go/image/login_bg.png") no-repeat center;
                background-size: cover;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                padding-bottom: 30px;
            }

            .login_info p {
                width: 60%;
            }

            .login_btn button {
                width: 60%;
            }

            .treaty {
                width: 60%;
            }
        }

        @media screen and (max-width: 750px) {
            .login_container {
                width: 100%;
                height: 100%;
                background: url("/theme/go/image/login_bg.png") no-repeat center;
                background-size: cover;
                padding-bottom: 30px;
            }

            .login_info p {
                width: 80%;
            }

            .login_btn button {
                width: 80%;
            }

            .treaty {
                width: 80%;
            }
        }

        .login_box {
            padding-top: 80px;
            text-align: center;
        }

        .login_box img {
            width: 120px;
        }

        .login_info {
            padding-top: 20px;
        }

        .login_info p {
            height: 60px;
            line-height: 70px;
            margin: 0 auto;
            border-bottom: 1px dashed #fff;
            text-align: left;
            overflow: hidden;
        }

        .login_info p input {
            font-size: 18px;
            color: #fff;
        }

        input::-webkit-input-placeholder {
            color: #fff;
        }

        input:-moz-placeholder {
            color: #fff;
        }

        input::-moz-placeholder {
            color: #fff;
        }

        input:-ms-input-placeholder {
            color: #fff;
        }

        .login_tips {
            color: #d0021b;
            height: 30px;
            line-height: 40px;
        }

        .login_info p button {
            font-size: 18px;
            color: #4a90e2;
            background: #fff;
            width: 50%;
            height: 40px;
            float: right;
            margin-top: 13px;
        }

        .login_btn {
            margin-top: 10px;
        }

        .login_btn button {
            font-size: 20px;
            color: #ffff;
            background: #1463ff;
            height: 50px;
        }

        .treaty {
            margin: 0 auto;
            color: #fff;
            height: 30px;
            line-height: 30px;
            text-align: left;
            margin-top: 30px;
        }

        .treaty .check {
            width: 20px;
            height: 20px;
            border: 1px solid #000;
            vertical-align: sub;
            background: #fff;
        }

        .active {
            background: url("/theme/go/image/check.png") 20px center !important;
            background-size: 22px !important;
        }

        .copyright {
            font-size: 12px;
            color: #fff;
            position: fixed;
            width: 100%;
            bottom: 30px;
        }

        .copyright p {
            text-align: center;
        }

        a {
            color: #fff;
        }
    </style>
</head>

<body>
    <div class="login_container">
        <div class="login_box">
            <!-- logo -->
            <div class="logo">
                <img src="/theme/go/image/logo.png" alt="" />
            </div>

            <!-- 登录信息 -->
            <div class="login_info">
                <p>
                    <input class="thin" type="text" name="tel" id="phone" placeholder="请输入手机号码" />
                </p>
                <p>
                    <input class="thin" type="text" name="ver" id="code" placeholder="请输入验证码" style="width: 40%" />
                    <button class="thin radius pro_send_sms">获取验证码</button>
                </p>
            </div>

            <!-- 提示信息 -->
            <div class="login_tips thin"></div>

            <!-- 登录按钮 -->
            <div class="login_btn">
                <button class="radius">登 录</button>
            </div>

            <!-- 隐私协议 -->
            <div class="treaty thin">
                <span class="check inline_block"></span>
                阅读并同意<a href="/go/privacy_policy" target="_blank">《隐私协议》</a>
            </div>

            <!-- copyright -->
            <div class="copyright thin">
                <p>© 2004-2020 健康通(北京) 京ICP备13039326号-8</p>
            </div>
        </div>
    </div>

    <script src="/theme/new_manage/umeditor/third-party/jquery.min.js"></script>
    <script>
        $(function() {

            // 禁用选择输入历史
            $("input").attr('autocomplete', 'off');

            // checkbox效果;
            $(".treaty .check").click(function() {
                $(this).toggleClass("active");
                $(".login_tips").text("");
            });

            // 手机验证
            $("input[name='tel']").blur(function() {
                if (!/^1[3456789]\d{9}$/.test($(this).val().trim())) {
                    $(".login_tips").text("请输入正确的手机号");
                    $(this).removeClass("correct");
                } else {
                    $(".login_tips").text("");
                    $(this).addClass("correct");
                }
            });


            // 获取验证码
            var scene = "<?php echo $scene; ?>";
            $(".pro_send_sms").click(function() {

                // 是否同意隐私以协议的验证
                if (!$(".treaty .check").hasClass("active")) {
                    $(".login_tips").text("请先阅读并同意隐私协议");
                    return
                }


                if (!$(this).hasClass("send")) {
                    var verify_mobile = $("#phone").val().trim();


                    if (verify_mobile) {
                        $.ajax({
                            url: "/go/go_survey_sms",
                            type: "post",
                            data: {
                                verify_mobile: verify_mobile,
                                code: '<?php echo $code;?>'
                            },
                            dataType: "json",
                            success: function(info) {
                                console.log(info)
                                $(".login_tips").text(info.rs_msg)
                                if (info.rs_code == "success") {
                                    var time = 60;
                                    getRandomCode();
                                    //倒计时
                                    function getRandomCode() {
                                        if (time === 0) {
                                            time = 60;
                                            $(".pro_send_sms").removeClass("send");
                                            $(".pro_send_sms").text("重新发送");
                                            return;
                                        } else {
                                            time--;
                                            $(".pro_send_sms").addClass("send");
                                            $(".pro_send_sms").text(time + "s");
                                        }
                                        setTimeout(function() {
                                            getRandomCode();
                                        }, 1000);
                                    }
                                }
                            }
                        })
                    } else {
                        $(".login_tips").text("请输入手机号")
                    }

                }
            })


            // 登录
            $(".login_btn button").click(function() {

                // 是否同意隐私以协议的验证
                if (!$(".treaty .check").hasClass("active")) {
                    $(".login_tips").text("请先阅读并同意隐私协议");
                    return
                }

                var verify_code = $("#code").val()
                var verify_mobile = $("#phone").val().trim();
                // 验证码验证
                $("input[name='ver']").blur(function() {
                    if ($(this).val().trim() != verify_code) {
                        $(".login_tips").text("请输入正确的验证码");
                        $(this).removeClass("correct");
                    } else {
                        $(".login_tips").text("");
                        $(this).addClass("correct");
                    }
                });

                $.ajax({
                    type: "post",
                    url: "/go/go_survey",
                    data: {
                        scene: scene,
                        verify_mobile: verify_mobile,
                        verify_code: verify_code,
                        code: '<?php echo $code;?>'
                    },
                    dataType: "json",
                    success: function(info) {
                        console.log(info)
                        if (info.rs_code == "success") {
                            if (info.rs_backurl) {
                                window.location.href = info.rs_backurl
                            } else {
                                window.location.href = "/"
                            }
                        } else {
                            // alert(info.rs_msg);
                            // return false;
                            $(".login_tips").text(info.rs_msg);
                            return false;
                        }

                    }
                })
            })
        });
    </script>
</body>

</html>
