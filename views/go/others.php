<!DOCTYPE>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, minimal-ui" />
    <meta name="keywords" content="上医说" />
    <meta name="description" content="上医说" />
    <meta name="renderer" content="webkit" />
    <meta name="robots" content="all,index,follow" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="browsermode" content="application" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <title>上医说</title>
    <link rel="shortcut icon" href="/theme/images/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="/theme/management/font-awesome/css/font-awesome.css" />
    <script src="/theme/<?= TEMPLATE_DIR?>/js/jquery-3.1.1.min.js"></script>
    <link href="/theme/css/web_style.css" rel="stylesheet">
    <!--防止被网络广告劫持弹出广告-->
    <style>
        html{display:none;}
    </style>
    <script>
        if( self == top ) {
            document.documentElement.style.display = 'block' ;
        } else {
            top.location = self.location ;
        }
    </script>
    <!--防止被网络广告劫持弹出广告-->

    <!-- font rem start -->
    <script>
        function px2rem() {
            var cw = parseInt(document.documentElement.getBoundingClientRect().width);
            cw = cw > 640 ? 640 : cw;
            window.rem = cw / 16;
            document.documentElement.style.fontSize = window.rem + 'px';
        }
        px2rem();
    </script>
    <!-- font rem end -->
</head>

<body onselectstart="return false" onresize="px2rem()">
<div class="layout">
        <div class="logo">
            <a href="javascript:;" target="_blank" title="drsay">
                <img src="/theme/admin/im/img/drsay_logo.jpg" title="drsay" alt="drsay" border="0" />
            </a>

            <div class="qrcode"><img src="/theme/admin/im/img/exchange.jpg" alt="" /></div>
        </div>
        <div class="box_pic_top"></div>
        <div class="box_pic_center">
            <h1 class="title_font">
                <span class="STYLE1">非常欢迎您参加本次调查、您的声音将会得到足够的重视！</span>
                <br>
                <br>
                <span style="color:red;">扫描右上方二维码进入上医说平台，跟踪项目进度，实时兑换！</span>
            </h1>
            <h2>电话或面访项目，无正式调查问卷！
                <br />客服电话：<span style="color:blue;font-weight: bold;">4009201081</span>
                <br />微信号：<span style="color:blue;font-weight: bold;">健康通JKT</span>
                <br />问卷链接：<span style="color:blue;font-weight: bold;"><?php echo $survey_link ? $survey_link : "";?></span>
                <br />项目编号：<span style="color:blue;font-weight: bold;"><?php echo $pid ? cus_pid($pid, "HCP") : "";?></span>
                <br />您的会员ID：<span style="color:blue;font-weight: bold;"><?php echo $uid;?></span>
            </h2>
            <br />
        </div>
        <div class="box_pic_bottom"></div>
</div>
</div>

<div class="footer" style="clear:both;">
    Copyright &copy; <?php echo date('Y');?> Powered by drsay.cn.
</div>

<script>
    var type = parseInt(document.body.getAttribute("data-return"));
    var u = navigator.userAgent,
        app = navigator.appVersion;
    var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //g
    var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端

    if(isIOS){
        var bridge = getJsBridge();
        bridge.call("completeApp", function (v) {});//带有App的是html页面调用App的方法
    }else{
        dsBridge.call("completeApp", function (v) {});//带有App的是html页面调用App的方法
    }
</script>
</body>
</html>
